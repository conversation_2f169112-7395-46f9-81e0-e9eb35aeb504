import{r as V,o as i,c as y,a as s,u as a,w as x,F as $,Z as T,b as o,d as I,e as _,f as r,k as f,v as b,n as P,g as N,T as O}from"./app-b7a94f67.js";import{_ as B,a as F}from"./AdminLayout-0f1fdf67.js";import{_ as u}from"./InputError-86b88c86.js";import{_ as d}from"./InputLabel-11b5d690.js";import{P as A}from"./PrimaryButton-4ffecd1c.js";import{_ as c}from"./TextInput-fea73171.js";import{_ as E}from"./SearchableDropdown-711fb977.js";/* empty css                                                                          */import{_ as j}from"./FileUpload-aa40fe3a.js";import{u as H}from"./index-5a4eda7d.js";import"./_plugin-vue_export-helper-c27b6911.js";const q={class:"animate-top h-screen"},z=o("div",{class:"sm:flex sm:items-center"},[o("div",{class:"sm:flex-auto"},[o("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create Maintenance")])],-1),Z={class:"mt-8 bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},G=["onSubmit"],J={class:"border-b border-gray-900/10 pb-12"},K={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},L={class:"sm:col-span-4"},Q={class:"sm:col-span-4"},R={class:"sm:col-span-2"},W={class:"sm:col-span-2"},X={class:"sm:col-span-3"},Y={class:"sm:col-span-3"},ee={class:"sm:col-span-3"},te={class:"relative mt-2"},ae={class:"sm:col-span-3"},ne={class:"sm:col-span-5"},se={class:"sm:col-span-2"},oe={class:"sm:col-span-2"},le={class:"sm:col-span-3"},ie={class:"sm:col-span-2"},de={class:"sm:col-span-2"},re=o("div",{class:"sm:col-span-10"},null,-1),me={key:0,class:"sm:col-span-3"},ue={key:1,class:"sm:col-span-3"},pe={key:2,class:"sm:col-span-3"},_e={key:3,class:"sm:col-span-3"},ce={class:"flex mt-6 items-center justify-between"},ge={class:"ml-auto flex items-center justify-end gap-x-6"},ve=o("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),ye={key:0,class:"text-sm text-gray-600"},Ue={__name:"Add",props:{maintenance_type:Array,filepath:Array},setup(k){const e=H("post","/maintenance-contract",{hospital_name:"",address:"",city:"",contact_no:"",contract_start_date:"",contract_end_date:"",maintenance_type:"",time_period:"",product_name:"",price:"",company_name:"",invoice_number:"",pm_date_1:"",pm_date_2:"",pm_date_3:"",pm_date_4:"",name:"/uploads/maintenancecontract/defaultimg.png"}),S=(p,t,n)=>{e[p]=t},h=V(""),l=V({pm_date_1:"",pm_date_2:"",pm_date_3:"",pm_date_4:""}),g=V(0),w=V(""),M=()=>{const p=parseInt(h.value,10);if(g.value=isNaN(p)?0:p,!e.contract_start_date){l.value={pm_date_1:"",pm_date_2:"",pm_date_3:"",pm_date_4:""};return}const t=new Date(e.contract_start_date);if(l.value={pm_date_1:"",pm_date_2:"",pm_date_3:"",pm_date_4:""},g.value===2){const n=new Date(t),m=new Date(t);n.setMonth(n.getMonth()+6),m.setMonth(m.getMonth()+11),l.value={pm_date_1:n.toISOString().split("T")[0],pm_date_2:m.toISOString().split("T")[0]}}else if(g.value===3){const n=new Date(t),m=new Date(t),v=new Date(t);n.setMonth(n.getMonth()+4),m.setMonth(m.getMonth()+8),v.setMonth(v.getMonth()+12),l.value={pm_date_1:n.toISOString().split("T")[0],pm_date_2:m.toISOString().split("T")[0],pm_date_3:v.toISOString().split("T")[0]}}else if(g.value===4){const n=new Date(t),m=new Date(t),v=new Date(t),D=new Date(t);n.setMonth(n.getMonth()+3),m.setMonth(m.getMonth()+6),v.setMonth(v.getMonth()+9),D.setMonth(D.getMonth()+12),l.value={pm_date_1:n.toISOString().split("T")[0],pm_date_2:m.toISOString().split("T")[0],pm_date_3:v.toISOString().split("T")[0],pm_date_4:D.toISOString().split("T")[0]}}g.value<=0?w.value="Please enter a valid number of PMs.":w.value=""},C=p=>{e.name=p},U=async()=>{e.pm_date_1=l.value.pm_date_1,e.pm_date_2=l.value.pm_date_2,e.pm_date_3=l.value.pm_date_3,e.pm_date_4=l.value.pm_date_4;try{await e.submit({preserveScroll:!0,onSuccess:()=>{e.reset(),l.value={pm_date_1:"",pm_date_2:"",pm_date_3:"",pm_date_4:""}}})}catch(p){console.error("Submission error:",p)}};return(p,t)=>(i(),y($,null,[s(a(T),{title:"Maintenance Contract"}),s(B,null,{default:x(()=>[o("div",q,[z,o("div",Z,[o("form",{onSubmit:I(U,["prevent"]),class:""},[o("div",J,[o("div",K,[o("div",L,[s(d,{for:"hospital_name",value:"Hospital Name"}),s(c,{id:"hospital_name",hospital_name:"text",modelValue:a(e).hospital_name,"onUpdate:modelValue":t[0]||(t[0]=n=>a(e).hospital_name=n),autocomplete:"hospital_name",onChange:t[1]||(t[1]=n=>a(e).validate("hospital_name"))},null,8,["modelValue"]),a(e).invalid("hospital_name")?(i(),_(u,{key:0,class:"",message:a(e).errors.hospital_name},null,8,["message"])):r("",!0)]),o("div",Q,[s(d,{for:"address",value:"Address"}),s(c,{id:"address",type:"text",modelValue:a(e).address,"onUpdate:modelValue":t[2]||(t[2]=n=>a(e).address=n),onChange:t[3]||(t[3]=n=>a(e).validate("address"))},null,8,["modelValue"]),a(e).invalid("address")?(i(),_(u,{key:0,class:"",message:a(e).errors.address},null,8,["message"])):r("",!0)]),o("div",R,[s(d,{for:"contact_no",value:"Contact No"}),s(c,{id:"contact_no",contact_no:"text",modelValue:a(e).contact_no,"onUpdate:modelValue":t[4]||(t[4]=n=>a(e).contact_no=n),numeric:!0,autocomplete:"contact_no",onChange:t[5]||(t[5]=n=>a(e).validate("contact_no"))},null,8,["modelValue"]),a(e).invalid("contact_no")?(i(),_(u,{key:0,class:"",message:a(e).errors.contact_no},null,8,["message"])):r("",!0)]),o("div",W,[s(d,{for:"city",value:"City"}),s(c,{id:"city",city:"text",modelValue:a(e).city,"onUpdate:modelValue":t[6]||(t[6]=n=>a(e).city=n),autocomplete:"city",onChange:t[7]||(t[7]=n=>a(e).validate("city"))},null,8,["modelValue"]),a(e).invalid("city")?(i(),_(u,{key:0,class:"",message:a(e).errors.city},null,8,["message"])):r("",!0)]),o("div",X,[s(d,{for:"contract_start_date",value:"Contract Start"}),f(o("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":t[8]||(t[8]=n=>a(e).contract_start_date=n),onChange:t[9]||(t[9]=(...n)=>p.updatePmDates&&p.updatePmDates(...n))},null,544),[[b,a(e).contract_start_date]]),a(e).invalid("contract_start_date")?(i(),_(u,{key:0,message:a(e).errors.contract_start_date},null,8,["message"])):r("",!0)]),o("div",Y,[s(d,{for:"contract_end_date",value:"Contract End"}),f(o("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":t[10]||(t[10]=n=>a(e).contract_end_date=n),onChange:t[11]||(t[11]=n=>a(e).validate("contract_end_date"))},null,544),[[b,a(e).contract_end_date]]),a(e).invalid("contract_end_date")?(i(),_(u,{key:0,class:"",message:a(e).errors.contract_end_date},null,8,["message"])):r("",!0)]),o("div",ee,[s(d,{for:"type",value:"Maintenance Type"}),o("div",te,[s(E,{options:k.maintenance_type,modelValue:a(e).maintenance_type,"onUpdate:modelValue":t[12]||(t[12]=n=>a(e).maintenance_type=n),onOnchange:t[13]||(t[13]=(n,m)=>S("maintenance_type",n,m)),class:P({"error rounded-md":a(e).errors.maintenance_type})},null,8,["options","modelValue","class"])]),a(e).invalid("maintenance_type")?(i(),_(u,{key:0,class:"",message:a(e).errors.maintenance_type},null,8,["message"])):r("",!0)]),o("div",ae,[s(d,{for:"time_period",value:"Time Period"}),s(c,{id:"time_period",time_period:"text",modelValue:a(e).time_period,"onUpdate:modelValue":t[14]||(t[14]=n=>a(e).time_period=n),autocomplete:"time_period",onChange:t[15]||(t[15]=n=>a(e).validate("time_period"))},null,8,["modelValue"]),a(e).invalid("time_period")?(i(),_(u,{key:0,class:"",message:a(e).errors.time_period},null,8,["message"])):r("",!0)]),o("div",ne,[s(d,{for:"product_name",value:"Equipment Name"}),s(c,{id:"product_name",product_name:"text",modelValue:a(e).product_name,"onUpdate:modelValue":t[16]||(t[16]=n=>a(e).product_name=n),autocomplete:"product_name",onChange:t[17]||(t[17]=n=>a(e).validate("product_name"))},null,8,["modelValue"]),a(e).invalid("product_name")?(i(),_(u,{key:0,class:"",message:a(e).errors.product_name},null,8,["message"])):r("",!0)]),o("div",se,[s(d,{for:"price",value:"Price"}),s(c,{id:"price",price:"text",modelValue:a(e).price,"onUpdate:modelValue":t[18]||(t[18]=n=>a(e).price=n),autocomplete:"price",onChange:t[19]||(t[19]=n=>a(e).validate("price"))},null,8,["modelValue"]),a(e).invalid("price")?(i(),_(u,{key:0,class:"",message:a(e).errors.price},null,8,["message"])):r("",!0)]),o("div",oe,[s(d,{for:"company_name",value:"Company Name"}),s(c,{id:"company_name",company_name:"text",modelValue:a(e).company_name,"onUpdate:modelValue":t[20]||(t[20]=n=>a(e).company_name=n),autocomplete:"company_name",onChange:t[21]||(t[21]=n=>a(e).validate("company_name"))},null,8,["modelValue"]),a(e).invalid("company_name")?(i(),_(u,{key:0,class:"",message:a(e).errors.company_name},null,8,["message"])):r("",!0)]),o("div",le,[s(d,{for:"invoice_number",value:"Invoice Number"}),s(c,{id:"invoice_number",invoice_number:"text",modelValue:a(e).invoice_number,"onUpdate:modelValue":t[22]||(t[22]=n=>a(e).invoice_number=n),autocomplete:"invoice_number",onChange:t[23]||(t[23]=n=>a(e).validate("invoice_number"))},null,8,["modelValue"]),a(e).invalid("invoice_number")?(i(),_(u,{key:0,class:"",message:a(e).errors.invoice_number},null,8,["message"])):r("",!0)]),o("div",ie,[s(d,{for:"inputField",value:"How Many PM ?"}),s(c,{id:"inputField",modelValue:h.value,"onUpdate:modelValue":t[24]||(t[24]=n=>h.value=n),onInput:M,onBlur:M},null,8,["modelValue"]),s(u,{message:w.value},null,8,["message"])]),o("div",de,[s(d,{for:"name",value:"Upload Document"}),s(j,{label:"Upload Document",inputId:"name",inputName:"name",fileUrl:a(e).name,onFile:C},null,8,["fileUrl"])]),re,g.value>=1?(i(),y("div",me,[s(d,{for:"pm_date_1",value:"PM Date 1"}),f(o("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":t[25]||(t[25]=n=>l.value.pm_date_1=n),readonly:""},null,512),[[b,l.value.pm_date_1]])])):r("",!0),g.value>=2?(i(),y("div",ue,[s(d,{for:"pm_date_2",value:"PM Date 2"}),f(o("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":t[26]||(t[26]=n=>l.value.pm_date_2=n),readonly:""},null,512),[[b,l.value.pm_date_2]])])):r("",!0),g.value>=3?(i(),y("div",pe,[s(d,{for:"pm_date_3",value:"PM Date 3"}),f(o("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":t[27]||(t[27]=n=>l.value.pm_date_3=n),readonly:""},null,512),[[b,l.value.pm_date_3]])])):r("",!0),g.value>=4?(i(),y("div",_e,[s(d,{for:"pm_date_4",value:"PM Date 4"}),f(o("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":t[28]||(t[28]=n=>l.value.pm_date_4=n),readonly:""},null,512),[[b,l.value.pm_date_4]])])):r("",!0)])]),o("div",ce,[o("div",ge,[s(F,{href:p.route("maintenance-contract.index")},{svg:x(()=>[ve]),_:1},8,["href"]),s(A,{disabled:a(e).processing},{default:x(()=>[N("Save")]),_:1},8,["disabled"]),s(O,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:x(()=>[a(e).recentlySuccessful?(i(),y("p",ye,"Saved.")):r("",!0)]),_:1})])])],40,G)])])]),_:1})],64))}};export{Ue as default};
