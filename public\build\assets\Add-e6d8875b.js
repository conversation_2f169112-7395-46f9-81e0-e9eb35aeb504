import{r as j,o as r,c as f,a as n,u as s,w as v,F as x,Z as N,b as o,t as q,k as _,v as M,d as B,n as V,e as d,f as u,P as g,i as D,g as A,T as E}from"./app-6a429cee.js";import{_ as O,a as P}from"./AdminLayout-dc64724f.js";import{_ as i}from"./InputError-17731bba.js";import{_ as l}from"./InputLabel-5e6ac969.js";import{P as T}from"./PrimaryButton-c589c744.js";import{_ as c}from"./TextInput-94a28154.js";import{_ as w}from"./TextArea-217f7d79.js";import{_ as h}from"./SearchableDropdown-aa57848c.js";import{C as W}from"./CheckboxWithLabel-d475950d.js";import{u as F}from"./index-beae658c.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                                          */const J={class:"animate-top h-screen"},L={class:"sm:flex sm:items-center"},R=o("div",{class:"sm:flex-auto"},[o("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create Jobcard")],-1),z={class:"w-auto"},H={class:"flex space-x-2"},I=o("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Jobcard Number:",-1),Z={class:"text-sm font-semibold text-gray-900 leading-6"},G={class:"flex space-x-2 items-center"},K=o("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Date :",-1),Q={class:"mt-8 bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},X=["onSubmit"],Y={class:"border-b border-gray-900/10 pb-12"},ee={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},se={class:"sm:col-span-5"},te={class:"relative mt-2"},ae=o("div",{class:"sm:col-span-7"},null,-1),oe={class:"sm:col-span-4"},ne={class:"sm:col-span-2"},le={class:"sm:col-span-2"},re={class:"sm:col-span-4"},ie={class:"sm:col-span-3"},de={class:"sm:col-span-3"},ue={class:"sm:col-span-3"},me={class:"sm:col-span-3"},ce={class:"sm:col-span-6"},pe={class:"sm:col-span-6"},_e={class:"sm:col-span-3"},ve={class:"relative mt-2"},ge={class:"sm:col-span-3"},ye={class:"mt-2 space-y-2"},fe={class:"flex items-center space-x-2"},be=o("span",null,"Warranty",-1),xe={class:"flex items-center space-x-2"},Ve=o("span",null,"Out of Warranty",-1),we={class:"flex items-center space-x-2"},he=o("span",null,"AMC",-1),Ce={class:"flex items-center space-x-2"},ke=o("span",null,"CMC",-1),$e={class:"sm:col-span-6"},Ue={class:"grid sm:grid-cols-6 relative mt-2"},Se={class:"flex mt-6 items-center justify-between"},je={class:"ml-auto flex items-center justify-end gap-x-6"},Ne=o("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),qe={key:0,class:"text-sm text-gray-600"},Re={__name:"Add",props:["customers","engineer","warranty_status","jobcard_number","checklist"],setup(p){const b=p,y=j([]),e=F("post","/jobcard",{customer_id:"",job_card_number:b.jobcard_number,engineer_id:"",hospital_name:"",address:"",city:"",contact_no:"",product_name:"",product_code:"",serial_no:"",accessories:"",warranty_status:"",jobchecks:[],date:new Date().toISOString().slice(0,10)}),C=()=>{e.jobchecks=y.value,e.submit({preserveScroll:!0,onSuccess:()=>e.reset()})},k=(m,t)=>{e.engineer_id=m,e.errors.engineer_id=null},$=m=>{y.value=m},U=(m,t)=>{if(e.customer_id=m,m=="new_customer")e.hospital_name="",e.address="",e.city="",e.contact_no="";else{const a=b.customers.find(S=>S.id===m);a&&(e.hospital_name=a.customer_name,e.address=a.address,e.city=a.city,e.contact_no=a.contact_no)}};return(m,t)=>(r(),f(x,null,[n(s(N),{title:"Jobcard"}),n(O,null,{default:v(()=>[o("div",J,[o("div",L,[R,o("div",z,[o("div",H,[I,o("span",Z,q(p.jobcard_number),1)]),o("div",G,[K,_(o("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":t[0]||(t[0]=a=>s(e).date=a),onChange:t[1]||(t[1]=a=>s(e).validate("date"))},null,544),[[M,s(e).date]])])])]),o("div",Q,[o("form",{onSubmit:B(C,["prevent"]),class:""},[o("div",Y,[o("div",ee,[o("div",se,[n(l,{for:"customer_id",value:"Customer Name"}),o("div",te,[n(h,{options:p.customers,modelValue:s(e).customer_id,"onUpdate:modelValue":t[2]||(t[2]=a=>s(e).customer_id=a),onOnchange:U,class:V({"error rounded-md":s(e).errors.customer_id})},null,8,["options","modelValue","class"])])]),ae,o("div",oe,[n(l,{for:"hospital_name",value:"Hospital Name"}),n(c,{id:"hospital_name",hospital_name:"text",modelValue:s(e).hospital_name,"onUpdate:modelValue":t[3]||(t[3]=a=>s(e).hospital_name=a),autocomplete:"hospital_name",onChange:t[4]||(t[4]=a=>s(e).validate("hospital_name"))},null,8,["modelValue"]),s(e).invalid("hospital_name")?(r(),d(i,{key:0,class:"",message:s(e).errors.hospital_name},null,8,["message"])):u("",!0)]),o("div",ne,[n(l,{for:"contact_no",value:"Contact No"}),n(c,{id:"contact_no",contact_no:"text",modelValue:s(e).contact_no,"onUpdate:modelValue":t[5]||(t[5]=a=>s(e).contact_no=a),numeric:!0,autocomplete:"contact_no",onChange:t[6]||(t[6]=a=>s(e).validate("contact_no"))},null,8,["modelValue"]),s(e).invalid("contact_no")?(r(),d(i,{key:0,class:"",message:s(e).errors.contact_no},null,8,["message"])):u("",!0)]),o("div",le,[n(l,{for:"city",value:"City"}),n(c,{id:"city",city:"text",modelValue:s(e).city,"onUpdate:modelValue":t[7]||(t[7]=a=>s(e).city=a),autocomplete:"city",onChange:t[8]||(t[8]=a=>s(e).validate("city"))},null,8,["modelValue"]),s(e).invalid("city")?(r(),d(i,{key:0,class:"",message:s(e).errors.city},null,8,["message"])):u("",!0)]),o("div",re,[n(l,{for:"address",value:"Address"}),n(w,{id:"address",type:"text",modelValue:s(e).address,"onUpdate:modelValue":t[9]||(t[9]=a=>s(e).address=a),onChange:t[10]||(t[10]=a=>s(e).validate("address"))},null,8,["modelValue"]),s(e).invalid("address")?(r(),d(i,{key:0,class:"",message:s(e).errors.address},null,8,["message"])):u("",!0)]),o("div",ie,[n(l,{for:"product_name",value:"Equipment"}),n(c,{id:"product_name",product_name:"text",modelValue:s(e).product_name,"onUpdate:modelValue":t[11]||(t[11]=a=>s(e).product_name=a),autocomplete:"product_name",onChange:t[12]||(t[12]=a=>s(e).validate("product_name"))},null,8,["modelValue"]),s(e).invalid("product_name")?(r(),d(i,{key:0,class:"",message:s(e).errors.product_name},null,8,["message"])):u("",!0)]),o("div",de,[n(l,{for:"product_code",value:"Model"}),n(c,{id:"product_code",product_code:"text",modelValue:s(e).product_code,"onUpdate:modelValue":t[13]||(t[13]=a=>s(e).product_code=a),autocomplete:"product_code",onChange:t[14]||(t[14]=a=>s(e).validate("product_code"))},null,8,["modelValue"]),s(e).invalid("product_code")?(r(),d(i,{key:0,class:"",message:s(e).errors.product_code},null,8,["message"])):u("",!0)]),o("div",ue,[n(l,{for:"serial_no",value:"Serial No"}),n(c,{id:"serial_no",serial_no:"text",modelValue:s(e).serial_no,"onUpdate:modelValue":t[15]||(t[15]=a=>s(e).serial_no=a),autocomplete:"serial_no",onChange:t[16]||(t[16]=a=>s(e).validate("serial_no"))},null,8,["modelValue"]),s(e).invalid("serial_no")?(r(),d(i,{key:0,class:"",message:s(e).errors.serial_no},null,8,["message"])):u("",!0)]),o("div",me,[n(l,{for:"accessories",value:"Accessories"}),n(c,{id:"accessories",accessories:"text",modelValue:s(e).accessories,"onUpdate:modelValue":t[17]||(t[17]=a=>s(e).accessories=a),autocomplete:"accessories",onChange:t[18]||(t[18]=a=>s(e).validate("accessories"))},null,8,["modelValue"]),s(e).invalid("accessories")?(r(),d(i,{key:0,class:"",message:s(e).errors.accessories},null,8,["message"])):u("",!0)]),o("div",ce,[n(l,{for:"problem_description",value:"Description"}),n(w,{id:"problem_description",type:"text",rows:3,modelValue:s(e).problem_description,"onUpdate:modelValue":t[19]||(t[19]=a=>s(e).problem_description=a),onChange:t[20]||(t[20]=a=>s(e).validate("problem_description"))},null,8,["modelValue"]),n(i,{class:"",message:s(e).errors.problem_description},null,8,["message"])]),o("div",pe,[n(l,{for:"parts_required",value:"Parts Required"}),n(c,{id:"parts_required",type:"text",modelValue:s(e).parts_required,"onUpdate:modelValue":t[21]||(t[21]=a=>s(e).parts_required=a),autocomplete:"parts_required",onChange:t[22]||(t[22]=a=>s(e).validate("parts_required"))},null,8,["modelValue"]),n(i,{class:"",message:s(e).errors.parts_required},null,8,["message"])]),o("div",_e,[n(l,{for:"engineer_id",value:"Engineer Name"}),o("div",ve,[n(h,{options:p.engineer,modelValue:s(e).engineer_id,"onUpdate:modelValue":t[23]||(t[23]=a=>s(e).engineer_id=a),onOnchange:k,class:V({"error rounded-md":s(e).errors.engineer_id})},null,8,["options","modelValue","class"])])]),o("div",ge,[n(l,{for:"warranty_status",value:"Warranty Status"}),o("div",ye,[o("label",fe,[_(o("input",{type:"radio",id:"warranty",value:"warranty","onUpdate:modelValue":t[24]||(t[24]=a=>s(e).warranty_status=a),onChange:t[25]||(t[25]=a=>s(e).validate("warranty_status")),class:"text-indigo-600 focus:ring-indigo-500 border-gray-300"},null,544),[[g,s(e).warranty_status]]),be]),o("label",xe,[_(o("input",{type:"radio",id:"out_of_warranty",value:"out_of_warranty","onUpdate:modelValue":t[26]||(t[26]=a=>s(e).warranty_status=a),onChange:t[27]||(t[27]=a=>s(e).validate("warranty_status")),class:"text-indigo-600 focus:ring-indigo-500 border-gray-300"},null,544),[[g,s(e).warranty_status]]),Ve]),o("label",we,[_(o("input",{type:"radio",id:"amc",value:"amc","onUpdate:modelValue":t[28]||(t[28]=a=>s(e).warranty_status=a),onChange:t[29]||(t[29]=a=>s(e).validate("warranty_status")),class:"text-indigo-600 focus:ring-indigo-500 border-gray-300"},null,544),[[g,s(e).warranty_status]]),he]),o("label",Ce,[_(o("input",{type:"radio",id:"cmc",value:"cmc","onUpdate:modelValue":t[30]||(t[30]=a=>s(e).warranty_status=a),onChange:t[31]||(t[31]=a=>s(e).validate("warranty_status")),class:"text-indigo-600 focus:ring-indigo-500 border-gray-300"},null,544),[[g,s(e).warranty_status]]),ke])]),s(e).invalid("warranty_status")?(r(),d(i,{key:0,message:s(e).errors.warranty_status},null,8,["message"])):u("",!0)]),o("div",$e,[n(l,{for:"engineer_id",value:"Checklist"}),o("div",Ue,[(r(!0),f(x,null,D(p.checklist,a=>(r(),d(W,{key:a.id,checked:y.value,value:a.id,label:a.type,"onUpdate:checked":$},null,8,["checked","value","label"]))),128))])])])]),o("div",Se,[o("div",je,[n(P,{href:m.route("jobcard.index")},{svg:v(()=>[Ne]),_:1},8,["href"]),n(T,{disabled:s(e).processing},{default:v(()=>[A("Save")]),_:1},8,["disabled"]),n(E,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:v(()=>[s(e).recentlySuccessful?(r(),f("p",qe,"Saved.")):u("",!0)]),_:1})])])],40,X)])])]),_:1})],64))}};export{Re as default};
