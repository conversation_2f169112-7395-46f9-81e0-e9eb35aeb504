<?php

namespace App\Http\Controllers;

use App\Traits\QueryTrait;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Company;
use App\Models\Organization;
use App\Models\PurchaseTransaction;
use App\Models\BankInfo;
use App\Models\PaymentPaid;
use App\Models\CompanyCredit;
use App\Models\BankTransaction;
use App\Models\CustomerTransaction;
use App\Models\Customer;
use App\Exports\CompanyTransactionExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Product;
use App\Http\Requests\CompanyStoreRequest;
use App\Http\Requests\paymentRequest;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Config;

class CompanyController extends Controller
{
    use QueryTrait;

    public function __construct()
    {
        $this->middleware('permission:List Company')->only(['index']);
        $this->middleware('permission:Create Company')->only(['create', 'store']);
        $this->middleware('permission:Edit Company')->only(['edit', 'update']);
        $this->middleware('permission:Delete Company')->only('destroy');
        $this->middleware('permission:Activation Company')->only('activation');
        $this->middleware('permission:Transaction Company')->only('companiesTransaction');
    }

    public function index(Request $request)
    {
        $query = Company::query();

        $query->withSum(['companyTransactions as credit_sum' => function ($q) {
            $q->where('payment_type', 'cr');
        }], 'amount');

        $query->withSum(['companyTransactions as debit_sum' => function ($q) {
            $q->where('payment_type', 'dr');
        }], 'amount');

        $searchableFields = ['name', 'website', 'gst_no', 'email', 'contact_no'];
        $this->searchAndSort($query, $request, $searchableFields);

        $data = $query->orderByRaw('name')->paginate(20);

        $partyIds = $data->pluck('party_id')->filter()->unique()->toArray();
        $customersByParty = Customer::whereIn('party_id', $partyIds)->get()->groupBy('party_id');
        $customerIds = $customersByParty->flatten()->pluck('id')->unique();

        $companyCreditSums = CustomerTransaction::selectRaw('customer_id, SUM(amount) as credit_sum')
            ->whereIn('customer_id', $customerIds)
            ->where('payment_type', 'cr')
            ->groupBy('customer_id')
            ->pluck('credit_sum', 'customer_id');

        $companyDebitSums = CustomerTransaction::selectRaw('customer_id, SUM(amount) as debit_sum')
            ->whereIn('customer_id', $customerIds)
            ->where('payment_type', 'dr')
            ->groupBy('customer_id')
            ->pluck('debit_sum', 'customer_id');

        $data->getCollection()->transform(function ($customer) use ( $customersByParty, $companyCreditSums, $companyDebitSums){
            $credit = $customer->credit_sum ?? 0;
            $debit = $customer->debit_sum ?? 0;
            $purchaseCr = 0;
            $purchaseDr = 0;

            if ($customer->party_id && isset($customersByParty[$customer->party_id])) {
                foreach ($customersByParty[$customer->party_id] as $company) {
                    $purchaseCr += $companyCreditSums[$company->id] ?? 0;
                    $purchaseDr += $companyDebitSums[$company->id] ?? 0;
                }
            }

            $balance = ($purchaseCr - $purchaseDr ) + ($credit - $debit);
            $prefix = $balance >= 0 ? 'cr' : 'dr';
            $customer->balance = $this->formatIndianRupees(abs($balance)) . ' ' . $prefix;
            $customer->balance_value = $balance;
            return $customer;
        });

        $paymentType = Config::get('constants.paymentTypes');
        $bankInfo  = BankInfo::select('id', DB::raw("CONCAT(bank_name, ' - ', account_number) AS name"), 'organization_id')->get();
        $organization  = Organization::select('id', 'name')->get();

        $permissions = [
            'canCreateCompany'      => auth()->user()->can('Create Company'),
            'canEditCompany'        => auth()->user()->can('Edit Company'),
            'canDeleteCompany'      => auth()->user()->can('Delete Company'),
            'canTransactionCompany' => auth()->user()->can('Transaction Company'),
            'canViewProducts'       => auth()->user()->can('List Products'),
        ];

        return Inertia::render('Company/List', compact('data', 'permissions', 'bankInfo', 'organization', 'paymentType'));
    }

    public function create()
    {
        $company_type = Config::get('constants.customerType');
        $gst_type = Config::get('constants.gstType');
        return Inertia::render('Company/Add' ,compact('company_type', 'gst_type'));
    }

    public function store(CompanyStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            $data['created_by'] = $data['updated_by'] = Auth::id();
            Company::create($data);
            DB::commit();
            return Redirect::to('/companies')->with('success', 'Company Added Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data = Company::findOrFail($id);
        $company_type = Config::get('constants.customerType');
        $gst_type = Config::get('constants.gstType');
        return Inertia::render('Company/Edit', compact('data', 'company_type', 'gst_type'));
    }

    public function update(CompanyStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $data['updated_by'] = Auth::id();
            $company = Company::findOrFail($request->id);
            $company->update($data);
            DB::commit();
            return Redirect::to('/companies')->with('success', 'Company Info Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $company = Company::findOrFail($id);
            if ($company->products()->count() > 0) {
                return Redirect::to('/companies')->with('error', 'Company cannot be deleted as it has associated products');
            }
            $company->delete();
            DB::commit();
            return Redirect::to('/companies')->with('success', 'Company Info Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/companies')->with('error', $e->getMessage());
        }
    }

    public function companiesTransaction($id, Request $request)
    {
        $organization = Organization::select('id', 'name')->get();
        $allOption = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOption);

        $organizationId = $request->input('organization_id');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');

        $company = Company::findOrFail($id);
        $companyId = $id;
        $partyId = $company->party_id;

        // Purchase transactions (existing)
        $purchaseQuery = PurchaseTransaction::with('company', 'organization')
            ->where('company_id', $id);

        // Company Credit
        $creditData = CompanyCredit::where('company_id', $id)
            ->where('unused_amount', '>', 0);

        if ($organizationId) {
            $purchaseQuery->where('organization_id', $organizationId);
            $creditData->where('organization_id', $organizationId);
        }

        if ($fromDate && $toDate) {
            $purchaseQuery->whereBetween('date', [$fromDate, $toDate]);
        }

        $purchases = $purchaseQuery->get();
        $sales = collect();

        if ($partyId) {
            $customerIds = Customer::where('party_id', $partyId)->pluck('id');

            $salesQuery = CustomerTransaction::with('customer', 'organization', 'paymentReceive')
                ->whereIn('customer_id', $customerIds);

            if ($organizationId) {
                $salesQuery->where('organization_id', $organizationId);
            }
            $sales = $salesQuery->get();
        }
        // Merge and sort all transactions by date
        $data = $purchases->concat($sales)->sortBy('date')->values();

        $creditdata = $creditData->get()->toArray();

        return Inertia::render('Company/Transaction', compact(
            'data',
            'organization',
            'organizationId',
            'creditdata',
            'companyId',
            'company'
        ));
    }


    public function companyCredit($id, Request $request)
    {
        $organization  = Organization::select('id', 'name')->get();
        $allOption = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOption);
        $organizationId = $request->input('organization_id');
        $query = CompanyCredit::with('paymentpaid.bankInfo', 'company', 'creditDetail.invoice')->where('company_id', $id);
        if ($organizationId) {
            $query->where('organization_id', $organizationId);
        }
        $data = $query->paginate(20);
        $company = Company::find($id);
        $companyId = $id;
        $paymentType = Config::get('constants.paymentType');
        $bankInfo = BankInfo::select('id', DB::raw("CONCAT(bank_name, ' - ', account_number) AS name"), 'organization_id')->get();
        $organizations  = Organization::select('id', 'name')->get();
        return Inertia::render('Company/Credit', compact('data','organization', 'company', 'companyId', 'bankInfo', 'organizations', 'paymentType'));
    }

    public function advancePayment(paymentRequest $request)
    {
        $data = $request->all();
        DB::beginTransaction();
        try {
            $data = $data['data'];
            if(isset($data['id'])){
                $creditInfo = CompanyCredit::find($data['id']);
                $data['updated_by'] = Auth::user()->id;
                $updatePaymentReceive = PaymentPaid::findOrFail($creditInfo->payment_paid_id);
                $updatePaymentReceive->update($data);
                $bankTransactionUpdate = BankTransaction::where(['entity_type' => 'payment_paid', 'entity_id' => $creditInfo->payment_paid_id])->first();
                $data['unused_amount'] = $data['amount'];
                if($data['payment_type'] == 'check'){
                    $data['payment_type'] = 'dr';
                    $data['note'] = 'Cheque No:' .$data['check_number'] .' '.'Advance Payment';
                    $bankTransactionUpdate->update($data);
                    $creditInfo->update($data);
                } else if($data['payment_type'] == 'NEFT'){
                    $data['payment_type'] = 'dr';
                    $data['note'] = 'NEFT'. ' ' .'Advance Payment';
                    $bankTransactionUpdate->update($data);
                    $creditInfo->update($data);
                } else if($data['payment_type'] == 'cash'){
                    $data['payment_type'] = 'dr';
                    $data['note'] = $data['note'].' '.'Advance Payment';
                    $creditInfo->update($data);
                }
                DB::commit();
                return Redirect::back()->with('success','Payment Updated Successfully');
                dd('update');
            } else {
                $data['created_by'] = $data['updated_by'] = Auth::user()->id;
                $paidPayment = PaymentPaid::create($data);
                $data['entity_id'] = $paidPayment->id;
                $data['payment_paid_id'] = $paidPayment->id;
                $data['entity_type'] = 'payment_paid';
                $data['unused_amount'] = $data['amount'];

                if($data['payment_type'] == 'check'){
                $data['payment_type'] = 'dr';
                $data['note'] = 'Cheque No:' .$data['check_number'] .' '.'Advance Payment';
                $bankTransaction = BankTransaction::create($data);
                CompanyCredit::create($data);
                } else if($data['payment_type'] == 'NEFT'){
                $data['payment_type'] = 'dr';
                $data['note'] = 'NEFT'. ' ' .'Advance Payment';
                $bankTransaction = BankTransaction::create($data);
                CompanyCredit::create($data);
                } else if($data['payment_type'] == 'cash'){
                $data['payment_type'] = 'dr';
                $data['note'] = $data['note'].' '.'Advance Payment';
                CompanyCredit::create($data);
                }
                PurchaseTransaction::create($data);
                DB::commit();
                return Redirect::to('/companies')->with('success','Payment Paid Successfully');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/companies')->with('error', $e->getMessage());
        }
    }

    public function activation(Request $request)
    {
        DB::beginTransaction();
        try {
            //Company::where('id', $request->id)->update(['status' => $request->status]);
            // $company = Company::findOrFail($request->id);
            // $company->status = $request->status;
            // $company->save();
            Company::where('id', $request->id)->update(['status' => $request->status]);
            Product::where('company_id', $request->id)->update(['status' => $request->status]);
            DB::commit();
            return Redirect::to('/companies')->with('success', 'Company Status Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/companies')->with('error', $e->getMessage());
        }
    }

    public function exportCompaniesTransaction(Request $request, $id)

        {
            $validated = $request->validate([
                'company_id' => 'required|integer',
                'organization_id' => 'nullable|integer',
                'from_date' => 'nullable|date',
                'to_date' => 'nullable|date',
            ]);

            $companyId = $validated['company_id'];
            $organizationId = $validated['organization_id'] ?? null;
            $fromDate = $validated['from_date'] ?? null;
            $toDate = $validated['to_date'] ?? null;

            $company = Company::findOrFail($companyId);
            // dd($company);
            $partyId = $company->party_id;

            // Get all transactions for opening balance calculation
            $allPurchaseQuery = PurchaseTransaction::with('company', 'organization')
                ->where('company_id', $companyId);
            if ($organizationId) {
                $allPurchaseQuery->where('organization_id', $organizationId);
            }
            $allPurchase = $allPurchaseQuery->get();

            $allSales = collect();
            if ($partyId) {
                $customerIds = Customer::where('party_id', $partyId)->pluck('id');
                $allSalesQuery = CustomerTransaction::with('customer', 'organization')
                    ->whereIn('customer_id', $customerIds);
                if ($organizationId) {
                    $allSalesQuery->where('organization_id', $organizationId);
                }
                $allSales = $allSalesQuery->get();
            }
            $allTransactions = $allPurchase->concat($allSales)->sortBy('date')->values();

            // Get filtered transactions for the specified date range
            $query = PurchaseTransaction::with('company', 'organization')
                ->where('company_id', $companyId);
            if ($organizationId) {
                $query->where('organization_id', $organizationId);
            }
            if ($fromDate && $toDate) {
                $query->whereBetween('date', [$fromDate, $toDate]);
            }
            $purchase = $query->get();

            $sales = collect();
            if ($partyId) {
                $customerIds = Customer::where('party_id', $partyId)->pluck('id');
                $salesQuery = CustomerTransaction::with('customer', 'organization')
                    ->whereIn('customer_id', $customerIds);
                if ($organizationId) {
                    $salesQuery->where('organization_id', $organizationId);
                }
                if ($fromDate && $toDate) {
                    $salesQuery->whereBetween('date', [$fromDate, $toDate]);
                }
                $sales = $salesQuery->get();
            }

            $transactions = $purchase->concat($sales)->sortBy('date')->values();
            $organizationName = $organizationId ? Organization::find($organizationId)->name : 'All Organizations';
            $companyName = $company->name ?? 'Unknown Customer';
            return Excel::download(
                new CompanyTransactionExport($transactions, $organizationName, $companyName, $fromDate, $toDate, $allTransactions),
                'company_transactions.xlsx'
            );
        }

    }
