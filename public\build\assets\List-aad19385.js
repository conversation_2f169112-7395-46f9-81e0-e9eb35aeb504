import{r as h,l as Z,o as n,c as d,a as o,u as v,w as r,F as M,Z as J,b as e,g as f,f as m,i as E,e as I,k as X,t as a,E as D,n as V}from"./app-6a429cee.js";import{s as ee}from"./sortAndSearch-de435c62.js";import{_ as te,b as se,a as oe}from"./AdminLayout-dc64724f.js";import{_ as le}from"./CreateButton-1a5625b4.js";import{_ as A}from"./SecondaryButton-9a822eb1.js";import{P as ne}from"./PrimaryButton-c589c744.js";import{D as ae}from"./DangerButton-c7881a4e.js";import{_ as N}from"./SimpleDropdown-0e0a895b.js";import{_ as q}from"./InputLabel-5e6ac969.js";import{M as $}from"./Modal-b05cc76d.js";import{_ as ie}from"./Pagination-cae8b9a9.js";import{_ as re}from"./ArrowIcon-2f445522.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const de={class:"animate-top"},ce={class:"sm:flex sm:items-center"},ue=e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Funnel")],-1),me={class:"flex justify-end items-center"},_e={class:"ml-6 flex space-x-6 mt-4 sm:mt-0 w-64"},he={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},fe=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),pe={key:0,class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},ge={class:"flex justify-end"},ye={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},ve={class:"flex mb-2"},xe=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),be={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center"},we={key:0,class:"sm:col-span-4"},ke={class:"relative mt-2"},Ce={class:"sm:col-span-4"},Se={class:"relative mt-2"},Me={class:"mt-8 overflow-x-auto sm:rounded-lg"},Ie={class:"shadow sm:rounded-lg"},qe={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Fe={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Be={class:"border-b-2"},Ee=["onClick"],Ve={key:0},Ae={key:0,class:"px-4 py-2.5 min-w-44"},Ne={scope:"row",class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},$e={class:""},Te={class:""},Ue={class:"px-4 py-2.5"},Oe={class:"px-4 py-2.5"},Pe={class:"px-4 py-2.5"},je={class:"px-4 py-2.5 min-w-52"},Le={class:"px-4 py-2.5 min-w-40"},Re={class:"px-4 py-2.5 min-w-40"},ze={class:"px-4 py-2.5 min-w-52"},Ye={class:"flex flex-1 items-center px-4 py-2.5"},We={class:"items-center px-4 py-2.5"},Ge={class:"flex items-center justify-start gap-4"},He=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Ke=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),Qe=e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),Ze=["onClick"],Je=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),Xe=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),De=[Je,Xe],et=["onClick"],tt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1),st=e("span",{class:"text-sm text-gray-700 leading-5"}," Close Funnel ",-1),ot=[tt,st],lt={key:1},nt=e("tr",{class:"bg-white"},[e("td",{colspan:"12",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),at=[nt],it={class:"p-6"},rt=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),dt={class:"mt-6 flex justify-end"},ct={class:"p-6"},ut=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you want to Close this Funnel? ",-1),mt={class:"mt-6 flex justify-end space-x-3"},_t={class:"w-32"},Vt={__name:"List",props:["data","user","userId","inquiry_type","inquiryTypes","userInfo","permissions"],setup(l){const p=l,{form:x,search:ht,sort:T,fetchData:ft,sortKey:U,sortDirection:O,updateParams:P}=ee("funnel.index",{user_id:p.userId,inquiry_type:p.inquiry_type}),b=h(""),c=h(p.userId),u=h(p.inquiry_type);Z([c,u],()=>{P({user_id:c.value,inquiry_type:u.value})});const j=[{field:"users.first_name",label:"ENGINEER",sortable:!0,visible:p.userInfo.role_id=="1"},{field:"customer_name",label:"CUSTOMER NAME",sortable:!0,multiFieldSort:["customer_name","dr_name"],visible:!0},{field:"place",label:"CITY",sortable:!0,visible:!0},{field:"mobile_number",label:"NUMBER",sortable:!1,visible:!0},{field:"company",label:"COMPANY",sortable:!0,visible:!0},{field:"product",label:"PRODUCT",sortable:!0,visible:!0},{field:"order_value",label:"ORDER VALUE (₹)",sortable:!0,visible:!0},{field:"order_month",label:"E.M. OF ORDER",sortable:!0,visible:!0},{field:"status",label:"STATUS",sortable:!1,visible:!0},{field:"inquiry_type",label:"INQUIRY",sortable:!0,visible:!0},{field:"action",label:"ACTION",sortable:!1,visible:!0}],w=(s,i,t)=>{b.value=s,x.get(route("funnel.index",{search:s,user_id:i,inquiry_type:t}),{preserveState:!0})},L=(s,i)=>{c.value=s,w(b.value,c.value,u.value)},k=h(!1),g=h(null),R=s=>{g.value=s,k.value=!0},C=()=>{k.value=!1},z=()=>{x.delete(route("funnel.destroy",{id:g.value}),{onSuccess:()=>C()})},S=h(!1),Y=s=>{g.value=s,S.value=!0},F=()=>{S.value=!1},W=()=>{const s=window.location.href;x.post(route("funnel.changeStatus",{id:g.value}),{onSuccess:()=>{window.location.href=s}})},G=(s,i)=>{u.value=s,w(b.value,c.value,u.value)},H=s=>{switch(s){case"Cold":return"bg-green-100";case"Warm":return"bg-yellow-100";case"Close":return"bg-red-100";default:return"bg-blue-100"}},K=s=>{switch(s){case"Cold":return"text-green-600";case"Warm":return"text-yellow-600";case"Close":return"text-red-600";default:return"text-blue-600"}},Q=s=>{let i=s.toFixed(2).toString(),[t,y]=i.split("."),_=t.substring(t.length-3),B=t.substring(0,t.length-3);return B!==""&&(_=","+_),`${B.replace(/\B(?=(\d{2})+(?!\d))/g,",")+_}.${y}`};return(s,i)=>(n(),d(M,null,[o(v(J),{title:"Funnel"}),o(te,null,{default:r(()=>[e("div",de,[e("div",ce,[ue,e("div",me,[e("div",_e,[e("div",he,[fe,e("input",{id:"search-field",onInput:i[0]||(i[0]=t=>w(t.target.value,c.value,u.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),l.permissions.canCreateFunnel?(n(),d("div",pe,[e("div",ge,[o(le,{href:s.route("funnel.create")},{default:r(()=>[f(" Add Funnel ")]),_:1},8,["href"])])])):m("",!0)])]),e("div",ye,[e("div",ve,[xe,o(q,{for:"customer_id",value:"Filters"})]),e("div",be,[l.permissions.canFilterFunnel?(n(),d("div",we,[o(q,{for:"customer_id",value:"Engineer Name"}),e("div",ke,[o(N,{options:l.user,modelValue:c.value,"onUpdate:modelValue":i[1]||(i[1]=t=>c.value=t),onOnchange:L},null,8,["options","modelValue"])])])):m("",!0),e("div",Ce,[o(q,{for:"customer_id",value:"Inquiry Type"}),e("div",Se,[o(N,{options:l.inquiryTypes,modelValue:u.value,"onUpdate:modelValue":i[2]||(i[2]=t=>u.value=t),onOnchange:G},null,8,["options","modelValue"])])])])]),e("div",Me,[e("div",Ie,[e("table",qe,[e("thead",Fe,[e("tr",Be,[(n(),d(M,null,E(j,(t,y)=>X(e("th",{key:y,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:_=>v(T)(t.field,t.sortable)},[f(a(t.label)+" ",1),t.sortable?(n(),I(re,{key:0,isSorted:v(U)===t.field,direction:v(O)},null,8,["isSorted","direction"])):m("",!0)],8,Ee),[[D,t.visible]])),64))])]),l.data.data&&l.data.data.length>0?(n(),d("tbody",Ve,[(n(!0),d(M,null,E(l.data.data,(t,y)=>(n(),d("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[l.userInfo.role_id=="1"?(n(),d("td",Ae,a(t.users.first_name??"")+" "+a(t.users.last_name??""),1)):m("",!0),e("th",Ne,[e("div",$e,a(t.customer_name??""),1),e("div",Te,a(t.dr_name??""),1)]),e("td",Ue,a(t.place??"-"),1),e("td",Oe,a(t.number??"-"),1),e("td",Pe,a(t.company??"-"),1),e("td",je,a(t.product??"-"),1),e("td",Le,a(Q(t.order_value)??"-"),1),e("td",Re,a(t.order_month??"-"),1),e("td",ze,a(t.status??"-"),1),e("td",Ye,[e("div",{class:V(["flex rounded-full px-4 py-1",H(t.inquiry_type)])},[e("span",{class:V(["text-sm font-semibold",K(t.inquiry_type)])},a(t.inquiry_type),3)],2)]),e("td",We,[e("div",Ge,[o(se,{align:"right",width:"48"},{trigger:r(()=>[He]),content:r(()=>[l.permissions.canEditFunnel&&t.inquiry_type!="Close"?(n(),I(oe,{key:0,href:s.route("funnel.edit",{id:t.id})},{svg:r(()=>[Ke]),text:r(()=>[Qe]),_:2},1032,["href"])):m("",!0),l.permissions.canDeleteFunnel?(n(),d("button",{key:1,type:"button",onClick:_=>R(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},De,8,Ze)):m("",!0),t.inquiry_type!="Close"?(n(),d("button",{key:2,type:"button",onClick:_=>Y(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},ot,8,et)):m("",!0)]),_:2},1024)])])]))),128))])):(n(),d("tbody",lt,at))])])]),l.data.data&&l.data.data.length>0?(n(),I(ie,{key:0,class:"mt-6",links:l.data.links},null,8,["links"])):m("",!0)]),o($,{show:k.value,onClose:C},{default:r(()=>[e("div",it,[rt,e("div",dt,[o(A,{onClick:C},{default:r(()=>[f(" Cancel ")]),_:1}),o(ae,{class:"ml-3",onClick:z},{default:r(()=>[f(" Delete ")]),_:1})])])]),_:1},8,["show"]),o($,{show:S.value,onClose:F},{default:r(()=>[e("div",ct,[ut,e("div",mt,[o(A,{onClick:F},{default:r(()=>[f(" Cancel ")]),_:1}),e("div",_t,[o(ne,{onClick:W,type:"button"},{default:r(()=>[f("Approve")]),_:1})])])])]),_:1},8,["show"])]),_:1})],64))}};export{Vt as default};
