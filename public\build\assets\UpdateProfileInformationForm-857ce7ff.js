import{K as x,h as v,o as m,c as u,b as a,a as t,u as e,k as _,v as h,g as c,w as g,E as b,f as y,T as V,d as w,y as k}from"./app-b7a94f67.js";import{_ as n}from"./InputError-86b88c86.js";import{_ as i}from"./InputLabel-11b5d690.js";import{P as U}from"./PrimaryButton-4ffecd1c.js";import{_ as d}from"./TextInput-fea73171.js";import{_ as N}from"./TextArea-500c5ac8.js";import"./_plugin-vue_export-helper-c27b6911.js";const B=a("header",null,[a("h2",{class:"text-lg font-medium text-gray-900"},"Profile Information"),a("p",{class:"text-sm text-gray-500"}," Update your account's profile information and email address. ")],-1),S={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},q={class:"sm:col-span-3"},E={class:"sm:col-span-3"},T={class:"sm:col-span-3"},$={class:"sm:col-span-3"},C={class:"sm:col-span-6"},P={class:"sm:col-span-6"},A={key:0},D={class:"text-sm mt-2 text-gray-800"},F={class:"mt-2 font-medium text-sm text-green-600"},I={class:"flex items-center gap-4"},M={key:0,class:"text-sm text-gray-600"},H={__name:"UpdateProfileInformationForm",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(f){const l=x().props.auth.user,s=v({first_name:l.first_name,last_name:l.last_name,contact_no:l.contact_no,email:l.email,address:l.address,dob:l.dob});return(p,o)=>(m(),u("section",null,[B,a("form",{onSubmit:o[6]||(o[6]=w(r=>e(s).patch(p.route("profile.update")),["prevent"])),class:"mt-6 space-y-4"},[a("div",S,[a("div",q,[t(i,{for:"first_name",value:"First Name"}),t(d,{id:"first_name",type:"text",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).first_name,"onUpdate:modelValue":o[0]||(o[0]=r=>e(s).first_name=r),required:"",autofocus:"",autocomplete:"first_name"},null,8,["modelValue"]),t(n,{class:"mt-2",message:e(s).errors.first_name},null,8,["message"])]),a("div",E,[t(i,{for:"last_name",value:"Last Name"}),t(d,{id:"last_name",type:"text",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).last_name,"onUpdate:modelValue":o[1]||(o[1]=r=>e(s).last_name=r),required:"",autofocus:"",autocomplete:"last_name"},null,8,["modelValue"]),t(n,{class:"mt-2",message:e(s).errors.last_name},null,8,["message"])]),a("div",T,[t(i,{for:"email",value:"Email"}),t(d,{id:"email",type:"email",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).email,"onUpdate:modelValue":o[2]||(o[2]=r=>e(s).email=r),required:"",autocomplete:"username"},null,8,["modelValue"]),t(n,{class:"mt-2",message:e(s).errors.email},null,8,["message"])]),a("div",$,[t(i,{for:"contact_no",value:"Contact No"}),t(d,{id:"contact_no",type:"text",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).contact_no,"onUpdate:modelValue":o[3]||(o[3]=r=>e(s).contact_no=r),required:"",autofocus:"",autocomplete:"contact_no"},null,8,["modelValue"]),t(n,{class:"mt-2",message:e(s).errors.contact_no},null,8,["message"])]),a("div",C,[t(i,{for:"dob",value:"DOB"}),_(a("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":o[4]||(o[4]=r=>e(s).dob=r)},null,512),[[h,e(s).dob]]),t(n,{class:"mt-2",message:e(s).errors.dob},null,8,["message"])]),a("div",P,[t(i,{for:"address",value:"Address"}),t(N,{id:"address",type:"text",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).address,"onUpdate:modelValue":o[5]||(o[5]=r=>e(s).address=r),required:"",autofocus:"",autocomplete:"address"},null,8,["modelValue"]),t(n,{class:"mt-2",message:e(s).errors.address},null,8,["message"])]),f.mustVerifyEmail&&e(l).email_verified_at===null?(m(),u("div",A,[a("p",D,[c(" Your email address is unverified. "),t(e(k),{href:p.route("verification.send"),method:"post",as:"button",class:"underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},{default:g(()=>[c(" Click here to re-send the verification email. ")]),_:1},8,["href"])]),_(a("div",F," A new verification link has been sent to your email address. ",512),[[b,f.status==="verification-link-sent"]])])):y("",!0)]),a("div",I,[t(U,{disabled:e(s).processing},{default:g(()=>[c("Save")]),_:1},8,["disabled"]),t(V,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:g(()=>[e(s).recentlySuccessful?(m(),u("p",M,"Saved.")):y("",!0)]),_:1})])],32)]))}};export{H as default};
