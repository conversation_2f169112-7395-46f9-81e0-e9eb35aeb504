import{o as a,c as t,F as c,i as m,k as p,P as v,b as u,t as g}from"./app-03250c83.js";const _={class:"flex space-x-4"},h=["value","id","onChange"],f=["for"],V={__name:"RadioButton",props:{modelValue:{type:[String,Number,Boolean],required:!0},options:{type:Array,required:!0}},emits:["update:modelValue"],setup(s,{emit:d}){const l=s,i=r=>{d("update:modelValue",r)};return(r,o)=>(a(),t("div",_,[(a(!0),t(c,null,m(s.options,e=>(a(),t("div",{class:"flex items-center",key:e.value},[p(u("input",{type:"radio",value:e.value,id:e.value,"onUpdate:modelValue":o[0]||(o[0]=n=>l.modelValue=n),onChange:n=>i(e.value),class:"border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"},null,40,h),[[v,l.modelValue]]),u("label",{for:e.value,class:"ml-2 cursor-pointer"},g(e.label),9,f)]))),128))]))}};export{V as _};
