import{r as U,o as p,c as m,a,u as l,w as _,F as x,Z as z,b as e,d as $,i as q,g,T as C,f,n,k as P,v as A,s as B,x as j}from"./app-4ea19997.js";import{_ as L,a as M}from"./AdminLayout-5eccc000.js";import{P as v}from"./PrimaryButton-15e730cb.js";import{_ as y}from"./TextInput-2009383d.js";import{_ as N}from"./SearchableDropdown-d15cf33a.js";import{u as O}from"./index-f02fbcd1.js";import{_ as T}from"./_plugin-vue_export-helper-c27b6911.js";const h=c=>(B("data-v-556b63a3"),c=c(),j(),c),F={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},D=h(()=>e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add Stock",-1)),E=["onSubmit"],H={class:"overflow-x-auto divide-y divide-gray-300",style:{"margin-bottom":"100px"}},R=h(()=>e("thead",null,[e("tr",null,[e("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Organization"),e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Batch"),e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Expiry Date"),e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)"),e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Purchase Price (₹)"),e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Stock")])],-1)),Z={class:"divide-y divide-gray-300 bg-white"},G={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-60"},J={class:"relative mt-2"},K={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-60"},Q={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-20"},W=["onUpdate:modelValue"],X={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-20"},Y={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-20"},ee={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-16"},te=["onClick"],se=h(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),oe=[se],ae={class:"flex mt-6 items-center justify-between"},re={class:"ml-auto flex items-center justify-end gap-x-6"},le={class:"flex mt-6 items-center justify-between"},ce={class:"ml-auto flex items-center justify-end gap-x-6"},ie=h(()=>e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),ne={key:0,class:"text-sm text-gray-600"},de={__name:"StockAdd",props:["product","organization","page"],setup(c){const u=c,o=O("post","/savestock",{stockItems:[],company_id:u.product.company_id,page:u.page}),b=(i,S,t)=>{d.value[t].organization_id=i},d=U([{product_id:u.product.id,organization_id:"",batch:"",expiry_date:"",mrp:"",purchase_price:"",receive_qty:"",sell_qty:0}]),w=()=>{d.value.push({product_id:u.product.id,organization_id:"",batch:"",expiry_date:"",mrp:"",purchase_price:"",receive_qty:"",sell_qty:0})},k=i=>{d.value.splice(i,1)},V=()=>{o.stockItems=d.value,o.submit({preserveScroll:!0,onSuccess:()=>o.reset()})};return(i,S)=>(p(),m(x,null,[a(l(z),{title:"Stock Add"}),a(L,null,{default:_(()=>[e("div",F,[D,e("form",{onSubmit:$(V,["prevent"]),class:""},[e("table",H,[R,e("tbody",Z,[(p(!0),m(x,null,q(d.value,(t,r)=>(p(),m("tr",{key:r},[e("td",G,[e("div",J,[a(N,{options:c.organization,modelValue:t.organization_id,"onUpdate:modelValue":s=>t.organization_id=s,onOnchange:(s,I)=>b(s,I,r),class:n({error:l(o).errors[`stockItems.${r}.organization_id`]})},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class"])])]),e("td",K,[a(y,{id:"batch",type:"text",modelValue:t.batch,"onUpdate:modelValue":s=>t.batch=s,class:n({error:l(o).errors[`stockItems.${r}.batch`]})},null,8,["modelValue","onUpdate:modelValue","class"])]),e("td",Q,[P(e("input",{class:n(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{error:l(o).errors[`stockItems.${r}.expiry_date`]}]),type:"date","onUpdate:modelValue":s=>t.expiry_date=s},null,10,W),[[A,t.expiry_date]])]),e("td",X,[a(y,{id:"mrp",type:"text",modelValue:t.mrp,"onUpdate:modelValue":s=>t.mrp=s,class:n({error:l(o).errors[`stockItems.${r}.mrp`]})},null,8,["modelValue","onUpdate:modelValue","class"])]),e("td",Y,[a(y,{id:"purchase_price",type:"text",modelValue:t.purchase_price,"onUpdate:modelValue":s=>t.purchase_price=s,class:n({error:l(o).errors[`stockItems.${r}.purchase_price`]})},null,8,["modelValue","onUpdate:modelValue","class"])]),e("td",ee,[a(y,{id:"receive_qty",type:"text",numeric:!0,modelValue:t.receive_qty,"onUpdate:modelValue":s=>t.receive_qty=s,class:n({error:l(o).errors[`stockItems.${r}.receive_qty`]})},null,8,["modelValue","onUpdate:modelValue","class"])]),e("td",null,[r!=0?(p(),m("button",{key:0,type:"button",class:"mt-1",onClick:s=>k(r)},oe,8,te)):f("",!0)])]))),128))])]),e("div",ae,[e("div",re,[a(v,{onClick:w,type:"button"},{default:_(()=>[g("Add Product")]),_:1})])]),e("div",le,[e("div",ce,[a(M,{href:c.product.category==="Sales"?i.route("salesstock"):i.route("servicestock")},{svg:_(()=>[ie]),_:1},8,["href"]),a(v,{disabled:l(o).processing},{default:_(()=>[g("Save")]),_:1},8,["disabled"]),a(C,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:_(()=>[l(o).recentlySuccessful?(p(),m("p",ne,"Saved.")):f("",!0)]),_:1})])])],40,E)])]),_:1})],64))}},ge=T(de,[["__scopeId","data-v-556b63a3"]]);export{ge as default};
