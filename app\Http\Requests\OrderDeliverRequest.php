<?php

namespace App\Http\Requests;
use App\Models\OrderDetails;
use Illuminate\Foundation\Http\FormRequest;

class OrderDeliverRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'invoice_number' => 'required|string|max:255',
        ];
    }

    /**
     * Get custom validation messages
     */
    public function messages(): array
    {
        return [
            'invoice_number.required' => 'Invoice number is required.',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $deliveredProducts = $this->input('deliveredProduct');
            $filledReceiveQtys = 0;

            foreach ($deliveredProducts as $key => $product) {
                $orderDetail = OrderDetails::find($product['order_details_id']);

                if ($orderDetail && $product['delivered_qty'] > ($orderDetail->qty - $orderDetail->delivered_qty)) {
                    $validator->errors()->add("deliveredProduct.$key.delivered_qty", __('The delivered quantity must not be greater than the available quantity.'));
                }

                if (!empty($product['delivered_qty'])) {
                    $filledReceiveQtys++;
                }
            }

            if ($filledReceiveQtys == 0) {
                $validator->errors()->add("deliveredProduct.$key.delivered_qty", __('At least one quantity field must be filled.'));
            }
        });
    }
}
