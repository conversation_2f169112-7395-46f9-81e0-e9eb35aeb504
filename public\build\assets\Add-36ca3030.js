import{r as b,j as Z,l as B,o as r,c as d,a as l,u as n,w as U,F as z,Z as te,b as o,t as c,f as p,d as oe,n as v,k as se,v as ae,i as I,g as ne,T as le,s as ie,x as re}from"./app-16701445.js";import{_ as de,a as ue}from"./AdminLayout-e15be38d.js";import{_ as f}from"./InputLabel-d69efee6.js";import{P as me}from"./PrimaryButton-eddb8b77.js";import{_ as k}from"./TextInput-764e3400.js";import{_ as ce}from"./TextArea-b68da786.js";import{_ as _e}from"./RadioButton-b4275d4f.js";import{_ as P}from"./SearchableDropdown-c456ce8e.js";import{u as pe}from"./index-10107770.js";/* empty css                                                                          */import{_ as ve}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as fe}from"./Checkbox-743761b5.js";const C=g=>(ie("data-v-944d4b96"),g=g(),re(),g),ye={class:"h-screen animate-top"},ge={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},xe={class:"sm:flex sm:items-center"},he=C(()=>o("div",{class:"sm:flex-auto"},[o("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Receive Payment")],-1)),be={class:"flex items-center justify-between"},ke={key:0,class:"text-base font-semibold leading-6 text-gray-900"},we=["onSubmit"],Ve={class:"border-b border-gray-900/10 pb-12"},Ce={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},Ae={class:"sm:col-span-3"},Ne={class:"relative mt-2"},Fe={class:"sm:col-span-3"},Se={class:"relative mt-2"},$e={class:"sm:col-span-2"},Ue={class:"relative mt-2"},ze={key:0,class:"sm:col-span-3"},Pe={class:"relative mt-2"},Te={key:1,class:"sm:col-span-3"},De={key:2,class:"sm:col-span-2"},Oe={key:3,class:"sm:col-span-1"},Be={key:4,class:"sm:col-span-1"},Ie={key:5,class:"sm:col-span-1"},je={key:6,class:"sm:col-span-3"},Ee={key:7,class:"sm:col-span-2"},Re={class:"mt-4 flex justify-start"},Me={class:"text-base font-semibold"},Le={key:8,class:"sm:col-span-2"},Ye={key:9,class:"sm:col-span-2"},qe={key:10,class:"sm:col-span-2"},He={class:"relative mt-2"},Ze={key:11,class:"sm:col-span-3"},Ge={class:"sm:col-span-6"},Je={class:"overflow-x-auto divide-y divide-gray-300 w-full"},Ke=C(()=>o("div",{class:"w-full"},[o("thead",{class:"w-full"},[o("tr",{class:""},[o("th",{scope:"col",class:""}),o("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),o("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),o("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1)),Qe={style:{"overflow-y":"auto","max-height":"318px"}},We={class:"divide-y divide-gray-300 bg-white"},Xe={class:"whitespace-nowrap px-2 text-sm text-gray-900"},et={class:"text-sm text-gray-900 leading-6 py-1.5"},tt={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},ot={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},st={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},at={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},nt={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},lt={key:0,class:"text-red-500 text-xs absolute"},it={class:"whitespace-nowrap px-2 text-sm text-gray-900"},rt={class:"sm:col-span-2"},dt={class:"mt-2 p-3 bg-gray-50 rounded-md"},ut={class:"space-y-2 text-sm"},mt={class:"flex items-center gap-2"},ct=C(()=>o("hr",{class:"my-2"},null,-1)),_t={class:"flex justify-between items-center font-semibold"},pt=C(()=>o("span",null,"Settlement:",-1)),vt={key:0,class:"text-red-500 text-xs mt-1"},ft={key:12,class:"sm:col-span-6"},yt={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},gt=C(()=>o("thead",null,[o("tr",null,[o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1)),xt={class:"divide-y divide-gray-300 bg-white"},ht={class:"whitespace-nowrap py-3 text-sm text-gray-900"},bt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},kt={class:"flex flex-col"},wt={class:"text-sm text-gray-900"},Vt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},Ct={class:"whitespace-nowrap py-3 text-sm text-gray-900"},At={class:"flex mt-6 items-center justify-between"},Nt={class:"ml-auto flex items-center justify-end gap-x-6"},Ft=C(()=>o("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),St={key:0,class:"text-sm text-gray-600"},$t={__name:"Add",props:["paymentType","bankinfo","organization","customers","invoices","credit"],setup(g){const A=g;b([]);const t=pe("post","/receipt",{organization_id:"",customer_id:"",payment_type:"",date:"",note:"",amount:0,tds_amount:0,discount_amount:0,round_off:0,check_number:"",bank_name:"",org_bank_id:"",invoice:[],settled_amount:"",advance_amount:"",is_credit:"",credit_data:[],total_unused_amount:""}),N=b(""),G=()=>{t.settled_amount=S.value,t.advance_amount=R.value,t.total_unused_amount=F.value,t.is_credit=m.value,t.invoice=y.value,t.credit_data=x.value,t.submit({preserveScroll:!0,onSuccess:()=>t.reset()})},J=(a,s)=>{N.value=s,t.payment_type=a,t.errors.payment_type=null,s==="Cash"?t.note="Cash":t.note==="Cash"&&(t.note="")},$=b([]),x=b([]),F=b(""),j=b([]),K=(a,s)=>{const e=A.bankinfo.filter(i=>i.organization_id===a);j.value=e,t.customer_id&&E(t.customer_id,a);const u=A.credit.filter(i=>i.organization_id===a&&i.customer_id===t.customer_id);x.value=u,F.value=x.value.reduce((i,_)=>i+_.unused_amount,0),t.organization_id=a,t.errors.organization_id=null},Q=(a,s)=>{E(a,t.organization_id);const e=A.credit.filter(u=>u.customer_id===a&&u.organization_id===t.organization_id);x.value=e,F.value=x.value.reduce((u,i)=>u+i.unused_amount,0),t.customer_id=a,t.errors.customer_id=null},E=(a,s)=>{if(!a||!s){$.value=[];return}const e=A.customers.find(_=>_.id===a),u=e==null?void 0:e.party_id,i=A.invoices.filter(_=>{const h=_.organization_id===s;return _.invoice_type==="sales"?h&&_.customer_id===a:_.invoice_type==="purchase"&&u?h&&_.party_id===u:!1});$.value=i},W=(a,s)=>{t.org_bank_id=a,t.errors.org_bank_id=null},S=Z(()=>y.value.reduce((a,s)=>{if(s.check&&s.amount){const e=parseFloat(s.amount);return s.invoice_type==="sales"?a+e:a-e}return a},0)),R=Z(()=>{const a=parseFloat(t.amount||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0),s=parseFloat(t.round_off||0),e=S.value;return a-e-s}),T=()=>{},w=a=>{let s=a.toFixed(2).toString(),[e,u]=s.split("."),i=e.substring(e.length-3),_=e.substring(0,e.length-3);return _!==""&&(i=","+i),`${_.replace(/\B(?=(\d{2})+(?!\d))/g,",")+i}.${u}`},M=a=>{const s=new Date(a),e={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",e)},m=b("No"),X=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],ee=a=>{const s=m.value==="Yes"?parseFloat(F.value||0):parseFloat(t.amount||0)+parseFloat(t.round_off||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0);if(!y.value[a].check){y.value[a].amount=0;return}let e=s;y.value.forEach((_,h)=>{_.check&&h!==a&&(e-=parseFloat(_.amount||0))});const u=parseFloat(y.value[a].pending_amount||0),i=Math.min(u,e);y.value[a].amount=i.toFixed(2)},y=b([{id:"",date:"",invoice_no:"",total_amount:"",pending_amount:"",check:!1,amount:0}]),D=()=>{y.value=$.value.map(a=>({id:a.id,date:a.date,invoice_no:a.invoice_no,total_amount:parseFloat(a.total_amount||0).toFixed(2),pending_amount:parseFloat(a.pending_amount||0).toFixed(2),invoice_type:a.invoice_type||"sales",check:!1,amount:"0.00"}))};B($,()=>{D()}),B(m,()=>{D()}),B(()=>t.amount,()=>{m.value==="No"&&D()});const V=a=>{t.errors[a]=null,t.errors.settled_amount=null};return(a,s)=>(r(),d(z,null,[l(n(te),{title:"Receipt"}),l(de,null,{default:U(()=>[o("div",ye,[o("div",ge,[o("div",xe,[he,o("div",be,[x.value.length>0?(r(),d("div",ke," Credits Available: ₹"+c(w(F.value)),1)):p("",!0)])]),o("form",{onSubmit:oe(G,["prevent"]),class:""},[o("div",Ve,[o("div",Ce,[o("div",Ae,[l(f,{for:"payment_type",value:"Organization"}),o("div",Ne,[l(P,{options:g.organization,modelValue:n(t).organization_id,"onUpdate:modelValue":s[0]||(s[0]=e=>n(t).organization_id=e),onOnchange:K,class:v({"error rounded-md":n(t).errors.organization_id})},null,8,["options","modelValue","class"])])]),o("div",Fe,[l(f,{for:"payment_type",value:"Customer"}),o("div",Se,[l(P,{options:g.customers,modelValue:n(t).customer_id,"onUpdate:modelValue":s[1]||(s[1]=e=>n(t).customer_id=e),onOnchange:Q,class:v({"error rounded-md":n(t).errors.customer_id})},null,8,["options","modelValue","class"])])]),o("div",$e,[l(f,{for:"role_id",value:"Payment Through Credit ?"}),o("div",Ue,[l(_e,{modelValue:m.value,"onUpdate:modelValue":s[2]||(s[2]=e=>m.value=e),options:X},null,8,["modelValue"])])]),m.value=="No"?(r(),d("div",ze,[l(f,{for:"payment_type",value:"Payment Type"}),o("div",Pe,[l(P,{options:g.paymentType,modelValue:n(t).payment_type,"onUpdate:modelValue":s[3]||(s[3]=e=>n(t).payment_type=e),onOnchange:J,class:v({"error rounded-md":n(t).errors.payment_type})},null,8,["options","modelValue","class"])])])):p("",!0),m.value=="No"?(r(),d("div",Te,[l(f,{for:"date",value:"Payment Date"}),se(o("input",{"onUpdate:modelValue":s[4]||(s[4]=e=>n(t).date=e),class:v(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":n(t).errors.date}]),type:"date",onChange:s[5]||(s[5]=e=>V("date"))},null,34),[[ae,n(t).date]])])):p("",!0),m.value=="No"?(r(),d("div",De)):p("",!0),m.value=="No"?(r(),d("div",Oe,[l(f,{for:"tds_amount",value:"TDS Amount"}),l(k,{type:"text",onChange:s[6]||(s[6]=e=>V("tds_amount")),onInput:s[7]||(s[7]=e=>T()),modelValue:n(t).tds_amount,"onUpdate:modelValue":s[8]||(s[8]=e=>n(t).tds_amount=e),class:v({"error rounded-md":n(t).errors.tds_amount})},null,8,["modelValue","class"])])):p("",!0),m.value=="No"?(r(),d("div",Be,[l(f,{for:"discount_amount",value:"Discount Amount"}),l(k,{type:"text",onChange:s[9]||(s[9]=e=>V("discount_amount")),onInput:s[10]||(s[10]=e=>T()),modelValue:n(t).discount_amount,"onUpdate:modelValue":s[11]||(s[11]=e=>n(t).discount_amount=e),class:v({"error rounded-md":n(t).errors.discount_amount})},null,8,["modelValue","class"])])):p("",!0),m.value=="No"?(r(),d("div",Ie,[l(f,{for:"round_off",value:"Round Off"}),l(k,{type:"text",onChange:s[12]||(s[12]=e=>V("round_off")),modelValue:n(t).round_off,"onUpdate:modelValue":s[13]||(s[13]=e=>n(t).round_off=e),class:v({"error rounded-md":n(t).errors.round_off})},null,8,["modelValue","class"])])):p("",!0),m.value=="No"?(r(),d("div",je,[l(f,{for:"amount",value:"Amount"}),l(k,{id:"amount",type:"text",onChange:s[14]||(s[14]=e=>V("amount")),onInput:s[15]||(s[15]=e=>T()),modelValue:n(t).amount,"onUpdate:modelValue":s[16]||(s[16]=e=>n(t).amount=e),class:v({"error rounded-md":n(t).errors.amount})},null,8,["modelValue","class"])])):p("",!0),m.value=="No"?(r(),d("div",Ee,[l(f,{for:"advance",value:"Advance(Ref) Amount"}),o("div",Re,[o("p",Me,c(w(R.value)),1)])])):p("",!0),N.value=="Cheque"&&m.value=="No"?(r(),d("div",Le,[l(f,{for:"check_number",value:"Cheque Number"}),l(k,{id:"check_number",type:"text",modelValue:n(t).check_number,"onUpdate:modelValue":s[17]||(s[17]=e=>n(t).check_number=e),class:v({"error rounded-md":n(t).errors["data.check_number"]})},null,8,["modelValue","class"])])):p("",!0),N.value=="Cheque"&&m.value=="No"?(r(),d("div",Ye,[l(f,{for:"bank_name",value:"Bank Name"}),l(k,{id:"bank_name",type:"text",modelValue:n(t).bank_name,"onUpdate:modelValue":s[18]||(s[18]=e=>n(t).bank_name=e),class:v({"error rounded-md":n(t).errors["data.bank_name"]})},null,8,["modelValue","class"])])):p("",!0),N.value!="Cash"&&m.value=="No"?(r(),d("div",qe,[l(f,{for:"org_bank_id",value:"Our Bank"}),o("div",He,[l(P,{options:j.value,modelValue:n(t).org_bank_id,"onUpdate:modelValue":s[19]||(s[19]=e=>n(t).org_bank_id=e),onOnchange:W,class:v({"error rounded-md":n(t).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):p("",!0),N.value!="Cash"&&m.value=="No"?(r(),d("div",Ze)):p("",!0),o("div",Ge,[o("table",Je,[Ke,o("div",Qe,[o("tbody",We,[(r(!0),d(z,null,I(y.value,(e,u)=>(r(),d("tr",{key:u},[o("td",Xe,[o("div",et,[l(fe,{name:"check",checked:e.check,"onUpdate:checked":i=>e.check=i,onChange:i=>ee(u)},null,8,["checked","onUpdate:checked","onChange"])])]),o("td",tt,[o("span",{class:v([e.invoice_type==="sales"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},c(e.invoice_type==="sales"?"SALES":"PURCHASE"),3)]),o("td",ot,c(e.invoice_no),1),o("td",st,c(e.total_amount),1),o("td",at,c(e.pending_amount),1),o("td",nt,[l(k,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":i=>e.amount=i,onChange:i=>V("invoice."+u+".amount"),class:v({error:n(t).errors[`invoice.${u}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),n(t).errors[`invoice.${u}.amount`]?(r(),d("p",lt,c(n(t).errors[`invoice.${u}.amount`]),1)):p("",!0)]),o("td",it,c(M(e.date)),1)]))),128))])])])]),o("div",rt,[l(f,{for:"note",value:"Settlement Summary"}),o("div",dt,[o("div",ut,[(r(!0),d(z,null,I(y.value.filter(e=>e.check),e=>(r(),d("div",{key:e.id,class:"flex justify-between items-center"},[o("div",mt,[o("span",{class:v([e.invoice_type==="sales"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800","inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium"])},c(e.invoice_type==="sales"?"S":"P"),3),o("span",null,c(e.invoice_no),1)]),o("span",{class:v([e.invoice_type==="sales"?"text-green-600":"text-blue-600","font-medium"])},c(e.invoice_type==="sales"?"+":"-")+"₹"+c(w(parseFloat(e.amount||0))),3)]))),128)),ct,o("div",_t,[pt,o("span",{class:v(S.value>=0?"text-green-600":"text-red-600")}," ₹"+c(w(Math.abs(S.value)))+" "+c(S.value>=0?"(Receive)":"(Pay)"),3)])])]),n(t).errors.settled_amount?(r(),d("p",vt,c(n(t).errors.settled_amount),1)):p("",!0)]),m.value=="No"?(r(),d("div",ft,[l(f,{for:"note",value:"Note"}),l(ce,{id:"note",type:"text",rows:2,modelValue:n(t).note,"onUpdate:modelValue":s[20]||(s[20]=e=>n(t).note=e)},null,8,["modelValue"])])):p("",!0)]),x.value.length>0&&m.value=="Yes"?(r(),d("table",yt,[gt,o("tbody",xt,[(r(!0),d(z,null,I(x.value,(e,u)=>{var i,_,h,O,L,Y,q,H;return r(),d("tr",{key:u},[o("td",ht,c(M(e.date)),1),o("td",bt,[o("div",kt,[o("div",wt,c((_=(i=e.paymentreceive)==null?void 0:i.bank_info)!=null&&_.bank_name?(O=(h=e.paymentreceive)==null?void 0:h.bank_info)==null?void 0:O.bank_name:"Cash")+" - "+c((Y=(L=e.paymentreceive)==null?void 0:L.bank_info)!=null&&Y.account_number?(H=(q=e.paymentreceive)==null?void 0:q.bank_info)==null?void 0:H.account_number:""),1)])]),o("td",Vt,c(w(e.amount)),1),o("td",Ct,c(w(e.unused_amount)),1)])}),128))])])):p("",!0)]),o("div",At,[o("div",Nt,[l(ue,{href:a.route("receipt.index")},{svg:U(()=>[Ft]),_:1},8,["href"]),l(me,{disabled:n(t).processing},{default:U(()=>[ne("Save")]),_:1},8,["disabled"]),l(le,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:U(()=>[n(t).recentlySuccessful?(r(),d("p",St,"Saved.")):p("",!0)]),_:1})])])],40,we)])])]),_:1})],64))}},Lt=ve($t,[["__scopeId","data-v-944d4b96"]]);export{Lt as default};
