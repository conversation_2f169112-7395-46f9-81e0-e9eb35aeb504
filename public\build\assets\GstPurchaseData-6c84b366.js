import{h as N,r as d,o as T,c as V,a as r,u as h,w as x,F as M,Z as $,b as e,g as B,k as w,v as b,n as k}from"./app-b7a94f67.js";import{_ as G}from"./AdminLayout-0f1fdf67.js";import{_ as L}from"./CreateButton-fedd28a2.js";/* empty css                                                              */import{_ as P}from"./SimpleDropdown-366207fb.js";import{_ as u}from"./InputLabel-11b5d690.js";const U={class:"animate-top h-screen"},j={class:"flex justify-between items-center"},E=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"GST Purchase Data")],-1),A={class:"flex mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},F={class:"flex ml-6"},I={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},R={class:"flex justify-between mb-2"},q={class:"flex"},X=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),H={class:"inline-flex items-center space-x-4 justify-end w-full"},Y=["src"],Z={class:"sm:col-span-3"},J={class:"grid grid-cols-1 sm:grid-cols-12 gap-x-6 gap-y-5 items-center"},K={class:"sm:col-span-4"},Q={class:"relative mt-2"},W={class:"sm:col-span-4"},ee={class:"sm:col-span-4"},le={__name:"GstPurchaseData",props:["purchaseOrders","organization","organizationId","from_date","to_date"],setup(f){const p=f,m=N({}),o=d(p.organizationId);d(p.companyId),d("ALL COMPANY");const n=d(""),l=d(""),g=d(""),_=(t,a,s,v)=>{m.get(route("gst-purchase-data",{search:t,organization_id:a,from_date:s,to_date:v}),{preserveState:!0})},y=(t,a)=>{o.value=t,_(g.value,o.value,n.value,l.value)},z=()=>{let t="";switch(o.value){case 1:t="MC";break;case 2:t="HC";break;case 3:t="NOX";break;default:t="All_Organizations";break}const a=`GST_Purchase_Data_${t}`,s={organization_id:o.value||"",from_date:n.value||"",to_date:l.value||""},S=`/export-gst-purchase-data?${new URLSearchParams(s).toString()}`;fetch(S,{method:"GET"}).then(i=>{if(!i.ok)throw new Error("Network response was not ok");return i.blob()}).then(i=>{const D=window.URL.createObjectURL(new Blob([i])),c=document.createElement("a");c.href=D,c.setAttribute("download",`${a}.xlsx`),document.body.appendChild(c),c.click(),document.body.removeChild(c)}).catch(i=>{console.error("Error exporting data:",i)})},C=()=>{_(g.value,o.value,n.value,l.value)},O=()=>{_(g.value,o.value,n.value,l.value)};return(t,a)=>(T(),V(M,null,[r(h($),{title:"GST Purchase Data"}),r(G,null,{default:x(()=>[e("div",U,[e("div",j,[E,e("div",A,[e("div",F,[r(L,{href:t.route("reports")},{default:x(()=>[B(" Back ")]),_:1},8,["href"])])])]),e("div",I,[e("div",R,[e("div",q,[X,r(u,{for:"customer_id",value:"Filters"})]),e("div",H,[e("button",{onClick:z},[e("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,Y)])])]),e("div",Z,[e("div",J,[e("div",K,[r(u,{for:"customer_id",value:"Organization Name"}),e("div",Q,[r(P,{options:f.organization,modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=s=>o.value=s),onOnchange:y},null,8,["options","modelValue"])])]),e("div",W,[r(u,{for:"date",value:"From Date"}),w(e("input",{"onUpdate:modelValue":a[1]||(a[1]=s=>n.value=s),class:k(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":h(m).errors.from_date}]),type:"date",onChange:C},null,34),[[b,n.value]])]),e("div",ee,[r(u,{for:"date",value:"To Date"}),w(e("input",{"onUpdate:modelValue":a[2]||(a[2]=s=>l.value=s),class:k(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":h(m).errors.to_date}]),type:"date",onChange:O},null,34),[[b,l.value]])])])])])])]),_:1})],64))}};export{le as default};
