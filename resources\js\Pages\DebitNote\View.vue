<template>
    <AdminLayout>
        <div class="px-2 sm:px-4 lg:px-8 py-4 sm:py-6">
            <!-- Header -->
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
                <h1 class="text-xl sm:text-2xl font-semibold text-gray-900">Debit Note Details</h1>
                <div class="flex gap-3">
                    <SecondaryButton @click="goBack">
                        Back to List
                    </SecondaryButton>
                </div>
            </div>

            <div v-if="data && data.length > 0" class="bg-white rounded-lg shadow-sm border">
                <div class="p-4 sm:p-6">
                    <!-- Debit Note Header -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">Debit Note Information</h2>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Debit Note No:</span>
                                    <span class="font-medium">{{ data[0].debit_note_no }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Date:</span>
                                    <span class="font-medium">{{ data[0].date }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Credit Note Number:</span>
                                    <span class="font-medium">{{ data[0].credit_note_number || '-' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Organization:</span>
                                    <span class="font-medium">{{ data[0].organization?.name || '-' }}</span>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">Company Information</h2>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Company Name:</span>
                                    <span class="font-medium">{{ data[0].company?.name || '-' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Purchase Invoice:</span>
                                    <span class="font-medium">{{ data[0].purchase_invoice?.invoice_no || '-' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reason -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Reason</h2>
                        <p class="text-gray-700 bg-gray-50 p-4 rounded-lg">{{ data[0].reason }}</p>
                    </div>

                    <!-- Product Details -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Product Details</h2>

                        <!-- Desktop Table -->
                        <div class="hidden lg:block overflow-x-auto">
                            <table class="w-full text-sm text-left text-gray-500">
                                <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3">Product</th>
                                        <th class="px-6 py-3">Serial Number</th>
                                        <th class="px-6 py-3">Batch</th>
                                        <th class="px-6 py-3">Expiry Date</th>
                                        <th class="px-6 py-3">Quantity</th>
                                        <th class="px-6 py-3">Rate</th>
                                        <th class="px-6 py-3">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="detail in data[0].debit_note_details" :key="detail.id" class="bg-white border-b">
                                        <td class="px-6 py-4 font-medium text-gray-900">
                                            {{ detail.product?.name || '-' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ detail.serial_numbers?.serial_number || '-' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ detail.serial_numbers?.batch || '-' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ detail.serial_numbers?.expiry_date || '-' }}
                                        </td>
                                        <td class="px-6 py-4">{{ detail.receive_qty || 0 }}</td>
                                        <td class="px-6 py-4">₹{{ detail.purchase_price || 0 }}</td>
                                        <td class="px-6 py-4">₹{{ detail.total_price || 0 }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Mobile Cards -->
                        <div class="lg:hidden space-y-4">
                            <div v-for="detail in data[0].debit_note_details" :key="detail.id"
                                 class="bg-gray-50 rounded-lg p-4">
                                <div class="space-y-2">
                                    <div class="font-medium text-gray-900">{{ detail.product?.name || '-' }}</div>
                                    <div class="grid grid-cols-2 gap-2 text-sm">
                                        <div>
                                            <span class="text-gray-500">Serial:</span>
                                            <span class="ml-1">{{ detail.serial_numbers?.serial_number || '-' }}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">Batch:</span>
                                            <span class="ml-1">{{ detail.serial_numbers?.batch || '-' }}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">Expiry:</span>
                                            <span class="ml-1">{{ detail.serial_numbers?.expiry_date || '-' }}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">Qty:</span>
                                            <span class="ml-1">{{ detail.receive_qty || 0 }}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">Rate:</span>
                                            <span class="ml-1">₹{{ detail.purchase_price || 0 }}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">Total:</span>
                                            <span class="ml-1 font-medium">₹{{ detail.total_price || 0 }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Amount Summary -->
                    <div class="border-t pt-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Amount Summary</h2>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Sub Total:</span>
                                    <span class="font-medium">₹{{ data[0].sub_total || 0 }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">CGST:</span>
                                    <span class="font-medium">₹{{ data[0].cgst || 0 }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">SGST:</span>
                                    <span class="font-medium">₹{{ data[0].sgst || 0 }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">IGST:</span>
                                    <span class="font-medium">₹{{ data[0].igst || 0 }}</span>
                                </div>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Discount Before Tax:</span>
                                    <span class="font-medium">₹{{ data[0].discount_before_tax || 0 }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Overall Discount:</span>
                                    <span class="font-medium">₹{{ data[0].overall_discount || 0 }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Total GST:</span>
                                    <span class="font-medium">₹{{ data[0].total_gst || 0 }}</span>
                                </div>
                                <div class="flex justify-between border-t pt-3">
                                    <span class="text-lg font-semibold text-gray-900">Total Amount:</span>
                                    <span class="text-lg font-bold text-indigo-600">₹{{ data[0].total_amount || 0 }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div v-else class="bg-white rounded-lg shadow-sm border p-8 text-center">
                <p class="text-gray-500">No debit note data found.</p>
            </div>
        </div>
    </AdminLayout>
</template>

<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import { router } from '@inertiajs/vue3';

const props = defineProps({
    data: Array
});

const goBack = () => {
    router.visit('/debitnote');
};
</script>
