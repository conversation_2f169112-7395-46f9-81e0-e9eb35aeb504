
<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import CreateButton from '@/Components/CreateButton.vue';
import { router, Head } from '@inertiajs/vue3';


const props = defineProps({
    data: Array
});

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

</script>
<template>
    <Head title="Debit Note"/>
    <AdminLayout>
        <div class="animate-top">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Credit Note Detail</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div>
                        <p class="text-sm font-semibold text-gray-900">{{ data[0].organization.name }}</p>
                    </div>
                    <div class="flex justify-end w-20">
                        <CreateButton :href="route('debitnote.index')">
                            Back
                        </CreateButton>
                    </div>
                </div>
            </div>

            <div v-if="data && data.length > 0" class="mt-6 bg-white rounded-lg shadow-sm border">
                <div class="p-4 sm:p-6">
                    <!-- Debit Note Header -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">Debit Note Information</h2>
                            <div class="space-y-3">
                                <div class="inline-flex items-center justify-start w-full space-x-2">
                                    <p class="text-sm text-gray-700 w-40">Invoice Number:</p>
                                    <p class="text-sm font-medium text-gray-900">{{ data[0].purchase_invoice.customer_invoice_no ?? '-'}}</p>
                                </div>
                                <div class="inline-flex items-center justify-start w-full space-x-2">
                                    <p class="text-sm text-gray-700 w-40">Debit Note Number:</p>
                                    <p class="text-sm font-medium text-gray-900">{{ data[0].debit_note_no ?? '-'}}</p>
                                </div>
                                <div class="inline-flex items-center justify-start w-full space-x-2">
                                    <p class="text-sm text-gray-700 w-40">Debit Note Date:</p>
                                    <p class="text-sm font-medium text-gray-900">{{ formatDate(data[0].date) ?? '-'}}</p>
                                </div>
                                <div class="inline-flex items-center justify-start w-full space-x-2">
                                    <p class="text-sm text-gray-700 w-40">Credit Note Number:</p>
                                    <p class="text-sm font-medium text-gray-900">{{ data[0].credit_note_number ?? '-'}}</p>
                                </div>
                                <div class="inline-flex items-center justify-start w-full space-x-2">
                                    <p class="text-sm text-gray-700 w-40">Reason:</p>
                                    <p class="text-sm font-medium text-gray-900">{{ data[0].reason ?? '-'}}</p>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">Company Information</h2>
                            <div class="space-y-3">
                                <div class="inline-flex items-center justify-start w-full space-x-2">
                                    <p class="text-sm text-gray-700 w-28">Company:</p>
                                    <p class="text-sm font-medium">{{ data[0].company.name ?? '-'}}</p>
                                </div>
                                <div class="inline-flex items-center justify-start w-full space-x-2">
                                    <p class="text-sm text-gray-700 w-28">GST No:</p>
                                    <p class="text-sm font-medium">{{ data[0].company.gst_no  ?? '-'}}</p>
                                </div>
                                <div class="inline-flex items-center justify-start w-full space-x-2">
                                    <p class="text-sm text-gray-700 w-28">Email:</p>
                                    <p class="text-sm font-medium">{{ data[0].company.email ?? '-'}}</p>
                                </div>
                                <div class="inline-flex items-center justify-start w-full space-x-2">
                                    <p class="text-sm text-gray-700 w-28">Contact No:</p>
                                    <p class="text-sm font-medium">{{ data[0].company.contact_no ?? '-'}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Product Details -->
                    <div class="mb-8">
                        <h2 class="text-lg font-semibold text-gray-900 mb-4">Product Details</h2>
                        <div class="overflow-x-auto sm:rounded-lg">
                            <div class="shadow sm:rounded-lg">
                                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                                 <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                                    <tr class="border-b-2">
                                        <th class="px-4 py-4 text-sm font-semi bold text-gray-900">SR No</th>
                                        <th class="px-4 py-4 text-sm font-semi bold text-gray-900">itme code</th>
                                        <th class="px-4 py-4 text-sm font-semi bold text-gray-900">name</th>
                                        <th class="px-4 py-4 text-sm font-semi bold text-gray-900">BATCH</th>
                                        <th class="px-4 py-4 text-sm font-semi bold text-gray-900">EXP DATE</th>
                                        <th class="px-4 py-4 text-sm font-semi bold text-gray-900">QTY</th>
                                        <th class="px-4 py-4 text-sm font-semi bold text-gray-900">RATE</th>
                                        <th class="px-4 py-4 text-sm font-semi bold text-gray-900">TOTAL PRICE</th>
                                        <th v-if="data[0].company.gst_type =='IGST'" class="px-4 py-4 text-sm font-semi bold text-gray-900">IGST (%)</th>
                                        <th v-if="data[0].company.gst_type =='IGST'" class="px-4 py-4 text-sm font-semi bold text-gray-900">IGST (₹)</th>
                                        <th v-if="data[0].company.gst_type =='CGST/SGST'" class="px-4 py-4 text-sm font-semi bold text-gray-900">CGST (%)</th>
                                        <th v-if="data[0].company.gst_type =='CGST/SGST'" class="px-4 py-4 text-sm font-semi bold text-gray-900">SGST (%)</th>
                                        <th v-if="data[0].company.gst_type =='CGST/SGST'" class="px-4 py-4 text-sm font-semi bold text-gray-900">Total GST (₹)</th>
                                        <th class="px-4 py-4 text-sm font-semi bold text-gray-900">TOTAL AMOUNT</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(detail, index) in data[0].debit_note_details" :key="detail.id" class="odd:bg-white even:bg-gray-50 border-b">
                                        <td class="px-4 py-2.5 min-w-20">{{ index + 1 }}</td>
                                        <td class="px-4 py-2.5 font-medium text-gray-900 min-w-20">
                                            {{ detail.product?.item_code || '-' }}
                                        </td>
                                        <td class="px-4 py-2.5 font-medium text-gray-900 min-w-40">
                                            {{ detail.product?.name || '-' }}
                                        </td>
                                        <td class="px-4 py-2.5 min-w-28">
                                            {{ detail.serial_numbers?.batch || '-' }}
                                        </td>
                                        <td class="px-4 py-2.5 min-w-28">
                                            {{ detail.serial_numbers?.expiry_date || '-' }}
                                        </td>
                                        <td class="px-4 py-2.5">{{ detail.qty || 0 }}</td>
                                        <td class="px-4 py-2.5 min-w-28">{{ detail.price || 0 }}</td>
                                        <td class="px-4 py-2.5 min-w-32">{{ detail.total_price || 0 }}</td>
                                        <td  v-if="data[0].company.gst_type =='IGST'" class="px-4 py-2.5 min-w-24">{{ detail.gst }}</td>
                                        <td  v-if="data[0].company.gst_type =='IGST'" class="px-4 py-2.5 min-w-24">{{ detail.gst_amount }}</td>
                                        <td  v-if="data[0].company.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-24">{{ detail.gst/2 }}</td>
                                        <td  v-if="data[0].company.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-24">{{ detail.gst/2 }}</td>
                                        <td  v-if="data[0].company.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-32">{{ detail.total_gst_amount }}</td>
                                        <td class="px-4 py-2.5 min-w-36">{{ detail.total_amount || 0 }}</td>
                                    </tr>
                                </tbody>
                            </table>
                            </div>
                        </div>
                    </div>

                    <!-- Amount Summary -->
                    <div class="border-t pt-6">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <div class="space-y-3">

                            </div>
                            <div class="space-y-3">

                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-700">Sub Total:</span>
                                    <span class="font-medium text-sm">₹{{ data[0].sub_total || 0 }}</span>
                                </div>
                                <div v-if="data[0].company.gst_type === 'CGST/SGST'" class="flex justify-between">
                                    <span class="text-sm text-gray-700">CGST:</span>
                                    <span class="font-medium text-sm">₹{{ data[0].cgst || 0 }}</span>
                                </div>
                                <div v-if="data[0].company.gst_type === 'CGST/SGST'" class="flex justify-between">
                                    <span class="text-sm text-gray-700">SGST:</span>
                                    <span class="font-medium text-sm">₹{{ data[0].sgst || 0 }}</span>
                                </div>
                                <div v-if="data[0].company.gst_type === 'IGST'" class="flex justify-between">
                                    <span class="text-sm text-gray-700">IGST:</span>
                                    <span class="font-medium text-sm">₹{{ data[0].igst || 0 }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-700">Discount Before Tax:</span>
                                    <span class="font-medium text-sm">₹{{ data[0].discount_before_tax || 0 }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-700">Overall Discount:</span>
                                    <span class="font-medium text-sm">₹{{ data[0].overall_discount || 0 }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-700">Total GST:</span>
                                    <span class="font-medium text-sm">₹{{ data[0].total_gst || 0 }}</span>
                                </div>
                                <div class="flex justify-between border-t pt-3">
                                    <span class="text-lg font-semibold text-gray-900">Total Amount:</span>
                                    <span class="text-lg font-bold text-gray-900">₹{{ data[0].total_amount || 0 }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div v-else class="bg-white rounded-lg shadow-sm border p-8 text-center">
                <p class="text-gray-500">No debit note data found.</p>
            </div>
        </div>
    </AdminLayout>
</template>

