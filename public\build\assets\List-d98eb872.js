import{K as tt,r as p,j as ye,l as st,o as a,c as d,a as r,u as w,n as z,t as o,f as u,w as _,F as $,Z as ot,b as e,g as C,i as W,e as U,k as lt,v as nt,s as at,x as it,O as dt}from"./app-4f4c883b.js";import{_ as rt,b as ut,a as G}from"./AdminLayout-d9d2bc31.js";import{_ as fe}from"./CreateButton-7995b8ff.js";import{_ as L}from"./SecondaryButton-69637431.js";import{_ as P}from"./TextInput-21f4f57b.js";import{_ as ct}from"./TextArea-b7098398.js";import{P as ae}from"./PrimaryButton-3e579b0b.js";import{M as Y}from"./Modal-85d770f4.js";import{N as mt,Q as _t}from"./tagify.esm-04603fe1.js";import{_ as vt}from"./Pagination-b0edb9e0.js";import{_ as ie}from"./SimpleDropdown-c621095c.js";import{_ as E}from"./SearchableDropdown-ace42120.js";import{_ as ge}from"./SearchableDropdownNew-a765fe75.js";import{_ as pt}from"./RadioButton-ef33c90c.js";import{D as xe}from"./DangerButton-b7cb11b9.js";import"./html2canvas.esm-9dbb97e1.js";import{_ as h}from"./InputLabel-468796e0.js";import{Q as ht}from"./vue-quill.snow-bc3564a6.js";import{_ as yt}from"./ArrowIcon-19315680.js";import{s as ft}from"./sortAndSearch-2a277c12.js";import{_ as gt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const l=c=>(at("data-v-20664a18"),c=c(),it(),c),xt={class:"animate-top"},bt={class:"flex justify-between items-center"},wt=l(()=>e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Invoice")],-1)),kt={class:"flex justify-end"},Ct={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},Tt={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},St=l(()=>e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),It={key:0,class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},Vt={class:"flex justify-end"},jt={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},zt={key:0,class:"flex justify-end"},Mt={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},Nt={class:"flex mb-2"},Ut=l(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),Pt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},At={class:"sm:col-span-4"},Ot={class:"relative mt-2"},Et={class:"sm:col-span-4"},Bt={class:"relative mt-2"},$t={class:"sm:col-span-4"},Gt={class:"relative mt-2"},Lt={class:"sm:col-span-4"},Ft={class:"relative mt-2"},Rt={class:"sm:col-span-4"},Ht={class:"relative mt-2"},qt={class:"mt-8 overflow-x-auto sm:rounded-lg"},Xt={class:"shadow sm:rounded-lg"},Wt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Yt={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Qt={class:"border-b-2"},Kt=["onClick"],Zt={key:0},Jt={class:"px-4 py-2.5 min-w-40"},Dt={class:"px-4 py-2.5"},es={class:"px-4 py-2.5"},ts={class:"px-4 py-2.5 min-w-52 font-medium text-gray-900 whitespace-nowrap truncate"},ss={class:"px-4 py-2.5 min-w-32"},os={class:"px-4 py-2.5 min-w-32"},ls={class:"px-4 py-2.5 min-w-40"},ns={class:"flex flex-1 items-center px-4 py-2.5"},as={class:"items-center px-4 py-2.5"},is={class:"flex items-center justify-start gap-4"},ds=l(()=>e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)),rs=l(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),us=l(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),cs=l(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),ms=l(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),_s=l(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),vs=l(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),ps=["onClick"],hs=l(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),ys=l(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)),fs=[hs,ys],gs=["onClick","disabled"],xs=l(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),bs=l(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)),ws=[xs,bs],ks=l(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M3 10h18M3 14h18M3 18h18"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M17 3v18a2 2 0 01-2 2H9a2 2 0 01-2-2V3a2 2 0 012-2h6a2 2 0 012 2z"})],-1)),Cs=l(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Credit Note ",-1)),Ts=l(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),Ss=l(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," View Invoice ",-1)),Is=["onClick"],Vs=l(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M6 2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm2 6h8m-4 4h4"})],-1)),js=l(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Generate PDF ",-1)),zs=[Vs,js],Ms=["onClick"],Ns=l(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M3 8l7 5 7-5M3 8v8m14-8v8M3 8l7 5 7-5"})],-1)),Us=l(()=>e("span",{class:"text-sm text-gray-700 leading-5"},"Send Email",-1)),Ps=[Ns,Us],As={key:1},Os=l(()=>e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),Es=[Os],Bs=l(()=>e("div",{class:"flex w-full justify-start bg-indigo-600 px-6 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"},[e("h2",{class:"text-2xl font-bold"},"Send Email")],-1)),$s={class:"p-6 overflow-y-auto max-h-screen"},Gs={class:"space-y-4"},Ls={key:0,class:"text-red-500 text-sm"},Fs={class:"relative mt-2"},Rs={key:0,class:"text-red-500 text-sm"},Hs={class:"flex space-x-4"},qs={class:"w-1/2"},Xs={class:"relative mt-2"},Ws={key:0,class:"text-red-500 text-sm"},Ys={class:"w-1/2"},Qs={class:"relative mt-2"},Ks={key:0,class:"text-red-500 text-sm"},Zs={key:0,class:"text-red-500 text-sm"},Js={class:"mb-20"},Ds={key:0,class:"text-red-500 text-sm"},eo={class:"px-10 py-4 bg-white flex justify-end absolute w-full right-0 bottom-0"},to={class:"w-36"},so={key:0},oo=l(()=>e("svg",{class:"animate-spin h-5 w-5 mr-2 text-white inline-block",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8v8H4z"})],-1)),lo={key:1},no={class:"p-6"},ao=l(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this Invoice? ",-1)),io={class:"mt-6 flex justify-end"},ro={class:"p-6"},uo=l(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this Transferred stock? ",-1)),co={class:"mt-6 flex justify-end"},mo={class:"p-6"},_o={class:"container1",id:"pdf-content"},vo={key:0,class:"header",style:{display:"flex","align-items":"center","justify-content":"space-between","margin-bottom":"10px"}},po=["src"],ho={style:{"font-size":"20px"}},yo=l(()=>e("h1",null,[e("div",{style:{width:"120px"}})],-1)),fo={key:1,class:"header",style:{"align-items":"start","justify-content":"center","text-align":"center"}},go=["src"],xo={style:{"align-items":"center","justify-content":"space-between","margin-bottom":"10px"}},bo={style:{"font-size":"20px"}},wo={style:{display:"flex","justify-content":"space-between"}},ko={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},Co={style:{"font-size":"14px","margin-top":"10px"}},To=l(()=>e("p",{style:{"margin-bottom":"4px"}},[e("strong")],-1)),So=l(()=>e("p",{style:{"margin-bottom":"4px"}},[e("strong")],-1)),Io={style:{display:"flex"}},Vo=l(()=>e("p",{style:{width:"40px"}},[e("strong",null,"Phone")],-1)),jo={style:{display:"flex"}},zo=l(()=>e("p",{style:{width:"40px"}},[e("strong",null,"Email")],-1)),Mo={style:{display:"flex"}},No=l(()=>e("p",{style:{width:"40px"}},[e("strong",null,"GST")],-1)),Uo={class:"invoice-details",style:{"margin-bottom":"20px","justify-items":"start",width:"320px"}},Po={style:{display:"flex"}},Ao=l(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Invoice Number")],-1)),Oo={style:{display:"flex"}},Eo=l(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Invoice Date")],-1)),Bo={key:0,style:{display:"flex"}},$o=l(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Challan Number")],-1)),Go={key:1,style:{display:"flex"}},Lo=l(()=>e("p",{style:{width:"100px"}},[e("strong",null,"PO Number")],-1)),Fo={key:2,style:{display:"flex"}},Ro=l(()=>e("p",{style:{width:"100px"}},[e("strong",null,"PO Date")],-1)),Ho={key:3,style:{display:"flex"}},qo=l(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Patient Name")],-1)),Xo={key:4,style:{display:"flex"}},Wo=l(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Eway Bill")],-1)),Yo={key:5,style:{display:"flex"}},Qo=l(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Due Days")],-1)),Ko=l(()=>e("p",{style:{"margin-bottom":"4px"}},[e("strong")],-1)),Zo={style:{"font-size":"14px","margin-top":"10px"}},Jo=l(()=>e("p",{style:{"margin-bottom":"4px"}},[e("strong")],-1)),Do={style:{display:"flex"}},el=l(()=>e("p",{style:{width:"40px"}},[e("strong",null,"Phone")],-1)),tl={style:{display:"flex"}},sl=l(()=>e("p",{style:{width:"40px"}},[e("strong",null,"Email")],-1)),ol={style:{display:"flex"}},ll=l(()=>e("p",{style:{width:"40px"}},[e("strong",null,"GST")],-1)),nl={style:{display:"flex","justify-content":"space-between"}},al={key:0,style:{display:"flex"}},il=l(()=>e("p",{style:{}},[e("strong",null,"Transport")],-1)),dl={key:1,style:{display:"flex"}},rl=l(()=>e("p",{style:{}},[e("strong",null,"Dispatch")],-1)),ul={style:{"overflow-x":"auto"}},cl=l(()=>e("th",null,"SN",-1)),ml={key:0},_l={key:1},vl=l(()=>e("th",null,"PRODUCT NAME",-1)),pl=l(()=>e("th",null,"HSN",-1)),hl=l(()=>e("th",null,"QTY",-1)),yl=l(()=>e("th",null,"BATCH",-1)),fl=l(()=>e("th",null,"EXP",-1)),gl=l(()=>e("th",null,"MRP",-1)),xl=l(()=>e("th",null,"RATE",-1)),bl={key:2},wl={key:3},kl={key:4},Cl=l(()=>e("th",null,"DIS.(₹)",-1)),Tl=l(()=>e("th",null,"AMOUNT",-1)),Sl={key:0},Il={key:1},Vl={style:{display:"flex"}},jl={key:2},zl={key:3},Ml={key:4},Nl={class:"",style:{"margin-bottom":"10px","justify-items":"start"}},Ul={style:{display:"flex","justify-content":"space-between"}},Pl={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"400px"}},Al=l(()=>e("p",null,[e("strong",null,"OUR BANK DETAILS")],-1)),Ol={key:0,style:{display:"flex"}},El=l(()=>e("p",null,[e("strong",null,"Bank Name")],-1)),Bl={key:1,style:{display:"flex"}},$l=l(()=>e("p",null,[e("strong",null,"Branch Name")],-1)),Gl={key:2,style:{display:"flex"}},Ll=l(()=>e("p",null,[e("strong",null,"Account No")],-1)),Fl={key:3,style:{display:"flex"}},Rl=l(()=>e("p",null,[e("strong",null,"IFSC Code")],-1)),Hl={key:4,style:{display:"flex"}},ql=l(()=>e("p",null,[e("strong",null,"Bank Name")],-1)),Xl={key:5,style:{display:"flex"}},Wl=l(()=>e("p",null,[e("strong",null,"Branch Name")],-1)),Yl={key:6,style:{display:"flex"}},Ql=l(()=>e("p",null,[e("strong",null,"Account No")],-1)),Kl={key:7,style:{display:"flex"}},Zl=l(()=>e("p",null,[e("strong",null,"IFSC Code")],-1)),Jl={key:8,style:{display:"flex"}},Dl=l(()=>e("p",null,[e("strong",null,"Bank Name")],-1)),en={key:9,style:{display:"flex"}},tn=l(()=>e("p",null,[e("strong",null,"Branch Name")],-1)),sn={key:10,style:{display:"flex"}},on=l(()=>e("p",null,[e("strong",null,"Account No")],-1)),ln={key:11,style:{display:"flex"}},nn=l(()=>e("p",null,[e("strong",null,"IFSC Code")],-1)),an={class:"invoice-details",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},dn={style:{display:"flex"}},rn=l(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Sub Total (₹)")],-1)),un={style:{display:"flex"}},cn=l(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Total Discount (₹)")],-1)),mn={key:0,style:{display:"flex"}},_n=l(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Total IGST (₹):")],-1)),vn={key:1,style:{display:"flex"}},pn=l(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Total CGST (₹):")],-1)),hn={key:2,style:{display:"flex"}},yn=l(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Total SGST (₹):")],-1)),fn={style:{display:"flex"}},gn=l(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Total Amount (₹)")],-1)),xn={style:{display:"flex","justify-content":"space-between"}},bn={class:"invoiceMCbank",style:{"margin-bottom":"20px","justify-items":"start",width:"405px"}},wn=l(()=>e("p",null,[e("strong",null,"TERMS & CONDITIONS")],-1)),kn={style:{display:"flex"}},Cn={style:{display:"flex"}},Tn={style:{display:"flex"}},Sn={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},In=l(()=>e("p",null,[e("strong",null,"FOR,")],-1)),Vn=["src"],jn={class:"mt-6 px-4 flex justify-end"},zn={class:"flex flex-col justify-end space-y-6"},Mn={class:"flex items-center space-x-2"},Nn={class:"flex justify-end"},Un={class:"w-36"},Pn={class:"p-6"},An={class:"flex items-center justify-between"},On=l(()=>e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Receive Payment",-1)),En={key:0,class:"text-base font-semibold leading-6 text-gray-900"},Bn={class:"border-b border-gray-900/10 pb-12"},$n={class:"mt-4 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Gn={class:"sm:col-span-3"},Ln={class:"inline-flex items-center justify-start w-full space-x-3"},Fn={class:"text-sm font-semibold text-gray-700"},Rn={class:"sm:col-span-3"},Hn={class:"inline-flex items-center justify-start w-full space-x-3"},qn={class:"text-sm font-semibold text-gray-700"},Xn={class:"mt-2 sm:col-span-4"},Wn={class:"relative mt-2"},Yn=l(()=>e("div",{class:"sm:col-span-3"},null,-1)),Qn={key:0,class:"mt-5 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Kn={class:"sm:col-span-3"},Zn={class:"relative mt-2"},Jn={class:"sm:col-span-3"},Dn={key:0,class:"sm:col-span-3"},ea={key:1,class:"sm:col-span-3"},ta={class:"sm:col-span-3"},sa={key:2,class:"sm:col-span-3"},oa={class:"relative mt-2"},la={class:"sm:col-span-6"},na={key:1},aa={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},ia=l(()=>e("thead",null,[e("tr",null,[e("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),e("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),e("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),e("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)"),e("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Amount To Credit (₹)")])],-1)),da={class:"divide-y divide-gray-300 bg-white"},ra={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ua={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ca={class:"flex flex-col"},ma={class:"text-sm text-gray-900"},_a={class:"text-sm text-gray-900"},va={class:"whitespace-nowrap py-3 text-sm text-gray-900"},pa={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ha={class:"whitespace-nowrap py-3 text-sm text-gray-900",style:{width:"22%"}},ya={class:"mt-4 flex justify-end"},fa={class:"text-base font-semibold"},ga={class:"mt-6 px-4 flex justify-end"},xa={class:"w-36"},ba={__name:"List",props:["data","user_id","permissions","organization","emailTemplates","email","customers","paymentType","terms","invoiceMCbank","invoiceHCbank","invoiceNOXbank","pagetypes","bankinfo","organizationId","customerId","invoicetypes","invoice_type","salesuser","salesUserId","category","categoryId"],setup(c){const x=c,{form:b,search:wa,sort:be,fetchData:ka,sortKey:we,sortDirection:ke,updateParams:Ce}=ft("invoice.index",{customer_id:x.customerId,invoice_type:x.invoiceType,organization_id:x.organizationId,sales_user_id:x.salesUserId,category:x.categoryId}),Q=tt().props.filepath.view,n=p([]),K=p(!1),Z=p(!1),Te=p("custom"),Se=p("custom2"),F=p("");p({});const Ie=[{field:"invoice_no",label:"INVOICE NUMBER",sortable:!0},{field:"invoice_type",label:"TYPE",sortable:!0},{field:"category",label:"CATEGORY",sortable:!0},{field:"customers.customer_name",label:"CUSTOMER NAME",sortable:!0},{field:"date",label:"DATE",sortable:!0},{field:"total_amount",label:"AMOUNT (₹)",sortable:!0},{field:"paid_amount",label:"PAID AMOUNT (₹)",sortable:!0},{field:"status",label:"STATUS",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],R=p("portrait"),H=p(!1),q=p(!1),M=p({show:!1,message:"",type:"success"}),v=p({to:"",from:"",template:"",pagetype:"",subject:"",content:""}),m=p({to:"",from:"",cc:"",template:"",subject:"",content:"",id:"",user_id:"",pagetype:"portrait"}),Ve=i=>{const s=x.data.data.find(t=>t.id===i);m.value={to:s?s.customers.email:"",cc:"",id:i,from:"",template:"",subject:"",content:"",user_id:x.user_id,pagetype:"portrait"},v.value={to:"",from:"",template:"",pagetype:"",subject:"",content:""},Array.isArray(x.email)&&x.email.length>0&&(m.value.from=x.email[0].id),q.value=!0,dt(()=>{const t=document.getElementById("cc");if(t){const f=new _t(t,{delimiters:",",pattern:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,placeholder:"Add CC emails..."});f.on("change",k=>{m.value.cc=f.value.map(N=>N.value).join(",")})}})},je=()=>{q.value=!1},ze=(i,s)=>{const t=x.emailTemplates.find(f=>f.id===i);t&&(m.value.template=s,m.value.subject=t.email_subject,m.value.content=t.content,v.value.template="",v.value.subject="",v.value.content="")},Me=(i,s)=>{m.value.from=i,v.value.from=""},Ne=(i,s)=>{m.value.pagetype=i,v.value.pagetype=""},Ue=()=>{let i=!0;if(v.value={to:"",from:"",template:"",pagetype:"",subject:"",content:""},(!m.value.to||m.value.to.trim()==="")&&(v.value.to="This to field is required.",i=!1),m.value.from||(v.value.from="This from field is required.",i=!1),m.value.template||(v.value.template="This template field is required.",i=!1),m.value.pagetype||(v.value.pagetype="This field is required.",i=!1),(!m.value.subject||m.value.subject.trim()==="")&&(v.value.subject="This subject field is required.",i=!1),(!m.value.content||m.value.content.trim()==="")&&(v.value.content="This content field is required.",i=!1),!i)return;H.value=!0;const s=new FormData;s.append("to",m.value.to),s.append("from",m.value.from),s.append("cc",m.value.cc),s.append("subject",m.value.subject),s.append("content",m.value.content),s.append("id",m.value.id),s.append("user_id",m.value.user_id),s.append("pagetype",m.value.pagetype),s.append("template",m.value.template),fetch(route("send-invoice-email"),{method:"POST",body:s,headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}}).then(async t=>{const f=await t.json();if(!t.ok)throw M.value={show:!0,message:f.error||"Failed to send email",type:"error"},new Error(f.error||"Failed to send email");M.value={show:!0,message:f.success||"Email sent successfully",type:"success"},q.value=!1,setTimeout(()=>{M.value.show=!1},3e3)}).catch(t=>{M.value={show:!0,message:t.message||"An unexpected error occurred",type:"error"}}).finally(()=>{H.value=!1,setTimeout(()=>{M.value.show=!1},5e3)})},Pe=p([]),B=p([]),Ae=p(""),Oe=i=>{const s=x.data.data.find(t=>t.id===i);n.value=s,K.value=!0},Ee=ye(()=>B.value.reduce((i,s)=>i+(s.amount_to_credit?parseFloat(s.amount_to_credit):0),0)),de=()=>{Z.value=!1},re=()=>{K.value=!1},T=p(x.organizationId),S=p(x.customerId),I=p(x.invoice_type),V=p(x.salesUserId),j=p(x.categoryId),A=p("");st([T,S,I,V,j],()=>{Ce({organization_id:T.value,customer_id:S.value,invoice_type:I.value,sales_user_id:V.value,category:j.value})});const O=(i,s,t,f,k,N)=>{A.value=i,b.get(route("invoice.index",{search:i,organization_id:s,customer_id:t,invoice_type:f,sales_user_id:k,category:N}),{preserveState:!0})},J=p(!1),ue=p(null),Be=i=>{ue.value=i,J.value=!0},D=()=>{J.value=!1},$e=()=>{b.delete(route("invoice.destroy",{id:ue.value}),{onSuccess:()=>D()})},ee=p(!1),ce=p(null),Ge=i=>{ce.value=i,ee.value=!0},te=()=>{ee.value=!1},Le=()=>{b.delete(route("stock-transfer.destroy",{id:ce.value}),{onSuccess:()=>te()})},Fe=(i,s)=>{T.value=i,O(A.value,T.value,S.value,I.value,V.value,j.value)},Re=(i,s)=>{S.value=i,O(A.value,T.value,S.value,I.value,V.value,j.value)},He=(i,s)=>{I.value=i,O(A.value,T.value,S.value,I.value,V.value,j.value)},qe=(i,s)=>{j.value=i,O(A.value,T.value,S.value,I.value,V.value,j.value)},Xe=(i,s)=>{V.value=i,O(A.value,T.value,S.value,I.value,V.value,j.value)},We=i=>{switch(i){case"Unpaid":return"bg-blue-100";case"Partially Paid":return"bg-yellow-100";case"Paid":return"bg-green-100";default:return"bg-gray-100"}},Ye=i=>{switch(i){case"Unpaid":return"text-blue-600";case"Partially Paid":return"text-yellow-600";case"Paid":return"text-green-600";default:return"text-gray-600"}},Qe=(i,s)=>{R.value=i},Ke=(i,s)=>{window.open(`/invoice/download/${i}/${s}`,"_blank")},se=i=>{const s=new Date(i),t={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",t)},g=i=>{if(i==null||isNaN(i))return"0.00";let s=Number(i).toFixed(2),[t,f]=s.split("."),k=t.substring(t.length-3),N=t.substring(0,t.length-3);return N!==""&&(k=","+k),`${N.replace(/\B(?=(\d{2})+(?!\d))/g,",")+k}.${f}`},X=p("No"),Ze=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],y=ye(()=>({organization_id:"",customer_id:"",org_bank_id:"",invoice_id:"",invoice_no:"",payment_type:"",amount:"",check_number:"",bank_name:"",date:"",note:"",invoice_amount:""})),Je=(i,s)=>{y.value.payment_type=i,F.value=s,b.errors["form.payment_type"]=null,s==="Cash"?y.value.note="Cash":y.value.note==="Cash"&&(y.value.note="")},De=(i,s)=>{y.value.org_bank_id=i,b.errors["form.org_bank_id"]=null},et=()=>{b.post(route("invoice.paymentreceive",{form:y.value,is_credit:X.value,credit_data:B.value}),{onSuccess:()=>{b.reset(),Z.value=!1},onError:i=>{}})},oe=i=>{b.errors[i]=null};return(i,s)=>(a(),d($,null,[r(w(ot),{title:"Invoice"}),M.value.show?(a(),d("div",{key:0,class:z(["notification",M.value.type]),onClick:s[0]||(s[0]=t=>M.value.show=!1)},o(M.value.message),3)):u("",!0),r(rt,null,{default:_(()=>[e("div",xt,[e("div",bt,[wt,e("div",kt,[e("div",Ct,[e("div",Tt,[St,e("input",{id:"search-field",onInput:s[1]||(s[1]=t=>O(t.target.value,T.value,S.value,I.value,V.value,j.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),c.permissions.canCreateInvoice?(a(),d("div",It,[e("div",Vt,[r(fe,{href:i.route("invoice.create")},{default:_(()=>[C(" Create Invoice ")]),_:1},8,["href"])])])):u("",!0),e("div",jt,[c.permissions.canStockTransfer?(a(),d("div",zt,[r(fe,{href:i.route("stocktransfer")},{default:_(()=>[C(" Stock Transfer ")]),_:1},8,["href"])])):u("",!0)])])]),e("div",Mt,[e("div",Nt,[Ut,r(h,{for:"customer_id",value:"Filters"})]),e("div",Pt,[e("div",At,[r(h,{for:"customer_id",value:"Organization Name"}),e("div",Ot,[r(ie,{options:c.organization,modelValue:T.value,"onUpdate:modelValue":s[2]||(s[2]=t=>T.value=t),onOnchange:Fe},null,8,["options","modelValue"])])]),e("div",Et,[r(h,{for:"customer_id",value:"Customer Name"}),e("div",Bt,[r(ge,{options:c.customers,modelValue:S.value,"onUpdate:modelValue":s[3]||(s[3]=t=>S.value=t),onOnchange:Re},null,8,["options","modelValue"])])]),e("div",$t,[r(h,{for:"customer_id",value:"Invoice Type"}),e("div",Gt,[r(ie,{options:c.invoicetypes,modelValue:I.value,"onUpdate:modelValue":s[4]||(s[4]=t=>I.value=t),onOnchange:He},null,8,["options","modelValue"])])]),e("div",Lt,[r(h,{for:"customer_id",value:"Sales Person"}),e("div",Ft,[r(ge,{options:c.salesuser,modelValue:V.value,"onUpdate:modelValue":s[5]||(s[5]=t=>V.value=t),onOnchange:Xe},null,8,["options","modelValue"])])]),e("div",Rt,[r(h,{for:"customer_id",value:"Category"}),e("div",Ht,[r(ie,{options:c.category,modelValue:j.value,"onUpdate:modelValue":s[6]||(s[6]=t=>j.value=t),onOnchange:qe},null,8,["options","modelValue"])])])])]),e("div",qt,[e("div",Xt,[e("table",Wt,[e("thead",Yt,[e("tr",Qt,[(a(),d($,null,W(Ie,(t,f)=>e("th",{key:f,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:k=>w(be)(t.field,t.sortable)},[C(o(t.label)+" ",1),t.sortable?(a(),U(yt,{key:0,isSorted:w(we)===t.field,direction:w(ke)},null,8,["isSorted","direction"])):u("",!0)],8,Kt)),64))])]),c.data.data&&c.data.data.length>0?(a(),d("tbody",Zt,[(a(!0),d($,null,W(c.data.data,(t,f)=>(a(),d("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Jt,o(t.invoice_no),1),e("td",Dt,o(t.invoice_type),1),e("td",es,o(t.category),1),e("td",ts,o(t.customers.customer_name),1),e("td",ss,o(se(t.date)),1),e("td",os,o(g(t.total_amount)),1),e("td",ls,o(g(t.paid_amount)),1),e("td",ns,[e("div",{class:z(["flex rounded-full px-4 py-1",We(t.status)])},[e("span",{class:z(["text-sm font-semibold whitespace-nowrap",Ye(t.status)])},o(t.status),3)],2)]),e("td",as,[e("div",is,[r(ut,{align:"right",width:"48"},{trigger:_(()=>[ds]),content:_(()=>[t.status=="Unpaid"&&t.entity_type=="invoice"&&t.customers.organization_id==null&&c.permissions.canEditInvoice?(a(),U(G,{key:0,href:i.route("invoice.edit",{id:t.id})},{svg:_(()=>[rs]),text:_(()=>[us]),_:2},1032,["href"])):u("",!0),t.status=="Unpaid"&&t.entity_type=="challan"&&t.customers.organization_id==null&&c.permissions.canEditInvoice?(a(),U(G,{key:1,href:i.route("challaninvoice.edit",{id:t.id})},{svg:_(()=>[cs]),text:_(()=>[ms]),_:2},1032,["href"])):u("",!0),t.status=="Unpaid"&&t.customers.organization_id!=null&&c.permissions.canStockTransferEdit?(a(),U(G,{key:2,href:i.route("stocktransfer.edit",{id:t.id})},{svg:_(()=>[_s]),text:_(()=>[vs]),_:2},1032,["href"])):u("",!0),t.status=="Unpaid"&&t.customers.organization_id==null?(a(),d("button",{key:3,type:"button",onClick:k=>Be(t.id)&&c.permissions.canDeleteInvoice,class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},fs,8,ps)):u("",!0),t.status==="Unpaid"&&t.customers.organization_id!=null&&c.permissions.canDeleteInvoice?(a(),d("button",{key:4,type:"button",onClick:k=>Ge(t.id),class:"flex space-x-2 items-center px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full",disabled:!c.permissions.canDeleteInvoice},ws,8,gs)):u("",!0),t.customers.organization_id==null&&c.permissions.canEditInvoice?(a(),U(G,{key:5,href:i.route("creditnote.add",{id:t.id})},{svg:_(()=>[ks]),text:_(()=>[Cs]),_:2},1032,["href"])):u("",!0),c.permissions.canViewInvoice?(a(),U(G,{key:6,href:i.route("invoice.view",{id:t.id,source:"invoice .index"})},{svg:_(()=>[Ts]),text:_(()=>[Ss]),_:2},1032,["href"])):u("",!0),e("button",{type:"button",onClick:k=>Oe(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},zs,8,Is),c.permissions.canCreateInvoice?(a(),d("button",{key:7,type:"button",onClick:k=>Ve(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Ps,8,Ms)):u("",!0)]),_:2},1024)])])]))),128))])):(a(),d("tbody",As,Es))])])]),c.data.data&&c.data.data.length>0?(a(),U(vt,{key:0,class:"mt-6",links:c.data.links},null,8,["links"])):u("",!0)]),r(mt,{show:q.value},{default:_(()=>[Bs,e("div",$s,[e("div",Gs,[e("div",null,[r(h,{for:"to",value:"To:"}),r(P,{id:"to",type:"email",modelValue:m.value.to,"onUpdate:modelValue":s[7]||(s[7]=t=>m.value.to=t),onInput:s[8]||(s[8]=t=>v.value.to="")},null,8,["modelValue"]),v.value.to?(a(),d("span",Ls,o(v.value.to),1)):u("",!0)]),e("div",null,[r(h,{for:"cc",value:"CC:"}),r(P,{id:"cc",type:"text",placeholder:"Enter multiple emails",ref:"ccInput"},null,512)]),e("div",null,[r(h,{for:"from",value:"From:"}),e("div",Fs,[r(E,{options:c.email,modelValue:m.value.from,"onUpdate:modelValue":s[9]||(s[9]=t=>m.value.from=t),onOnchange:Me},null,8,["options","modelValue"])]),v.value.from?(a(),d("span",Rs,o(v.value.from),1)):u("",!0)]),e("div",Hs,[e("div",qs,[r(h,{value:"Template:"}),e("div",Xs,[r(E,{options:c.emailTemplates,modelValue:m.value.template,"onUpdate:modelValue":s[10]||(s[10]=t=>m.value.template=t),onOnchange:ze},null,8,["options","modelValue"])]),v.value.template?(a(),d("span",Ws,o(v.value.template),1)):u("",!0)]),e("div",Ys,[r(h,{value:"PDF Page:"}),e("div",Qs,[r(E,{options:c.pagetypes,modelValue:m.value.pagetype,"onUpdate:modelValue":s[11]||(s[11]=t=>m.value.pagetype=t),onOnchange:Ne},null,8,["options","modelValue"])]),v.value.pagetype?(a(),d("span",Ks,o(v.value.pagetype),1)):u("",!0)])]),e("div",null,[r(h,{for:"subject",value:"Subject:"}),r(P,{id:"subject",type:"text",modelValue:m.value.subject,"onUpdate:modelValue":s[12]||(s[12]=t=>m.value.subject=t),onInput:s[13]||(s[13]=t=>v.value.subject="")},null,8,["modelValue"]),v.value.subject?(a(),d("span",Zs,o(v.value.subject),1)):u("",!0)]),e("div",Js,[r(h,{for:"content",value:"Content:"}),r(w(ht),{content:m.value.content,"onUpdate:content":[s[14]||(s[14]=t=>m.value.content=t),s[15]||(s[15]=t=>v.value.content="")],contentType:"html",theme:"snow",toolbar:"essential"},null,8,["content"]),v.value.content?(a(),d("span",Ds,o(v.value.content),1)):u("",!0)])])]),e("div",eo,[r(L,{onClick:je},{default:_(()=>[C("Cancel")]),_:1}),e("div",to,[r(ae,{class:"ml-3 w-20",onClick:Ue,disabled:H.value},{default:_(()=>[H.value?(a(),d("span",so,[oo,C(" Sending... ")])):(a(),d("span",lo,"Send Email"))]),_:1},8,["disabled"])])])]),_:1},8,["show"]),r(Y,{show:J.value,onClose:D},{default:_(()=>[e("div",no,[ao,e("div",io,[r(L,{onClick:D},{default:_(()=>[C(" Cancel ")]),_:1}),r(xe,{class:"ml-3",onClick:$e},{default:_(()=>[C(" Delete ")]),_:1})])])]),_:1},8,["show"]),r(Y,{show:ee.value,onClose:te},{default:_(()=>[e("div",ro,[uo,e("div",co,[r(L,{onClick:te},{default:_(()=>[C(" Cancel ")]),_:1}),r(xe,{class:"ml-3",onClick:Le},{default:_(()=>[C(" Delete ")]),_:1})])])]),_:1},8,["show"]),r(Y,{show:K.value,onClose:re,maxWidth:Te.value},{default:_(()=>[e("div",mo,[e("div",_o,[n.value.organization.id=="3"?(a(),d("div",vo,[e("img",{class:"w-20 h-20",src:w(Q)+n.value.organization.logo,alt:"logo"},null,8,po),e("p",null,[e("strong",ho,o(n.value.customers.customer_type)+" Invoice",1)]),yo])):u("",!0),n.value.organization.id=="1"||n.value.organization.id=="2"?(a(),d("div",fo,[e("img",{class:"w-full h-10",src:w(Q)+n.value.organization.logo,alt:"logo"},null,8,go),e("div",xo,[e("p",bo,[e("strong",null,o(n.value.customers.customer_type)+" Invoice",1)])])])):u("",!0),e("div",wo,[e("div",ko,[e("p",null,[e("strong",Co,o(n.value.organization.name),1)]),To,e("p",null,o(n.value.organization.address_line_1),1),e("p",null,o(n.value.organization.address_line_2),1),e("p",null,o(n.value.organization.pincode)+" , "+o(n.value.organization.city),1),So,e("div",Io,[Vo,e("p",null,": "+o(n.value.organization.contact_no),1)]),e("div",jo,[zo,e("p",null,": "+o(n.value.organization.email),1)]),e("div",Mo,[No,e("p",null,": "+o(n.value.organization.gst_no),1)])]),e("div",Uo,[e("div",Po,[Ao,e("p",null,": "+o(n.value.invoice_no),1)]),e("div",Oo,[Eo,e("p",null,": "+o(se(n.value.date)),1)]),n.value.entity_type=="challan"?(a(),d("div",Bo,[$o,e("p",null,": "+o(n.value.challan.challan_number),1)])):u("",!0),n.value.customer_po_number!=null?(a(),d("div",Go,[Lo,e("p",null,": "+o(n.value.customer_po_number),1)])):u("",!0),n.value.customer_po_date!=null?(a(),d("div",Fo,[Ro,e("p",null,": "+o(n.value.customer_po_date),1)])):u("",!0),n.value.patient_name!=null?(a(),d("div",Ho,[qo,e("p",null,": "+o(n.value.patient_name),1)])):u("",!0),n.value.eway_bill!=null?(a(),d("div",Xo,[Wo,e("p",null,": "+o(n.value.eway_bill),1)])):u("",!0),n.value.due_days!=null?(a(),d("div",Yo,[Qo,e("p",null,": "+o(n.value.due_days)+" Days",1)])):u("",!0),Ko,e("p",null,[e("strong",Zo,o(n.value.customers.customer_name),1)]),e("p",null,o(n.value.customers.address),1),Jo,e("div",Do,[el,e("p",null,": "+o(n.value.customers.contact_no),1)]),e("div",tl,[sl,e("p",null,": "+o(n.value.customers.email),1)]),e("div",ol,[ll,e("p",null,": "+o(n.value.customers.gst_no),1)]),e("div",nl,[n.value.transport!=null?(a(),d("div",al,[il,e("p",null,": "+o(n.value.transport),1)])):u("",!0),n.value.dispatch!=null?(a(),d("div",dl,[rl,e("p",null,": "+o(n.value.dispatch),1)])):u("",!0)])])]),e("div",ul,[e("table",null,[e("thead",null,[e("tr",null,[cl,n.value.category=="Service"?(a(),d("th",ml,"PART NO")):u("",!0),n.value.category=="Sales"?(a(),d("th",_l,"CODE")):u("",!0),vl,pl,hl,yl,fl,gl,xl,n.value.customers.gst_type=="IGST"?(a(),d("th",bl,"IGST(%)")):u("",!0),n.value.customers.gst_type=="CGST/SGST"?(a(),d("th",wl,"CGST(%)")):u("",!0),n.value.customers.gst_type=="CGST/SGST"?(a(),d("th",kl,"SGST(%)")):u("",!0),Cl,Tl])]),e("tbody",null,[(a(!0),d($,null,W(n.value.invoice_detail,(t,f)=>(a(),d("tr",{key:t.id,class:""},[e("td",null,o(f+1),1),n.value.category=="Service"?(a(),d("td",Sl,o(t.serialnumbers.product.item_code),1)):u("",!0),n.value.category=="Sales"?(a(),d("td",Il,o(t.serialnumbers.product.item_code),1)):u("",!0),e("td",null,[C(o(t.serialnumbers.product.name)+" ",1),e("span",Vl,o(t.description),1)]),e("td",null,o(t.serialnumbers.product.hsn_code),1),e("td",null,o(t.qty),1),e("td",null,o(t.serialnumbers.batch??"-"),1),e("td",null,o(t.serialnumbers.expiry_date??"-"),1),e("td",null,o(t.serialnumbers.mrp?g(t.serialnumbers.mrp):"-"),1),e("td",null,o(g(t.price)),1),n.value.customers.gst_type=="IGST"?(a(),d("td",jl,o(g(t.gst)??"-"),1)):u("",!0),n.value.customers.gst_type=="CGST/SGST"?(a(),d("td",zl,o(g(t.gst/2)??"-"),1)):u("",!0),n.value.customers.gst_type=="CGST/SGST"?(a(),d("td",Ml,o(g(t.gst/2)??"-"),1)):u("",!0),e("td",null,o(g(t.discount_amount)??"-"),1),e("td",null,o(g(t.total_amount)??"-"),1)]))),128))])])]),e("div",Nl,[e("p",null,o(n.value.note),1)]),e("div",Ul,[e("div",Pl,[Al,n.value.organization.id=="1"?(a(),d("div",Ol,[El,e("p",null,": "+o(c.invoiceMCbank.bank_name),1)])):u("",!0),n.value.organization.id=="1"?(a(),d("div",Bl,[$l,e("p",null,": "+o(c.invoiceMCbank.branch_name),1)])):u("",!0),n.value.organization.id=="1"?(a(),d("div",Gl,[Ll,e("p",null,": "+o(c.invoiceMCbank.account_no),1)])):u("",!0),n.value.organization.id=="1"?(a(),d("div",Fl,[Rl,e("p",null,": "+o(c.invoiceMCbank.ifsc_code),1)])):u("",!0),n.value.organization.id=="2"?(a(),d("div",Hl,[ql,e("p",null,": "+o(c.invoiceHCbank.bank_name),1)])):u("",!0),n.value.organization.id=="2"?(a(),d("div",Xl,[Wl,e("p",null,": "+o(c.invoiceHCbank.branch_name),1)])):u("",!0),n.value.organization.id=="2"?(a(),d("div",Yl,[Ql,e("p",null,": "+o(c.invoiceHCbank.account_no),1)])):u("",!0),n.value.organization.id=="2"?(a(),d("div",Kl,[Zl,e("p",null,": "+o(c.invoiceHCbank.ifsc_code),1)])):u("",!0),n.value.organization.id=="3"?(a(),d("div",Jl,[Dl,e("p",null,": "+o(c.invoiceNOXbank.bank_name),1)])):u("",!0),n.value.organization.id=="3"?(a(),d("div",en,[tn,e("p",null,": "+o(c.invoiceNOXbank.branch_name),1)])):u("",!0),n.value.organization.id=="3"?(a(),d("div",sn,[on,e("p",null,": "+o(c.invoiceNOXbank.account_no),1)])):u("",!0),n.value.organization.id=="3"?(a(),d("div",ln,[nn,e("p",null,": "+o(c.invoiceNOXbank.ifsc_code),1)])):u("",!0)]),e("div",an,[e("div",dn,[rn,e("p",null,": "+o(g(n.value.sub_total)),1)]),e("div",un,[cn,e("p",null,": "+o(g(n.value.total_discount)),1)]),n.value.customers.gst_type=="IGST"?(a(),d("div",mn,[_n,e("p",null,": "+o(g(n.value.igst)),1)])):u("",!0),n.value.customers.gst_type=="CGST/SGST"?(a(),d("div",vn,[pn,e("p",null,": "+o(g(n.value.cgst)),1)])):u("",!0),n.value.customers.gst_type=="CGST/SGST"?(a(),d("div",hn,[yn,e("p",null,": "+o(g(n.value.sgst)),1)])):u("",!0),e("div",fn,[gn,e("p",null,": "+o(g(n.value.total_amount)),1)])])]),e("div",xn,[e("div",bn,[wn,e("div",kn,[e("li",null,o(c.terms.term1),1)]),e("div",Cn,[e("li",null,o(c.terms.term2),1)]),e("div",Tn,[e("li",null,o(c.terms.term3),1)])]),e("div",Sn,[In,e("p",null,[e("strong",null,o(n.value.organization.name),1)]),e("img",{class:"h-28",src:w(Q)+n.value.organization.signature,alt:"logo"},null,8,Vn)])])]),e("div",jn,[e("div",zn,[e("div",Mn,[r(h,{for:"customer_id",value:"Page Type :"}),r(E,{options:c.pagetypes,modelValue:R.value,"onUpdate:modelValue":s[16]||(s[16]=t=>R.value=t),onOnchange:Qe},null,8,["options","modelValue"])]),e("div",Nn,[r(L,{onClick:re},{default:_(()=>[C(" Cancel ")]),_:1}),e("div",Un,[r(ae,{class:"ml-3 w-20",onClick:s[17]||(s[17]=t=>Ke(n.value.id,R.value))},{default:_(()=>[C(" Generate Pdf ")]),_:1})])])])])])]),_:1},8,["show","maxWidth"]),r(Y,{show:Z.value,onClose:de,maxWidth:Se.value},{default:_(()=>[e("div",Pn,[e("div",An,[On,B.value.length>0?(a(),d("div",En," Credits Available: ₹"+o(g(Ae.value)),1)):u("",!0)]),e("div",Bn,[e("div",$n,[e("div",Gn,[e("div",Ln,[r(h,{for:"role_id",value:"Invoice No:"}),e("p",Fn,o(y.value.invoice_no),1)])]),e("div",Rn,[e("div",Hn,[r(h,{for:"role_id",value:"Total Amount (₹):"}),e("p",qn,o(g(y.value.invoice_amount)),1)])])]),e("div",Xn,[r(h,{for:"role_id",value:"Payment Through Credit ?"}),e("div",Wn,[r(pt,{modelValue:X.value,"onUpdate:modelValue":s[18]||(s[18]=t=>X.value=t),options:Ze},null,8,["modelValue"])])]),Yn,X.value=="No"?(a(),d("div",Qn,[e("div",Kn,[r(h,{for:"role_id",value:"Payment Type"}),e("div",Zn,[r(E,{options:c.paymentType,modelValue:y.value.payment_type,"onUpdate:modelValue":s[19]||(s[19]=t=>y.value.payment_type=t),onOnchange:Je,class:z({"error rounded-md":w(b).errors["form.payment_type"]})},null,8,["options","modelValue","class"])])]),e("div",Jn,[r(h,{for:"amount",value:"Amount"}),r(P,{id:"amount",type:"text",onChange:s[20]||(s[20]=t=>oe("form.amount")),modelValue:y.value.amount,"onUpdate:modelValue":s[21]||(s[21]=t=>y.value.amount=t),class:z({"error rounded-md":w(b).errors["form.amount"]})},null,8,["modelValue","class"])]),F.value=="Cheque"?(a(),d("div",Dn,[r(h,{for:"check_number",value:"Cheque Number"}),r(P,{id:"check_number",type:"text",modelValue:y.value.check_number,"onUpdate:modelValue":s[22]||(s[22]=t=>y.value.check_number=t),class:z({"error rounded-md":w(b).errors["form.check_number"]})},null,8,["modelValue","class"])])):u("",!0),F.value=="Cheque"?(a(),d("div",ea,[r(h,{for:"bank_name",value:"Bank Name"}),r(P,{id:"bank_name",type:"text",modelValue:y.value.bank_name,"onUpdate:modelValue":s[23]||(s[23]=t=>y.value.bank_name=t),class:z({"error rounded-md":w(b).errors["form.bank_name"]})},null,8,["modelValue","class"])])):u("",!0),e("div",ta,[r(h,{for:"date",value:"Payment Date"}),lt(e("input",{"onUpdate:modelValue":s[24]||(s[24]=t=>y.value.date=t),class:z(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":w(b).errors["form.date"]}]),type:"date",onChange:s[25]||(s[25]=t=>oe("form.date"))},null,34),[[nt,y.value.date]])]),F.value!="Cash"?(a(),d("div",sa,[r(h,{for:"org_bank_id",value:"Our Bank"}),e("div",oa,[r(E,{options:Pe.value,modelValue:y.value.org_bank_id,"onUpdate:modelValue":s[26]||(s[26]=t=>y.value.org_bank_id=t),onOnchange:De,class:z({"error rounded-md":w(b).errors["form.org_bank_id"]})},null,8,["options","modelValue","class"])])])):u("",!0),e("div",la,[r(h,{for:"note",value:"Note"}),r(ct,{id:"note",type:"text",rows:2,modelValue:y.value.note,"onUpdate:modelValue":s[27]||(s[27]=t=>y.value.note=t)},null,8,["modelValue"])])])):(a(),d("div",na,[B.value.length>0?(a(),d("table",aa,[ia,e("tbody",da,[(a(!0),d($,null,W(B.value,(t,f)=>{var k,N,le,ne,me,_e,ve,pe;return a(),d("tr",{key:f},[e("td",ra,o(se(t.date)),1),e("td",ua,[e("div",ca,[e("div",ma,o((N=(k=t.paymentreceive)==null?void 0:k.bank_info)!=null&&N.bank_name?(ne=(le=t.paymentreceive)==null?void 0:le.bank_info)==null?void 0:ne.bank_name:"Cash"),1),e("div",_a,o((_e=(me=t.paymentreceive)==null?void 0:me.bank_info)!=null&&_e.account_number?(pe=(ve=t.paymentreceive)==null?void 0:ve.bank_info)==null?void 0:pe.account_number:""),1)])]),e("td",va,o(g(t.amount)),1),e("td",pa,o(g(t.unused_amount)),1),e("td",ha,[r(P,{id:"amount_to_credit",type:"text",modelValue:t.amount_to_credit,"onUpdate:modelValue":he=>t.amount_to_credit=he,onChange:he=>oe("creditData."+f+".amount_to_credit"),class:z({error:w(b).errors[`creditData.${f}.amount_to_credit`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])])])}),128))])])):u("",!0),e("div",ya,[e("p",fa,"Total Amount To Credit: "+o(g(Ee.value)),1)])]))]),e("div",ga,[r(L,{onClick:de},{default:_(()=>[C(" Cancel ")]),_:1}),e("div",xa,[r(ae,{class:"ml-3 w-20",onClick:et},{default:_(()=>[C(" Save ")]),_:1})])])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},Xa=gt(ba,[["__scopeId","data-v-20664a18"]]);export{Xa as default};
