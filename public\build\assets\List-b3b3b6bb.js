import{_ as j,b as D,a as A}from"./AdminLayout-0f1fdf67.js";import{r as u,h as E,o as i,c as d,a as o,u as T,w as l,F as g,Z as L,b as e,g as _,i as k,e as C,f as M,t as r,O as U}from"./app-b7a94f67.js";import{_ as F}from"./SimpleDropdown-366207fb.js";import{_ as R}from"./Pagination-50283e81.js";import{_ as H}from"./SecondaryButton-c893313c.js";import{D as K}from"./DangerButton-a612a79a.js";import{_ as P}from"./CreateButton-fedd28a2.js";import{_ as v}from"./InputLabel-11b5d690.js";import{_ as Y}from"./SearchableDropdownNew-6e56f54c.js";import{_ as Z}from"./ArrowIcon-dce9e610.js";import{M as q}from"./Modal-e44dcdf0.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const G={class:"animate-top"},J={class:"flex justify-between items-center"},Q=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Debit Notes")],-1),W={class:"flex justify-end"},X={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},ee={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},te=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),se={class:"flex mt-4 sm:mt-0 sm:flex-none"},oe={class:"flex ml-6"},ae={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},le={class:"flex justify-between mb-2"},ne={class:"flex"},ie=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),re={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},de={class:"sm:col-span-4"},ce={class:"relative mt-2"},me={class:"sm:col-span-4"},ue={class:"relative mt-2"},_e={class:"mt-8 overflow-x-auto sm:rounded-lg"},he={class:"shadow sm:rounded-lg"},fe={class:"w-full text-sm text-left rtl:text-right text-gray-500"},pe={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},ge={class:"border-b-2"},ve=["onClick"],be={key:0},ye={class:"px-4 py-2.5 min-w-40"},xe={class:"px-4 py-2.5 min-w-44"},we={class:"px-4 py-2.5 min-w-36"},ke={scope:"row",class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},Ce={class:"px-4 py-2.5 min-w-32"},Me={class:"px-4 py-2.5 min-w-32"},Ne={class:"items-center px-4 py-2.5"},Oe={class:"flex items-center justify-start gap-4"},Be=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),ze=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),Ie=e("span",{class:"text-sm text-gray-700 leading-5"}," View DebitNote ",-1),Ve=["onClick"],$e=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),Se=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),je=[$e,Se],De={key:1},Ae=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Ee=[Ae],Te={class:"p-6"},Le=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this debit note? ",-1),Ue={class:"mt-6 flex justify-end"},tt={__name:"List",props:{data:Object,permissions:Object,organization:Array,companies:Array,organizationId:String,companyId:String},setup(a){const b=a,N=[{field:"credit_note_no",label:"DEBIT NUMBER",sortable:!0},{field:"debit_note_number",label:"CREDIT NOTE NUMBER",sortable:!0},{field:"purchase_invoice.customer_invoice_no",label:"INVOICE NO",sortable:!0},{field:"company.name",label:"COMPANY",sortable:!0},{field:"date",label:"DATE",sortable:!0},{field:"total_amount",label:"AMOUNT (₹)",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],O=u(""),c=u(b.organizationId),m=u(b.companyId),h=u(!1),f=u(null),B=E({}),y=()=>{U.get("/debitnote",{search:O.value,organization_id:c.value,company_id:m.value},{preserveState:!0,replace:!0})},z=s=>{c.value=s,y()},I=s=>{m.value=s,y()},V=s=>{f.value=s,h.value=!0},x=()=>{h.value=!1,f.value=null},$=()=>{B.delete(route("debitnote.destroy",f.value),{onSuccess:()=>{x()}})},S=s=>{const n=new Date(s),t={year:"numeric",month:"short",day:"numeric"};return n.toLocaleDateString("en-US",t)};return(s,n)=>(i(),d(g,null,[o(T(L),{title:"Debit Note"}),o(j,null,{default:l(()=>[e("div",G,[e("div",J,[Q,e("div",W,[e("div",X,[e("div",ee,[te,e("input",{id:"search-field",onInput:n[0]||(n[0]=t=>s.handleSearchChange(t.target.value,c.value,m.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),e("div",se,[e("div",oe,[o(P,{href:s.route("reports")},{default:l(()=>[_(" Back ")]),_:1},8,["href"])])])])]),e("div",ae,[e("div",le,[e("div",ne,[ie,o(v,{for:"customer_id",value:"Filters"})])]),e("div",re,[e("div",de,[o(v,{for:"customer_id",value:"Organization Name"}),e("div",ce,[o(F,{options:a.organization,modelValue:c.value,"onUpdate:modelValue":n[1]||(n[1]=t=>c.value=t),onOnchange:z},null,8,["options","modelValue"])])]),e("div",me,[o(v,{for:"customer_id",value:"Customer Name"}),e("div",ue,[o(Y,{options:a.companies,modelValue:m.value,"onUpdate:modelValue":n[2]||(n[2]=t=>m.value=t),onOnchange:I},null,8,["options","modelValue"])])])])]),e("div",_e,[e("div",he,[e("table",fe,[e("thead",pe,[e("tr",ge,[(i(),d(g,null,k(N,(t,w)=>e("th",{key:w,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:p=>s.sort(t.field,t.sortable)},[_(r(t.label)+" ",1),t.sortable?(i(),C(Z,{key:0,isSorted:s.sortKey===t.field,direction:s.sortDirection},null,8,["isSorted","direction"])):M("",!0)],8,ve)),64))])]),a.data.data&&a.data.data.length>0?(i(),d("tbody",be,[(i(!0),d(g,null,k(a.data.data,(t,w)=>{var p;return i(),d("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",ye,r(t.debit_note_no),1),e("td",xe,r(t.credit_note_number),1),e("td",we,r(((p=t.purchase_invoice)==null?void 0:p.customer_invoice_no)??"-"),1),e("th",ke,r(t.company.name??"-")+" - "+r(t.company.city??"-"),1),e("td",Ce,r(S(t.date)),1),e("td",Me,r(t.total_amount),1),e("td",Ne,[e("div",Oe,[o(D,{align:"right",width:"48"},{trigger:l(()=>[Be]),content:l(()=>[o(A,{href:s.route("debitnote.show",{id:t.id})},{svg:l(()=>[ze]),text:l(()=>[Ie]),_:2},1032,["href"]),e("button",{type:"button",onClick:Fe=>V(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},je,8,Ve)]),_:2},1024)])])])}),128))])):(i(),d("tbody",De,Ee))])])]),a.data.data&&a.data.data.length>0?(i(),C(R,{key:0,class:"mt-6",links:a.data.links},null,8,["links"])):M("",!0)]),o(q,{show:h.value,onClose:s.closeModal},{default:l(()=>[e("div",Te,[Le,e("div",Ue,[o(H,{onClick:x},{default:l(()=>[_(" Cancel ")]),_:1}),o(K,{class:"ml-3",onClick:$},{default:l(()=>[_(" Delete ")]),_:1})])])]),_:1},8,["show","onClose"])]),_:1})],64))}};export{tt as default};
