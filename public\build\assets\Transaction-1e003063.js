import{h as Y,r as x,j as L,m as P,o as r,c as l,a as c,u as k,w as A,F as b,Z as j,b as t,t as i,g as O,f as g,k as F,v as I,n as S,i as q}from"./app-97275a91.js";import{_ as G}from"./AdminLayout-595ad5a7.js";import{_ as X}from"./CreateButton-91ea7c7b.js";import{_ as Z}from"./SimpleDropdown-f072c5ba.js";/* empty css                                                              */import{_ as w}from"./InputLabel-eb73087c.js";const H={class:"animate-top"},J={class:"sm:flex sm:items-center"},K=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Customer Transaction")],-1),Q={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},W={class:"flex items-center space-x-4"},ee={class:"text-lg font-semibold leading-7 text-gray-900"},te={class:"flex justify-end w-20"},se={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},oe={class:"flex justify-between mb-2"},ae={class:"flex"},ne=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),re={class:"inline-flex items-center space-x-4 justify-end w-full"},le={key:0,class:"p-2 flex justify-end text-base font-semibold leading-6 text-gray-900"},ie=["src"],de={class:"grid grid-cols-1 sm:grid-cols-12 gap-x-6 gap-y-5 items-center"},ce={class:"sm:col-span-4"},ue={class:"relative mt-2"},me={class:"sm:col-span-4"},pe={class:"sm:col-span-4"},ge={class:"mt-8 overflow-x-auto sm:rounded-lg"},_e={key:0,class:"shadow sm:rounded-lg"},ye={class:"w-full text-sm text-left rtl:text-right text-gray-500"},he={key:0,class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},fe=t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," DATE "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," NARRATION "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," DEBIT (₹) "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," CREDIT (₹) "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," BALANCE (₹) ")],-1),xe=[fe],ve={key:1},be={class:"px-4 py-2.5 min-w-24"},we={class:"px-4 py-2.5 max-w-[32rem] flex items-center space-x-2 font-medium text-gray-900"},De={class:""},ke={class:"flex flex-col overflow-x-auto overflow-y-hidden whitespace-nowrap"},Se=["href"],Ce={class:"inline-block"},Te={key:0,class:"text-xs text-gray-700"},Ee={key:1,class:"text-xs text-gray-700"},Ne={class:"px-4 py-2.5 min-w-28"},Ae={class:"px-4 py-2.5 min-w-28"},Fe={class:"px-4 py-2.5 min-w-28"},Ie={key:2},Be=t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),$e=[Be],Le={__name:"Transaction",props:["data","customer","organization","organizationId","customerId","creditdata"],setup(h){const _=h,D=Y({}),B=x("customer tranasction"),$=()=>{const s=new Date,o=s.getMonth(),e=s.getFullYear();return`${o<3?e-1:e}-04-01`},M=()=>{const s=new Date,o=s.getFullYear(),e=String(s.getMonth()+1).padStart(2,"0"),n=String(s.getDate()).padStart(2,"0");return`${o}-${e}-${n}`},u=x($()),y=x(M()),f=x(_.organizationId),z=s=>{D.get(route("customers.transaction",{id:_.customerId,organization_id:s}),{preserveState:!0})},U=(s,o)=>{f.value=s,z(f.value)},m=L(()=>{let s=0;_.data.forEach(e=>{const n=new Date(e.date),a=new Date(u.value);n<a&&(e.payment_type==="cr"?s+=parseFloat(e.amount):e.payment_type==="dr"&&(s-=parseFloat(e.amount)))});let o=_.data.filter(e=>{const n=new Date(e.date),a=new Date(u.value),p=new Date(y.value);if(u.value&&y.value)return n>=a&&n<=p;const d=!u.value||n>=a,N=!y.value||n<=p;return d&&N});return o=o.map(e=>{e.payment_type==="cr"?s+=parseFloat(e.amount):e.payment_type==="dr"&&(s-=parseFloat(e.amount));let n=s>=0?"cr":"dr",a=v(Math.abs(s))+" "+n;return{...e,balance:a}}),o}),C=()=>{};P(()=>{});const R=()=>{const s=B.value.replace(/\s+/g,"_"),o={customer_id:_.customerId||"",organization_id:f.value||"",from_date:u.value||"",to_date:y.value||""},n=`/export-customer-transaction?${new URLSearchParams(o).toString()}`;fetch(n,{method:"GET"}).then(a=>{if(!a.ok)throw new Error("Network response was not ok");return a.blob()}).then(a=>{const p=window.URL.createObjectURL(new Blob([a])),d=document.createElement("a");d.href=p,d.setAttribute("download",`${s}.xlsx`),document.body.appendChild(d),d.click(),document.body.removeChild(d)}).catch(a=>{console.error("Error exporting data:",a)})},v=s=>{let o=s.toFixed(2).toString(),[e,n]=o.split("."),a=e.substring(e.length-3),p=e.substring(0,e.length-3);return p!==""&&(a=","+a),`${p.replace(/\B(?=(\d{2})+(?!\d))/g,",")+a}.${n}`},V=s=>{const o=new Date(s),e={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",e)},T=x("");T.value=_.creditdata.reduce((s,o)=>s+o.unused_amount,0);const E=s=>{switch(s){case"invoice":return{label:"SALES",style:"bg-green-100 text-green-800"};case"purchase_invoice":return{label:"PURC.",style:"bg-blue-100 text-blue-800"};case"payment_receive":return{label:"RECEIPT",style:"bg-green-100 text-green-800"};case"payment_paid":return{label:"PAYMENT",style:"bg-blue-100 text-blue-800"};case"credit_note":return{label:"CREDIT",style:"bg-yellow-100 text-yellow-800"};case"debit_note":return{label:"DEBIT",style:"bg-purple-100 text-purple-800"};default:return{label:s.toUpperCase(),style:"bg-gray-100 text-gray-800"}}};return(s,o)=>(r(),l(b,null,[c(k(j),{title:"Customers"}),c(G,null,{default:A(()=>[t("div",H,[t("div",J,[K,t("div",Q,[t("div",W,[t("h1",ee,i(h.customer.customer_name)+" - "+i(h.customer.city),1),t("div",te,[c(X,{href:s.route("customers.index")},{default:A(()=>[O(" Back ")]),_:1},8,["href"])])])])]),t("div",se,[t("div",oe,[t("div",ae,[ne,c(w,{for:"customer_id",value:"Filters"})]),t("div",re,[h.creditdata.length>0?(r(),l("div",le," Credits Available: ₹"+i(v(T.value)),1)):g("",!0),t("button",{onClick:R},[t("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,ie)])])]),t("div",de,[t("div",ce,[c(w,{for:"customer_id",value:"Organization Name"}),t("div",ue,[c(Z,{options:h.organization,modelValue:f.value,"onUpdate:modelValue":o[0]||(o[0]=e=>f.value=e),onOnchange:U},null,8,["options","modelValue"])])]),t("div",me,[c(w,{for:"date",value:"From Date"}),F(t("input",{"onUpdate:modelValue":o[1]||(o[1]=e=>u.value=e),class:S(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":k(D).errors.from_date}]),type:"date",onChange:C},null,34),[[I,u.value]])]),t("div",pe,[c(w,{for:"date",value:"To Date"}),F(t("input",{"onUpdate:modelValue":o[2]||(o[2]=e=>y.value=e),class:S(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":k(D).errors.to_date}]),type:"date",onChange:C},null,34),[[I,y.value]])])])]),t("div",ge,[m.value&&m.value.length>0?(r(),l("div",_e,[t("table",ye,[m.value&&m.value.length>0?(r(),l("thead",he,xe)):g("",!0),m.value&&m.value.length>0?(r(),l("tbody",ve,[(r(!0),l(b,null,q(m.value,(e,n)=>(r(),l("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",be,i(V(e.date)??"-"),1),t("td",we,[t("div",De,[e.entity_type?(r(),l("span",{key:0,class:S(`${E(e.entity_type).style} inline-flex items-center rounded-md px-2 py-1 text-xs font-medium`)},i(E(e.entity_type).label),3)):g("",!0)]),t("div",ke,[e.entity_type=="invoice"?(r(),l("a",{key:0,href:s.route("invoice.view",{id:e.entity_id}),class:"inline-block"},i(e.note??"-"),9,Se)):(r(),l(b,{key:1},[t("span",Ce,i(e.note??"-"),1),e.entity_type==="payment_receive"&&(e!=null&&e.payment_receive)?(r(),l(b,{key:0},[e.payment_receive.tds_amount>0?(r(),l("div",Te," TDS: ₹"+i(e.payment_receive.tds_amount),1)):g("",!0),e.payment_receive.discount_amount>0?(r(),l("div",Ee," Discount: ₹"+i(e.payment_receive.discount_amount),1)):g("",!0)],64)):g("",!0)],64))])]),t("td",Ne,i(e.payment_type=="dr"?v(e.amount):"-"),1),t("td",Ae,i(e.payment_type=="cr"?v(e.amount):"-"),1),t("td",Fe,i(e.balance),1)]))),128))])):(r(),l("tbody",Ie,$e))])])):g("",!0)])])]),_:1})],64))}};export{Le as default};
