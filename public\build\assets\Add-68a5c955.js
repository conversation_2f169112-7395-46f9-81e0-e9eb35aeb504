import{o as l,c as V,a,u as s,w as f,F as b,Z as k,b as n,d as C,e as d,f as i,n as _,g as U,T as w,s as S,x as $}from"./app-16701445.js";import{_ as N,a as A}from"./AdminLayout-e15be38d.js";import{_ as m}from"./InputError-11376965.js";import{_ as r}from"./InputLabel-d69efee6.js";import{P as O}from"./PrimaryButton-eddb8b77.js";import{_ as p}from"./TextInput-764e3400.js";import{_ as T}from"./TextArea-b68da786.js";import{_ as v}from"./SearchableDropdown-c456ce8e.js";import{u as h}from"./index-10107770.js";import{_ as B}from"./_plugin-vue_export-helper-c27b6911.js";const x=u=>(S("data-v-31f1026a"),u=u(),$(),u),I={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},F=x(()=>n("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Customer",-1)),L=["onSubmit"],P={class:"border-b border-gray-900/10 pb-12"},j={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},D={class:"sm:col-span-3"},E={class:"sm:col-span-3"},G={class:"sm:col-span-2"},M={class:"relative mt-2"},Z={class:"sm:col-span-2"},q={class:"relative mt-2"},H={class:"sm:col-span-2"},J={class:"relative mt-2"},K={class:"sm:col-span-2"},Q={class:"sm:col-span-2"},R={class:"sm:col-span-2"},W={class:"sm:col-span-2"},X={class:"sm:col-span-2"},Y={class:"sm:col-span-2"},ee={class:"relative mt-2"},se={key:0,class:"sm:col-span-2"},oe={class:"relative mt-2"},te={class:"sm:col-span-6"},ae={class:"flex mt-6 items-center justify-between"},ne={class:"ml-auto flex items-center justify-end gap-x-6"},le=x(()=>n("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),re={key:0,class:"text-sm text-gray-600"},ie={__name:"Add",props:{types:Array,customer_type:Array,gst_type:Array,is_organization:Array,organization:Array},setup(u){const e=h("post","/customers",{customer_name:"",person_name:"",address:"",city:"",contact_no:"",email:"",drug_licence_no:"",gst_no:"",type:"",customer_type:"",gst_type:"",is_organization:"no",organization_id:""}),z=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()}),g=(y,o,t)=>{switch(y){case"customer_type":e.errors.customer_type=null;break;case"gst_type":e.errors.gst_type=null;break;case"type":e.errors.type=null;break;default:console.warn(`Unhandled field: ${y}`)}e[y]=o};return(y,o)=>(l(),V(b,null,[a(s(k),{title:"Customers"}),a(N,null,{default:f(()=>[n("div",I,[F,n("form",{onSubmit:C(z,["prevent"]),class:""},[n("div",P,[n("div",j,[n("div",D,[a(r,{for:"customer_name",value:"Customer Name"}),a(p,{id:"customer_name",type:"text",modelValue:s(e).customer_name,"onUpdate:modelValue":o[0]||(o[0]=t=>s(e).customer_name=t),onChange:o[1]||(o[1]=t=>s(e).validate("customer_name"))},null,8,["modelValue"]),s(e).invalid("customer_name")?(l(),d(m,{key:0,class:"",message:s(e).errors.customer_name},null,8,["message"])):i("",!0)]),n("div",E,[a(r,{for:"person_name",value:"Contact Person Name"}),a(p,{id:"person_name",type:"text",modelValue:s(e).person_name,"onUpdate:modelValue":o[2]||(o[2]=t=>s(e).person_name=t)},null,8,["modelValue"])]),n("div",G,[a(r,{for:"type",value:"Customer Type"}),n("div",M,[a(v,{options:u.customer_type,modelValue:s(e).customer_type,"onUpdate:modelValue":o[3]||(o[3]=t=>s(e).customer_type=t),onOnchange:o[4]||(o[4]=(t,c)=>g("customer_type",t,c)),class:_({"error rounded-md":s(e).errors.customer_type})},null,8,["options","modelValue","class"])]),s(e).invalid("customer_type")?(l(),d(m,{key:0,class:"",message:s(e).errors.customer_type},null,8,["message"])):i("",!0)]),n("div",Z,[a(r,{for:"type",value:"GST Type"}),n("div",q,[a(v,{options:u.gst_type,modelValue:s(e).gst_type,"onUpdate:modelValue":o[5]||(o[5]=t=>s(e).gst_type=t),onOnchange:o[6]||(o[6]=(t,c)=>g("gst_type",t,c)),class:_({"error rounded-md":s(e).errors.gst_type})},null,8,["options","modelValue","class"])]),s(e).invalid("gst_type")?(l(),d(m,{key:0,class:"",message:s(e).errors.gst_type},null,8,["message"])):i("",!0)]),n("div",H,[a(r,{for:"type",value:"Occupation Type"}),n("div",J,[a(v,{options:u.types,modelValue:s(e).type,"onUpdate:modelValue":o[7]||(o[7]=t=>s(e).type=t),onOnchange:o[8]||(o[8]=(t,c)=>g("type",t,c)),class:_({"error rounded-md":s(e).errors.type})},null,8,["options","modelValue","class"])]),s(e).invalid("type")?(l(),d(m,{key:0,class:"",message:s(e).errors.type},null,8,["message"])):i("",!0)]),n("div",K,[a(r,{for:"email",value:"Email"}),a(p,{id:"email",type:"email",modelValue:s(e).email,"onUpdate:modelValue":o[9]||(o[9]=t=>s(e).email=t)},null,8,["modelValue"]),s(e).invalid("email")?(l(),d(m,{key:0,class:"",message:s(e).errors.email},null,8,["message"])):i("",!0)]),n("div",Q,[a(r,{for:"contact_no",value:"Contact No"}),a(p,{id:"contact_no",type:"text",numeric:!0,maxLength:"10",modelValue:s(e).contact_no,"onUpdate:modelValue":o[10]||(o[10]=t=>s(e).contact_no=t),onChange:o[11]||(o[11]=t=>s(e).validate("contact_no"))},null,8,["modelValue"]),s(e).invalid("contact_no")?(l(),d(m,{key:0,class:"",message:s(e).errors.contact_no},null,8,["message"])):i("",!0)]),n("div",R,[a(r,{for:"drug_licence_no",value:"Drug Licence No"}),a(p,{id:"drug_licence_no",type:"text",modelValue:s(e).drug_licence_no,"onUpdate:modelValue":o[12]||(o[12]=t=>s(e).drug_licence_no=t)},null,8,["modelValue"])]),n("div",W,[a(r,{for:"gst_no",value:"GST No"}),a(p,{id:"gst_no",type:"text",maxLength:"15",modelValue:s(e).gst_no,"onUpdate:modelValue":o[13]||(o[13]=t=>s(e).gst_no=t),onChange:o[14]||(o[14]=t=>s(e).validate("gst_no"))},null,8,["modelValue"]),s(e).invalid("gst_no")?(l(),d(m,{key:0,class:"",message:s(e).errors.gst_no},null,8,["message"])):i("",!0)]),n("div",X,[a(r,{for:"city",value:"City"}),a(p,{id:"city",type:"text",modelValue:s(e).city,"onUpdate:modelValue":o[15]||(o[15]=t=>s(e).city=t),class:_({"error rounded-md":s(e).errors.city})},null,8,["modelValue","class"])]),n("div",Y,[a(r,{for:"type",value:"Is Organization ?"}),n("div",ee,[a(v,{options:u.is_organization,modelValue:s(e).is_organization,"onUpdate:modelValue":o[16]||(o[16]=t=>s(e).is_organization=t),onOnchange:o[17]||(o[17]=(t,c)=>g("is_organization",t,c)),class:_({"error rounded-md":s(e).errors.is_organization})},null,8,["options","modelValue","class"])]),s(e).invalid("is_organization")?(l(),d(m,{key:0,class:"",message:s(e).errors.is_organization},null,8,["message"])):i("",!0)]),s(e).is_organization=="yes"?(l(),V("div",se,[a(r,{for:"type",value:"Organization Name"}),n("div",oe,[a(v,{options:u.organization,modelValue:s(e).organization_id,"onUpdate:modelValue":o[18]||(o[18]=t=>s(e).organization_id=t),onOnchange:o[19]||(o[19]=(t,c)=>g("organization_id",t,c)),class:_({"error rounded-md":s(e).errors.organization_id})},null,8,["options","modelValue","class"])]),s(e).invalid("organization_id")?(l(),d(m,{key:0,class:"",message:s(e).errors.organization_id},null,8,["message"])):i("",!0)])):i("",!0),n("div",te,[a(r,{for:"address",value:"Address"}),a(T,{id:"address",type:"text",modelValue:s(e).address,"onUpdate:modelValue":o[20]||(o[20]=t=>s(e).address=t),rows:4,onChange:o[21]||(o[21]=t=>s(e).validate("address"))},null,8,["modelValue"]),s(e).invalid("address")?(l(),d(m,{key:0,class:"",message:s(e).errors.address},null,8,["message"])):i("",!0)])])]),n("div",ae,[n("div",ne,[a(A,{href:y.route("customers.index")},{svg:f(()=>[le]),_:1},8,["href"]),a(O,{disabled:s(e).processing},{default:f(()=>[U("Save")]),_:1},8,["disabled"]),a(w,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:f(()=>[s(e).recentlySuccessful?(l(),V("p",re,"Saved.")):i("",!0)]),_:1})])])],40,L)])]),_:1})],64))}},Ve=B(ie,[["__scopeId","data-v-31f1026a"]]);export{Ve as default};
