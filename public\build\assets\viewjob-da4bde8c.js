import{r as u,K as C,o as i,c as l,a as n,u as d,w as _,F as h,Z as M,b as t,d as N,g as b,t as s,i as f,f as k,e as D}from"./app-4ea19997.js";import{_ as P}from"./AdminLayout-5eccc000.js";import{_ as U}from"./CreateButton-19955a3e.js";import{_ as B}from"./InputLabel-3a43d7c9.js";import{C as O}from"./CheckboxWithLabel-e4dfd21a.js";import{M as T}from"./Modal-65b3f5d9.js";import{_ as $}from"./FileViewer-2b20675d.js";import{_ as q}from"./SecondaryButton-79105c78.js";/* empty css                                                                          */import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const S={class:"animate-top h-screen"},E={class:"sm:flex sm:items-center"},V=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Jobcard Detail")],-1),A={class:"flex items-center space-x-4"},J=t("div",null,null,-1),I={class:"flex justify-end w-20"},L={class:"mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},F={class:"inline-flex items-start space-x-6 justify-start w-full"},W={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},H={class:"inline-flex items-center justify-start w-full space-x-2"},K=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Hospital Name:",-1),Q={class:"text-sm leading-6 text-gray-700"},R={class:"inline-flex items-center justify-start w-full space-x-2"},Z=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1),z={class:"text-sm leading-6 text-gray-700"},G={class:"inline-flex items-center justify-start w-full space-x-2"},X=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Address:",-1),Y={class:"text-sm leading-6 text-gray-700"},tt={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},et={class:"inline-flex items-center justify-start w-full space-x-2"},st=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Jobcard Number:",-1),ot={class:"text-sm leading-6 text-gray-700"},at={class:"inline-flex items-center justify-start w-full space-x-2"},it=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Date:",-1),lt={class:"text-sm leading-6 text-gray-700"},ct={class:"inline-flex items-center justify-start w-full space-x-2"},nt=t("p",{class:"text-sm font-semibold text-gray-900"},"Engineer Name:",-1),dt={class:"text-sm leading-6 text-gray-700"},rt={class:"inline-flex items-center justify-start w-full space-x-2"},mt=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Job Status:",-1),_t={class:"text-sm leading-6 text-gray-700"},ht={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border overflow-x-auto"},xt={class:"overflow-x-auto divide-y divide-gray-300 w-full"},pt=t("thead",null,[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Equipment"),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Model"),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Serial No")])],-1),ut={class:"divide-y divide-gray-300 bg-white"},ft={class:"whitespace-nowrap pr-4 py-3 text-sm text-gray-900"},yt={class:"whitespace-nowrap px-4 py-3 text-sm text-gray-900"},gt={class:"whitespace-nowrap px-4 py-3 text-sm text-gray-900"},wt={class:"mt-6 space-y-1"},vt={class:"inline-flex items-center justify-start w-full space-x-2"},bt=t("p",{class:"text-sm font-semibold text-gray-900"},"Problem Description:",-1),kt={class:"text-sm leading-6 text-gray-700"},jt={class:"inline-flex items-center justify-start w-full space-x-2"},Ct=t("p",{class:"text-sm font-semibold text-gray-900"},"Parts Required:",-1),Mt={class:"text-sm leading-6 text-gray-700"},Nt={class:"w-1/2"},Dt={class:"grid sm:grid-cols-6 relative"},Pt={class:"inline-flex items-center justify-start w-full space-x-2"},Ut=t("p",{class:"text-sm font-semibold text-gray-900"},"Close Note:",-1),Bt={class:"text-sm leading-6 text-gray-700"},Ot={class:"inline-flex items-center justify-start w-full space-x-2"},Tt=t("p",{class:"text-sm font-semibold text-gray-900"},"Close Date:",-1),$t={class:"text-sm leading-6 text-gray-700"},qt={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-4"},St={key:0,class:"bg-white p-1 shadow sm:rounded-lg border"},Et={class:"min-w-full divide-y divide-gray-300"},Vt=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"QUOTATION DOCUMENT"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1),At={class:"divide-y divide-gray-300 bg-white"},Jt={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},It={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},Lt=["onClick"],Ft=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),Wt=[Ft],Ht={key:1,class:"bg-white p-1 shadow sm:rounded-lg border"},Kt={class:"min-w-full divide-y divide-gray-300"},Qt=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"PO DOCUMENT"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1),Rt={class:"divide-y divide-gray-300 bg-white"},Zt={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},zt={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},Gt=["onClick"],Xt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),Yt=[Xt],te={class:"p-6"},ee={class:"mt-6 px-4 flex justify-end"},he={__name:"viewjob",props:["data","checklist","filePath"],setup(e){const y=u([]),c=C().props.data[0];y.value=c.job_card_checks.map(a=>a.job_card_checklist_id);const g=a=>{const r=new Date(a),m={year:"numeric",month:"short",day:"numeric"};return r.toLocaleDateString("en-US",m)},x=u(!1),p=u(""),w=a=>{p.value=a,x.value=!0},v=()=>{x.value=!1,p.value=""};return(a,r)=>(i(),l(h,null,[n(d(M),{title:"Jobcard"}),n(P,null,{default:_(()=>{var m;return[t("div",S,[t("form",{onSubmit:r[0]||(r[0]=N((...o)=>a.submit&&a.submit(...o),["prevent"])),class:""},[t("div",E,[V,t("div",A,[J,t("div",I,[n(U,{href:a.route("jobcard.index")},{default:_(()=>[b(" Back ")]),_:1},8,["href"])])])]),t("div",L,[t("div",F,[t("div",W,[t("div",H,[K,t("p",Q,s(e.data[0].hospital_name??"-"),1)]),t("div",R,[Z,t("p",z,s(e.data[0].contact_no??"-"),1)]),t("div",G,[X,t("p",Y,s(e.data[0].address??"-"),1)])]),t("div",tt,[t("div",et,[st,t("p",ot,s(e.data[0].job_card_number??"-"),1)]),t("div",at,[it,t("p",lt,s(g(e.data[0].date)??"-"),1)]),t("div",ct,[nt,t("p",dt,s(e.data[0].users.first_name??"-")+" "+s(e.data[0].users.last_name??"-"),1)]),t("div",rt,[mt,t("p",_t,s(e.data[0].job_status??"-"),1)])])])]),t("div",ht,[t("table",xt,[pt,t("tbody",ut,[t("tr",null,[t("td",ft,s(e.data[0].product_name??"-"),1),t("td",yt,s(e.data[0].product_code??"-"),1),t("td",gt,s(e.data[0].serial_no??"-"),1)])])]),t("div",wt,[t("div",vt,[bt,t("p",kt,s(e.data[0].problem_description??"-"),1)]),t("div",jt,[Ct,t("p",Mt,s(e.data[0].parts_required??"-"),1)]),t("div",Nt,[n(B,{for:"engineer_id",value:"Checklist :"}),t("div",Dt,[(i(!0),l(h,null,f(e.checklist,o=>(i(),D(O,{key:o.id,checked:y.value,value:o.id,label:o.type,"onUpdate:checked":a.updateChecked},null,8,["checked","value","label","onUpdate:checked"]))),128))])]),t("div",Pt,[Ut,t("p",Bt,s(e.data[0].close_note??"-"),1)]),t("div",Ot,[Tt,t("p",$t,s((m=e.data[0])!=null&&m.close_date?g(e.data[0].close_date):"-"),1)])]),t("div",qt,[d(c).quotation_documents&&d(c).quotation_documents.length>0?(i(),l("div",St,[t("table",Et,[Vt,t("tbody",At,[(i(!0),l(h,null,f(d(c).quotation_documents,o=>(i(),l("tr",{key:o.id},[t("td",Jt,s(o.orignal_name),1),t("td",It,[t("button",{type:"button",onClick:j=>w(o.name)},Wt,8,Lt)])]))),128))])])])):k("",!0),d(c).po_documents&&d(c).po_documents.length>0?(i(),l("div",Ht,[t("table",Kt,[Qt,t("tbody",Rt,[(i(!0),l(h,null,f(d(c).po_documents,o=>(i(),l("tr",{key:o.id},[t("td",Zt,s(o.orignal_name),1),t("td",zt,[t("button",{type:"button",onClick:j=>w(o.name)},Yt,8,Gt)])]))),128))])])])):k("",!0)])])],32)]),n(T,{show:x.value,onClose:v,maxWidth:"xl"},{default:_(()=>{var o;return[t("div",te,[n($,{fileUrl:((o=e.filePath)==null?void 0:o.view)+p.value},null,8,["fileUrl"]),t("div",ee,[n(q,{onClick:v},{default:_(()=>[b("Close")]),_:1})])])]}),_:1},8,["show"])]}),_:1})],64))}};export{he as default};
