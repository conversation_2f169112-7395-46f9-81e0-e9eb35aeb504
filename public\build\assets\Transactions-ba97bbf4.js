import{h as $,r as g,j,o as p,c as _,a as c,u as b,w as D,F as T,Z as U,b as t,t as i,g as B,k as C,v as N,n as A,f as E,i as M}from"./app-4f4c883b.js";import{_ as R}from"./AdminLayout-d9d2bc31.js";import{_ as P}from"./CreateButton-7995b8ff.js";/* empty css                                                              */import{_ as O}from"./SearchableDropdownNew-a765fe75.js";import{_ as x}from"./InputLabel-468796e0.js";const z={class:"animate-top"},X={class:"sm:flex sm:items-center"},q={class:"sm:flex-auto"},G={class:"text-2xl font-semibold leading-7 text-gray-900"},Z={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},H={class:"flex items-center space-x-4"},J={class:"flex justify-end w-20"},K={class:"flex justify-end w-20"},Q={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},W={class:"flex justify-between mb-2"},Y={class:"flex"},ee=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),te={class:"inline-flex items-center space-x-4 justify-end w-full"},se=["src"],oe={class:"grid grid-cols-1 sm:grid-cols-12 gap-x-6 gap-y-5 items-center"},ae={class:"sm:col-span-4"},ne={class:"relative mt-2"},re={class:"sm:col-span-4"},le={class:"sm:col-span-4"},de={class:"mt-8 overflow-x-auto sm:rounded-lg"},ce={key:0,class:"shadow sm:rounded-lg"},ie={class:"w-full text-sm text-left rtl:text-right text-gray-500"},me={key:0,class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},ue=t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," DATE "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," NARRATION "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," DEBIT (₹) "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," CREDIT (₹) "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," BALANCE (₹) ")],-1),pe=[ue],_e={key:1},he={class:"px-4 py-2.5 min-w-24"},fe={class:"px-4 py-2.5 text-sm flex flex-col font-medium text-gray-900"},ge={class:"tooltiptext text-xs"},xe={class:"px-4 py-2.5 min-w-28"},ye={class:"px-4 py-2.5 min-w-28"},ve={class:"px-4 py-2.5 min-w-36"},be={key:2},we=t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),ke=[we],Ee={__name:"Transactions",props:["data","accountType","bankList","bank_id","accountId"],setup(I){const r=I,y=$({});g(r.organizationId);const f=g(r.bank_id),m=g(""),h=g(""),S=()=>{const o="Account_Type_Transactions_"+r.accountType.name.replace(/\s+/g,"_"),a={account_id:r.accountId,bank_id:f.value||"",from_date:m.value||"",to_date:h.value||""},e=new URLSearchParams(a).toString(),n=`/export-account-type-transactions/${r.accountId}?${e}`;fetch(n,{method:"GET"}).then(s=>{if(!s.ok)throw new Error("Network response was not ok");return s.blob()}).then(s=>{const l=window.URL.createObjectURL(new Blob([s])),d=document.createElement("a");d.href=l,d.setAttribute("download",`${o}.xlsx`),document.body.appendChild(d),d.click(),document.body.removeChild(d)}).catch(s=>{console.error("Error exporting data:",s)})},u=j(()=>{let o=0;r.data.forEach(e=>{const n=new Date(e.date),s=new Date(m.value);n<s&&(e.payment_type==="cr"?o+=parseFloat(e.amount):e.payment_type==="dr"&&(o-=parseFloat(e.amount)))});let a=r.data.filter(e=>{const n=new Date(e.date),s=new Date(m.value),l=new Date(h.value);if(m.value&&h.value)return n>=s&&n<=l;const d=!m.value||n>=s,k=!h.value||n<=l;return d&&k});return a=a.map(e=>{e.payment_type==="cr"?o+=parseFloat(e.amount):e.payment_type==="dr"&&(o-=parseFloat(e.amount));let n=o>=0?"cr":"dr",s=v(Math.abs(o))+" "+n;return{...e,balance:s}}),a}),v=o=>{let a=o.toFixed(2).toString(),[e,n]=a.split("."),s=e.substring(e.length-3),l=e.substring(0,e.length-3);return l!==""&&(s=","+s),`${l.replace(/\B(?=(\d{2})+(?!\d))/g,",")+s}.${n}`},F=o=>{const a=new Date(o),e={year:"numeric",month:"short",day:"numeric"};return a.toLocaleDateString("en-US",e)},w=()=>{},V=o=>{y.get(route("account-type.transactions",{id:r.accountId,bank_id:o}),{preserveState:!0})},L=(o,a)=>{f.value=o,V(f.value)};return(o,a)=>(p(),_(T,null,[c(b(U),{title:"View transactions"}),c(R,null,{default:D(()=>[t("div",z,[t("div",X,[t("div",q,[t("h1",G,i(r.accountType.name),1)]),t("div",Z,[t("div",H,[t("div",J,[t("div",K,[c(P,{href:o.route("account-type.index")},{default:D(()=>[B(" Back ")]),_:1},8,["href"])])])])])]),t("div",Q,[t("div",W,[t("div",Y,[ee,c(x,{for:"customer_id",value:"Filters"})]),t("div",te,[t("button",{onClick:S},[t("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"Export XLS"},null,8,se)])])]),t("div",oe,[t("div",ae,[c(x,{for:"bank_id",value:"Bank Name"}),t("div",ne,[c(O,{options:r.bankList,"option-label":"bank_name","option-value":"id",modelValue:f.value,"onUpdate:modelValue":a[0]||(a[0]=e=>f.value=e),onOnchange:L},null,8,["options","modelValue"])])]),t("div",re,[c(x,{for:"date",value:"From Date"}),C(t("input",{"onUpdate:modelValue":a[1]||(a[1]=e=>m.value=e),class:A(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":b(y).errors.from_date}]),type:"date",onChange:w},null,34),[[N,m.value]])]),t("div",le,[c(x,{for:"date",value:"To Date"}),C(t("input",{"onUpdate:modelValue":a[2]||(a[2]=e=>h.value=e),class:A(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":b(y).errors.to_date}]),type:"date",onChange:w},null,34),[[N,h.value]])])])]),t("div",de,[u.value&&u.value.length>0?(p(),_("div",ce,[t("table",ie,[u.value&&u.value.length>0?(p(),_("thead",me,pe)):E("",!0),u.value&&u.value.length>0?(p(),_("tbody",_e,[(p(!0),_(T,null,M(u.value,(e,n)=>{var s,l;return p(),_("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",he,i(F(e.date)??"-"),1),t("td",fe,[B(i(((s=e.bank)==null?void 0:s.bank_name)??"-")+" - "+i(((l=e.bank)==null?void 0:l.account_number)??"-")+" ",1),t("span",ge,i(e.note??"-"),1)]),t("td",xe,i(e.payment_type=="dr"?v(e.amount):"-"),1),t("td",ye,i(e.payment_type=="cr"?v(e.amount):"-"),1),t("td",ve,i(e.balance),1)])}),128))])):(p(),_("tbody",be,ke))])])):E("",!0)])])]),_:1})],64))}};export{Ee as default};
