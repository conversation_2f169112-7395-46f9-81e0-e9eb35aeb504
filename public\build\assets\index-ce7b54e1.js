import{_ as w,b as v,a as p}from"./AdminLayout-0f1fdf67.js";import{_}from"./CreateButton-fedd28a2.js";import{_ as b}from"./SecondaryButton-c893313c.js";import{D as k}from"./DangerButton-a612a79a.js";import{M}from"./Modal-e44dcdf0.js";import{_ as C}from"./Pagination-50283e81.js";import{r as g,h as B,o as n,c as i,a as s,u as j,w as e,F as x,Z as z,b as t,g as c,i as L,e as $,f as N,t as r}from"./app-b7a94f67.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const S={class:"animate-top"},T={class:"sm:flex sm:items-center"},V=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"SMTP")],-1),E={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},O={class:"flex justify-end w-20"},P={class:"flex justify-end"},A={class:"mt-8",style:{"min-height":"500px","margin-bottom":"80px"}},F={class:"p-1 bg-white shadow ring-1 overflow-x-auto ring-black ring-opacity-5 sm:rounded-lg"},H={class:"min-w-full divide-y divide-gray-300"},U=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"Host"),t("th",{scope:"col",class:"py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"Port"),t("th",{scope:"col",class:"py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"Username "),t("th",{scope:"col",class:"py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"Email"),t("th",{scope:"col",class:"py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"Encryption"),t("th",{scope:"col",class:"px-3 py-3 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1),I={key:0},Z={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900"},q={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},G={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},J={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},K={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},Q={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},R={class:"flex items-center justify-start gap-4"},W=t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),X=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),Y=t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),D=["onClick"],tt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),et=t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),st=[tt,et],ot=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M3 8l7 5 7-5M3 8v8m14-8v8M3 8l7 5 7-5"})],-1),at=t("span",{class:"text-sm text-gray-700 leading-5"}," Send Mail ",-1),lt={key:1},nt=t("tr",{class:"bg-white"},[t("td",{colspan:"8",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),it=[nt],rt=t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),ct={class:"p-6"},dt=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),mt={class:"mt-6 flex justify-end"},Mt={__name:"index",props:{data:{type:Object,default:()=>({data:[]})}},setup(a){const f=l=>{h.value=l,m.value=!0},d=()=>{m.value=!1},u=()=>{y.delete(route("mail-configs.destroy",{id:h.value}),{onSuccess:()=>d()})},m=g(!1),h=g(null),y=B({});return(l,ht)=>(n(),i(x,null,[s(j(z),{title:"SMTP"}),s(w,null,{trigger:e(()=>[rt]),default:e(()=>[t("div",S,[t("div",T,[V,t("div",E,[t("div",O,[s(_,{href:l.route("setting")},{default:e(()=>[c(" Back ")]),_:1},8,["href"])]),t("div",P,[s(_,{href:l.route("mail-configs.create")},{default:e(()=>[c("Create SMTP")]),_:1},8,["href"])])])]),t("div",A,[t("div",F,[t("table",H,[U,a.data.data&&a.data.data.length?(n(),i("tbody",I,[(n(!0),i(x,null,L(a.data.data,(o,pt)=>(n(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:o.id},[t("td",Z,r(o.host),1),t("td",q,r(o.port),1),t("td",G,r(o.username),1),t("td",J,r(o.email),1),t("td",K,r(o.encryption),1),t("td",Q,[t("div",R,[s(v,{align:"right",width:"48"},{trigger:e(()=>[W]),content:e(()=>[s(p,{href:l.route("mail-configs.edit",{id:o.id})},{svg:e(()=>[X]),text:e(()=>[Y]),_:2},1032,["href"]),t("button",{type:"button",onClick:_t=>f(o.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},st,8,D),s(p,{href:l.route("mail-configs.edit",{id:o.id})},{svg:e(()=>[ot]),text:e(()=>[at]),_:2},1032,["href"])]),_:2},1024)])])]))),128))])):(n(),i("tbody",lt,it))])]),a.data.data&&a.data.data.length>0?(n(),$(C,{key:0,class:"mt-6",links:a.data.links},null,8,["links"])):N("",!0)])]),s(M,{show:m.value,onClose:d},{default:e(()=>[t("div",ct,[dt,t("div",mt,[s(b,{onClick:d},{default:e(()=>[c(" Cancel ")]),_:1}),s(k,{class:"ml-3",onClick:u},{default:e(()=>[c(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{Mt as default};
