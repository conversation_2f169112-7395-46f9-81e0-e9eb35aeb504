import{o as n,c as _,a,u as s,w as c,F as f,Z as V,b as l,d as b,e as d,f as i,n as x,g as w,T as k}from"./app-03250c83.js";import{_ as C,a as $}from"./AdminLayout-a6b1643c.js";import{_ as r}from"./InputError-564dc17f.js";import{_ as m}from"./InputLabel-28ecec2a.js";import{P as S}from"./PrimaryButton-e6f8c536.js";import{_ as u}from"./TextInput-374b3fdd.js";import{_ as U}from"./TextArea-0cb791f6.js";import{u as N}from"./index-1ecceea2.js";import{_ as T}from"./SearchableDropdown-517e9849.js";import"./_plugin-vue_export-helper-c27b6911.js";const h={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},A=l("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Company",-1),B=["onSubmit"],F={class:"border-b border-gray-900/10 pb-12"},G={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},L={class:"sm:col-span-3"},j={class:"sm:col-span-3"},E={class:"sm:col-span-2"},P={class:"relative mt-2"},z={class:"sm:col-span-2"},D={class:"sm:col-span-2"},M={class:"sm:col-span-2"},O={class:"sm:col-span-2"},W=l("div",{class:"sm:col-span-2"},null,-1),Z={class:"sm:col-span-2"},q={class:"sm:col-span-2"},H={class:"sm:col-span-4"},I={class:"flex mt-6 items-center justify-between"},J={class:"ml-auto flex items-center justify-end gap-x-6"},K=l("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Q={key:0,class:"text-sm text-gray-600"},ie={__name:"Add",props:{company_type:{type:Array},gst_type:{type:Array}},setup(g){const e=N("post","/companies",{name:"",address:"",city:"",state:"",contact_no:"",email:"",drug_licence_no:"",gst_no:"",website:"",company_type:"Tax",gst_type:""}),y=(p,t)=>{e.gst_type=t,e.errors.gst_type=null},v=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()});return(p,t)=>(n(),_(f,null,[a(s(V),{title:"Company Add"}),a(C,null,{default:c(()=>[l("div",h,[A,l("form",{onSubmit:b(v,["prevent"]),class:""},[l("div",F,[l("div",G,[l("div",L,[a(m,{for:"name",value:"Name"}),a(u,{id:"name",type:"text",modelValue:s(e).name,"onUpdate:modelValue":t[0]||(t[0]=o=>s(e).name=o),onChange:t[1]||(t[1]=o=>s(e).validate("name"))},null,8,["modelValue"]),s(e).invalid("name")?(n(),d(r,{key:0,class:"",message:s(e).errors.name},null,8,["message"])):i("",!0)]),l("div",j,[a(m,{for:"website",value:"Website"}),a(u,{id:"website",type:"text",modelValue:s(e).website,"onUpdate:modelValue":t[2]||(t[2]=o=>s(e).website=o)},null,8,["modelValue"]),s(e).invalid("website")?(n(),d(r,{key:0,class:"",message:s(e).errors.website},null,8,["message"])):i("",!0)]),l("div",E,[a(m,{for:"type",value:"GST Type"}),l("div",P,[a(T,{options:g.gst_type,modelValue:s(e).gst_type,"onUpdate:modelValue":t[3]||(t[3]=o=>s(e).gst_type=o),onOnchange:y,class:x({"error rounded-md":s(e).errors.gst_type})},null,8,["options","modelValue","class"])]),s(e).invalid("gst_type")?(n(),d(r,{key:0,class:"",message:s(e).errors.gst_type},null,8,["message"])):i("",!0)]),l("div",z,[a(m,{for:"gst_no",value:"GST No"}),a(u,{id:"gst_no",type:"text",maxLength:"15",modelValue:s(e).gst_no,"onUpdate:modelValue":t[4]||(t[4]=o=>s(e).gst_no=o),onChange:t[5]||(t[5]=o=>s(e).validate("gst_no"))},null,8,["modelValue"]),s(e).invalid("gst_no")?(n(),d(r,{key:0,class:"",message:s(e).errors.gst_no},null,8,["message"])):i("",!0)]),l("div",D,[a(m,{for:"drug_licence_no",value:"Drug Licence No"}),a(u,{id:"drug_licence_no",type:"text",modelValue:s(e).drug_licence_no,"onUpdate:modelValue":t[6]||(t[6]=o=>s(e).drug_licence_no=o)},null,8,["modelValue"]),s(e).invalid("drug_licence_no")?(n(),d(r,{key:0,class:"",message:s(e).errors.drug_licence_no},null,8,["message"])):i("",!0)]),l("div",M,[a(m,{for:"email",value:"Email"}),a(u,{id:"email",type:"email",modelValue:s(e).email,"onUpdate:modelValue":t[7]||(t[7]=o=>s(e).email=o)},null,8,["modelValue"]),s(e).invalid("email")?(n(),d(r,{key:0,class:"",message:s(e).errors.email},null,8,["message"])):i("",!0)]),l("div",O,[a(m,{for:"contact_no",value:"Contact No"}),a(u,{id:"contact_no",type:"text",numeric:!0,maxLength:"10",modelValue:s(e).contact_no,"onUpdate:modelValue":t[8]||(t[8]=o=>s(e).contact_no=o)},null,8,["modelValue"]),s(e).invalid("contact_no")?(n(),d(r,{key:0,class:"",message:s(e).errors.contact_no},null,8,["message"])):i("",!0)]),W,l("div",Z,[a(m,{for:"city",value:"City"}),a(u,{id:"city",type:"text",modelValue:s(e).city,"onUpdate:modelValue":t[9]||(t[9]=o=>s(e).city=o),onChange:t[10]||(t[10]=o=>s(e).validate("city"))},null,8,["modelValue"]),s(e).invalid("city")?(n(),d(r,{key:0,class:"",message:s(e).errors.city},null,8,["message"])):i("",!0)]),l("div",q,[a(m,{for:"state",value:"State"}),a(u,{id:"state",type:"text",modelValue:s(e).state,"onUpdate:modelValue":t[11]||(t[11]=o=>s(e).state=o),onChange:t[12]||(t[12]=o=>s(e).validate("state"))},null,8,["modelValue"]),s(e).invalid("state")?(n(),d(r,{key:0,class:"",message:s(e).errors.state},null,8,["message"])):i("",!0)]),l("div",H,[a(m,{for:"address",value:"Address"}),a(U,{id:"address",type:"text",rows:4,modelValue:s(e).address,"onUpdate:modelValue":t[13]||(t[13]=o=>s(e).address=o),onChange:t[14]||(t[14]=o=>s(e).validate("address"))},null,8,["modelValue"]),s(e).invalid("address")?(n(),d(r,{key:0,class:"",message:s(e).errors.address},null,8,["message"])):i("",!0)])])]),l("div",I,[l("div",J,[a($,{href:p.route("companies.index")},{svg:c(()=>[K]),_:1},8,["href"]),a(S,{disabled:s(e).processing},{default:c(()=>[w("Save")]),_:1},8,["disabled"]),a(k,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:c(()=>[s(e).recentlySuccessful?(n(),_("p",Q,"Saved.")):i("",!0)]),_:1})])])],40,B)])]),_:1})],64))}};export{ie as default};
