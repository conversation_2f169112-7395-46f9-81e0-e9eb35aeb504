import{r as P,j as k,l as mt,o as _,c as p,a as r,u as i,w as A,F as Y,Z as pt,b as t,t as m,f as g,k as gt,v as vt,d as xt,n as y,e as Z,i as ft,g as J,s as yt,x as ht,q as wt}from"./app-21e66fd5.js";import{_ as bt,a as Vt}from"./AdminLayout-db62264f.js";import{_ as B}from"./InputError-01d93b90.js";import{_ as f}from"./InputLabel-4a50badc.js";import{P as K}from"./PrimaryButton-ed35dcb4.js";import{_ as x}from"./TextInput-625f6add.js";import{_ as St}from"./TextArea-2c14c909.js";import{_ as G}from"./SearchableDropdown-06b090a4.js";import{_ as It}from"./MultipleFileUpload-cc973978.js";import{u as Tt}from"./index-c670349c.js";import{_ as Pt}from"./_plugin-vue_export-helper-c27b6911.js";const c=S=>(yt("data-v-4f449a3b"),S=S(),ht(),S),Ct={class:"animate-top"},Ft={class:"sm:flex sm:items-center"},Gt=c(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create Invoice For Stock Transfer")],-1)),$t={class:"w-auto"},Ut={class:"flex space-x-2 items-center"},kt=c(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Invoice Number:",-1)),Nt={key:0,class:"text-sm font-semibold text-gray-900 leading-6"},Dt={key:1,class:"text-sm font-semibold text-gray-900 leading-6"},At={class:"flex space-x-2 items-center"},Ot=c(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Date :",-1)),zt=["onSubmit"],jt={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},qt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Bt={class:"sm:col-span-4"},Et={class:"relative mt-2"},Rt={class:"sm:col-span-4"},Lt={class:"relative mt-2"},Mt={class:"sm:col-span-4"},Ht={class:"relative mt-2"},Qt={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border w-full"},Xt={class:"overflow-x-auto w-full"},Yt={class:"overflow-x-auto divide-y divide-gray-300",style:{"margin-bottom":"160px"}},Zt=c(()=>t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Product Name",-1)),Jt=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Batch",-1)),Kt=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"HSN",-1)),Wt=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"EXP",-1)),te=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),ee=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),se=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),oe=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),ae=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Sell Price (₹)",-1)),le=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),ne={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ie={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},re={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ce={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},de={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ue=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),_e=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),me=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),pe={class:"divide-y divide-gray-300 bg-white"},ge={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-96"},ve={class:"relative mt-2"},xe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-60"},fe={class:"relative mt-2"},ye={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},he={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},we={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},be={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ve={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Se={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ie={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Te={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Pe={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ce={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Fe={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ge={key:3,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},$e={key:4,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ue={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ke={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ne={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 flex space-x-2 min-w-48"},De={class:"px-3 py-3 text-sm text-gray-900"},Ae=["onClick"],Oe=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),ze=[Oe],je={class:"flex items-center justify-between"},qe={key:0,class:""},Be=c(()=>t("span",{class:"text-lg font-semibold text-gray-900 leading-6"},"PREVIOUS INVOICE",-1)),Ee=[Be],Re={class:"ml-auto flex items-center justify-end gap-x-6"},Le={key:0,class:"flex justify-between"},Me={class:"flex space-x-2 items-center"},He=c(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"PRODUCT:",-1)),Qe={class:"text-sm font-semibold text-gray-700 leading-6"},Xe={class:"flex space-x-2 items-center"},Ye=c(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"HSN Code:",-1)),Ze={class:"text-sm font-semibold text-gray-700 leading-6"},Je={class:"flex space-x-2 items-center"},Ke=c(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"QTY:",-1)),We={class:"text-sm font-semibold text-gray-700 leading-6"},ts={class:"flex space-x-2 items-center"},es=c(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"PRICE (₹):",-1)),ss={class:"text-sm font-semibold text-gray-700 leading-6"},os={class:"flex space-x-2 items-center"},as=c(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"GST (%):",-1)),ls={class:"text-sm font-semibold text-gray-700 leading-6"},ns={class:"flex space-x-2 items-center"},is=c(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"TOTAL AMOUNT (₹):",-1)),rs={class:"text-sm font-semibold text-gray-700 leading-6"},cs=c(()=>t("div",null,null,-1)),ds={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},us={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},_s={class:"sm:col-span-3 space-y-4"},ms={class:"flex space-x-4"},ps={class:"w-full"},gs={class:"w-full"},vs={class:"relative mt-2"},xs={class:"flex space-x-4"},fs={class:"w-full"},ys={class:"w-full"},hs={class:"w-full"},ws={class:"flex space-x-4"},bs={class:"w-full"},Vs={class:"w-full"},Ss={class:"w-full"},Is={class:"flex space-x-4"},Ts={class:"w-full"},Ps={class:"w-full"},Cs={class:"sm:col-span-3"},Fs={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Gs={class:"inline-flex items-center justify-end w-full space-x-3"},$s=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),Us={class:"text-base font-semibold text-gray-900 w-w-32"},ks={class:"inline-flex items-center justify-end w-full space-x-3"},Ns=c(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Discount On Sub Total(₹):",-1)),Ds={class:"w-40"},As={class:"inline-flex items-center justify-end w-full space-x-3"},Os=c(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),zs={class:"w-40"},js={class:"inline-flex items-center justify-end w-full space-x-3"},qs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),Bs={class:"text-base font-semibold text-gray-900 w-w-32"},Es={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},Rs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),Ls={class:"text-base font-semibold text-gray-900 w-w-32"},Ms={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},Hs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),Qs={class:"text-base font-semibold text-gray-900 w-w-32"},Xs={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},Ys=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),Zs={class:"text-base font-semibold text-gray-900 w-w-32"},Js={class:"inline-flex items-center justify-end w-full space-x-3"},Ks=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),Ws={class:"text-base font-semibold text-gray-900 w-w-32"},to={class:"flex mt-6 items-center justify-between"},eo={class:"ml-auto flex items-center justify-end gap-x-6"},so=c(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),oo={__name:"TransferStockAdd",props:["customers","invoice_no","retail_invoice_no","serialno","products","organization","category","salesuser","invoice_details"],setup(S){const V=S,O=P(),z=P(),d=P([{serial_number_id:"",product_id:"",product_name:"",item_code:"",hsn_code:"",price:"",sell_price:"",discount:"",discount_amount:"",discount_before_tax_product:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",qty:"",description:"",expiry_date:"",mrp:""}]),w=P({product_name:"",hsn_code:"",qty:"",price:"",gst:"",total_amount:""}),v=P(null),$=P(null),W=(n,a)=>{s.customer_id=n,s.errors.customer_id=null;const e=V.customers.find(o=>o.id===n);e&&(v.value=e.gst_type,$.value=e.customer_type)},tt=P([]),et=async(n,a,e)=>{s.errors[`selectedProductItem.${e}.product_id`]=null;const o=V.serialno.filter(l=>l.product_id===n&&l.organization_id===s.organization_id);tt.value=o,d.value[e].product_id=n;try{const l=await wt.post("/api/invoices/previous",{customer_id:s.customer_id,organization_id:s.organization_id,product_id:n});l.data.success?w.value=l.data.data:w.value={product_name:"",hsn_code:"",qty:"",price:"",gst:"",total_amount:""}}catch(l){console.error("Error fetching previous invoice:",l),w.value={product_name:"",hsn_code:"",qty:"",price:"",gst:"",total_amount:""}}},st=(n,a,e)=>{const o=V.serialno.find(l=>l.id===n);o&&(d.value[e].qty="",d.value[e].product_id=o.product.id,d.value[e].serial_number_id=o.id,d.value[e].item_code=o.product.item_code,d.value[e].expiry_date=o.expiry_date,d.value[e].mrp=o.mrp?parseFloat(o.mrp).toFixed(2):"-",d.value[e].product_name=o.product.name,d.value[e].hsn_code=o.product.hsn_code,d.value[e].discount="0.00",d.value[e].price=parseFloat(o.purchase_price).toFixed(2),d.value[e].total_price=parseFloat(o.purchase_price).toFixed(2),d.value[e].gst=parseFloat(o.product.gst).toFixed(2),d.value[e].sgst=parseFloat(o.product.gst/2).toFixed(2),d.value[e].gst_amount="",d.value[e].total_gst_amount="",d.value[e].total_amount="",d.value[e].description="",s.errors[`selectedProductItem.${e}.serial_number_id`]=null)},ot=(n,a)=>{const e=parseFloat(n.sell_price),o=parseFloat(n.discount_before_tax_product)||0,l=parseFloat(n.discount)||0,u=v.value=="IGST"?n.gst:parseFloat(n.sgst*2),b=parseFloat(n.qty);let h=0,D=0;l>0||o>0?h=e*b:h=e*b*(1+u/100);const U=h*(l/100)||0,Q=e*1*(u/100),q=(e*b-U-o)*(u/100);l>0||o>0?D=h-U-o+q:D=h-U;const X=e*b;return n.total_price=isNaN(X)?"":parseFloat(X).toFixed(2),n.gst_amount=isNaN(Q)?"":parseFloat(Q).toFixed(2),n.total_gst_amount=isNaN(q)?"":parseFloat(q).toFixed(2),n.discount_amount=isNaN(U)?"":parseFloat(U).toFixed(2),n.gst=u,isNaN(D)?"":parseFloat(D).toFixed(2)},T=(n,a)=>{H(),n.total_amount=ot(n)},E=k(()=>{const n=Math.round(d.value.reduce((e,o)=>e+(o.total_amount?parseFloat(o.total_amount):0),0)),a=s.overall_discount?parseFloat(s.overall_discount):0;return n-a}),C=k(()=>d.value.reduce((n,a)=>n+(a.total_gst_amount?parseFloat(a.total_gst_amount):0),0)),R=k(()=>d.value.reduce((n,a)=>n+(a.total_price?parseFloat(a.total_price):0),0)),L=k(()=>{const n=d.value.reduce((o,l)=>o+(l.discount_amount?parseFloat(l.discount_amount):0),0),a=s.overall_discount?parseFloat(s.overall_discount):0,e=s.discount_before_tax?parseFloat(s.discount_before_tax):0;return n+a+e}),s=Tt("post","/invoice",{stock_transfer:"yes",date:new Date().toISOString().slice(0,10),note:"",sales_user_id:"",invoice_type:"",selectedProductItem:[],customer_id:"",category:"",invoice_no:"",document:"",cgst:"",sgst:"",igst:"",total_gst:"",sub_total:"",total_amount:"",total_discount:"",organization_id:"",dispatch:"",transport:"",patient_name:"",customer_po_date:"",customer_po_number:"",eway_bill:"",due_days:"",cr_dr_note:"",overall_discount:"",discount_before_tax:""}),M=()=>{d.value=[{serial_number_id:"",product_id:"",product_name:"",item_code:"",hsn_code:"",price:"",sell_price:"",discount:"",discount_amount:"",discount_before_tax_product:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",qty:"",description:"",expiry_date:"",mrp:""}]},j=P([]),at=(n,a)=>{M(),V.products.filter(o=>o.category===n);const e=V.products.filter(o=>o.sales_products.some(u=>u.organization_id===s.organization_id));j.value=e,s.category=n,s.errors.category=null},lt=(n,a)=>{s.sales_user_id=n,s.errors.sales_user_id=null},nt=(n,a)=>{M(),V.products.filter(o=>o.category===s.category);const e=V.products.filter(o=>o.sales_products.some(u=>u.organization_id===n));j.value=e,s.organization_id=n,s.errors.organization_id=null,O.value=V.invoice_no[n],z.value=V.retail_invoice_no[n]},it=()=>{s.sub_total=R.value,s.cgst=v.value=="CGST/SGST"?C.value/2:"0",s.sgst=v.value=="CGST/SGST"?C.value/2:"0",s.igst=v.value=="IGST"?C.value:"0",s.total_gst=C.value,s.total_amount=E.value,s.total_discount=L.value,s.invoice_no=$.value=="Tax"?O.value:z.value,s.invoice_type=$.value,s.selectedProductItem=d.value,s.submit({preserveScroll:!0,onSuccess:()=>s.reset()})},rt=()=>{d.value.push({serial_number_id:"",product_name:"",hsn_code:""})},ct=n=>{d.value.splice(n,1)},F=n=>{s.errors[n]=null};k(()=>{const n=new Date,a={year:"numeric",month:"long",day:"numeric"};return n.toLocaleDateString("en-US",a)});const dt=n=>{s.document=n},I=n=>{if(n==null||isNaN(n))return"0.00";let a=Number(n).toFixed(2),[e,o]=a.split("."),l=e.substring(e.length-3),u=e.substring(0,e.length-3);return u!==""&&(l=","+l),`${u.replace(/\B(?=(\d{2})+(?!\d))/g,",")+l}.${o}`},ut=(n,a,e)=>V.serialno.filter(l=>l.product_id===n&&l.organization_id===s.organization_id),_t=n=>n,N=(n,a)=>{const e=a.length,o=a.reduce((b,h)=>b+(h.total_price?parseFloat(h.total_price):0),0),l=d.value.reduce((b,h)=>b+(h.total_price?parseFloat(h.total_price):0),0),u=n*o/l/e;a.forEach(b=>{b.discount_before_tax_product=u})},H=()=>{const n=parseFloat(s.discount_before_tax)||0,a=d.value.filter(u=>u.gst==5&&u.total_price>0),e=d.value.filter(u=>u.gst==12&&u.total_price>0),o=d.value.filter(u=>u.gst==18&&u.total_price>0),l=d.value.filter(u=>u.gst==28&&u.total_price>0);N(n,a),N(n,e),N(n,o),N(n,l)};return mt(()=>s.discount_before_tax,n=>{H(),d.value.forEach(a=>{T(a)})}),(n,a)=>(_(),p(Y,null,[r(i(pt),{title:"Invoice"}),r(bt,null,{default:A(()=>[t("div",Ct,[t("div",Ft,[Gt,t("div",$t,[t("div",Ut,[kt,$.value=="Retail"?(_(),p("span",Nt,m(z.value),1)):g("",!0),$.value=="Tax"?(_(),p("span",Dt,m(O.value),1)):g("",!0)]),t("div",At,[Ot,gt(t("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":a[0]||(a[0]=e=>i(s).date=e),onChange:a[1]||(a[1]=e=>i(s).validate("date"))},null,544),[[vt,i(s).date]])])])]),t("form",{onSubmit:xt(it,["prevent"]),class:""},[t("div",jt,[t("div",qt,[t("div",Bt,[r(f,{for:"company_name",value:"From Organization"}),t("div",Et,[r(G,{options:S.organization,modelValue:i(s).organization_id,"onUpdate:modelValue":a[2]||(a[2]=e=>i(s).organization_id=e),onOnchange:nt,class:y({"error rounded-md":i(s).errors.organization_id})},null,8,["options","modelValue","class"])])]),t("div",Rt,[r(f,{for:"customer_id",value:"To Organization"}),t("div",Lt,[r(G,{options:S.customers,modelValue:i(s).customer_id,"onUpdate:modelValue":a[3]||(a[3]=e=>i(s).customer_id=e),onOnchange:W,class:y({"error rounded-md":i(s).errors.customer_id})},null,8,["options","modelValue","class"]),i(s).invalid("customer_id")?(_(),Z(B,{key:0,class:"",message:i(s).errors.customer_id},null,8,["message"])):g("",!0)])]),t("div",Mt,[r(f,{for:"company_name",value:"Category"}),t("div",Ht,[r(G,{options:S.category,modelValue:i(s).category,"onUpdate:modelValue":a[4]||(a[4]=e=>i(s).category=e),onOnchange:at,class:y({"error rounded-md":i(s).errors.category})},null,8,["options","modelValue","class"])])])])]),t("div",Qt,[t("div",Xt,[t("table",Yt,[t("thead",null,[t("tr",null,[Zt,Jt,Kt,Wt,te,ee,se,oe,ae,le,v.value=="IGST"?(_(),p("th",ne,"IGST (%)")):g("",!0),v.value=="IGST"?(_(),p("th",ie,"IGST (₹)")):g("",!0),v.value=="CGST/SGST"?(_(),p("th",re,"CGST (%)")):g("",!0),v.value=="CGST/SGST"?(_(),p("th",ce,"SGST (%)")):g("",!0),v.value=="CGST/SGST"?(_(),p("th",de,"Total GST (₹)")):g("",!0),ue,_e,me])]),t("tbody",pe,[(_(!0),p(Y,null,ft(d.value,(e,o)=>(_(),p("tr",{key:o},[t("td",ge,[t("div",ve,[r(G,{options:_t(j.value),modelValue:e.product_id,"onUpdate:modelValue":l=>e.product_id=l,onOnchange:(l,u)=>et(l,u,o),onChange:a[5]||(a[5]=l=>i(s).validate("product_id")),class:y({"error rounded-md":i(s).errors[`selectedProductItem.${o}.product_id`]})},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class"])])]),t("td",xe,[t("div",fe,[r(G,{options:ut(e.product_id),modelValue:e.serial_number_id,"onUpdate:modelValue":l=>e.serial_number_id=l,onOnchange:(l,u)=>st(l,u,o),onChange:a[6]||(a[6]=l=>i(s).validate("serial_number_id")),class:y({"error rounded-md":i(s).errors[`selectedProductItem.${o}.serial_number_id`]})},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class"])])]),t("td",ye,m(e.hsn_code),1),t("td",he,m(e.expiry_date??"-"),1),t("td",we,[r(x,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":l=>e.description=l,onInput:l=>T(e,o),onChange:l=>F("selectedProductItem."+o+".description"),class:y({error:i(s).errors[`selectedProductItem.${o}.description`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",be,m(e.mrp??"-"),1),t("td",Ve,m(e.price),1),t("td",Se,[r(x,{id:"qty",type:"text",modelValue:e.qty,"onUpdate:modelValue":l=>e.qty=l,numeric:!0,onInput:l=>T(e,o),onChange:l=>F("selectedProductItem."+o+".qty"),class:y({error:i(s).errors[`selectedProductItem.${o}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"]),r(B,{message:i(s).errors[`selectedProductItem.${o}.qty`]},null,8,["message"])]),t("td",Ie,[r(x,{id:"sell_price",type:"text",modelValue:e.sell_price,"onUpdate:modelValue":l=>e.sell_price=l,onInput:l=>T(e,o),onChange:l=>F("selectedProductItem."+o+".sell_price"),class:y({error:i(s).errors[`selectedProductItem.${o}.sell_price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Te,m(e.total_price),1),v.value=="IGST"?(_(),p("td",Pe,[r(x,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":l=>e.gst=l,onInput:l=>T(e,o),onChange:l=>F("selectedProductItem."+o+".gst"),class:y({error:i(s).errors[`selectedProductItem.${o}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):g("",!0),v.value=="CGST/SGST"?(_(),p("td",Ce,[r(x,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":l=>e.sgst=l,onInput:l=>T(e,o),onChange:l=>F("selectedProductItem."+o+".gst"),class:y({error:i(s).errors[`selectedProductItem.${o}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):g("",!0),v.value=="CGST/SGST"?(_(),p("td",Fe,[r(x,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":l=>e.sgst=l,onInput:l=>T(e,o),onChange:l=>F("selectedProductItem."+o+".gst"),class:y({error:i(s).errors[`selectedProductItem.${o}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):g("",!0),v.value=="IGST"?(_(),p("td",Ge,m(e.total_gst_amount),1)):g("",!0),v.value=="CGST/SGST"?(_(),p("td",$e,m(e.total_gst_amount),1)):g("",!0),t("td",Ue,[r(x,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":l=>e.discount=l,onInput:l=>T(e,o),onChange:l=>F("selectedProductItem."+o+".discount"),class:y({error:i(s).errors[`selectedProductItem.${o}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",ke,m(e.discount_amount),1),t("td",Ne,[t("div",De,m(e.total_amount),1),o!=0?(_(),p("button",{key:0,type:"button",class:"mt-1",onClick:l=>ct(o)},ze,8,Ae)):g("",!0)])]))),128))])])]),t("div",je,[w.value.product_name?(_(),p("div",qe,Ee)):g("",!0),t("div",Re,[r(K,{onClick:rt,type:"button"},{default:A(()=>[J("Add Product")]),_:1})])]),w.value.product_name?(_(),p("div",Le,[t("div",null,[t("div",Me,[He,t("span",Qe,m(w.value.product_name),1)]),t("div",Xe,[Ye,t("span",Ze,m(w.value.hsn_code),1)]),t("div",Je,[Ke,t("span",We,m(w.value.qty),1)])]),t("div",null,[t("div",ts,[es,t("span",ss,m(I(w.value.price)),1)]),t("div",os,[as,t("span",ls,m(I(w.value.gst)),1)]),t("div",ns,[is,t("span",rs,m(I(w.value.total_amount)),1)])]),cs])):g("",!0)]),t("div",ds,[t("div",us,[t("div",_s,[t("div",ms,[t("div",ps,[r(f,{for:"note",value:"Upload Documents"}),r(It,{inputId:"document",inputName:"document",onFiles:dt})]),t("div",gs,[r(f,{for:"sales_user_id",value:"Sales Person"}),t("div",vs,[r(G,{options:S.salesuser,modelValue:i(s).sales_user_id,"onUpdate:modelValue":a[7]||(a[7]=e=>i(s).sales_user_id=e),onOnchange:lt,class:y({"error rounded-md":i(s).errors.sales_user_id})},null,8,["options","modelValue","class"])])])]),t("div",xs,[t("div",fs,[r(f,{for:"company_name",value:"Transport"}),r(x,{id:"gst",type:"text",modelValue:i(s).dispatch,"onUpdate:modelValue":a[8]||(a[8]=e=>i(s).dispatch=e)},null,8,["modelValue"])]),t("div",ys,[r(f,{for:"company_name",value:"Dispatch"}),r(x,{id:"transport",type:"text",modelValue:i(s).transport,"onUpdate:modelValue":a[9]||(a[9]=e=>i(s).transport=e)},null,8,["modelValue"])]),t("div",hs,[r(f,{for:"eway_bill",value:"Eway Bill"}),r(x,{id:"eway_bill",type:"text",modelValue:i(s).eway_bill,"onUpdate:modelValue":a[10]||(a[10]=e=>i(s).eway_bill=e)},null,8,["modelValue"])])]),t("div",ws,[t("div",bs,[r(f,{for:"company_name",value:"PO Number"}),r(x,{id:"gst",type:"text",modelValue:i(s).customer_po_number,"onUpdate:modelValue":a[11]||(a[11]=e=>i(s).customer_po_number=e)},null,8,["modelValue"])]),t("div",Vs,[r(f,{for:"company_name",value:"PO Date"}),r(x,{id:"customer_po_date",type:"date",modelValue:i(s).customer_po_date,"onUpdate:modelValue":a[12]||(a[12]=e=>i(s).customer_po_date=e)},null,8,["modelValue"])]),t("div",Ss,[r(f,{for:"due_days",value:"Due Days"}),r(x,{id:"due_days",type:"text",modelValue:i(s).due_days,"onUpdate:modelValue":a[13]||(a[13]=e=>i(s).due_days=e)},null,8,["modelValue"])])]),t("div",Is,[t("div",Ts,[r(f,{for:"patient_name",value:"Patient Name"}),r(x,{id:"patient_name",type:"text",modelValue:i(s).patient_name,"onUpdate:modelValue":a[14]||(a[14]=e=>i(s).patient_name=e)},null,8,["modelValue"])]),t("div",Ps,[r(f,{for:"cr_dr_note",value:"CR DR Note"}),r(x,{id:"cr_dr_note",type:"text",modelValue:i(s).cr_dr_note,"onUpdate:modelValue":a[15]||(a[15]=e=>i(s).cr_dr_note=e)},null,8,["modelValue"])])]),t("div",null,[r(f,{for:"note",value:"Note"}),r(St,{id:"note",type:"text",modelValue:i(s).note,"onUpdate:modelValue":a[16]||(a[16]=e=>i(s).note=e),onChange:a[17]||(a[17]=e=>i(s).validate("note"))},null,8,["modelValue"]),i(s).invalid("note")?(_(),Z(B,{key:0,class:"",message:i(s).errors.note},null,8,["message"])):g("",!0)])]),t("div",Cs,[t("div",Fs,[t("div",Gs,[$s,t("p",Us,m(I(R.value)),1)]),t("div",ks,[Ns,t("div",Ds,[r(x,{id:"discount_before_tax",type:"text",modelValue:i(s).discount_before_tax,"onUpdate:modelValue":a[18]||(a[18]=e=>i(s).discount_before_tax=e)},null,8,["modelValue"])])]),t("div",As,[Os,t("div",zs,[r(x,{id:"overall_discount",type:"text",modelValue:i(s).overall_discount,"onUpdate:modelValue":a[19]||(a[19]=e=>i(s).overall_discount=e)},null,8,["modelValue"])])]),t("div",js,[qs,t("p",Bs,m(I(L.value)),1)]),v.value=="IGST"?(_(),p("div",Es,[Rs,t("p",Ls,m(I(C.value)),1)])):g("",!0),v.value=="CGST/SGST"?(_(),p("div",Ms,[Hs,t("p",Qs,m(I(C.value/2)),1)])):g("",!0),v.value=="CGST/SGST"?(_(),p("div",Xs,[Ys,t("p",Zs,m(I(C.value/2)),1)])):g("",!0),t("div",Js,[Ks,t("p",Ws,m(I(E.value)),1)])])])])]),t("div",to,[t("div",eo,[r(Vt,{href:n.route("invoice.index")},{svg:A(()=>[so]),_:1},8,["href"]),r(K,{disabled:i(s).processing},{default:A(()=>[J("Submit")]),_:1},8,["disabled"])])])],40,zt)])]),_:1})],64))}},vo=Pt(oo,[["__scopeId","data-v-4f449a3b"]]);export{vo as default};
