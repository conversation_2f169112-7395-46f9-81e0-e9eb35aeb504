import{_ as pe,b as fe,a as z}from"./AdminLayout-0f1fdf67.js";import{_ as ge}from"./CreateButton-fedd28a2.js";import{_ as x}from"./InputLabel-11b5d690.js";import{_ as F}from"./SearchableDropdown-711fb977.js";import{_ as L}from"./SecondaryButton-c893313c.js";import{P as G}from"./PrimaryButton-4ffecd1c.js";import{D as ve}from"./DangerButton-a612a79a.js";import{M as I}from"./Modal-e44dcdf0.js";import{_ as be}from"./Pagination-50283e81.js";import{_ as xe}from"./TextArea-500c5ac8.js";import{C as ye}from"./CheckboxWithLabel-cd28292b.js";import{_ as we}from"./SearchableDropdownNew-6e56f54c.js";import{_ as ke}from"./SimpleDropdown-366207fb.js";import"./html2canvas.esm-18903d57.js";import{r as u,l as Ce,o as r,c as _,a as i,u as y,w as d,F as E,Z as je,b as e,g,f,i as U,e as C,t as a,n as w,s as Se,x as Me}from"./app-b7a94f67.js";import{_ as Ee}from"./ArrowIcon-dce9e610.js";import{s as Ve}from"./sortAndSearch-77279369.js";import{_ as Je}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              *//* empty css                                                                          */const o=n=>(Se("data-v-fb5e07d9"),n=n(),Me(),n),Ne={class:"animate-top"},Oe={class:"sm:flex sm:items-center"},Be=o(()=>e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Jobcard")],-1)),$e={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},Le={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},Ie={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},Ue=o(()=>e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),Ae={key:0,class:"flex justify-end"},Te={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},Pe={class:"flex justify-between mb-2"},We={class:"flex"},Re=o(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),ze=["src"],Fe={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Ge={class:"sm:col-span-4"},He={class:"relative mt-2"},qe={key:0,class:"sm:col-span-4"},Ke={class:"relative mt-2"},Xe={class:"mt-8 overflow-x-auto sm:rounded-lg"},Ye={class:"shadow sm:rounded-lg"},Ze={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Qe={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},De={class:"border-b-2"},et=["onClick"],tt={key:0},st={class:"px-4 py-2.5 min-w-40"},ot={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap truncate"},lt={scope:"row",class:"px-4 py-2.5 whitespace-normal min-w-52 truncate"},at={class:"px-4 py-2.5 min-w-48"},nt={class:"px-4 py-2.5 min-w-52"},it={class:"px-4 py-2.5 min-w-32"},rt={class:"px-4 py-2.5 min-w-32"},dt={class:"px-4 py-2.5"},ct={class:"flex justify-center item-center w-full"},ut={class:"flex flex-1 items-center px-4 py-2.5"},_t={class:"items-center px-4 py-2.5"},mt={class:"flex items-center justify-start gap-4"},ht=o(()=>e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)),pt=o(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),ft=o(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),gt=["onClick"],vt=o(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),bt=o(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)),xt=[vt,bt],yt=o(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),wt=o(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," View Jobcard ",-1)),kt=["onClick"],Ct=o(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M6 2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm2 6h8m-4 4h4"})],-1)),jt=o(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Generate PDF ",-1)),St=[Ct,jt],Mt=["onClick"],Et=o(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M6 18L18 6M6 6l12 12"})],-1)),Vt=o(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Close Jobcard ",-1)),Jt=[Et,Vt],Nt={key:1},Ot=o(()=>e("tr",{class:"bg-white"},[e("td",{colspan:"12",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),Bt=[Ot],$t={class:"p-6"},Lt=o(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),It={class:"mt-6 flex justify-end"},Ut={class:"p-6"},At={class:"container1 p-2",id:"pdf-content"},Tt=o(()=>e("div",{class:"header",style:{"align-items":"start","justify-content":"center","text-align":"center"}},[e("div",{style:{"align-items":"center","justify-content":"space-between","margin-bottom":"10px"}},[e("p",{style:{"font-size":"20px"}},[e("strong",null,"Maintenance Workshop")])])],-1)),Pt={style:{display:"flex","justify-content":"space-between"}},Wt={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},Rt=o(()=>e("p",{style:{"margin-bottom":"4px"}},[e("strong")],-1)),zt={style:{display:"flex"}},Ft=o(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Hospital Name")],-1)),Gt={style:{display:"flex"}},Ht=o(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Address")],-1)),qt={style:{display:"flex"}},Kt=o(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Contact Number")],-1)),Xt={class:"invoice-details",style:{"margin-bottom":"20px","justify-items":"start",width:"320px"}},Yt={style:{display:"flex"}},Zt=o(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Job Number")],-1)),Qt={style:{display:"flex"}},Dt=o(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Job Date")],-1)),es={style:{display:"flex"}},ts=o(()=>e("p",{style:{width:"100px"}},[e("strong",null,"Engineer")],-1)),ss=o(()=>e("thead",null,[e("tr",null,[e("th",null,"Equipment Name"),e("th",null,"Model"),e("th",null,"Serial No")])],-1)),os={class:""},ls={class:""},as={style:{display:"flex"}},ns=o(()=>e("p",null,[e("strong",null,"Problem Description")],-1)),is={class:"mt-2"},rs={style:{display:"flex"}},ds=o(()=>e("p",null,[e("strong",null,"Accessories")],-1)),cs={class:"sm:col-span-6 mt-2"},us=o(()=>e("p",{style:{width:"200px"}},[e("strong",null,"Checklist")],-1)),_s={class:"grid sm:grid-cols-6 relative",style:{"margin-bottom":"20px",width:"500px"}},ms={style:{display:"flex","justify-content":"space-between","margin-top":"20px"}},hs={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"400"}},ps={style:{display:"flex"}},fs=o(()=>e("p",null,[e("strong",null,"Close Note")],-1)),gs={class:"",style:{"margin-bottom":"20px","justify-items":"end",width:"260px"}},vs={style:{display:"flex"}},bs=o(()=>e("p",null,[e("strong",null,"Close Date")],-1)),xs={class:"mt-6 px-4 flex justify-end"},ys={class:"flex flex-col justify-end space-y-6"},ws={class:"flex items-center space-x-2"},ks={class:"flex justify-end"},Cs={class:"w-36"},js={class:"p-6"},Ss=o(()=>e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Close Jobcard",-1)),Ms={class:"border-b border-gray-900/10 pb-12"},Es={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Vs={class:"sm:col-span-6"},Js={class:"relative mt-2"},Ns={class:"sm:col-span-6"},Os={class:"mt-6 px-4 flex justify-end"},Bs={class:"w-36"},$s={__name:"List",props:["data","permissions","checklist","jobStatus","statusId","jobFilterStatus","pagetypes","notifications","serviceEngineer","engineerId"],setup(n){const V=n,{form:v,search:Ls,sort:H,fetchData:Is,sortKey:q,sortDirection:K,updateParams:X}=Ve("jobcard.index",{status:V.statusId}),J=u(!1),A=u(null),N=u(!1),Y=u("custom"),c=u([]),T=u([]),j=u(!1),Z=u("custom2"),h=u("Open"),b=u(V.engineerId),O=u("");Ce([h],()=>{X({status:h.value})});const Q=[{field:"job_card_number",label:"JOB NUMBER",sortable:!0},{field:"hospital_name",label:"HOSPITAL NAME",sortable:!0},{field:"product_name",label:"PRODUCT",sortable:!0,multiFieldSort:["product_name","product_code"]},{field:"serial_no",label:"SERIAL NUMBER",sortable:!0},{field:"users.first_name",label:"ENGINEER",sortable:!0},{field:"date",label:"DATE",sortable:!0},{field:"time_ago",label:"TIME",sortable:!0},{field:"warranty_status",label:"WARRANTY STATUS",sortable:!0},{field:"status",label:"STATUS",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],D=s=>{A.value=s,J.value=!0},B=()=>{J.value=!1},ee=()=>{v.delete(route("jobcard.destroy",{id:A.value}),{onSuccess:()=>B()})},te=s=>{const l=V.data.data.find(t=>t.id===s);c.value=l,T.value=l.job_card_checks.map(t=>t.job_card_checklist_id),N.value=!0},P=()=>{N.value=!1},p={job_status:"",close_note:"",id:""},se=s=>{p.job_status="",p.close_note="",j.value=!0,p.id=s},W=()=>{j.value=!1},oe=()=>{v.post(route("jobcard.close",{data:p}),{onSuccess:()=>{v.reset(),j.value=!1},onError:s=>{}})},le=(s,l)=>{h.value=s,$(O.value,h.value,b.value)},ae=(s,l)=>{b.value=s,$(O.value,h.value,b.value)},ne=()=>{const s="Jobcard_Report_"+new Date().toISOString().split("T")[0],l={status:h.value},M=`/export-jobcard-report?${new URLSearchParams(l).toString()}`;fetch(M,{method:"GET"}).then(m=>{if(!m.ok)throw new Error("Network response was not ok");return m.blob()}).then(m=>{const he=window.URL.createObjectURL(new Blob([m])),k=document.createElement("a");k.href=he,k.setAttribute("download",s+".xlsx"),document.body.appendChild(k),k.click(),document.body.removeChild(k)}).catch(m=>{console.error("Error exporting data:",m)})},ie=(s,l)=>{p.job_status=s,v.errors["data.job_status"]=null},re=s=>{switch(s){case"Open":return"bg-blue-100";case"Close":return"bg-green-100"}},de=s=>{switch(s){case"Open":return"text-blue-600";case"Close":return"text-green-600"}},ce=s=>{switch(s){case"Out Of Warranty":return"bg-red-100";case"amc":return"bg-cyan-100";case"cmc":return"bg-green-100";case"warranty":return"bg-blue-100";default:return"bg-gray-100"}},ue=s=>{switch(s){case"Out Of Warranty":return"text-red-600";case"amc":return"text-cyan-600";case"cmc":return"text-green-600";case"warranty":return"text-blue-600";default:return"text-gray-600"}},$=(s,l,t)=>{O.value=s,v.get(route("jobcard.index",{search:s,status:l,engineer_id:t}),{preserveState:!0})},R=s=>{const l=new Date(s),t={year:"numeric",month:"short",day:"numeric"};return l.toLocaleDateString("en-US",t)},S=u("portrait"),_e=(s,l)=>{S.value=s},me=(s,l)=>{window.open(`/jobcard/download/${s}/${l}`,"_blank")};return(s,l)=>(r(),_(E,null,[i(y(je),{title:"Jobcard"}),i(pe,{notifications:n.notifications},{default:d(()=>[e("div",Ne,[e("div",Oe,[Be,e("div",$e,[e("div",Le,[e("div",Ie,[Ue,e("input",{id:"search-field",onInput:l[0]||(l[0]=t=>$(t.target.value,h.value,b.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),n.permissions.canCreateJobcard?(r(),_("div",Ae,[i(ge,{href:s.route("jobcard.create")},{default:d(()=>[g(" Create Jobcard ")]),_:1},8,["href"])])):f("",!0)])]),e("div",Te,[e("div",Pe,[e("div",We,[Re,i(x,{for:"customer_id",value:"Filters"})]),n.permissions.isServiceEngineer?f("",!0):(r(),_("button",{key:0,onClick:ne},[e("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,ze)]))]),e("div",Fe,[e("div",Ge,[i(x,{for:"customer_id",value:"Status"}),e("div",He,[i(ke,{options:n.jobFilterStatus,modelValue:h.value,"onUpdate:modelValue":l[1]||(l[1]=t=>h.value=t),onOnchange:le},null,8,["options","modelValue"])])]),n.permissions.isServiceEngineer?f("",!0):(r(),_("div",qe,[i(x,{for:"customer_id",value:"Service Engineer"}),e("div",Ke,[i(we,{options:n.serviceEngineer,modelValue:b.value,"onUpdate:modelValue":l[2]||(l[2]=t=>b.value=t),onOnchange:ae},null,8,["options","modelValue"])])]))])]),e("div",Xe,[e("div",Ye,[e("table",Ze,[e("thead",Qe,[e("tr",De,[(r(),_(E,null,U(Q,(t,M)=>e("th",{key:M,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:m=>y(H)(t.field,t.sortable)},[g(a(t.label)+" ",1),t.sortable?(r(),C(Ee,{key:0,isSorted:y(q)===t.field,direction:y(K)},null,8,["isSorted","direction"])):f("",!0)],8,et)),64))])]),n.data.data&&n.data.data.length>0?(r(),_("tbody",tt,[(r(!0),_(E,null,U(n.data.data,(t,M)=>(r(),_("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",st,a(t.job_card_number),1),e("td",ot,a(t.hospital_name??"-"),1),e("td",lt,a(t.product_name??"")+" "+a(t.product_code??""),1),e("td",at,a(t.serial_no??"-"),1),e("td",nt,a(t.users.first_name)+" "+a(t.users.last_name),1),e("td",it,a(R(t.date)),1),e("td",rt,a(t.time_ago),1),e("td",dt,[e("div",ct,[e("div",{class:w(["flex rounded-full px-4 py-1",ce(t.warranty_status)])},[e("span",{class:w(["text-sm font-semibold",ue(t.warranty_status)])},a(t.warranty_status??"-"),3)],2)])]),e("td",ut,[e("div",{class:w(["flex rounded-full px-4 py-1",re(t.status)])},[e("span",{class:w(["text-sm font-semibold",de(t.status)])},a(t.status),3)],2)]),e("td",_t,[e("div",mt,[i(fe,{align:"right",width:"48"},{trigger:d(()=>[ht]),content:d(()=>[t.status=="Open"&&n.permissions.canEditJobcard?(r(),C(z,{key:0,href:s.route("jobcard.edit",{id:t.id})},{svg:d(()=>[pt]),text:d(()=>[ft]),_:2},1032,["href"])):f("",!0),t.status=="Open"&&n.permissions.canDeleteJobcard?(r(),_("button",{key:1,type:"button",onClick:m=>D(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},xt,8,gt)):f("",!0),n.permissions.canViewJobcard?(r(),C(z,{key:2,href:s.route("jobcard.show",{id:t.id})},{svg:d(()=>[yt]),text:d(()=>[wt]),_:2},1032,["href"])):f("",!0),e("button",{type:"button",onClick:m=>te(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},St,8,kt),t.status=="Open"&&n.permissions.canCreateJobcard?(r(),_("button",{key:3,type:"button",onClick:m=>se(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Jt,8,Mt)):f("",!0)]),_:2},1024)])])]))),128))])):(r(),_("tbody",Nt,Bt))])])]),n.data.data&&n.data.data.length>0?(r(),C(be,{key:0,class:"mt-6",links:n.data.links},null,8,["links"])):f("",!0)]),i(I,{show:J.value,onClose:B},{default:d(()=>[e("div",$t,[Lt,e("div",It,[i(L,{onClick:B},{default:d(()=>[g(" Cancel ")]),_:1}),i(ve,{class:"ml-3",onClick:ee},{default:d(()=>[g(" Delete ")]),_:1})])])]),_:1},8,["show"]),i(I,{show:N.value,onClose:P,maxWidth:Y.value},{default:d(()=>[e("div",Ut,[e("div",At,[Tt,e("div",Pt,[e("div",Wt,[Rt,e("div",zt,[Ft,e("p",null,": "+a(c.value.hospital_name),1)]),e("div",Gt,[Ht,e("p",null,": "+a(c.value.address),1)]),e("div",qt,[Kt,e("p",null,": "+a(c.value.contact_no),1)])]),e("div",Xt,[e("div",Yt,[Zt,e("p",null,": "+a(c.value.job_card_number),1)]),e("div",Qt,[Dt,e("p",null,": "+a(R(c.value.date)),1)]),e("div",es,[ts,e("p",null,": "+a(c.value.users.first_name)+" "+a(c.value.users.last_name),1)])])]),e("table",null,[ss,e("tbody",null,[e("tr",os,[e("td",null,a(c.value.product_name??"-"),1),e("td",null,a(c.value.product_code??"-"),1),e("td",null,a(c.value.serial_no??"-"),1)])])]),e("div",null,[e("div",ls,[e("div",as,[ns,e("p",null,": "+a(c.value.problem_description??"NA"),1)])]),e("div",is,[e("div",rs,[ds,e("p",null,": "+a(c.value.accessories??"NA"),1)])])]),e("div",cs,[us,e("div",_s,[(r(!0),_(E,null,U(n.checklist,t=>(r(),C(ye,{key:t.id,checked:T.value,value:t.id,label:t.type,"onUpdate:checked":s.updateChecked},null,8,["checked","value","label","onUpdate:checked"]))),128))])]),e("div",ms,[e("div",hs,[e("div",ps,[fs,e("p",null,": "+a(c.value.problem_description),1)])]),e("div",gs,[e("div",vs,[bs,e("p",null,": "+a(c.value.close_date),1)])])])]),e("div",xs,[e("div",ys,[e("div",ws,[i(x,{for:"customer_id",value:"Page Type :"}),i(F,{options:n.pagetypes,modelValue:S.value,"onUpdate:modelValue":l[3]||(l[3]=t=>S.value=t),onOnchange:_e},null,8,["options","modelValue"])]),e("div",ks,[i(L,{onClick:P},{default:d(()=>[g(" Cancel ")]),_:1}),e("div",Cs,[i(G,{class:"ml-3 w-20",onClick:l[4]||(l[4]=t=>me(c.value.id,S.value))},{default:d(()=>[g(" Generate Pdf ")]),_:1})])])])])])]),_:1},8,["show","maxWidth"]),i(I,{show:j.value,onClose:W,maxWidth:Z.value},{default:d(()=>[e("div",js,[Ss,e("div",Ms,[e("div",Es,[e("div",Vs,[i(x,{for:"role_id",value:"Job Status"}),e("div",Js,[i(F,{options:n.jobStatus,modelValue:p.job_status,"onUpdate:modelValue":l[5]||(l[5]=t=>p.job_status=t),onOnchange:ie,class:w({"error rounded-md":y(v).errors["data.job_status"]})},null,8,["options","modelValue","class"])])]),e("div",Ns,[i(x,{for:"close_note",value:"Close Note"}),i(xe,{id:"close_note",type:"text",rows:3,modelValue:p.close_note,"onUpdate:modelValue":l[6]||(l[6]=t=>p.close_note=t),class:w({"error rounded-md":y(v).errors["data.close_note"]})},null,8,["modelValue","class"])])])]),e("div",Os,[i(L,{onClick:W},{default:d(()=>[g(" Cancel ")]),_:1}),e("div",Bs,[i(G,{class:"ml-3 w-20",onClick:oe},{default:d(()=>[g(" Save ")]),_:1})])])])]),_:1},8,["show","maxWidth"])]),_:1},8,["notifications"])],64))}},oo=Je($s,[["__scopeId","data-v-fb5e07d9"]]);export{oo as default};
