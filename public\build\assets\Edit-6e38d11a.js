import{K as v,h as V,A as h,o as k,c as x,a as o,u as a,w as u,F as z,Z as B,b as n,d as w,n as _,g as C}from"./app-97275a91.js";import{_ as S}from"./AdminLayout-595ad5a7.js";import{_ as i}from"./InputError-b3250228.js";import{_ as m}from"./InputLabel-eb73087c.js";import{P as U}from"./PrimaryButton-46ac4375.js";import{_ as d}from"./TextInput-11c46564.js";import{_ as p}from"./SearchableDropdown-9d1b12d3.js";import"./_plugin-vue_export-helper-c27b6911.js";const E={class:"animate-top bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},N=n("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Bank",-1),O=["onSubmit"],T={class:"border-b border-gray-900/10 pb-12"},$={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},A={class:"sm:col-span-3"},F={class:"relative mt-2"},j={class:"sm:col-span-3"},I={class:"sm:col-span-3"},L={class:"sm:col-span-3"},P={class:"sm:col-span-3"},D={class:"sm:col-span-3"},K={class:"relative mt-2"},M={class:"flex mt-6 items-center justify-between"},Z={class:"ml-auto flex items-center justify-end gap-x-6"},q=n("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1),ee={__name:"Edit",props:["data","organization","amounttype"],setup(c){const l=v().props.data,e=V({id:l.id,organization_id:l.organization_id,bank_name:l.bank_name,account_number:l.account_number,balance:l.balance,ifsc_code:l.ifsc_code,amount_type:l.amount_type}),b=()=>{e.patch(route("bankinfo.update",{id:e.id}),{preserveScroll:!0,onSuccess:()=>{e.reset()},onError:r=>{r.account_number&&(e.errors.account_number="This account number already exists in the bank.")}})},f=r=>{e.organization_id=r,e.errors.organization_id=null},g=r=>{e.amount_type=r,e.errors.amount_type=null};return(r,t)=>{const y=h("Link");return k(),x(z,null,[o(a(B),{title:"Edit Bank Info"}),o(S,null,{default:u(()=>[n("div",E,[N,n("form",{onSubmit:w(b,["prevent"])},[n("div",T,[n("div",$,[n("div",A,[o(m,{for:"organization",value:"Organization"}),n("div",F,[o(p,{options:c.organization,modelValue:a(e).organization_id,"onUpdate:modelValue":t[0]||(t[0]=s=>a(e).organization_id=s),onOnchange:f,class:_({"error rounded-md":a(e).errors.organization_id})},null,8,["options","modelValue","class"]),o(i,{message:a(e).errors.organization_id},null,8,["message"])])]),n("div",j,[o(m,{for:"bank_name",value:"Bank Name"}),o(d,{id:"bank_name",type:"text",modelValue:a(e).bank_name,"onUpdate:modelValue":t[1]||(t[1]=s=>a(e).bank_name=s),autocomplete:"bank_name"},null,8,["modelValue"]),o(i,{message:a(e).errors.bank_name},null,8,["message"])]),n("div",I,[o(m,{for:"account_number",value:"Account Number"}),o(d,{id:"account_number",type:"text",modelValue:a(e).account_number,"onUpdate:modelValue":t[2]||(t[2]=s=>a(e).account_number=s),autocomplete:"account_number"},null,8,["modelValue"]),o(i,{message:a(e).errors.account_number},null,8,["message"])]),n("div",L,[o(m,{for:"ifsc_code",value:"IFSC Code"}),o(d,{id:"ifsc_code",type:"text",modelValue:a(e).ifsc_code,"onUpdate:modelValue":t[3]||(t[3]=s=>a(e).ifsc_code=s),autocomplete:"ifsc_code"},null,8,["modelValue"]),o(i,{message:a(e).errors.ifsc_code},null,8,["message"])]),n("div",P,[o(m,{for:"balance",value:"Opening Balance"}),o(d,{id:"balance",type:"text",modelValue:a(e).balance,"onUpdate:modelValue":t[4]||(t[4]=s=>a(e).balance=s),autocomplete:"balance"},null,8,["modelValue"]),o(i,{message:a(e).errors.balance},null,8,["message"])]),n("div",D,[o(m,{for:"amount_type",value:"Amount Type"}),n("div",K,[o(p,{options:c.amounttype,modelValue:a(e).amount_type,"onUpdate:modelValue":t[5]||(t[5]=s=>a(e).amount_type=s),onOnchange:g,class:_({"error rounded-md":a(e).errors.amount_type})},null,8,["options","modelValue","class"]),o(i,{message:a(e).errors.amount_type},null,8,["message"])])])])]),n("div",M,[n("div",Z,[o(y,{href:r.route("bankinfo.index")},{default:u(()=>[q]),_:1},8,["href"]),o(U,{disabled:a(e).processing},{default:u(()=>[C("Save")]),_:1},8,["disabled"])])])],40,O)])]),_:1})],64)}}};export{ee as default};
