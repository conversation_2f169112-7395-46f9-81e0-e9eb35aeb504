import{h as de,r as i,l as ce,m as P,p as q,j as ue,o as d,c,a as v,u as ve,w as T,F as U,q as me,Z as he,b as e,k as G,v as R,f as b,i as Y,n as pe,t as k,g as fe,s as ge,x as _e}from"./app-4f4c883b.js";import{_ as xe}from"./AdminLayout-d9d2bc31.js";import{_ as ye}from"./SecondaryButton-69637431.js";import{_ as Z}from"./SearchableDropdownNew-a765fe75.js";import{_ as F}from"./InputLabel-468796e0.js";import{M as we}from"./Modal-85d770f4.js";import{_ as be}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const p=_=>(ge("data-v-722914bd"),_=_(),_e(),_),ke={class:"flex justify-between items-center mb-6"},Ce=p(()=>e("h1",{class:"text-2xl font-semibold text-gray-900"},"User Activity",-1)),Me={class:"ml-6 flex space-x-6 mt-4 sm:mt-0 w-64"},Le={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},$e=p(()=>e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),Fe={class:"mt-4 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},Ve={class:"flex mb-2"},je=p(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),Ae={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center"},De={class:"sm:col-span-3"},Se={class:"sm:col-span-3"},Te={class:"sm:col-span-3"},Ue={class:"relative mt-2"},Be={class:"sm:col-span-3"},Ie={class:"relative mt-2"},Ne={class:"mt-6 w-full"},Ee={key:0,class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"},Oe={class:"text-lg font-bold text-gray-800 mb-4"},He={class:"relative px-4 py-4 bg-white rounded-lg shadow-md w-full"},ze=["onClick"],Pe=p(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),qe=[Pe],Ge={class:"flex items-start space-x-4 items-center"},Re={class:"w-10 h-10"},Ye={key:0,xmlns:"http://www.w3.org/2000/svg",width:"40",height:"40",viewBox:"0 0 48 48",fill:"none"},Ze=p(()=>e("circle",{cx:"24",cy:"24",r:"22",fill:"url(#grad1)",stroke:"#28c62c","stroke-width":"3"},null,-1)),Je=p(()=>e("g",{transform:"translate(10, 12)",stroke:"#FFFFFF","stroke-width":"3","stroke-linecap":"round","stroke-linejoin":"round"},[e("rect",{x:"2",y:"4",width:"24",height:"16",rx:"3",ry:"3",fill:"none"}),e("path",{d:"M3 5l10 10 12-10"})],-1)),Ke=p(()=>e("defs",null,[e("linearGradient",{id:"grad1",x1:"0%",y1:"0%",x2:"100%",y2:"100%"},[e("stop",{offset:"0%",style:{"stop-color":"#3ADF5B","stop-opacity":"1"}}),e("stop",{offset:"100%",style:{"stop-color":"#28c62c","stop-opacity":"1"}})])],-1)),Qe=[Ze,Je,Ke],We={key:1,width:"40",height:"40",xmlns:"http://www.w3.org/2000/svg"},Xe=["fill"],et={x:"50%",y:"50%","dominant-baseline":"middle","text-anchor":"middle",fill:"#FFF","font-size":"14","font-weight":"bold","font-family":"Arial, sans-serif"},tt={class:"flex flex-col justify-between w-full"},st={class:"flex items-start"},ot={class:"text-gray-700"},lt=["innerHTML"],at={key:1,class:"text-md font-semibold ml-1"},nt={class:"text-sm text-gray-500"},rt={class:"flex justify-center my-4"},it={key:0,class:"loader"},dt=p(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 15l7-7 7 7"})],-1)),ct=[dt],ut={class:"p-6"},vt=p(()=>e("h2",{class:"text-2xl font-semibold text-gray-800 mb-4"},"Details",-1)),mt=["innerHTML"],ht={class:"mt-4 flex justify-end"},pt={__name:"ActivityLog",props:["activityLogs","user","userId","logNames"],setup(_){const D=_,J=de({}),x=i(""),m=i(D.userId),K=i("ALL Users"),f=i(""),g=i(""),h=i(""),C=i(null),y=i([...D.activityLogs]),B=i(!0),V=i(!1);ce(()=>D.activityLogs,o=>{y.value=[...o]});const Q=async()=>{if(!B.value||V.value)return;V.value=!0;const t={offset:y.value.length,search:x.value,causer_id:m.value,from_date:f.value,to_date:g.value,log_name:h.value};try{const s=await me.get(route("logs.loadMore"),{params:t}),n=s.data.activityLogs;y.value=y.value.concat(n),B.value=s.data.hasMore}catch(s){console.error("Error loading more logs",s)}finally{V.value=!1}};let j=null;P(()=>{j=new IntersectionObserver(o=>{o.forEach(t=>{t.isIntersecting&&Q()})}),C.value&&j.observe(C.value)}),q(()=>{j&&C.value&&j.unobserve(C.value)});const M=(o,t,s,n,l)=>{x.value=o,J.get(route("logs",{search:o,causer_id:t,from_date:s,to_date:n,log_name:l}),{preserveState:!0})},W=()=>{M(x.value,m.value,f.value,g.value,h.value)},X=()=>{M(x.value,m.value,f.value,g.value,h.value)},ee=(o,t)=>{m.value=o,K.value=t,M(x.value,m.value,f.value,g.value,h.value)},te=o=>{h.value=o,M(x.value,m.value,f.value,g.value,h.value)},se=o=>{const t=new Date(o),s={year:"numeric",month:"short",day:"numeric"};return t.toLocaleDateString("en-US",s)},A={created:{color:"text-green-500",svgColor:"#22C55E"},updated:{color:"text-blue-500",svgColor:"#3B82F6"},deleted:{color:"text-red-500",svgColor:"#EF4444"},received:{color:"text-yellow-500",svgColor:"#F59E0B"},payment:{color:"text-purple-500",svgColor:"#8B5CF6"},default:{color:"text-gray-500",svgColor:"#9CA3AF"}},I=ue(()=>y.value.reduce((o,t)=>{const s=se(t.created_at);return o[s]||(o[s]=[]),o[s].push(t),o},{})),oe=o=>{var $;const t=o.properties||{},s=o.event||"",n=(($=A[s])==null?void 0:$.color)||A.default.color;if(!Object.keys(t).some(a=>t[a]&&Object.keys(t[a]).length>0))return"";let r="";return s==="deleted"&&t.old?(r+='<div class="space-y-1">',Object.keys(t.old).forEach(a=>{const u=t.old[a]??"NA",w=`bg-red-100 ${n} px-3 py-1 rounded text-sm`;r+=`
        <div class="flex justify-between items-center">
          <div class="${n} font-medium text-sm">${a}</div>
          <span class="${w}">${u}</span>
        </div>
      `}),r+="</div>",r):(t.attributes&&(r+='<div class="space-y-1">',Object.keys(t.attributes).forEach(a=>{var z;const u=((z=t.old)==null?void 0:z[a])??null,w=t.attributes[a]??"NA",H=s==="updated"&&u===null?"NA":u,re="bg-red-100 text-red-600 px-3 py-1 rounded text-sm",ie="bg-green-100 text-green-600 px-3 py-1 rounded text-sm";r+=`
        <div class="flex justify-between items-center">
          <div class="${n} font-medium text-sm">${a}</div>
          <div class="flex items-center gap-2">
            ${s==="updated"?`<span class="${re}">${H}</span>
                   <span class="text-gray-500 text-sm">→</span>`:""}
            <span class="${ie}">${w}</span>
          </div>
        </div>
      `}),r+="</div>"),Object.keys(t).forEach(a=>{if(a!=="attributes"&&a!=="old"){r===""&&(r+='<div class="space-y-1">');let u=t[a];typeof u=="string"&&(u=u.replace(/^"|"$/g,"")),r+=`
        <div class="flex justify-between items-center">
          <div class="${n} font-medium text-sm">${a}</div>
          <div class="bg-gray-100 text-gray-800 px-3 py-1 rounded text-sm">${u}</div>
        </div>
      `}}),r?`<div class="space-y-1">${r}</div>`:"")},S=i(!1),L=i(null),N=()=>{S.value=!1,L.value=null},le=o=>{L.value=y.value.find(t=>t.id===o),S.value=!0},ae=(o="",t="")=>{const s=o.charAt(0).toUpperCase()||"",n=t.charAt(0).toUpperCase()||"";return`${s}${n}`||"U"},E=i(!1),ne=()=>{window.scrollTo({top:0,behavior:"smooth"})},O=()=>{E.value=window.pageYOffset>300};return P(()=>{window.addEventListener("scroll",O)}),q(()=>{window.removeEventListener("scroll",O)}),(o,t)=>(d(),c(U,null,[v(ve(he),{title:"Activity Logs"}),v(xe,null,{default:T(()=>[e("div",ke,[Ce,e("div",Me,[e("div",Le,[$e,e("input",{id:"search-field",onInput:t[0]||(t[0]=s=>M(s.target.value,m.value,f.value,g.value,h.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])])]),e("div",Fe,[e("div",Ve,[je,v(F,{for:"customer_id",value:"Filters"})]),e("div",Ae,[e("div",De,[v(F,{for:"date",value:"From Date"}),G(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>f.value=s),class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date",onChange:W},null,544),[[R,f.value]])]),e("div",Se,[v(F,{for:"date",value:"To Date"}),G(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>g.value=s),class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date",onChange:X},null,544),[[R,g.value]])]),e("div",Te,[v(F,{for:"log_name",value:"Task"}),e("div",Ue,[v(Z,{options:[{id:"",name:"All Task"},..._.logNames.map(s=>({id:s,name:s}))],modelValue:h.value,"onUpdate:modelValue":t[3]||(t[3]=s=>h.value=s),onOnchange:te},null,8,["options","modelValue"])])]),e("div",Be,[v(F,{for:"customer_id",value:"Users"}),e("div",Ie,[v(Z,{options:_.user,modelValue:m.value,"onUpdate:modelValue":t[4]||(t[4]=s=>m.value=s),onOnchange:ee},null,8,["options","modelValue"])])])])]),e("div",Ne,[Object.keys(I.value).length===0?(d(),c("div",Ee," No activity logs found. ")):b("",!0),(d(!0),c(U,null,Y(I.value,(s,n)=>(d(),c("div",{key:n,class:"mb-8"},[e("h2",Oe,k(n),1),(d(!0),c(U,null,Y(s,l=>{var r,$,a,u,w;return d(),c("div",{key:l.id,class:"flex items-start mb-4"},[e("div",He,[l.log_name!=="Number Setting Update"?(d(),c("button",{key:0,onClick:H=>le(l.id),class:"absolute top-1/2 right-0 transform -translate-y-1/2 px-6 text-gray-500 hover:text-gray-700 rounded-full"},qe,8,ze)):b("",!0),e("div",Ge,[e("div",Re,[l.event==="sent"?(d(),c("svg",Ye,Qe)):(d(),c("svg",We,[e("circle",{cx:"20",cy:"20",r:"20",fill:((r=A[l.event])==null?void 0:r.svgColor)||"#3B82F6"},null,8,Xe),e("text",et,k(ae(($=l.causer)==null?void 0:$.first_name,(a=l.causer)==null?void 0:a.last_name)),1)]))]),e("div",tt,[e("div",st,[e("p",ot,[l.description?(d(),c("span",{key:0,class:"",innerHTML:l.description},null,8,lt)):b("",!0),l.log_name!=="PaymentPaid"&&l.log_name!=="Payment-Receive"&&l.log_name!=="Send-Invoice-Email"?(d(),c("span",at,k(((u=l.causer)==null?void 0:u.first_name)||"Unknown")+" "+k(((w=l.causer)==null?void 0:w.last_name)||""),1)):b("",!0)])]),e("div",nt,k(new Date(l.created_at).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})),1)])])])])}),128))]))),128)),e("div",rt,[V.value?(d(),c("div",it)):b("",!0)]),e("div",{ref_key:"sentinel",ref:C,class:"h-1"},null,512)]),E.value?(d(),c("button",{key:0,onClick:ne,class:"fixed bottom-5 right-5 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg"},ct)):b("",!0),v(we,{show:S.value,onClose:N},{default:T(()=>{var s,n,l;return[e("div",ut,[vt,e("p",{class:pe([((n=A[(s=L.value)==null?void 0:s.event])==null?void 0:n.color)||"text-gray-700","text-md mb-4 font-semibold"])},k((l=L.value)==null?void 0:l.event)+" details: ",3),e("div",{class:"overflow-y-auto p-4 mb-4 border rounded-lg shadow-sm bg-white",style:{"max-height":"340px"},innerHTML:oe(L.value)},null,8,mt),e("div",ht,[v(ye,{onClick:N},{default:T(()=>[fe("Close")]),_:1})])])]}),_:1},8,["show"])]),_:1})],64))}},Ct=be(pt,[["__scopeId","data-v-722914bd"]]);export{Ct as default};
