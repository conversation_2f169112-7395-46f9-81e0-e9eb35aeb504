<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Jobcard;
use App\Models\JobcardCheck;
use App\Models\JobChecklist;
use App\Models\User;
use App\Models\Organization;
use App\Models\Customer;
use App\Models\Document;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\JobcardRequest;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\CloseJobcardRequest;
use Illuminate\Support\Facades\DB;
use App\Traits\CommonTrait;
use App\Traits\FileUploadTrait;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\JobcardExport;
use App\Traits\QueryTrait;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use App\Notifications\JobCardUpdated;
use PDF;

class JobCardController extends Controller
{
    use CommonTrait;
    use FileUploadTrait;
    use QueryTrait;

    public function __construct()
    {
        $this->middleware('permission:List Jobcard')->only(['index']);
        $this->middleware('permission:Create Jobcard')->only(['create', 'store']);
        $this->middleware('permission:Edit Jobcard')->only(['edit', 'update']);
        $this->middleware('permission:Delete Jobcard')->only('destroy');
        $this->middleware('permission:View Jobcard')->only('view');
    }

    public function index(Request $request)
    {
        $search = $request->input('search');
        $statusId = $request->input('status');

        $engineerId = $request->input('engineer_id');
        $query = Jobcard::with('jobCardChecks', 'users');

        $notifications = auth()->user()->notifications;
        // $notifications = auth()->user()->unreadNotifications;

        if (auth()->user()->can('Create Jobcard') != true) {
            $query->where('engineer_id', Auth::user()->id);
        }

        if($statusId == "") {
            $statusId = 'Open';
        }

        if($statusId != "null") {
            $query->where('status', $statusId);
        }

        if($engineerId) {
            $query->where('engineer_id', $engineerId);
        }

        if (!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->orWhere('job_card_number', 'like', "%$search%")
                      ->orWhere('hospital_name', 'like', "%$search%")
                      ->orWhere('product_name', 'like', "%$search%");
            });
        }

        $searchableFields = [
            'job_card_number',
            'hospital_name',
            'product_name',
            'product_code',
            'serial_no',
            'users.first_name',
            'date',
            'status'
        ];

        $this->searchAndSort($query, $request, $searchableFields);

        $perPage = Config::get('constants.perPage');
        $statusOrder = ['Open', 'Close'];

        $query->orderByRaw("FIELD(status, '" . implode("', '", $statusOrder) . "') ASC");
        $data = $query->orderBy('id', 'desc')->paginate($perPage);

        $data->getCollection()->transform(function ($jobcard) {
            $jobcard->time_ago = \Carbon\Carbon::parse($jobcard->created_at)->diffForHumans();
            return $jobcard;
        });

        $serviceEngineer = User::where(['status' => '1'])->whereIn('role_id', [7,8])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $allEngineer  = ['id' => null, 'name' => 'ALL ENGINEERS'];
        $serviceEngineer->prepend($allEngineer);

        $permissions = [
            'canCreateJobcard' => auth()->user()->can('Create Jobcard'),
            'canEditJobcard' => auth()->user()->can('Edit Jobcard'),
            'canDeleteJobcard' => auth()->user()->can('Delete Jobcard'),
            'canViewJobcard' => auth()->user()->can('View Jobcard'),
            'isServiceEngineer' => auth()->user()->hasRole('Service Engineer'),
        ];

        $checklist = JobChecklist::select('type', 'id')->get();
        $jobStatus = Config::get('constants.jobStatus');
        $jobFilterStatus = Config::get('constants.jobFilterStatus');
        $pagetypes = collect(Config::get('constants.pageTypes'));

        return Inertia::render('Jobcard/List', compact(
            'data',
            'permissions',
            'checklist',
            'jobStatus',
            'statusId',
            'jobFilterStatus',
            'serviceEngineer',
            'engineerId',
            'pagetypes',
            'notifications'
        ));
    }

    public function create(Request $request)
    {
        $jobcard_number = $this->generateJobNumber();

        $customers = Customer::where('status', '1')
            ->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 35), " - ", city) as name, id ,person_name, gst_type, contact_no , city, address, customer_name')
            ->orderByRaw('customer_name')
            ->get();

        $addNewCustomer = ['id' => 'new_customer', 'name' => 'NEW CUSTOMER'];
        $customers->prepend($addNewCustomer);

        $engineer = User::where(['status' => '1'])
            ->whereIn('role_id', [7,8])
            ->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"), 'id')
            ->orderByRaw('first_name')
            ->get();

        $checklist = JobChecklist::select('type','id')->get();

        $warranty_status = null;
        if ($request->has('jobcard_id')) {
            $jobcard = JobCard::find($request->jobcard_id);
            $warranty_status = $jobcard ? $jobcard->warranty_status : null;
        }

        return Inertia::render('Jobcard/Add', compact(
            'customers',
            'engineer',
            'jobcard_number',
            'checklist',
            'warranty_status'
        ));
    }

    public function store(JobcardRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            $data['warranty_status'] = $request->warranty_status;
            $jobNumber = $this->generateJobNumber();
            while (Jobcard::where('job_card_number', $jobNumber)->exists()) {
                $this->updateJobNumber($jobNumber);
                $jobNumber = $this->generateJobNumber();
            }
            $data['job_card_number'] = $jobNumber;
            $data['created_by'] = $data['updated_by'] = auth()->id();
            $createJob = Jobcard::create($data);
            if ($createJob) {
                if (isset($data['jobchecks']) && is_array($data['jobchecks'])) {
                    foreach ($data['jobchecks'] as $value) {
                        $jobCheckData = [
                            'job_card_id' => $createJob->id,
                            'job_card_checklist_id' => $value,
                            'created_by' => auth()->id(),
                            'updated_by' => auth()->id(),
                        ];
                        JobcardCheck::create($jobCheckData);
                    }
                }
            }
            DB::commit();
            return Redirect::to('/jobcard')->with('success', 'Jobcard Created Successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function update(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            $jobcard = Jobcard::findOrFail($id);

            // Update jobcard data
            $data = $request->except(['quotation_document', 'po_document', 'jobchecks']);
            $data['updated_by'] = auth()->id();
            $jobcard->update($data);

             $this->notifySalesCoordinator($jobcard);
            // Update job checks
            if ($request->has('jobchecks') && is_array($request->jobchecks)) {
                JobcardCheck::where('job_card_id', $id)->delete();
                foreach ($request->jobchecks as $value) {
                    JobcardCheck::create([
                        'job_card_id' => $id,
                        'job_card_checklist_id' => $value,
                        'created_by' => auth()->id(),
                        'updated_by' => auth()->id(),
                    ]);
                }
            }

            // Handle quotation file uploads
            $quotationFile = $request->file('quotation_document');
            if ($quotationFile) {
                $filePath = Config::get('constants.uploadFilePath.jobcardDocument');
                // Remove existing quotation documents before uploading new one
                $this->removeExistingDocuments($id, 'jobcard_quotation');
                // Wrap single file in array for compatibility with uploadDocuments method
                $this->uploadDocuments([$quotationFile], $filePath, $id, 'jobcard_quotation');
            }

            // Handle PO file uploads
            $poFile = $request->file('po_document');
            if ($poFile) {
                $filePath = Config::get('constants.uploadFilePath.jobcardDocument');
                // Remove existing PO documents before uploading new one
                $this->removeExistingDocuments($id, 'jobcard_po');
                // Wrap single file in array for compatibility with uploadDocuments method
                $this->uploadDocuments([$poFile], $filePath, $id, 'jobcard_po');
            }

            DB::commit();
            return Redirect::to('/jobcard')->with('success', 'Jobcard Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit(string $id)
    {
        $data = Jobcard::with(['jobCardChecks', 'quotationDocuments', 'poDocuments'])->find($id);
        $engineer = User::where(['status' => '1'])
            ->whereIn('role_id', [7,8])
            ->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"), 'id')
            ->orderByRaw('first_name')
            ->get();

        $checklist = JobChecklist::select('type', 'id')->get();
        $permissions = [
            'canCreateJobcard' => auth()->user()->can('Create Jobcard'),
            'canEditJobcard' => auth()->user()->can('Edit Jobcard'),
            'canDeleteJobcard' => auth()->user()->can('Delete Jobcard'),
            'canViewJobcard' => auth()->user()->can('View Jobcard'),
            'isServiceEngineer' => auth()->user()->hasRole('Service Engineer'),
        ];

        $filePath = Config::get('constants.uploadFilePath.jobcardDocument');

        return Inertia::render('Jobcard/Edit', compact('data', 'engineer', 'checklist', 'permissions', 'filePath'));
    }

    public function closeJobcard(CloseJobcardRequest $request)
    {
        $data = $request->all();
        DB::beginTransaction();
        try {
            $data = $data['data'];
            $data['close_date'] = date('Y-m-d');
            $data['close_by'] = auth()->id();
            $data['status'] = 'Close';
            $jobCard = Jobcard::find($data['id']);
            $jobCard->update($data);
            DB::commit();
            return Redirect::to('/jobcard')->with('success','Jobcard Close Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function show(string $id)
    {
       $data = Jobcard::where('id', $id)->with('jobCardChecks', 'users', 'quotationDocuments', 'poDocuments')->get()->toArray();
       $checklist = JobChecklist::select('type','id')->get();
       $filePath = Config::get('constants.uploadFilePath.jobcardDocument');
       return Inertia::render('Jobcard/viewjob', compact('data', 'checklist', 'filePath'));
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $user = Jobcard::find($id);
            JobcardCheck::where('job_card_id', $id)->delete();
            $user->delete();
            DB::commit();
            return Redirect::back()->with('success','Jobcard Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function exportJobcard(Request $request)
    {

        $data = $request->input();
        $status = $data['status'];
        $query = Jobcard::with('jobCardChecks', 'users');
        if($status != "null") {
            $query->where('status', $status);
        }
        $allData = $query->get()->toArray();
        return Excel::download(new JobcardExport($allData), 'Jobcard_Report_' . now()->format('Y-m-d') . '.xlsx');
    }

    public function downloadJobCard($id, $type)
    {
        $data = JobCard::where('id', $id)->with('jobCardChecks', 'users')->get();
        $checkedValues = $data[0]->jobCardChecks->pluck('job_card_checklist_id')->toArray();
        $checklist = JobChecklist::select('type', 'id')->get();
        $pdf = PDF::loadView('pdf.jobcard', compact('data', 'checkedValues', 'checklist'))->setPaper('A4', $type);
        $sanitizedFilename = $this->sanitizeFilename($data[0]->hospital_name);
        return $pdf->download("Jobcard{$sanitizedFilename}.pdf");
        // return $pdf->stream();
    }

    protected function notifySalesCoordinator($jobCard)
    {
        $salesCoordinator = User::find($jobCard->created_by);
        if ($salesCoordinator) {
            $salesCoordinator->notify(new JobCardUpdated($jobCard));
        }
    }

    private function removeExistingDocuments($jobcardId, $documentType)
    {
        try {
            $existingDocuments = Document::where('entity_id', $jobcardId)
                                       ->where('entity_type', $documentType)
                                       ->get();

            $filePath = Config::get('constants.uploadFilePath.jobcardDocument');

            foreach ($existingDocuments as $document) {
                // Delete physical file
                $fullPath = $filePath['default'] . $document->name;
                if (file_exists($fullPath)) {
                    unlink($fullPath);
                }
                // Delete database record
                $document->delete();
            }
        } catch (\Exception $e) {
            // Log error but don't stop the upload process
            Log::error('Error removing existing documents: ' . $e->getMessage());
        }
    }

    public function deleteDocument($id)
    {
        try {
            $document = Document::findOrFail($id);
            $filePath = Config::get('constants.uploadFilePath.jobcardDocument');
            $fullPath = $filePath['default'] . $document->name;

            if (file_exists($fullPath)) {
                unlink($fullPath);
            }

            $document->delete();
            return response()->json(['success' => true, 'message' => 'Document deleted successfully']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }
}



