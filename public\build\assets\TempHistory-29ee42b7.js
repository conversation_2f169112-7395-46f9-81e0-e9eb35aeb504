import{_ as B}from"./AdminLayout-36b0d46a.js";import{K as I,h as U,r as c,o as n,c as d,a as o,u as w,w as l,F as v,Z as V,b as t,g as p,i as $,e as P,f as j,t as r,s as A,x as D}from"./app-4c3f0163.js";import{_ as H}from"./CreateButton-fed8c592.js";import{_ as g}from"./SecondaryButton-d521cdbf.js";import{D as L}from"./DangerButton-b3c50a37.js";import{M as k}from"./Modal-61735c0a.js";import{_ as O}from"./Pagination-52b28f25.js";import{_ as W}from"./FileViewer-e6911454.js";import{_ as F}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const i=s=>(A("data-v-e0086ff4"),s=s(),D(),s),z={class:"animate-top"},G={class:"sm:flex sm:items-center"},K=i(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Uploaded Service Reports")],-1)),Y={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},Z={class:"flex justify-end w-20"},q={class:"mt-6 flow-root"},J={class:"-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},Q={class:"overflow-hidden inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8",style:{"min-height":"500px"}},X={class:"p-1 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},tt={class:"min-w-full divide-y divide-gray-300"},et=i(()=>t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"DATE"),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"REPORT NAME"),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"REPORT TYPE "),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"SERVICE ENGINEER"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1)),st={key:0,class:"divide-y divide-gray-300 bg-white"},ot={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},at={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900"},lt={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},nt={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},it={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},ct=["onClick"],dt=i(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),rt=[dt],mt=["onClick"],pt=i(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),_t=[pt],ht={key:1},ft=i(()=>t("tr",{class:"bg-white"},[t("td",{colspan:"5",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),ut=[ft],xt={class:"p-6"},yt=i(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),wt={class:"mt-6 flex justify-end"},vt={class:"p-6"},gt={class:"mt-6 px-4 flex justify-end"},kt={__name:"TempHistory",props:["data","customer","filepath"],setup(s){const f=I().props.filepath.view,b=U({}),u=c(!1),C=c(null),_=()=>{u.value=!1},M=()=>{b.delete(route("service-reports.destroy",{id:C.value}),{onSuccess:()=>_()})},h=c(!1),x=c(null),E=c("custom"),S=a=>{x.value=a,h.value=!0},y=()=>{h.value=!1},T=a=>{const m=window.location.origin+f+a,e=document.createElement("a");e.href=m,e.setAttribute("download",a),document.body.appendChild(e),e.click(),document.body.removeChild(e)},N=a=>{const m=new Date(a),e={year:"numeric",month:"short",day:"numeric"};return m.toLocaleDateString("en-US",e)};return(a,m)=>(n(),d(v,null,[o(w(V),{title:"Uploaded Service Reports"}),o(B,null,{default:l(()=>[t("div",z,[t("div",G,[K,t("div",Y,[t("div",Z,[o(H,{href:a.route("service-reports.show",{id:s.data.data[0].customer_id})},{default:l(()=>[p(" Back ")]),_:1},8,["href"])])])]),t("div",q,[t("div",J,[t("div",Q,[t("div",X,[t("table",tt,[et,s.data.data[0].report_detail&&s.data.data[0].report_detail.length>0?(n(),d("tbody",st,[(n(!0),d(v,null,$(s.data.data[0].report_detail,(e,bt)=>(n(),d("tr",{key:e.id,class:""},[t("td",ot,r(N(e.date)??"-"),1),t("td",at,r(e.document_name??"-"),1),t("td",lt,r(e.type??"-"),1),t("td",nt,r(e.engineer.first_name??"-")+" "+r(e.engineer.last_name??"-"),1),t("td",it,[t("button",{type:"button",onClick:R=>S(e.document_name)},rt,8,ct),t("button",{type:"button",onClick:R=>T(e.document_name)},_t,8,mt)])]))),128))])):(n(),d("tbody",ht,ut))])]),s.data.data&&s.data.data.length>0?(n(),P(O,{key:0,class:"mt-6",links:s.data.links},null,8,["links"])):j("",!0)])])])]),o(k,{show:u.value,onClose:_},{default:l(()=>[t("div",xt,[yt,t("div",wt,[o(g,{onClick:_},{default:l(()=>[p(" Cancel ")]),_:1}),o(L,{class:"ml-3",onClick:M},{default:l(()=>[p(" Delete ")]),_:1})])])]),_:1},8,["show"]),o(k,{show:h.value,onClose:y,maxWidth:E.value},{default:l(()=>[t("div",vt,[o(W,{fileUrl:w(f)+x.value},null,8,["fileUrl"]),t("div",gt,[o(g,{onClick:y},{default:l(()=>[p(" Cancel ")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},Vt=F(kt,[["__scopeId","data-v-e0086ff4"]]);export{Vt as default};
