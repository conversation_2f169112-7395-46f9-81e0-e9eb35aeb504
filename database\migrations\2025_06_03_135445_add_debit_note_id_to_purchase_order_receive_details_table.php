<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchase_order_receive_details', function (Blueprint $table) {
            $table->foreignId('debit_note_id')->nullable()->constrained('debit_notes')->onDelete('set null');
            $table->enum('is_receive', ['yes', 'no'])->default('no');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('purchase_order_receive_details', function (Blueprint $table) {
            $table->dropForeign(['debit_note_id']);
            $table->dropColumn(['debit_note_id', 'is_receive']);
        });
    }
};
