import{o as n,c as p,a as o,u as s,w as c,F as v,Z as g,b as l,d as y,k as b,v as x,e as i,f as r,g as V,T as k}from"./app-97275a91.js";import{_ as w,a as C}from"./AdminLayout-595ad5a7.js";import{_ as d}from"./InputError-b3250228.js";import{_ as m}from"./InputLabel-eb73087c.js";import{P as $}from"./PrimaryButton-46ac4375.js";import{_ as u}from"./TextInput-11c46564.js";import{_ as h}from"./TextArea-5e21e606.js";import{u as S}from"./index-05d29b1c.js";import"./_plugin-vue_export-helper-c27b6911.js";const U={class:"animate-top h-screen"},B={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},N=l("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add Plan",-1),P=["onSubmit"],D={class:"border-b border-gray-900/10 pb-12"},T={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},A={class:"sm:col-span-3"},F={class:"sm:col-span-3"},j={class:"sm:col-span-3"},M={class:"sm:col-span-3"},E={class:"sm:col-span-3"},O={class:"sm:col-span-3"},Z={class:"sm:col-span-6"},q={class:"flex mt-6 items-center justify-between"},z={class:"ml-auto flex items-center justify-end gap-x-6"},G=l("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),H={key:0,class:"text-sm text-gray-600"},se={__name:"Add",setup(I){const e=S("post","/weeklyplan",{customer_name:"",dr_name:"",place:"",company:"",product:"",date:"",status:"Open",brief_discussion:""}),_=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()});return(f,t)=>(n(),p(v,null,[o(s(g),{title:"Add Plan"}),o(w,null,{default:c(()=>[l("div",U,[l("div",B,[N,l("form",{onSubmit:y(_,["prevent"]),class:""},[l("div",D,[l("div",T,[l("div",A,[o(m,{for:"dob",value:"Date"}),b(l("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":t[0]||(t[0]=a=>s(e).date=a),onChange:t[1]||(t[1]=a=>s(e).validate("date"))},null,544),[[x,s(e).date]]),s(e).invalid("date")?(n(),i(d,{key:0,class:"",message:s(e).errors.date},null,8,["message"])):r("",!0)]),l("div",F,[o(m,{for:"customer_name",value:"Customer Name"}),o(u,{id:"customer_name",type:"text",modelValue:s(e).customer_name,"onUpdate:modelValue":t[2]||(t[2]=a=>s(e).customer_name=a),onChange:t[3]||(t[3]=a=>s(e).validate("customer_name"))},null,8,["modelValue"]),s(e).invalid("customer_name")?(n(),i(d,{key:0,class:"",message:s(e).errors.customer_name},null,8,["message"])):r("",!0)]),l("div",j,[o(m,{for:"dr_name",value:"Doctor Name"}),o(u,{id:"dr_name",type:"text",modelValue:s(e).dr_name,"onUpdate:modelValue":t[4]||(t[4]=a=>s(e).dr_name=a)},null,8,["modelValue"]),s(e).invalid("dr_name")?(n(),i(d,{key:0,class:"",message:s(e).errors.dr_name},null,8,["message"])):r("",!0)]),l("div",M,[o(m,{for:"place",value:"Place/City"}),o(u,{id:"place",type:"text",modelValue:s(e).place,"onUpdate:modelValue":t[5]||(t[5]=a=>s(e).place=a),onChange:t[6]||(t[6]=a=>s(e).validate("place"))},null,8,["modelValue"]),s(e).invalid("place")?(n(),i(d,{key:0,class:"",message:s(e).errors.place},null,8,["message"])):r("",!0)]),l("div",E,[o(m,{for:"company",value:"Company"}),o(u,{id:"company",type:"text",modelValue:s(e).company,"onUpdate:modelValue":t[7]||(t[7]=a=>s(e).company=a)},null,8,["modelValue"]),s(e).invalid("company")?(n(),i(d,{key:0,class:"",message:s(e).errors.company},null,8,["message"])):r("",!0)]),l("div",O,[o(m,{for:"product",value:"Products"}),o(u,{id:"product",type:"text",modelValue:s(e).product,"onUpdate:modelValue":t[8]||(t[8]=a=>s(e).product=a),onChange:t[9]||(t[9]=a=>s(e).validate("product"))},null,8,["modelValue"]),s(e).invalid("product")?(n(),i(d,{key:0,class:"",message:s(e).errors.product},null,8,["message"])):r("",!0)]),l("div",Z,[o(m,{for:"brief_discussion",value:"Brief Discussion"}),o(h,{id:"brief_discussion",type:"text",rows:3,modelValue:s(e).brief_discussion,"onUpdate:modelValue":t[10]||(t[10]=a=>s(e).brief_discussion=a),onChange:t[11]||(t[11]=a=>s(e).validate("brief_discussion"))},null,8,["modelValue"]),s(e).invalid("brief_discussion")?(n(),i(d,{key:0,class:"",message:s(e).errors.brief_discussion},null,8,["message"])):r("",!0)])])]),l("div",q,[l("div",z,[o(C,{href:f.route("weeklyplan.index")},{svg:c(()=>[G]),_:1},8,["href"]),o($,{disabled:s(e).processing},{default:c(()=>[V("Save")]),_:1},8,["disabled"]),o(k,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:c(()=>[s(e).recentlySuccessful?(n(),p("p",H,"Saved.")):r("",!0)]),_:1})])])],40,P)])])]),_:1})],64))}};export{se as default};
