<?php

namespace App\Http\Controllers;

use App\Traits\QueryTrait;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\DebitNote;
use App\Models\DebitNoteDetail;
use App\Models\Organization;
use App\Models\Company;
use App\Models\PurchaseTransaction;
use App\Models\PurchaseOrderReceives;
use App\Models\PurchaseOrderReceiveDetails;
use App\Models\SerialNumbers;
use App\Models\CompanyCredit;
use App\Models\PaymentPaid;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\DB;

class DebitNoteController extends Controller
{
    public function __construct()
    {
        // Add permissions as needed
        // $this->middleware('permission:List Debit Note')->only(['index']);
        // $this->middleware('permission:Delete Debit Note')->only('destroy');
    }

    use QueryTrait;

    public function index(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $companyId = $request->input('company_id');
        $query = DebitNote::with('debitNoteDetails', 'company', 'organization', 'purchaseInvoice');

        if ($companyId) {
            $query->where('company_id', $companyId);
        }
        if ($organizationId) {
            $query->where('organization_id', $organizationId);
        }

        $searchableFields = ['debit_note_no', 'credit_note_number', 'date', 'purchase_invoice.customer_invoice_no', 'company.name', 'total_amount'];
        $this->searchAndSort($query, $request, $searchableFields);

        $data = $query->orderBy('id', 'desc')->paginate(20);
        $organization = Organization::select('id', 'name')->get();
        $companies = Company::where('status', '1')->selectRaw('CONCAT(SUBSTRING(name, 1, 28), " - ", city) as name, id')->orderByRaw('name')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $allCompanies = ['id' => null, 'name' => 'ALL COMPANIES'];
        $organization->prepend($allOrganization);
        $companies->prepend($allCompanies);
        $data->withQueryString()->links();

        $permissions = [
            // 'canCreateDebitNote' => auth()->user()->can('Create Debit Note'),
            // 'canDeleteDebitNote' => auth()->user()->can('Delete Debit Note')
        ];

        return Inertia::render('DebitNote/List', compact('data', 'permissions', 'organization', 'companies', 'organizationId', 'companyId'));
    }

    public function show(Request $request, $id)
    {
        $data = DebitNote::where('id', $id)
            ->with([
                'debitNoteDetails.product',
                'debitNoteDetails.serialNumbers',
                'debitNoteDetails.purchaseOrderReceiveDetail',
                'company',
                'organization',
                'purchaseInvoice'
            ])
            ->get()
            ->toArray();
        return Inertia::render('DebitNote/View', compact('data'));
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            dd($id);
            $debitNote = DebitNote::with('debitNoteDetails')->findOrFail($id);

            // Delete related transactions
            $relatedTransactions = PurchaseTransaction::where('entity_id', $id)
                ->where('entity_type', 'debit_note')
                ->get();

            foreach ($relatedTransactions as $transaction) {
                $transaction->delete();
            }

            $purchaseInvoiceId = $debitNote->purchase_invoice_id;

            // Restore serial number quantities (similar to credit note deletion)
            foreach ($debitNote->debitNoteDetails as $detail) {
                if ($detail->serial_number_id) {
                    $serialNumber = SerialNumbers::find($detail->serial_number_id);
                    if ($serialNumber) {
                        // Restore the quantity by reducing sell_qty
                        $serialNumber->sell_qty -= $detail->qty;
                        $serialNumber->save();
                    }
                }

                // Delete the debit note detail
                $detail->delete();
            }

            // Update purchase invoice pending amount
            $purchaseInvoice = PurchaseOrderReceives::find($purchaseInvoiceId);
            if ($purchaseInvoice) {
                $purchaseInvoice->pending_amount += $debitNote->total_amount;
                $purchaseInvoice->save();
            }

            $debitNote->delete();

            DB::commit();
            return Redirect::back()->with('success', 'Debit Note deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }
}
