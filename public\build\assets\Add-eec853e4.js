import{r as k,j as Z,l as D,o as m,c as p,a as r,u as n,w as z,F as P,Z as oe,b as t,t as _,f as v,d as se,n as y,k as ae,v as ne,i as E,g as le,T as ie,s as re,x as ce}from"./app-6a429cee.js";import{_ as de,a as ue}from"./AdminLayout-dc64724f.js";import{_ as g}from"./InputLabel-5e6ac969.js";import{P as me}from"./PrimaryButton-c589c744.js";import{_ as I}from"./TextInput-94a28154.js";import{_ as pe}from"./TextArea-217f7d79.js";import{_ as _e}from"./RadioButton-4e48f1b7.js";import{_ as T}from"./SearchableDropdown-aa57848c.js";import{u as fe}from"./index-beae658c.js";/* empty css                                                                          */import{_ as ve}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as ye}from"./Checkbox-4f9a3a6d.js";const F=b=>(re("data-v-0c577901"),b=b(),ce(),b),he={class:"h-screen animate-top"},ge={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},xe={class:"sm:flex sm:items-center"},be=F(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Make Payment")],-1)),we={class:"flex items-center justify-between"},ke={key:0,class:"text-base font-semibold leading-6 text-gray-900"},Ve=["onSubmit"],Fe={class:"border-b border-gray-900/10 pb-12"},Ce={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},Se={class:"sm:col-span-3"},Ae={class:"relative mt-2"},Ne={class:"sm:col-span-3"},Ue={class:"relative mt-2"},$e={class:"sm:col-span-2"},ze={class:"relative mt-2"},Pe={key:0,class:"sm:col-span-3"},Ie={class:"relative mt-2"},Te={key:1,class:"sm:col-span-1"},Oe={key:2,class:"sm:col-span-2"},De={key:3,class:"sm:col-span-2"},Ee={class:"mt-4 flex justify-start"},Me={class:"text-base font-semibold"},Be={key:4,class:"sm:col-span-3"},je={key:5,class:"sm:col-span-3"},Le={key:6,class:"sm:col-span-3"},Re={key:7,class:"sm:col-span-3"},Ye={class:"relative mt-2"},qe={class:"sm:col-span-6"},He={class:"overflow-x-auto divide-y divide-gray-300 w-full"},Ze=F(()=>t("div",{class:"w-full"},[t("thead",{class:"w-full"},[t("tr",{class:""},[t("th",{scope:"col",class:""}),t("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),t("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),t("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1)),Ge={style:{"overflow-y":"auto","max-height":"318px"}},Je={class:"divide-y divide-gray-300 bg-white"},Ke={class:"whitespace-nowrap px-2 text-sm text-gray-900"},Qe={class:"text-sm text-gray-900 leading-6 py-1.5"},We={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},Xe={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},et={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},tt={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},ot={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},st={key:0,class:"text-red-500 text-xs absolute"},at={class:"whitespace-nowrap px-2 text-sm text-gray-900"},nt={class:"sm:col-span-2"},lt={class:"mt-2 p-3 bg-gray-50 rounded-md"},it={class:"space-y-2 text-sm"},rt={class:"flex items-center gap-2"},ct=F(()=>t("hr",{class:"my-2"},null,-1)),dt={class:"flex justify-between items-center font-semibold"},ut=F(()=>t("span",null,"Net Settlement:",-1)),mt={key:0,class:"text-red-500 text-xs mt-1"},pt={key:8,class:"sm:col-span-6"},_t={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},ft=F(()=>t("thead",null,[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1)),vt={class:"divide-y divide-gray-300 bg-white"},yt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ht={class:"whitespace-nowrap py-3 text-sm text-gray-900"},gt={class:"flex flex-col"},xt={class:"text-sm text-gray-900"},bt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},wt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},kt={class:"flex mt-6 items-center justify-between"},Vt={class:"ml-auto flex items-center justify-end gap-x-6"},Ft=F(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),Ct={key:0,class:"text-sm text-gray-600"},St={__name:"Add",props:["paymentType","bankinfo","organization","companies","invoices","credit"],setup(b){const C=b;k([]);const o=fe("post","/payment",{organization_id:"",company_id:"",payment_type:"",date:"",note:"",amount:"",discount_amount:0,round_off:0,check_number:"",org_bank_id:"",invoice:[],settled_amount:"",advance_amount:"",is_credit:"",credit_data:[],total_unused_amount:""}),N=k(""),G=()=>{o.settled_amount=A.value,o.advance_amount=j.value,o.total_unused_amount=S.value,o.is_credit=f.value,o.invoice=x.value,o.credit_data=w.value,o.submit({preserveScroll:!0,onSuccess:()=>o.reset()})},J=(a,s)=>{N.value=s,o.payment_type=a,o.errors.payment_type=null,s==="Cash"?o.note="Cash":o.note==="Cash"&&(o.note="")},U=k([]),M=k([]),w=k([]),S=k(""),K=(a,s)=>{o.organization_id=a,o.errors.organization_id=null;const e=C.bankinfo.filter(i=>i.organization_id===a);M.value=e,o.company_id&&B(o.company_id,a);const l=C.credit.filter(i=>i.organization_id===a&&i.company_id===o.company_id);w.value=l,S.value=w.value.reduce((i,d)=>i+d.unused_amount,0)},Q=(a,s)=>{o.company_id=a,B(a,o.organization_id);const e=C.credit.filter(l=>l.company_id===a&&l.organization_id===o.organization_id);w.value=e,S.value=w.value.reduce((l,i)=>l+i.unused_amount,0),o.errors.company_id=null},B=(a,s)=>{if(!a||!s){U.value=[];return}const e=C.companies.find(d=>d.id===a),l=e==null?void 0:e.party_id,i=C.invoices.filter(d=>{const c=d.organization_id===s;return d.invoice_type==="purchase"?c&&d.company_id===a:d.invoice_type==="sales"&&l?c&&d.party_id===l:!1});U.value=i},W=(a,s)=>{o.org_bank_id=a,o.errors.org_bank_id=null},A=Z(()=>{const a=x.value.reduce((s,e)=>{if(e.check&&e.amount){const l=parseFloat(e.amount);return e.invoice_type==="purchase"?s+l:s-l}return s},0);return parseFloat(a.toFixed(2))}),j=Z(()=>{const a=parseFloat(o.amount||0),s=parseFloat(o.round_off||0),e=A.value;return a>e?a-e-s:0}),V=a=>{let s=a.toFixed(2).toString(),[e,l]=s.split("."),i=e.substring(e.length-3),d=e.substring(0,e.length-3);return d!==""&&(i=","+i),`${d.replace(/\B(?=(\d{2})+(?!\d))/g,",")+i}.${l}`},L=a=>{const s=new Date(a),e={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",e)},X=(a,s)=>{const e=f.value==="Yes"?parseFloat(S.value||0):parseFloat(o.amount||0)+parseFloat(o.round_off||0);if(s===void 0&&(s=x.value.findIndex(l=>l.check===a.target.checked)),!(s===-1||s>=x.value.length)){if(!x.value[s].check){x.value[s].amount=0,R(e);return}R(e)}},R=a=>{x.value.forEach(c=>{c.check&&(c.amount=0)});const s=x.value.filter(c=>c.check);if(s.length===0||ee(a,s))return;const l=s.filter(c=>c.invoice_type==="purchase"),i=s.filter(c=>c.invoice_type==="sales");let d=a;for(const c of l){if(d<=0)break;const u=parseFloat(c.pending_amount||0),h=Math.min(u,d);c.amount=h.toFixed(2),d-=h}for(const c of i){if(d<=0)break;const u=parseFloat(c.pending_amount||0),h=Math.min(u,d);c.amount=h.toFixed(2),d-=h}},ee=(a,s)=>{const e=s.filter(u=>u.invoice_type==="purchase"),l=s.filter(u=>u.invoice_type==="sales"),i=e.reduce((u,h)=>u+parseFloat(h.pending_amount||0),0),d=l.reduce((u,h)=>u+parseFloat(h.pending_amount||0),0),c=i-d;return Math.abs(a-Math.abs(c))<=1?(s.forEach(u=>{u.amount=parseFloat(u.pending_amount||0).toFixed(2)}),!0):c>0&&a>=c?(s.forEach(u=>{u.amount=parseFloat(u.pending_amount||0).toFixed(2)}),!0):!1},x=k([{id:"",date:"",invoice_no:"",total_amount:"",pending_amount:"",check:!1,amount:0}]),f=k("No"),te=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],O=()=>{x.value=U.value.map(a=>({id:a.id,date:a.date,invoice_no:a.invoice_no,total_amount:parseFloat(a.total_amount||0).toFixed(2),pending_amount:parseFloat(a.pending_amount||0).toFixed(2),invoice_type:a.invoice_type||"purchase",check:!1,amount:"0.00"}))};D(U,()=>{O()}),D(f,()=>{O()}),D(()=>o.amount,()=>{f.value==="No"&&O()});const $=a=>{o.errors[a]=null,o.errors.settled_amount=null};return(a,s)=>(m(),p(P,null,[r(n(oe),{title:"Payment"}),r(de,null,{default:z(()=>[t("div",he,[t("div",ge,[t("div",xe,[be,t("div",we,[w.value.length>0?(m(),p("div",ke," Credits Available: ₹"+_(V(S.value)),1)):v("",!0)])]),t("form",{onSubmit:se(G,["prevent"]),class:""},[t("div",Fe,[t("div",Ce,[t("div",Se,[r(g,{for:"payment_type",value:"Organization"}),t("div",Ae,[r(T,{options:b.organization,modelValue:n(o).organization_id,"onUpdate:modelValue":s[0]||(s[0]=e=>n(o).organization_id=e),onOnchange:K,class:y({"error rounded-md":n(o).errors.organization_id})},null,8,["options","modelValue","class"])])]),t("div",Ne,[r(g,{for:"payment_type",value:"Company"}),t("div",Ue,[r(T,{options:b.companies,modelValue:n(o).company_id,"onUpdate:modelValue":s[1]||(s[1]=e=>n(o).company_id=e),onOnchange:Q,class:y({"error rounded-md":n(o).errors.company_id})},null,8,["options","modelValue","class"])])]),t("div",$e,[r(g,{for:"role_id",value:"Payment Through Credit ?"}),t("div",ze,[r(_e,{modelValue:f.value,"onUpdate:modelValue":s[2]||(s[2]=e=>f.value=e),options:te},null,8,["modelValue"])])]),f.value=="No"?(m(),p("div",Pe,[r(g,{for:"payment_type",value:"Payment Type"}),t("div",Ie,[r(T,{options:b.paymentType,modelValue:n(o).payment_type,"onUpdate:modelValue":s[3]||(s[3]=e=>n(o).payment_type=e),onOnchange:J,class:y({"error rounded-md":n(o).errors.payment_type})},null,8,["options","modelValue","class"])])])):v("",!0),f.value=="No"?(m(),p("div",Te,[r(g,{for:"round_off",value:"Round Off"}),r(I,{type:"text",onChange:s[4]||(s[4]=e=>$("round_off")),modelValue:n(o).round_off,"onUpdate:modelValue":s[5]||(s[5]=e=>n(o).round_off=e),class:y({"error rounded-md":n(o).errors.round_off})},null,8,["modelValue","class"])])):v("",!0),f.value=="No"?(m(),p("div",Oe,[r(g,{for:"amount",value:"Amount"}),r(I,{id:"amount",type:"text",onChange:s[6]||(s[6]=e=>$("amount")),modelValue:n(o).amount,"onUpdate:modelValue":s[7]||(s[7]=e=>n(o).amount=e),class:y({"error rounded-md":n(o).errors.amount})},null,8,["modelValue","class"])])):v("",!0),f.value=="No"?(m(),p("div",De,[r(g,{for:"advance",value:"Advance(Ref) Amount"}),t("div",Ee,[t("p",Me,_(V(j.value)),1)])])):v("",!0),N.value=="Cheque"&&f.value=="No"?(m(),p("div",Be,[r(g,{for:"check_number",value:"Cheque Number"}),r(I,{id:"check_number",type:"text",modelValue:n(o).check_number,"onUpdate:modelValue":s[8]||(s[8]=e=>n(o).check_number=e),class:y({"error rounded-md":n(o).errors["data.check_number"]})},null,8,["modelValue","class"])])):v("",!0),f.value=="No"?(m(),p("div",je,[r(g,{for:"date",value:"Payment Date"}),ae(t("input",{"onUpdate:modelValue":s[9]||(s[9]=e=>n(o).date=e),class:y(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":n(o).errors.date}]),type:"date",onChange:s[10]||(s[10]=e=>$("date"))},null,34),[[ne,n(o).date]])])):v("",!0),N.value=="Cash"&&f.value=="No"?(m(),p("div",Le)):v("",!0),N.value!="Cash"&&f.value=="No"?(m(),p("div",Re,[r(g,{for:"org_bank_id",value:"Our Bank"}),t("div",Ye,[r(T,{options:M.value,modelValue:n(o).org_bank_id,"onUpdate:modelValue":s[11]||(s[11]=e=>n(o).org_bank_id=e),onOnchange:W,class:y({"error rounded-md":n(o).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):v("",!0),t("div",qe,[t("table",He,[Ze,t("div",Ge,[t("tbody",Je,[(m(!0),p(P,null,E(x.value,(e,l)=>(m(),p("tr",{key:l},[t("td",Ke,[t("div",Qe,[r(ye,{name:"check",checked:e.check,"onUpdate:checked":i=>e.check=i,onChange:i=>X(i,l)},null,8,["checked","onUpdate:checked","onChange"])])]),t("td",We,[t("span",{class:y([e.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},_(e.invoice_type==="purchase"?"PURCHASE":"SALES"),3)]),t("td",Xe,_(e.invoice_no),1),t("td",et,_(e.total_amount),1),t("td",tt,_(e.pending_amount),1),t("td",ot,[r(I,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":i=>e.amount=i,onChange:i=>$("invoice."+l+".amount"),class:y({error:n(o).errors[`invoice.${l}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),n(o).errors[`invoice.${l}.amount`]?(m(),p("p",st,_(n(o).errors[`invoice.${l}.amount`]),1)):v("",!0)]),t("td",at,_(L(e.date)),1)]))),128))])])])]),t("div",nt,[r(g,{for:"note",value:"Settlement Summary"}),t("div",lt,[t("div",it,[(m(!0),p(P,null,E(x.value.filter(e=>e.check),e=>(m(),p("div",{key:e.id,class:"flex justify-between items-center"},[t("div",rt,[t("span",{class:y([e.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium"])},_(e.invoice_type==="purchase"?"P":"S"),3),t("span",null,_(e.invoice_no),1)]),t("span",{class:y([e.invoice_type==="purchase"?"text-blue-600":"text-green-600","font-medium"])},_(e.invoice_type==="purchase"?"+":"-")+"₹"+_(V(parseFloat(e.amount||0))),3)]))),128)),ct,t("div",dt,[ut,t("span",{class:y(A.value>=0?"text-blue-600":"text-red-600")}," ₹"+_(V(Math.abs(A.value)))+" "+_(A.value>=0?"(Pay)":"(Receive)"),3)])])]),n(o).errors.settled_amount?(m(),p("p",mt,_(n(o).errors.settled_amount),1)):v("",!0)]),f.value=="No"?(m(),p("div",pt,[r(g,{for:"note",value:"Note"}),r(pe,{id:"note",type:"text",rows:2,modelValue:n(o).note,"onUpdate:modelValue":s[12]||(s[12]=e=>n(o).note=e)},null,8,["modelValue"])])):v("",!0)]),w.value.length>0&&f.value=="Yes"?(m(),p("table",_t,[ft,t("tbody",vt,[(m(!0),p(P,null,E(w.value,(e,l)=>{var i,d,c,u,h,Y,q,H;return m(),p("tr",{key:l},[t("td",yt,_(L(e.date)),1),t("td",ht,[t("div",gt,[t("div",xt,_((d=(i=e.paymentpaid)==null?void 0:i.bank_info)!=null&&d.bank_name?(u=(c=e.paymentpaid)==null?void 0:c.bank_info)==null?void 0:u.bank_name:"Cash")+" - "+_((Y=(h=e.paymentpaid)==null?void 0:h.bank_info)!=null&&Y.account_number?(H=(q=e.paymentpaid)==null?void 0:q.bank_info)==null?void 0:H.account_number:""),1)])]),t("td",bt,_(V(e.amount)),1),t("td",wt,_(V(e.unused_amount)),1)])}),128))])])):v("",!0)]),t("div",kt,[t("div",Vt,[r(ue,{href:a.route("receipt.index")},{svg:z(()=>[Ft]),_:1},8,["href"]),r(me,{disabled:n(o).processing},{default:z(()=>[le("Save")]),_:1},8,["disabled"]),r(ie,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:z(()=>[n(o).recentlySuccessful?(m(),p("p",Ct,"Saved.")):v("",!0)]),_:1})])])],40,Ve)])])]),_:1})],64))}},Bt=ve(St,[["__scopeId","data-v-0c577901"]]);export{Bt as default};
