<template>
    <div class="responsive-card" :class="cardClasses">
        <!-- Card Header -->
        <div v-if="title || $slots.header" class="card-header">
            <slot name="header">
                <h3 class="card-title">{{ title }}</h3>
                <p v-if="subtitle" class="card-subtitle">{{ subtitle }}</p>
            </slot>
        </div>

        <!-- Card Content -->
        <div class="card-content">
            <slot />
        </div>

        <!-- Card Footer -->
        <div v-if="$slots.footer" class="card-footer">
            <slot name="footer" />
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    subtitle: {
        type: String,
        default: ''
    },
    variant: {
        type: String,
        default: 'default',
        validator: (value) => ['default', 'bordered', 'elevated', 'flat'].includes(value)
    },
    padding: {
        type: String,
        default: 'normal',
        validator: (value) => ['none', 'small', 'normal', 'large'].includes(value)
    }
});

const cardClasses = computed(() => {
    const classes = [];
    
    // Variant classes
    switch (props.variant) {
        case 'bordered':
            classes.push('border-2 border-gray-200');
            break;
        case 'elevated':
            classes.push('shadow-lg');
            break;
        case 'flat':
            classes.push('shadow-none border border-gray-100');
            break;
        default:
            classes.push('shadow-sm border border-gray-200');
    }
    
    // Padding classes
    switch (props.padding) {
        case 'none':
            classes.push('p-0');
            break;
        case 'small':
            classes.push('p-2 sm:p-3');
            break;
        case 'large':
            classes.push('p-6 sm:p-8');
            break;
        default:
            classes.push('p-4 sm:p-6');
    }
    
    return classes.join(' ');
});
</script>

<style scoped>
.responsive-card {
    @apply bg-white rounded-lg transition-all duration-200;
}

.card-header {
    @apply mb-4 pb-4 border-b border-gray-100;
}

.card-title {
    @apply text-lg sm:text-xl font-semibold text-gray-900;
}

.card-subtitle {
    @apply text-sm text-gray-600 mt-1;
}

.card-content {
    @apply flex-1;
}

.card-footer {
    @apply mt-4 pt-4 border-t border-gray-100;
}

/* Mobile optimizations */
@media (max-width: 640px) {
    .card-title {
        @apply text-base;
    }
    
    .responsive-card {
        @apply rounded-md;
    }
}
</style>
