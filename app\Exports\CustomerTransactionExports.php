<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Carbon\Carbon;

class CustomerTransactionExports implements FromCollection, WithHeadings, WithColumnFormatting, WithStyles
{
    protected $transactions;
    protected $allTransactions;
    protected $organizationName;
    protected $fromDate;
    protected $toDate;
    protected $customerName;

    public function __construct($transactions, $organizationName = null, $fromDate = null, $toDate = null, $customerName = null, $allTransactions = null)
    {
        $this->transactions = $transactions;
        $this->allTransactions = $allTransactions ?? $transactions;
        $this->organizationName = $organizationName;
        $this->fromDate = $fromDate;
        $this->toDate = $toDate;
        $this->customerName = $customerName;
    }

    public function headings(): array
    {
        return [
            [$this->organizationName ?? 'Organization Name'],
            [
                'From: ' . ($this->fromDate ? Carbon::parse($this->fromDate)->format('d-m-Y') : 'N/A') .
                'To: ' . ($this->toDate ? Carbon::parse($this->toDate)->format('d-m-Y') : 'N/A')
            ],
            ['Customer: ' . ($this->customerName ?? 'Unknown Customer')],
            ['Sno', 'Date', 'Narration', 'Debit', 'Credit', 'Balance'],
        ];
    }

    public function collection()
    {
        $data = [];
        $sno = 1;

        // Calculate opening balance from transactions before from_date
        $openingBalance = 0;
        if ($this->fromDate) {
            $fromDateCarbon = Carbon::parse($this->fromDate);
            foreach ($this->allTransactions as $transaction) {
                $transactionDate = Carbon::parse($transaction->date);
                if ($transactionDate->lt($fromDateCarbon)) {
                    if ($transaction->payment_type === 'cr') {
                        $openingBalance += $transaction->amount;
                    } else if ($transaction->payment_type === 'dr') {
                        $openingBalance -= $transaction->amount;
                    }
                }
            }
        }

        // Add opening balance row if there's a from_date and opening balance exists
        if ($this->fromDate && $openingBalance != 0) {
            $prefix = $openingBalance >= 0 ? 'cr' : 'dr';
            $data[] = [
                'Sno'       => '',
                'Date'      => '',
                'Narration' => 'Opening Balance',
                'Debit (₹)' => '',
                'Credit (₹)' => '',
                'Balance (₹)' => number_format(abs($openingBalance), 2, '.', '') . ' ' . $prefix,
            ];
        }

        // Initialize cumulative balance with opening balance
        $cumulativeBalance = $openingBalance;

        foreach ($this->transactions as $transaction) {
            $amount = $transaction->amount;
            $isCredit = $transaction->payment_type === 'cr';
            $debitAmount = $isCredit ? '' : number_format($amount, 2, '.', '');
            $creditAmount = $isCredit ? number_format(abs($amount), 2, '.', '') : '';

            // Update cumulative balance
            if ($isCredit) {
                $cumulativeBalance += abs($amount);
            } else {
                $cumulativeBalance -= $amount;
            }

            // Format balance with CR/DR indicator
            $prefix = $cumulativeBalance >= 0 ? 'cr' : 'dr';
            $formattedBalance = number_format(abs($cumulativeBalance), 2, '.', '') . ' ' . $prefix;

            $data[] = [
                'Sno'       => $sno++,
                'Date'      => Carbon::parse($transaction->date)->format('d-m-Y'),
                'Narration' => $transaction->note,
                'Debit (₹)' => $debitAmount,
                'Credit (₹)' => $creditAmount,
                'Balance (₹)' => $formattedBalance,
            ];
        }

        // Add total row
        $finalPrefix = $cumulativeBalance >= 0 ? 'cr' : 'dr';
        $data[] = [
            'Sno'       => 'TOTAL',
            'Date'      => '',
            'Narration' => '',
            'Debit (₹)' => '',
            'Credit (₹)' => '',
            'Balance (₹)' => number_format(abs($cumulativeBalance), 2, '.', '') . ' ' . $finalPrefix,
        ];

        return collect($data);
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_DATE_XLSX15,
            'D' => NumberFormat::FORMAT_NUMBER_00,
            'E' => NumberFormat::FORMAT_NUMBER_00,
            'F' => NumberFormat::FORMAT_NUMBER_00,
        ];
    }

    public function styles($sheet)
    {
        $openingBalance = 0;

        if ($this->fromDate) {
            $fromDateCarbon = Carbon::parse($this->fromDate);
            foreach ($this->allTransactions as $transaction) {
                $transactionDate = Carbon::parse($transaction->date);
                if ($transactionDate->lt($fromDateCarbon)) {
                    if ($transaction->payment_type === 'cr') {
                        $openingBalance += $transaction->amount;
                    } else if ($transaction->payment_type === 'dr') {
                        $openingBalance -= $transaction->amount;
                    }
                }
            }
        }

        $hasOpeningBalanceRow = $this->fromDate && $openingBalance != 0;

        $totalRows = 4 + count($this->transactions) + 1; // 4 headers + TOTAL row + transactions
        if ($hasOpeningBalanceRow) {
            $totalRows += 1;
        }

        $sheet->mergeCells('A1:F1');
        $sheet->mergeCells('A2:F2');
        $sheet->mergeCells('A3:F3');

        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->setCellValue('A1', $this->organizationName ?? 'Organization Name');

        $dateRange = $this->fromDate && $this->toDate
            ? 'From Date: ' . $this->fromDate . ' To Date: ' . $this->toDate
            : 'Date Range: Not Specified';
        $sheet->getStyle('A2')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A2')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->setCellValue('A2', $dateRange);

        $sheet->getStyle('A3')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('A3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->setCellValue('A3', 'Customer: ' . ($this->customerName ?? 'Unknown Customer'));

        $sheet->getStyle('A4:F4')->getFont()->setBold(true);
        $sheet->getStyle('A4:F4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A4:F4')->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('CCCCCC');

        $sheet->getStyle("A4:F$totalRows")
            ->getBorders()
            ->getAllBorders()
            ->setBorderStyle(Border::BORDER_THIN);

        $sheet->getStyle("A5:F$totalRows")
            ->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER)
            ->setVertical(Alignment::VERTICAL_CENTER);

        foreach (['A', 'B', 'C', 'D', 'E', 'F'] as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        $sheet->getStyle("A$totalRows:F$totalRows")->getFont()->setBold(true);
        $sheet->getStyle("A$totalRows:F$totalRows")->getFill()
            ->setFillType(Fill::FILL_SOLID)
            ->getStartColor()->setRGB('FFFF00');
        $sheet->getStyle("A$totalRows:F$totalRows")->getAlignment()
            ->setHorizontal(Alignment::HORIZONTAL_CENTER);

        return $sheet;
    }
}


