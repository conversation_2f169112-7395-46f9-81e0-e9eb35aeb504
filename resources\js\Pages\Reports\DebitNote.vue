<template>
    <AdminLayout>
        <div class="px-2 sm:px-4 lg:px-8 py-4 sm:py-6 animate-top">
            <!-- Header -->
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
                <div class="items-start">
                    <h1 class="text-xl sm:text-2xl font-semibold leading-7 text-gray-900">Debit Note Report</h1>
                </div>
                <div class="flex flex-col sm:flex-row gap-3 sm:gap-4">
                    <!-- Search -->
                    <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64">
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path>
                        </svg>
                        <input id="search-field" v-model="search" @input="fetchData" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div>
                    <SimpleDropdown
                        :options="organization"
                        v-model="organizationId"
                        @onchange="setOrganization"
                        placeholder="Select Organization"
                    />
                </div>
                <div>
                    <SimpleDropdown
                        :options="companies"
                        v-model="companyId"
                        @onchange="setCompany"
                        placeholder="Select Company"
                    />
                </div>
                <div>
                    <TextInput
                        type="date"
                        v-model="fromDate"
                        @input="fetchData"
                        placeholder="From Date"
                        class="w-full"
                    />
                </div>
                <div>
                    <TextInput
                        type="date"
                        v-model="toDate"
                        @input="fetchData"
                        placeholder="To Date"
                        class="w-full"
                    />
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="bg-white rounded-lg shadow-sm border p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Debit Notes</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ total_data?.length || 0 }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm border p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Amount</p>
                            <p class="text-2xl font-semibold text-gray-900">₹{{ totalAmount }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm border p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Average Amount</p>
                            <p class="text-2xl font-semibold text-gray-900">₹{{ averageAmount }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Desktop Table -->
            <div class="hidden lg:block mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3">Debit Note No</th>
                                <th scope="col" class="px-6 py-3">Company</th>
                                <th scope="col" class="px-6 py-3">Date</th>
                                <th scope="col" class="px-6 py-3">Purchase Invoice</th>
                                <th scope="col" class="px-6 py-3">Total Amount</th>
                                <th scope="col" class="px-6 py-3">Reason</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-if="data.data && (data.data.length > 0)" class="odd:bg-white even:bg-gray-50 border-b" v-for="debitNote in data.data" :key="debitNote.id">
                                <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">
                                    {{ debitNote.debit_note_no ?? '-' }}
                                </td>
                                <td class="px-6 py-4">{{ debitNote.company?.name ?? '-' }}</td>
                                <td class="px-6 py-4">{{ debitNote.date ?? '-' }}</td>
                                <td class="px-6 py-4">{{ debitNote.purchase_invoice?.customer_invoice_no ?? '-' }}</td>
                                <td class="px-6 py-4">₹{{ debitNote.total_amount ?? '-' }}</td>
                                <td class="px-6 py-4">
                                    <span class="truncate max-w-xs block" :title="debitNote.reason">
                                        {{ debitNote.reason ?? '-' }}
                                    </span>
                                </td>
                            </tr>
                            <tr v-else>
                                <td colspan="6" class="px-6 py-4 text-center">
                                    <p class="text-sm font-semibold text-gray-900">No data found.</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Mobile Cards -->
            <div class="lg:hidden mt-6 space-y-4">
                <template v-if="data.data && (data.data.length > 0)">
                    <div v-for="debitNote in data.data" :key="debitNote.id" 
                         class="bg-white rounded-lg shadow-sm border p-4">
                        <div class="space-y-3">
                            <!-- Header -->
                            <div class="flex justify-between items-start">
                                <h3 class="font-medium text-gray-900 text-base">{{ debitNote.debit_note_no ?? '-' }}</h3>
                                <span class="text-sm text-gray-500">{{ debitNote.date ?? '-' }}</span>
                            </div>
                            
                            <!-- Details Grid -->
                            <div class="grid grid-cols-1 gap-2 text-sm">
                                <div>
                                    <span class="text-gray-500">Company:</span>
                                    <span class="ml-1 font-medium">{{ debitNote.company?.name ?? '-' }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">Purchase Invoice:</span>
                                    <span class="ml-1 font-medium">{{ debitNote.purchase_invoice?.customer_invoice_no ?? '-' }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">Amount:</span>
                                    <span class="ml-1 font-medium">₹{{ debitNote.total_amount ?? '-' }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">Reason:</span>
                                    <span class="ml-1 font-medium">{{ debitNote.reason ?? '-' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                
                <!-- No data message for mobile -->
                <div v-else class="bg-white rounded-lg shadow-sm border p-8 text-center">
                    <p class="text-sm font-semibold text-gray-900">No data found.</p>
                </div>
            </div>

            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>
    </AdminLayout>
</template>

<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import { ref, computed } from 'vue';
import { router } from '@inertiajs/vue3';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import TextInput from '@/Components/TextInput.vue';
import Pagination from '@/Components/Pagination.vue';

const props = defineProps({
    data: Object,
    total_data: Array,
    permissions: Object,
    organization: Array,
    companies: Array,
    organizationId: String,
    companyId: String,
    from_date: String,
    to_date: String
});

const search = ref('');
const organizationId = ref(props.organizationId);
const companyId = ref(props.companyId);
const fromDate = ref(props.from_date);
const toDate = ref(props.to_date);

const totalAmount = computed(() => {
    if (!props.total_data || props.total_data.length === 0) return '0.00';
    const total = props.total_data.reduce((sum, item) => sum + parseFloat(item.total_amount || 0), 0);
    return total.toFixed(2);
});

const averageAmount = computed(() => {
    if (!props.total_data || props.total_data.length === 0) return '0.00';
    const total = parseFloat(totalAmount.value);
    const average = total / props.total_data.length;
    return average.toFixed(2);
});

const fetchData = () => {
    router.get('/reports/debitnote', {
        search: search.value,
        organization_id: organizationId.value,
        company_id: companyId.value,
        from_date: fromDate.value,
        to_date: toDate.value
    }, {
        preserveState: true,
        replace: true
    });
};

const setOrganization = (value) => {
    organizationId.value = value;
    fetchData();
};

const setCompany = (value) => {
    companyId.value = value;
    fetchData();
};
</script>
