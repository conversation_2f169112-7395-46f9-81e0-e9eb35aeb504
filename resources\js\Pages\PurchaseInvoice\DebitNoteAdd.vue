<script setup>
import { ref, onMounted, watch, computed, onBeforeMount } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import CreateButton from '@/Components/CreateButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SvgLink from '@/Components/ActionLink.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import InputLabel from '@/Components/InputLabel.vue';
import InputError from '@/Components/InputError.vue';
import { Head } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

const props = defineProps(['data', 'debit_no', 'serialNumbers']);

// Track selected serial numbers and their quantities
const selectedSerialNumbers = ref({});
const serialQuantities = ref({});
const allSelected = ref(false);

// Initialize selection state
onBeforeMount(() => {
    if (props.serialNumbers) {
        props.serialNumbers.forEach(serial => {
            selectedSerialNumbers.value[serial.id] = false;
            serialQuantities.value[serial.id] = 0;
        });
    }
});

const form = useForm('post', '/debitnotesave', {
    organization_id: props.data[0].purchase_order.organization_id,
    company_id: props.data[0].purchase_order.company_id,
    purchase_invoice_id: props.data[0].id,
    debit_note_no: props.debit_no[props.data[0].purchase_order.organization_id],
    credit_note_number: '',
    date: new Date().toISOString().split('T')[0],
    reason: '',
    selectedProductItem: [],
    sub_total: '',
    cgst: '',
    sgst: '',
    igst: '',
    total_gst: '',
    total_amount: ''
});

const submit = () => {
    form.sub_total = totalPrice.value;
    form.total_gst = totalGstAmount.value;
    form.cgst = cgstAmount.value;
    form.sgst = sgstAmount.value;
    form.igst = igstAmount.value;
    form.total_amount = totalAmount.value;
    form.selectedProductItem = getSelectedItems();
    form.submit({
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
};

// Select/Deselect all functionality
const selectAll = () => {
    if (props.serialNumbers) {
        props.serialNumbers.forEach(serial => {
            selectedSerialNumbers.value[serial.id] = allSelected.value;
            if (allSelected.value) {
                serialQuantities.value[serial.id] = serial.receive_qty - serial.sell_qty;
            } else {
                serialQuantities.value[serial.id] = 0;
            }
        });
    }
};

// Update selection for individual items
const updateSelection = (serialId) => {
    if (!selectedSerialNumbers.value[serialId]) {
        serialQuantities.value[serialId] = 0;
    } else {
        const serial = props.serialNumbers.find(s => s.id === serialId);
        if (serial) {
            serialQuantities.value[serialId] = serial.receive_qty - serial.sell_qty;
        }
    }
};

// Calculate line total for a serial number
const calculateLineTotal = (serial) => {
    if (!selectedSerialNumbers.value[serial.id] || !serialQuantities.value[serial.id]) return 0;
    const qty = serialQuantities.value[serial.id];
    const price = parseFloat(serial.purchase_price) || 0;
    const gst = parseFloat(serial.purchase_order_receive_details?.purchase_order_detail?.gst) || 0;
    const total_price = price * qty;
    const gst_amount = total_price * (gst / 100);
    return total_price + gst_amount;
};

// Get selected items for form submission
const getSelectedItems = () => {
    const items = [];
    if (props.serialNumbers) {
        props.serialNumbers.forEach(serial => {
            if (selectedSerialNumbers.value[serial.id] && serialQuantities.value[serial.id] > 0) {
                items.push({
                    serial_number_id: serial.id,
                    purchase_detail_id: serial.purchase_order_receive_detail_id,
                    product_id: serial.product_id,
                    qty: serialQuantities.value[serial.id],
                    purchase_price: serial.purchase_price,
                    batch: serial.batch,
                    expiry_date: serial.expiry_date
                });
            }
        });
    }
    return items;
};

// Computed totals
const totalAmount = computed(() => {
    let total = 0;
    if (props.serialNumbers) {
        props.serialNumbers.forEach(serial => {
            total += calculateLineTotal(serial);
        });
    }
    return total;
});

const totalPrice = computed(() => {
    let total = 0;
    if (props.serialNumbers) {
        props.serialNumbers.forEach(serial => {
            if (selectedSerialNumbers.value[serial.id] && serialQuantities.value[serial.id] > 0) {
                const qty = serialQuantities.value[serial.id];
                const price = parseFloat(serial.purchase_price) || 0;
                total += price * qty;
            }
        });
    }
    return total;
});

const totalGstAmount = computed(() => {
    let total = 0;
    if (props.serialNumbers) {
        props.serialNumbers.forEach(serial => {
            if (selectedSerialNumbers.value[serial.id] && serialQuantities.value[serial.id] > 0) {
                const qty = serialQuantities.value[serial.id];
                const price = parseFloat(serial.purchase_price) || 0;
                const gst = parseFloat(serial.purchase_order_receive_details?.purchase_order_detail?.gst) || 0;
                total += (price * qty) * (gst / 100);
            }
        });
    }
    return total;
});

const cgstAmount = computed(() => {
    if (props.data[0].purchase_order.company.gst_type === 'CGST/SGST') {
        return totalGstAmount.value / 2;
    }
    return 0;
});

const sgstAmount = computed(() => {
    if (props.data[0].purchase_order.company.gst_type === 'CGST/SGST') {
        return totalGstAmount.value / 2;
    }
    return 0;
});

const igstAmount = computed(() => {
    if (props.data[0].purchase_order.company.gst_type === 'IGST') {
        return totalGstAmount.value;
    }
    return 0;
});

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
    let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const goBack = () => {
    window.history.back();
};

</script>

<template>
    <Head title="Create Debit Note"/>

    <AdminLayout>
        <div class="animate-top">
        <form @submit.prevent="submit" class="">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Create Debit Note</h1>
            </div>
            <div class="flex items-center space-x-4">
                <div>
                    <p class="text-sm font-semibold text-gray-900">{{ data[0].purchase_order.organization.name }}</p>
                </div>
                <div class="flex justify-end w-20">
                    <CreateButton :href="route('purchaseinvoice.index')">
                        Back
                    </CreateButton>
                </div>
            </div>
        </div>

        <div class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Debit Note Information</h2>
                    <div class="grid grid-cols-1 gap-x-6 sm:grid-cols-6 pb-2">
                        <div class="sm:col-span-2">
                            <InputLabel for="debit_note_no" value="Debit Note No" />
                            <TextInput
                                id="debit_note_no"
                                type="text"
                                v-model="form.debit_note_no"
                                readonly
                                class="bg-gray-100"
                            />
                            <InputError class="" :message="form.errors.debit_note_no" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="date" value="Date" />
                            <input
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                type="date" v-model="form.date"
                                @change="clearError('date')"
                                :class="{ 'error rounded-md': form.errors.date }"
                            />
                            <InputError v-if="form.invalid('date')" class="" :message="form.errors.date" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="credit_note_number" value="Credit Note Number" />
                            <TextInput
                                id="credit_note_number"
                                type="text"
                                v-model="form.credit_note_number"
                                @change="clearError('credit_note_number')"
                                :class="{ 'error rounded-md': form.errors.credit_note_number }"
                                required
                            />
                            <InputError v-if="form.invalid('credit_note_number')" class="" :message="form.errors.credit_note_number" />
                        </div>
                        <div class="sm:col-span-6 mt-4">
                            <InputLabel for="reason" value="Reason for Debit Note" />
                            <textarea
                                id="reason"
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                rows="4"
                                v-model="form.reason"
                                @change="clearError('reason')"
                                :class="{ 'error rounded-md': form.errors.reason }"
                                required
                                placeholder="Enter the reason for creating this debit note..."
                            ></textarea>
                            <InputError v-if="form.invalid('reason')" class="" :message="form.errors.reason" />
                        </div>
                    </div>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Purchase Invoice Information</h2>
                    <div class="space-y-3 bg-gray-50 p-4 rounded-lg">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-700">Invoice No:</span>
                            <span class="font-medium text-sm">{{ data[0]?.customer_invoice_no || '-' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-700">Date:</span>
                            <span class="font-medium text-sm">{{ formatDate(data[0]?.customer_invoice_date) || '-' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <p class="text-sm text-gray-700">Company Name:</p>
                            <p class="font-medium text-sm">{{ data[0].purchase_order.company.name ?? '-' }} {{ data[0].purchase_order.company.city ?? '' }}</p>
                        </div>
                        <div class="flex justify-between">
                            <p class="text-sm text-gray-700">GST No:</p>
                            <p class="font-medium text-sm">{{ data[0].purchase_order.company.gst_no ?? '-' }}</p>
                        </div>
                        <div class="flex justify-between">
                            <p class="text-sm text-gray-700">Email:</p>
                            <p class="font-medium text-sm">{{ data[0].purchase_order.company.email ?? '-' }}</p>
                        </div>
                        <div class="flex justify-between">
                            <p class="text-sm text-gray-700">Contact No:</p>
                            <p class="font-medium text-sm">{{ data[0].purchase_order.company.contact_no ?? '-' }}</p>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-700">Invoice Amount:</span>
                            <span class="font-medium text-sm">₹{{ data[0]?.total_amount || 0 }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Serial Numbers Table -->
        <div v-if="serialNumbers && serialNumbers.length > 0" class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border divide-y divide-gray-300 overflow-x-auto">
            <div class="grid grid-cols-1 gap-x-4 sm:grid-cols-12 pb-2" style="width: 160%;">
                <div class="sm:col-span-1">
                    <input type="checkbox" v-model="allSelected" @change="selectAll" class="rounded">
                </div>
                <div class="sm:col-span-2">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Product</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Batch</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Expiry Date</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Price (₹)</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">GST %</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Available QTY</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Debit QTY</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Total Price (₹)</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Total Amount (₹)</p>
                </div>
            </div>
            <div class="grid grid-cols-1 gap-x-4 sm:grid-cols-12 items-center py-2" style="width: 160%;" v-for="(serial, index) in serialNumbers" :key="index">
                <div class="sm:col-span-1">
                    <input
                        type="checkbox"
                        v-model="selectedSerialNumbers[serial.id]"
                        @change="updateSelection(serial.id)"
                        class="rounded"
                    >
                </div>
                <div class="sm:col-span-2">
                    <p class="text-sm leading-5 text-gray-700">{{ serial.product?.item_code ?? '' }} {{ serial.product?.name ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ serial.batch ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ serial.expiry_date ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ formatAmount(serial.purchase_price) ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ serial.purchase_order_receive_details?.purchase_order_detail?.gst ?? '-' }}%</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ (serial.receive_qty - serial.sell_qty) ?? 0 }}</p>
                </div>
                <div class="sm:col-span-1 mb-2">
                    <TextInput
                        type="number"
                        v-model="serialQuantities[serial.id]"
                        @input="calculateLineTotal(serial)"
                        :disabled="!selectedSerialNumbers[serial.id]"
                        min="0"
                        :max="serial.receive_qty - serial.sell_qty"
                        class="w-20"
                    />
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">
                        {{ selectedSerialNumbers[serial.id] && serialQuantities[serial.id] > 0 ?
                           formatAmount(parseFloat(serial.purchase_price) * serialQuantities[serial.id]) : '0.00' }}
                    </p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">
                        {{ selectedSerialNumbers[serial.id] && serialQuantities[serial.id] > 0 ?
                           formatAmount(calculateLineTotal(serial)) : '0.00' }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Totals Section -->
        <div v-if="serialNumbers && serialNumbers.length > 0" class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                <div class="sm:col-span-3 space-y-2">
                    <!-- Product History Section -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-sm font-semibold text-gray-900 mb-2">Product History</h3>
                        <div class="text-xs text-gray-600">
                            <p>Total Products: {{ serialNumbers.length }}</p>
                            <p>Available Quantity: {{ serialNumbers.reduce((sum, serial) => sum + (serial.receive_qty - serial.sell_qty), 0) }}</p>
                            <p>Selected Items: {{ Object.values(selectedSerialNumbers).filter(Boolean).length }}</p>
                        </div>
                    </div>
                </div>
                <div class="sm:col-span-3">
                    <div class="inline-flex flex-col space-y-1 items-center justify-end w-full">
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm text-gray-700">Sub Total (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalPrice) }}</p>
                        </div>
                        <div v-if="data[0].purchase_order.company.gst_type === 'CGST/SGST'" class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm text-gray-700">CGST (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(cgstAmount) }}</p>
                        </div>
                        <div v-if="data[0].purchase_order.company.gst_type === 'CGST/SGST'" class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm text-gray-700">SGST (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(sgstAmount) }}</p>
                        </div>
                        <div v-if="data[0].purchase_order.company.gst_type === 'IGST'" class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm text-gray-700">IGST (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(igstAmount) }}</p>
                        </div>
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm text-gray-700">Total GST (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalGstAmount) }}</p>
                        </div>
                        <div class="inline-flex items-center justify-end w-full space-x-3 border-t pt-2">
                            <p class="font-semibold text-gray-900">Total Amount (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalAmount) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex mt-6 items-center justify-between">
            <div class="ml-auto flex items-center justify-end gap-x-6">
                <SvgLink :href="route('purchaseinvoice.index')">
                    <template #svg>
                        <button type="button" class="text-sm font-semibold leading-6 text-gray-900">
                            Cancel
                        </button>
                    </template>
                </SvgLink>
                <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    Submit
                </PrimaryButton>
            </div>
        </div>
        </form>
        </div>
    </AdminLayout>
</template>
