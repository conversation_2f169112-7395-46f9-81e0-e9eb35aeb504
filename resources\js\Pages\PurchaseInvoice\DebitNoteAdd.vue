<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import { Head, usePage ,  useForm, router } from '@inertiajs/vue3';
import { ref, computed, watch } from 'vue';

const props = defineProps({
    data: Array,
    debit_no: Object,
    selectedSerialNumbersIds: Array
});

const selectedProducts = ref([]);
const productQuantities = ref([]);
const allSelected = ref(false);

// Initialize arrays
if (props.data && props.data[0]?.purchase_order_receive_details) {
    props.data[0].purchase_order_receive_details.forEach((_, index) => {
        selectedProducts.value[index] = false;
        productQuantities.value[index] = 0;
    });
}

const form = useForm({
    organization_id: props.data[0]?.purchase_order?.organization_id || '',
    company_id: props.data[0]?.purchase_order?.company_id || '',
    purchase_invoice_id: props.data[0]?.id || '',
    debit_note_no: props.debit_no[props.data[0]?.purchase_order?.organization_id] || '',
    credit_note_number: '',
    date: new Date().toISOString().split('T')[0],
    reason: '',
    sub_total: 0,
    cgst: 0,
    sgst: 0,
    igst: 0,
    total_gst: 0,
    total_amount: 0,
    selectedProductItem: []
});

const selectAll = () => {
    if (props.data && props.data[0]?.purchase_order_receive_details) {
        props.data[0].purchase_order_receive_details.forEach((_, index) => {
            selectedProducts.value[index] = allSelected.value;
            if (allSelected.value) {
                productQuantities.value[index] = props.data[0].purchase_order_receive_details[index].receive_qty;
            } else {
                productQuantities.value[index] = 0;
            }
        });
        updateTotals();
    }
};

const updateSelection = (index) => {
    if (!selectedProducts.value[index]) {
        productQuantities.value[index] = 0;
    } else {
        productQuantities.value[index] = props.data[0].purchase_order_receive_details[index].receive_qty;
    }
    updateTotals();
};

const updateTotal = (index) => {
    updateTotals();
};

const calculateLineTotal = (index) => {
    if (!selectedProducts.value[index] || !productQuantities.value[index]) return 0;
    const detail = props.data[0].purchase_order_receive_details[index];
    return (productQuantities.value[index] * (detail.serial_numbers[0].purchase_price || 0)).toFixed(2);
};

const updateTotals = () => {
    let subTotal = 0;
    let totalGst = 0;

    if (props.data && props.data[0]?.purchase_order_receive_details) {
        props.data[0].purchase_order_receive_details.forEach((detail, index) => {
            if (selectedProducts.value[index] && productQuantities.value[index] > 0) {
                const lineTotal = productQuantities.value[index] * (detail.serial_numbers[0].purchase_price || 0);
                subTotal += lineTotal;

                // Calculate GST (assuming 18% GST)
                const gstAmount = lineTotal * detail.purchase_order[0].gst;
                totalGst += gstAmount;

            }
        });
    }

    console.log('subTotal', subTotal);

    form.sub_total = subTotal.toFixed(2);
    form.total_gst = totalGst.toFixed(2);
    form.cgst = (totalGst / 2).toFixed(2);
    form.sgst = (totalGst / 2).toFixed(2);
    form.igst = 0;
    form.total_amount = (subTotal + totalGst).toFixed(2);
};

const submit = () => {
    form.post('/debitnotesave', {
        onSuccess: () => {
            router.visit('/purchase-invoice');
        }
    });
};

const goBack = () => {
    router.visit('/purchase-invoice');
};
</script>

<template>
    <Head title="PurchaseInvoice"/>
    <AdminLayout>

        <div class="animate-top">
            <!-- Header -->
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
                <h1 class="text-xl sm:text-2xl font-semibold text-gray-900">Create Debit Note</h1>
                <div class="flex gap-3">
                    <SecondaryButton @click="goBack">
                        Back to Purchase Invoices
                    </SecondaryButton>
                </div>
            </div>

            <form @submit.prevent="submit" class="space-y-6">
                <div class="bg-white rounded-lg shadow-sm border">
                    <div class="p-4 sm:p-6">
                        <!-- Basic Information -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                            <div>
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">Debit Note Information</h2>
                                <div class="space-y-4">
                                    <div>
                                        <InputLabel for="debit_note_no" value="Debit Note No" />
                                        <TextInput
                                            id="debit_note_no"
                                            type="text"
                                            class="mt-1 block w-full"
                                            v-model="form.debit_note_no"
                                            readonly
                                        />
                                        <InputError class="mt-2" :message="form.errors.debit_note_no" />
                                    </div>

                                    <div>
                                        <InputLabel for="date" value="Date" />
                                        <TextInput
                                            id="date"
                                            type="date"
                                            class="mt-1 block w-full"
                                            v-model="form.date"
                                            required
                                        />
                                        <InputError class="mt-2" :message="form.errors.date" />
                                    </div>

                                    <div>
                                        <InputLabel for="credit_note_number" value="Credit Note Number" />
                                        <TextInput
                                            id="credit_note_number"
                                            type="text"
                                            class="mt-1 block w-full"
                                            v-model="form.credit_note_number"
                                            required
                                        />
                                        <InputError class="mt-2" :message="form.errors.credit_note_number" />
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h2 class="text-lg font-semibold text-gray-900 mb-4">Purchase Invoice Information</h2>
                                <div class="space-y-3 bg-gray-50 p-4 rounded-lg">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Invoice No:</span>
                                        <span class="font-medium">{{ data[0]?.customer_invoice_no || '-' }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Company:</span>
                                        <span class="font-medium">{{ data[0]?.purchase_order?.company?.name || '-' }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Date:</span>
                                        <span class="font-medium">{{ data[0]?.customer_invoice_date || '-' }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Total Amount:</span>
                                        <span class="font-medium">₹{{ data[0]?.total_amount || 0 }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Reason -->
                        <div class="mb-8">
                            <InputLabel for="reason" value="Reason for Debit Note" />
                            <textarea
                                id="reason"
                                class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm"
                                rows="4"
                                v-model="form.reason"
                                required
                                placeholder="Enter the reason for creating this debit note..."
                            ></textarea>
                            <InputError class="mt-2" :message="form.errors.reason" />
                        </div>

                        <!-- Product Selection -->
                        <div class="mb-8">
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">Select Products</h2>

                            <!-- Desktop Table -->
                            <div class="hidden lg:block overflow-x-auto">
                                <table class="w-full text-sm text-left text-gray-500">
                                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3">
                                                <input type="checkbox" @change="selectAll" v-model="allSelected" class="rounded">
                                            </th>
                                            <th class="px-6 py-3">Product</th>
                                            <th class="px-6 py-3">Serial Number</th>
                                            <th class="px-6 py-3">Available Qty</th>
                                            <th class="px-6 py-3">Debit Qty</th>
                                            <th class="px-6 py-3">Rate</th>
                                            <th class="px-6 py-3">Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(detail, index) in data[0]?.purchase_order_receive_details" :key="detail.id" class="bg-white border-b">
                                            <td class="px-6 py-4">
                                                <input
                                                    type="checkbox"
                                                    v-model="selectedProducts[index]"
                                                    @change="updateSelection(index)"
                                                    class="rounded"
                                                >
                                            </td>
                                            <td class="px-6 py-4 font-medium text-gray-900">
                                                {{ detail.product?.name || '-' }}
                                            </td>
                                            <td class="px-6 py-4">
                                                {{ detail.serial_numbers[0].unique_id || '-' }}
                                            </td>
                                            <td class="px-6 py-4">{{ detail.receive_qty || 0 }}</td>
                                            <td class="px-6 py-4">
                                                <TextInput
                                                    type="number"
                                                    min="0"
                                                    :max="detail.receive_qty"
                                                    v-model="productQuantities[index]"
                                                    @input="updateTotal(index)"
                                                    :disabled="!selectedProducts[index]"
                                                    class="w-20"
                                                />
                                            </td>
                                            <td class="px-6 py-4">₹{{ detail.serial_numbers[0].purchase_price || 0 }}</td>
                                            <td class="px-6 py-4">₹{{ calculateLineTotal(index) }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Mobile Cards -->
                            <div class="lg:hidden space-y-4">
                                <div v-for="(detail, index) in data[0]?.purchase_order_receive_details" :key="detail.id"
                                     class="bg-gray-50 rounded-lg p-4">
                                    <div class="flex items-start justify-between mb-3">
                                        <input
                                            type="checkbox"
                                            v-model="selectedProducts[index]"
                                            @change="updateSelection(index)"
                                            class="rounded mt-1"
                                        >
                                        <div class="flex-1 ml-3">
                                            <div class="font-medium text-gray-900">{{ detail.product?.name || '-' }}</div>
                                            <div class="text-sm text-gray-500">{{ detail.serial_numbers?.serial_number || '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-2 gap-3 text-sm">
                                        <div>
                                            <span class="text-gray-500">Available:</span>
                                            <span class="ml-1">{{ detail.receive_qty || 0 }}</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">Rate:</span>
                                            <span class="ml-1">₹{{ detail.purchase_price || 0 }}</span>
                                        </div>
                                        <div>
                                            <label class="text-gray-500">Debit Qty:</label>
                                            <TextInput
                                                type="number"
                                                min="0"
                                                :max="detail.receive_qty"
                                                v-model="productQuantities[index]"
                                                @input="updateTotal(index)"
                                                :disabled="!selectedProducts[index]"
                                                class="w-full mt-1"
                                            />
                                        </div>
                                        <div>
                                            <span class="text-gray-500">Total:</span>
                                            <span class="ml-1 font-medium">₹{{ calculateLineTotal(index) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Amount Summary -->
                        <div class="border-t pt-6">
                            <h2 class="text-lg font-semibold text-gray-900 mb-4">Amount Summary</h2>
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Sub Total:</span>
                                        <span class="font-medium">₹{{ form.sub_total }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">CGST:</span>
                                        <span class="font-medium">₹{{ form.cgst }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">SGST:</span>
                                        <span class="font-medium">₹{{ form.sgst }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">IGST:</span>
                                        <span class="font-medium">₹{{ form.igst }}</span>
                                    </div>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Total GST:</span>
                                        <span class="font-medium">₹{{ form.total_gst }}</span>
                                    </div>
                                    <div class="flex justify-between border-t pt-3">
                                        <span class="text-lg font-semibold text-gray-900">Total Amount:</span>
                                        <span class="text-lg font-bold text-indigo-600">₹{{ form.total_amount }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end pt-6 border-t">
                            <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                                Create Debit Note
                            </PrimaryButton>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>


