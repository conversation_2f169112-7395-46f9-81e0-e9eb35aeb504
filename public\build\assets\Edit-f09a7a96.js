import{_ as o}from"./AdminLayout-36b0d46a.js";import i from"./DeleteUserForm-9896a909.js";import m from"./UpdatePasswordForm-89bec534.js";import r from"./UpdateProfileInformationForm-be124ab7.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-4c3f0163.js";import"./DangerButton-b3c50a37.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-64c2d172.js";import"./InputLabel-d6414ecf.js";import"./Modal-61735c0a.js";/* empty css                                                              */import"./SecondaryButton-d521cdbf.js";import"./TextInput-e8957d69.js";import"./PrimaryButton-353715d1.js";import"./TextArea-259ebf66.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
