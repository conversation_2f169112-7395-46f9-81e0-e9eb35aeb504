import{o as m,c as p,b as t,t as s,f as S,K as B,j as n,r as w,a as d,u as R,w as b,F as v,Z as T,d as A,g as P,i as N}from"./app-4f4c883b.js";import{_ as I}from"./AdminLayout-d9d2bc31.js";import{_ as q}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as F}from"./SecondaryButton-69637431.js";import{_ as O}from"./CreateButton-7995b8ff.js";import{M as V}from"./Modal-85d770f4.js";import{_ as U}from"./FileViewer-ea07265d.js";/* empty css                                                              */const G={props:{data:{type:Array,required:!0},colspan:{type:Number,default:4},message:{type:String,default:"No Records Found..."}},computed:{noRecords(){return this.data.length===0}}},L={key:0},W=["colspan"];function E(a,l,_,h,k,f){return f.noRecords?(m(),p("tr",L,[t("td",{colspan:_.colspan,class:"py-4 text-center text-sm text-red-500 font-semibold"},s(_.message),9,W)])):S("",!0)}const Q=q(G,[["render",E]]),z={class:"animate-top h-screen"},H={class:"sm:flex sm:items-center"},K=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Purchase Invoice")],-1),Z={class:"flex items-center space-x-4"},J={class:"text-sm font-semibold text-gray-900"},X={class:"flex justify-end w-20"},Y={class:"mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},tt={class:"inline-flex items-start space-x-6 justify-start w-full"},st={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},et={class:"inline-flex items-center justify-start w-full space-x-2"},at=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Company Name:",-1),ot={class:"text-sm leading-6 text-gray-700"},lt={class:"inline-flex items-center justify-start w-full space-x-2"},ct=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"GST No:",-1),rt={class:"text-sm leading-6 text-gray-700"},it={class:"inline-flex items-center justify-start w-full space-x-2"},nt=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Invoice No:",-1),dt={class:"text-sm leading-6 text-gray-700"},mt={class:"inline-flex items-center justify-start w-full space-x-2"},pt=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Invoice Date:",-1),_t={class:"text-sm leading-6 text-gray-700"},ut={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},xt={class:"inline-flex items-center justify-start w-full space-x-2"},ht=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Number:",-1),ft={class:"text-sm leading-6 text-gray-700"},yt={class:"inline-flex items-center justify-start w-full space-x-2"},gt=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Date:",-1),wt={class:"text-sm leading-6 text-gray-700"},bt={class:"inline-flex items-center justify-start w-full space-x-2"},vt=t("p",{class:"text-sm font-semibold text-gray-900"},"PO Received By",-1),kt={class:"text-sm leading-6 text-gray-700"},jt={class:"flow-root"},Pt={class:"-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},Nt={class:"inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8 overflow-hidden"},St={class:"p-1 mt-4 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},Dt={class:"min-w-full divide-y divide-gray-300"},Mt=t("thead",{class:"bg-gray-50 border"},[t("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-10"},[t("th",{scope:"col",class:"py-3.5 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Product Code"),t("th",{scope:"col",class:"py-3.5 sm:col-span-3 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Product Description"),t("th",{scope:"col",class:"py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"HSN"),t("th",{scope:"col",class:"py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty"),t("th",{scope:"col",class:"py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"GST"),t("th",{scope:"col",class:"py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price"),t("th",{scope:"col",class:"py-3.5 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Action")])],-1),$t={class:"divide-y divide-gray-300 bg-white"},Ct={class:"whitespace-nowrap sm:col-span-2 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6"},Bt={class:"whitespace-nowrap sm:col-span-3 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6"},Rt={class:"whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6"},Tt={class:"whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6"},At={class:"whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6"},It={class:"whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6"},qt={class:"whitespace-nowrap sm:col-span-1 py-2 pl-4 pr-3 text-sm text-gray-700 sm:pl-6"},Ft=["onClick"],Ot=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),Vt=[Ot],Ut={key:0,class:"divide-y divide-gray-300 sm:col-span-10 product-details border mx-6 mb-4"},Gt=t("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-5 bg-gray-50"},[t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Batch"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Expiry Date"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"MRP (₹)"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Purchase Price (₹)"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Qty")],-1),Lt={class:"divide-y divide-gray-300 bg-white grid grid-cols-1"},Wt={class:"py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},Et={class:"py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},Qt={class:"py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},zt={class:"py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},Ht={class:"py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},Kt={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Zt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Jt={class:"sm:col-span-3 space-y-2"},Xt={class:"w-full"},Yt=t("p",{class:"text-sm font-semibold text-gray-700"},"Note:",-1),ts={class:"text-sm text-gray-900 break-words"},ss={class:"sm:col-span-3"},es={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},as={class:"inline-flex items-center justify-end w-full space-x-3"},os=t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1),ls={class:"text-base font-semibold text-gray-900 w-32"},cs={class:"inline-flex items-center justify-end w-full space-x-3"},rs=t("p",{class:"text-sm font-semibold text-gray-700"},"Total GST (₹):",-1),is={class:"text-base font-semibold text-gray-900 w-32"},ns={class:"inline-flex items-center justify-end w-full space-x-3"},ds=t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1),ms={class:"text-base font-semibold text-gray-900 w-32"},ps={class:"p-6"},_s={class:"mt-6 px-4 flex justify-end"},vs={__name:"View",props:["data"],setup(a){const l=a;B().props.data[0],n(()=>l.data[0].purchase_order_detail?l.data[0].purchase_order_detail.reduce((e,o)=>e+o.total_amount,0):0),n(()=>l.data[0].purchase_order_detail?l.data[0].purchase_order_detail.reduce((e,o)=>e+o.qty,0):0),n(()=>l.data[0].purchase_order_detail?l.data[0].purchase_order_detail.reduce((e,o)=>e+o.receive_qty,0):0),n(()=>l.data[0].purchase_order_detail?l.data[0].purchase_order_detail.reduce((e,o)=>e+o.total_price,0):0),n(()=>l.data[0].purchase_order_detail?l.data[0].purchase_order_detail.reduce((e,o)=>e+o.total_gst_amount,0):0),n(()=>l.receivedOrder?l.receivedOrder.reduce((e,o)=>{const r=o.total_amount;return e+r},0):0);const _=e=>{const o=new Date(e),r={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",r)},h=w([]),k=e=>{h.value[e]=!h.value[e]},f=w(!1),D=w(null),M=w("custom"),j=()=>{f.value=!1},x=e=>{let o=e.toFixed(2).toString(),[r,y]=o.split("."),u=r.substring(r.length-3),c=r.substring(0,r.length-3);return c!==""&&(u=","+u),`${c.replace(/\B(?=(\d{2})+(?!\d))/g,",")+u}.${y}`},$=n(()=>new URLSearchParams(window.location.search).get("source")==="dashboard"?route("dashboard"):route("purchaseinvoice.index"));return(e,o)=>(m(),p(v,null,[d(R(T),{title:"Purchase Invoice"}),d(I,null,{default:b(()=>{var r,y,u;return[t("div",z,[t("form",{onSubmit:o[0]||(o[0]=A((...c)=>e.submit&&e.submit(...c),["prevent"])),class:""},[t("div",H,[K,t("div",Z,[t("div",null,[t("p",J,s(a.data[0].purchase_order.organization.name),1)]),t("div",X,[d(O,{href:$.value},{default:b(()=>[P(" Back ")]),_:1},8,["href"])])])]),t("div",Y,[t("div",tt,[t("div",st,[t("div",et,[at,t("p",ot,s(a.data[0].purchase_order.company.name??"-"),1)]),t("div",lt,[ct,t("p",rt,s(a.data[0].purchase_order.company.gst_no??"-"),1)]),t("div",it,[nt,t("p",dt,s(a.data[0].customer_invoice_no??"-"),1)]),t("div",mt,[pt,t("p",_t,s(_(a.data[0].customer_invoice_date)??"-"),1)])]),t("div",ut,[t("div",xt,[ht,t("p",ft,s(a.data[0].purchase_order.po_number??"-"),1)]),t("div",yt,[gt,t("p",wt,s(_(a.data[0].purchase_order.date)??"-"),1)]),t("div",bt,[vt,t("p",kt,s(a.data[0].users.first_name)+" "+s(a.data[0].users.last_name),1)])])])]),t("div",jt,[t("div",Pt,[t("div",Nt,[t("div",St,[t("table",Dt,[Mt,t("tbody",$t,[d(Q,{data:a.data[0].purchase_order_receive_details,colspan:7},null,8,["data"]),(m(!0),p(v,null,N(a.data[0].purchase_order_receive_details,(c,g)=>(m(),p("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-10",key:g},[t("td",Ct,s(c.product.item_code??"-"),1),t("td",Bt,s(c.product.name??"-"),1),t("td",Rt,s(c.product.hsn_code??"-"),1),t("td",Tt,s(c.receive_qty??"-"),1),t("td",At,s(c.purchase_order_detail.gst??"-"),1),t("td",It,s(c.serial_numbers[0].purchase_price??"-"),1),t("td",qt,[t("button",{onClick:i=>k(g)},Vt,8,Ft)]),h.value[g]&&c.serial_numbers.length!=0?(m(),p("div",Ut,[Gt,t("tbody",Lt,[(m(!0),p(v,null,N(c.serial_numbers,(i,C)=>(m(),p("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-5",key:C},[t("td",Wt,s(i.batch??"-"),1),t("td",Et,s(i.expiry_date!=null?_(i.expiry_date):"-"),1),t("td",Qt,s(i.mrp?x(i.mrp):"-"),1),t("td",zt,s(i.purchase_price?x(i.purchase_price):"-"),1),t("td",Ht,s(i.receive_qty??"-"),1)]))),128))])])):S("",!0)]))),128))])])])])])]),t("div",Kt,[t("div",Zt,[t("div",Jt,[t("div",Xt,[Yt,t("p",ts,s(((y=(r=a.data[0])==null?void 0:r.purchase_order_receives)==null?void 0:y.note)||((u=a.data[0])==null?void 0:u.note)||"-"),1)])]),t("div",ss,[t("div",es,[t("div",as,[os,t("p",ls,s(x(a.data[0].total_price)),1)]),t("div",cs,[rs,t("p",is,s(x(a.data[0].total_gst_amount)),1)]),t("div",ns,[ds,t("p",ms,s(x(a.data[0].total_amount)),1)])])])])])],32)]),d(V,{show:f.value,onClose:j,maxWidth:M.value},{default:b(()=>[t("div",ps,[d(U,{fileUrl:e.file+D.value},null,8,["fileUrl"]),t("div",_s,[d(F,{onClick:j},{default:b(()=>[P(" Cancel ")]),_:1})])])]),_:1},8,["show","maxWidth"])]}),_:1})],64))}};export{vs as default};
