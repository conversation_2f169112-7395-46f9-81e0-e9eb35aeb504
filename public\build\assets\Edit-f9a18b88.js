import{K as et,r as x,C as Ft,j as U,o as u,c as m,a as r,u as a,w as y,F as z,Z as It,b as t,t as p,k as st,v as ot,d as Pt,e as lt,f as g,n as f,i as at,g as V,s as Tt,x as $t}from"./app-b7a94f67.js";import{_ as Dt,a as Ut}from"./AdminLayout-0f1fdf67.js";import{_ as B}from"./InputError-86b88c86.js";import{_ as k}from"./InputLabel-11b5d690.js";import{P as nt}from"./PrimaryButton-4ffecd1c.js";import{_ as b}from"./TextInput-fea73171.js";import{_ as Gt}from"./TextArea-500c5ac8.js";import{_ as $}from"./SearchableDropdown-711fb977.js";import{D as dt}from"./DangerButton-a612a79a.js";import{_ as L}from"./SecondaryButton-c893313c.js";import{M as E}from"./Modal-e44dcdf0.js";import{_ as qt}from"./MultipleFileUpload-e62c96d8.js";import{_ as Mt}from"./FileViewer-d3655eec.js";import{u as Nt}from"./index-5a4eda7d.js";import{_ as At}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const c=w=>(Tt("data-v-99f904f6"),w=w(),$t(),w),Ot={class:"animate-top"},jt={class:"sm:flex sm:items-center"},zt=c(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Purchase Order")],-1)),Bt={class:"w-auto"},Lt={class:"flex space-x-6 items-center"},Et={class:"text-sm font-semibold text-gray-900"},Ht={class:"flex space-x-2 items-center"},Qt=c(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6"},"PO Number:",-1)),Wt={class:"text-sm font-semibold text-gray-900 leading-6"},Rt={class:"flex space-x-2 items-center"},Yt=c(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Date :",-1)),Kt=["onSubmit"],Zt={class:"shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},Jt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Xt={class:"sm:col-span-4"},te={class:"sm:col-span-4"},ee={class:"sm:col-span-4"},se={class:"relative mt-2"},oe={class:"sm:col-span-4"},le={class:"relative mt-2"},ae={class:"sm:col-span-4"},ne={class:"relative mt-2"},de={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border w-full"},re={class:"overflow-x-auto w-full"},ie={class:"overflow-x-auto divide-y divide-gray-300",style:{"margin-bottom":"160px"}},ce=c(()=>t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Product",-1)),ue=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"HSN",-1)),me=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Pkg Of Qty",-1)),_e=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"QTY",-1)),pe=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),ge=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Rec. QTY",-1)),he=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),ye={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ve={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},xe={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},fe={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},we={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text- text-sm font-semibold text-gray-900"},be=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),ke=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),Ce=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),Ve=c(()=>t("th",{scope:"col"},null,-1)),Se={class:"divide-y divide-gray-300 bg-white"},Fe={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-80"},Ie={class:"relative mt-2"},Pe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Te={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},$e={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},De={key:0,class:"text-red-500 text-xs"},Ue={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Ge={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},qe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Me={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ne={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ae={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Oe={key:3,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},je={key:4,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ze={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Be={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Le={class:"whitespace-nowrap px-3 py-3 flex space-x-2 min-w-36"},Ee={class:"px-3 py-3 text-sm text-gray-900"},He=["onClick"],Qe=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),We=[Qe],Re={class:"flex items-center justify-between"},Ye={class:"ml-auto flex items-center justify-end gap-x-6"},Ke={key:0,class:"bg-white p-1 shadow sm:rounded-lg border"},Ze={class:"min-w-full divide-y divide-gray-300"},Je=c(()=>t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"UPLOADED DOCUMENT"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1)),Xe={class:"divide-y divide-gray-300 bg-white"},ts={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},es={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},ss=["onClick"],os=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),ls=[os],as=["onClick"],ns=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),ds=[ns],rs=["onClick"],is=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),cs=[is],us={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},ms={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},_s={class:"sm:col-span-3 space-y-2"},ps={class:"flex space-x-4"},gs={class:"w-full"},hs={class:"w-full"},ys={class:"relative mt-2"},vs={class:"sm:col-span-3"},xs={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},fs={class:"inline-flex items-center justify-end w-full space-x-3"},ws=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),bs={class:"text-base font-semibold text-gray-900 w-32"},ks={class:"inline-flex items-center justify-end w-full space-x-3"},Cs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),Vs={class:"w-40"},Ss={class:"inline-flex items-center justify-end w-full space-x-3"},Fs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),Is={class:"text-base font-semibold text-gray-900 w-w-32"},Ps={class:"inline-flex items-center justify-end w-full space-x-3"},Ts=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total GST (₹):",-1)),$s={class:"text-base font-semibold text-gray-900 w-32"},Ds={class:"inline-flex items-center justify-end w-full space-x-3"},Us=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),Gs={class:"text-base font-semibold text-gray-900 w-32"},qs={class:"flex items-center justify-between"},Ms={class:"ml-auto flex items-center justify-end gap-x-6"},Ns=c(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),As={class:"p-6"},Os=c(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this product? ",-1)),js={class:"mt-6 flex justify-end"},zs={class:"p-6"},Bs=c(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this document? ",-1)),Ls={class:"mt-6 flex justify-end"},Es={class:"p-6"},Hs={class:"mt-6 px-4 flex justify-end"},Qs={__name:"Edit",props:["companies","category","type","po_number","organization","filepath","products","salesuser"],setup(w){const rt=w,H=et().props.filepath.view,G=x([]),i=et().props.data[0],it=rt.products.filter(o=>o.company_id===i.company_id);G.value=it;const h=x(i.company.gst_type),s=Nt("post","/companypo",{note:i.note,po_date:i.date,selectedProductItem:[],company_id:i.company_id,sales_user_id:i.sales_user_id,total_amount:i.total_amount,category:i.category,type:i.type,cgst:i.cgst,sgst:i.sgst,igst:i.igst,total_gst:i.total_gst,sub_total:i.sub_total,po_number:i.po_number,purchase_order_id:i.id,total_discount:i.total_discount,overall_discount:i.overall_discount,document:i.documents,organization_id:i.organization_id,sales_order_no:i.sales_order_no,sales_order_date:i.sales_order_date}),ct=()=>{s.total_amount=W.value,s.total_discount=Y.value,s.sub_total=R.value,s.cgst=h.value=="CGST/SGST"?I.value/2:"0",s.sgst=h.value=="CGST/SGST"?I.value/2:"0",s.igst=h.value=="IGST"?I.value:"0",s.total_gst=I.value,s.selectedProductItem=_.value,s.submit({preserveScroll:!0,onSuccess:()=>s.reset()})},ut=(o,l)=>{s.organization_id=o,s.errors.organization_id=null},mt=(o,l)=>{s.sales_user_id=o},_=x([{product_id:"",editmode:"",item_code:"",hsn_code:"",pkg_of_qty:"",qty:"",receive_qty:"",price:"",gst:"",sgst:"",discount:"",discount_amount:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",purchase_order_detail_id:"",description:""}]);Ft(()=>{_.value=i.purchase_order_detail.map(o=>({product_id:o.product_id,editmode:"editMode",receive_qty:o.receive_qty,purchase_order_detail_id:o.id,hsn_code:o.product.hsn_code,item_code:o.product.item_code,pkg_of_qty:o.pkg_of_qty,qty:o.qty,description:o.description,price:parseFloat(o.price).toFixed(2),total_price:parseFloat(o.total_price).toFixed(2),gst:parseFloat(o.gst).toFixed(2),sgst:parseFloat(o.gst/2).toFixed(2),gst_amount:parseFloat(o.gst_amount).toFixed(2),total_gst_amount:parseFloat(o.total_gst_amount).toFixed(2),total_amount:parseFloat(o.total_amount).toFixed(2),discount:parseFloat(o.discount).toFixed(2),discount_amount:parseFloat(o.discount_amount).toFixed(2)??"0"}))});const _t=(o,l,e,d)=>{const n=G.value.find(v=>v.id===o);n&&(_.value[e].product_id=n.id,_.value[e].price=parseFloat(n.price).toFixed(2),_.value[e].hsn_code=n.hsn_code,_.value[e].item_code=n.item_code,_.value[e].gst=parseFloat(n.gst).toFixed(2),_.value[e].sgst=parseFloat(n.gst/2).toFixed(2),_.value[e].discount="0.00",s.errors[`selectedProductItem.${e}.product_id`]=null,s.errors[`selectedProductItem.${e}.price`]=null,C(d))},pt=()=>{_.value.push({product_id:"",item_code:"",receive_qty:"",hsn_code:"",pkg_of_qty:"",qty:"",price:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",purchase_order_detail_id:"",description:""})},q=x(!1),Q=x(null),gt=x(null),M=()=>{q.value=!1},ht=()=>{s.get(route("removeproduct",{id:Q.value,model:"PurchaseOrderDetail"}),{onSuccess:()=>{M(),_.value.splice(index,1)}})},yt=(o,l)=>{l!==void 0&&l!=""?(Q.value=l,gt.value=o,q.value=!0):_.value.splice(o,1)},vt=(o,l)=>{const e=parseFloat(o.price),d=parseFloat(o.discount)||0,n=h.value=="IGST"?parseFloat(o.gst):parseFloat(o.sgst*2),v=parseFloat(o.qty);let F=0,P=0;d>0?F=e*v:F=e*v*(1+n/100);const T=F*(d/100)||0,X=e*1*(n/100),j=(e*v-T)*(n/100);d>0?P=F-T+j:P=F-T;const tt=e*v;return o.total_price=isNaN(tt)?"":parseFloat(tt).toFixed(2),o.gst_amount=isNaN(X)?"":parseFloat(X).toFixed(2),o.total_gst_amount=isNaN(j)?"":parseFloat(j).toFixed(2),o.discount_amount=isNaN(T)?"":parseFloat(T).toFixed(2),isNaN(P)?"":parseFloat(P).toFixed(2)},C=(o,l)=>{o.total_amount=vt(o)},W=U(()=>{const o=Math.round(_.value.reduce((e,d)=>e+(d.total_amount?parseFloat(d.total_amount):0),0)),l=s.overall_discount?parseFloat(s.overall_discount):0;return o-l}),I=U(()=>_.value.reduce((o,l)=>o+(l.total_gst_amount?parseFloat(l.total_gst_amount):0),0)),R=U(()=>_.value.reduce((o,l)=>o+(l.total_price?parseFloat(l.total_price):0),0)),Y=U(()=>{const o=_.value.reduce((e,d)=>e+(d.discount_amount?parseFloat(d.discount_amount):0),0),l=s.overall_discount?parseFloat(s.overall_discount):0;return o+l}),S=o=>{s.errors[o]=null},xt=o=>{s.document=o},N=x(!1),K=x(null),ft=o=>{K.value=o,N.value=!0},wt=()=>{s.get(route("removedocument",{id:K.value,name:"purchaseOrderDocument"}),{onSuccess:()=>{A()}})},A=()=>{N.value=!1},O=x(!1),Z=x(null),bt=x("custom"),kt=o=>{Z.value=o,O.value=!0},J=()=>{O.value=!1},Ct=o=>{const l=window.location.origin+H+o,e=document.createElement("a");e.href=l,e.setAttribute("download",o),document.body.appendChild(e),e.click(),document.body.removeChild(e)},D=o=>{let l=o.toFixed(2).toString(),[e,d]=l.split("."),n=e.substring(e.length-3),v=e.substring(0,e.length-3);return v!==""&&(n=","+n),`${v.replace(/\B(?=(\d{2})+(?!\d))/g,",")+n}.${d}`},Vt=(o,l)=>{s.category=o,s.errors.category=null},St=(o,l)=>{s.type=o,s.errors.type=null};return(o,l)=>(u(),m(z,null,[r(a(It),{title:"Comapany PO"}),r(Dt,null,{default:y(()=>[t("div",Ot,[t("div",jt,[zt,t("div",Bt,[t("div",Lt,[t("p",Et,p(a(i).company.name??"-"),1),t("div",null,[t("div",Ht,[Qt,t("span",Wt,p(a(i).po_number),1)]),t("div",Rt,[Yt,st(t("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":l[0]||(l[0]=e=>a(s).po_date=e),onChange:l[1]||(l[1]=e=>a(s).validate("date"))},null,544),[[ot,a(s).po_date]])])])])])]),t("form",{onSubmit:Pt(ct,["prevent"]),class:"mt-6 space-y-6"},[t("div",Zt,[t("div",Jt,[t("div",Xt,[r(k,{for:"sales_order_no",value:"Sales Order No"}),r(b,{id:"sales_order_no",type:"text",modelValue:a(s).sales_order_no,"onUpdate:modelValue":l[2]||(l[2]=e=>a(s).sales_order_no=e),onChange:l[3]||(l[3]=e=>a(s).validate("sales_order_no")),maxLength:"30"},null,8,["modelValue"]),r(B,{class:"",message:a(s).errors.sales_order_no},null,8,["message"])]),t("div",te,[r(k,{for:"sales_order_date",value:"Sales Order Date"}),st(t("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date",onChange:l[4]||(l[4]=e=>a(s).validate("sales_order_date")),"onUpdate:modelValue":l[5]||(l[5]=e=>a(s).sales_order_date=e)},null,544),[[ot,a(s).sales_order_date]]),a(s).invalid("sales_order_date")?(u(),lt(B,{key:0,class:"",message:a(s).errors.sales_order_date},null,8,["message"])):g("",!0)]),t("div",ee,[r(k,{for:"organization_id",value:"Organization"}),t("div",se,[r($,{options:w.organization,modelValue:a(s).organization_id,"onUpdate:modelValue":l[6]||(l[6]=e=>a(s).organization_id=e),onOnchange:ut,class:f({"error rounded-md":a(s).errors.organization_id})},null,8,["options","modelValue","class"])])]),t("div",oe,[r(k,{for:"category",value:"Category"}),t("div",le,[r($,{options:w.category,modelValue:a(s).category,"onUpdate:modelValue":l[7]||(l[7]=e=>a(s).category=e),onOnchange:Vt,class:f({"error rounded-md":a(s).errors.category})},null,8,["options","modelValue","class"])])]),t("div",ae,[r(k,{for:"type",value:"Purchase Type"}),t("div",ne,[r($,{options:w.type,modelValue:a(s).type,"onUpdate:modelValue":l[8]||(l[8]=e=>a(s).type=e),onOnchange:St,class:f({"error rounded-md":a(s).errors.type})},null,8,["options","modelValue","class"])])])])]),t("div",de,[t("div",re,[t("table",ie,[t("thead",null,[t("tr",null,[ce,ue,me,_e,pe,ge,he,h.value=="IGST"?(u(),m("th",ye,"IGST (%)")):g("",!0),h.value=="IGST"?(u(),m("th",ve,"IGST (₹)")):g("",!0),h.value=="CGST/SGST"?(u(),m("th",xe,"CGST (%)")):g("",!0),h.value=="CGST/SGST"?(u(),m("th",fe,"SGST (%)")):g("",!0),h.value=="CGST/SGST"?(u(),m("th",we,"Total GST (₹)")):g("",!0),be,ke,Ce,Ve])]),t("tbody",Se,[(u(!0),m(z,null,at(_.value,(e,d)=>(u(),m("tr",{key:d},[t("td",Fe,[t("div",Ie,[r($,{options:G.value,modelValue:e.product_id,"onUpdate:modelValue":n=>e.product_id=n,onOnchange:(n,v)=>_t(n,v,d,e),onChange:l[9]||(l[9]=n=>a(s).validate("product_id")),class:f({"error rounded-md":a(s).errors[`selectedProductItem.${d}.product_id`]}),editMode:e.editmode},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class","editMode"])])]),t("td",Pe,p(e.hsn_code??"-"),1),t("td",Te,[r(b,{id:"pkg_of_qty",type:"text",numeric:!0,modelValue:e.pkg_of_qty,"onUpdate:modelValue":n=>e.pkg_of_qty=n},null,8,["modelValue","onUpdate:modelValue"])]),t("td",$e,[r(b,{id:"qty",type:"text",numeric:!0,modelValue:e.qty,"onUpdate:modelValue":n=>e.qty=n,onInput:n=>C(e,d),onChange:n=>S("selectedProductItem."+d+".qty"),class:f({error:a(s).errors[`selectedProductItem.${d}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"]),a(s).errors[`selectedProductItem.${d}.qty1`]?(u(),m("p",De,p(a(s).errors[`selectedProductItem.${d}.qty1`]),1)):g("",!0)]),t("td",Ue,[r(b,{id:"price",type:"text",modelValue:e.price,"onUpdate:modelValue":n=>e.price=n,onInput:n=>C(e,d),onChange:n=>S("selectedProductItem."+d+".price"),class:f({error:a(s).errors[`selectedProductItem.${d}.price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Ge,p(e.receive_qty),1),t("td",qe,p(e.total_price),1),h.value=="IGST"?(u(),m("td",Me,[r(b,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":n=>e.gst=n,onInput:n=>C(e,d),onChange:n=>S("selectedProductItem."+d+".gst"),class:f({error:a(s).errors[`selectedProductItem.${d}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):g("",!0),h.value=="CGST/SGST"?(u(),m("td",Ne,[r(b,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":n=>e.sgst=n,onInput:n=>C(e,d),onChange:n=>S("selectedProductItem."+d+".gst"),class:f({error:a(s).errors[`selectedProductItem.${d}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):g("",!0),h.value=="CGST/SGST"?(u(),m("td",Ae,[r(b,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":n=>e.sgst=n,onInput:n=>C(e,d),onChange:n=>S("selectedProductItem."+d+".gst"),class:f({error:a(s).errors[`selectedProductItem.${d}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):g("",!0),h.value=="IGST"?(u(),m("td",Oe,p(e.total_gst_amount),1)):g("",!0),h.value=="CGST/SGST"?(u(),m("td",je,p(e.total_gst_amount),1)):g("",!0),t("td",ze,[r(b,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":n=>e.discount=n,onInput:n=>C(e,d),onChange:n=>S("selectedProductItem."+d+".discount"),class:f({error:a(s).errors[`selectedProductItem.${d}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Be,p(e.discount_amount),1),t("td",Le,[t("div",Ee,p(e.total_amount),1)]),t("td",null,[e.receive_qty==0?(u(),m("button",{key:0,type:"button",class:"mt-1",onClick:n=>yt(d,e.purchase_order_detail_id)},We,8,He)):g("",!0)])]))),128))])])]),t("div",Re,[t("div",Ye,[r(nt,{onClick:pt,type:"button"},{default:y(()=>[V("Add Product")]),_:1})])])]),a(i).documents&&a(i).documents.length>0?(u(),m("div",Ke,[t("table",Ze,[Je,t("tbody",Xe,[(u(!0),m(z,null,at(a(i).documents,(e,d)=>(u(),m("tr",{key:a(i).id,class:""},[t("td",ts,p(e.orignal_name),1),t("td",es,[t("button",{type:"button",onClick:n=>ft(e.id)},ls,8,ss),t("button",{type:"button",onClick:n=>kt(e.name)},ds,8,as),t("button",{type:"button",onClick:n=>Ct(e.name)},cs,8,rs)])]))),128))])])])):g("",!0),t("div",us,[t("div",ms,[t("div",_s,[t("div",ps,[t("div",gs,[r(k,{for:"note",value:"Upload Documents"}),r(qt,{inputId:"document",inputName:"document",uploadedFiles:a(s).document,onFiles:xt},null,8,["uploadedFiles"])]),t("div",hs,[r(k,{for:"company_name",value:"Person Name"}),t("div",ys,[r($,{options:w.salesuser,modelValue:a(s).sales_user_id,"onUpdate:modelValue":l[10]||(l[10]=e=>a(s).sales_user_id=e),onOnchange:mt,class:f({"error rounded-md":a(s).errors.sales_user_id})},null,8,["options","modelValue","class"])])])]),t("div",null,[r(k,{for:"note",value:"Note"}),r(Gt,{id:"note",type:"text",modelValue:a(s).note,"onUpdate:modelValue":l[11]||(l[11]=e=>a(s).note=e),onChange:l[12]||(l[12]=e=>a(s).validate("note"))},null,8,["modelValue"]),a(s).invalid("note")?(u(),lt(B,{key:0,class:"",message:a(s).errors.note},null,8,["message"])):g("",!0)])]),t("div",vs,[t("div",xs,[t("div",fs,[ws,t("p",bs,p(D(R.value)),1)]),t("div",ks,[Cs,t("div",Vs,[r(b,{id:"overall_discount",type:"text",modelValue:a(s).overall_discount,"onUpdate:modelValue":l[13]||(l[13]=e=>a(s).overall_discount=e)},null,8,["modelValue"])])]),t("div",Ss,[Fs,t("p",Is,p(D(Y.value)),1)]),t("div",Ps,[Ts,t("p",$s,p(D(I.value)),1)]),t("div",Ds,[Us,t("p",Gs,p(D(W.value)),1)])])])])]),t("div",qs,[t("div",Ms,[r(Ut,{href:o.route("companypo.index")},{svg:y(()=>[Ns]),_:1},8,["href"]),r(nt,{disabled:a(s).processing},{default:y(()=>[V("Submit")]),_:1},8,["disabled"])])])],40,Kt)]),r(E,{show:q.value,onClose:M},{default:y(()=>[t("div",As,[Os,t("div",js,[r(L,{onClick:M},{default:y(()=>[V(" Cancel ")]),_:1}),r(dt,{class:"ml-3",onClick:ht},{default:y(()=>[V(" Delete ")]),_:1})])])]),_:1},8,["show"]),r(E,{show:N.value,onClose:A},{default:y(()=>[t("div",zs,[Bs,t("div",Ls,[r(L,{onClick:A},{default:y(()=>[V(" Cancel ")]),_:1}),r(dt,{class:"ml-3",onClick:wt},{default:y(()=>[V(" Delete ")]),_:1})])])]),_:1},8,["show"]),r(E,{show:O.value,onClose:J,maxWidth:bt.value},{default:y(()=>[t("div",Es,[r(Mt,{fileUrl:a(H)+Z.value},null,8,["fileUrl"]),t("div",Hs,[r(L,{onClick:J},{default:y(()=>[V(" Cancel ")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},co=At(Qs,[["__scopeId","data-v-99f904f6"]]);export{co as default};
