import{h as L,r as g,o as p,c as x,a as n,u as k,w as T,F as z,Z as j,b as t,g as F,k as O,v as V,n as M,i as P,e as q,f as G,t as h}from"./app-6cdaf2bc.js";import{_ as X}from"./AdminLayout-b73e8538.js";import{_ as H}from"./CreateButton-8dce0eb5.js";/* empty css                                                              */import{_ as K}from"./Pagination-3fdd18f9.js";import{_ as Z}from"./SearchableDropdown-8ff24da4.js";import{_ as J}from"./SimpleDropdown-a7f8688d.js";import{_ as y}from"./InputLabel-38b98ddd.js";const Q={class:"animate-top"},W={class:"flex justify-between items-center"},Y=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"TDS Data Report")],-1),D={class:"flex justify-end"},tt={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},et={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},ot=t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),st={class:"flex ml-6"},at={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},nt={class:"flex justify-between mb-2"},lt={class:"flex"},rt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),it={class:"inline-flex items-center space-x-4 justify-end w-full"},dt=["src"],ct={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},mt={class:"sm:col-span-3"},ut={class:"relative mt-2"},gt={class:"sm:col-span-3"},pt={class:"relative mt-2"},ht={class:"sm:col-span-3"},_t={class:"sm:col-span-3"},ft={class:"mt-8 overflow-x-auto sm:rounded-lg"},vt={class:"shadow sm:rounded-lg"},xt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},yt=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"DATE"),t("th",{scope:"col",class:"py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"CUSTOMER NAME"),t("th",{scope:"col",class:"py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"INVOICE NO"),t("th",{scope:"col",class:"py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"BANK"),t("th",{scope:"col",class:"py-3.5 pl-3 pr-3 text-left text-sm font-semibold text-gray-900"},"AMOUNT (₹)")])],-1),wt={key:0},bt={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500 min-w-32"},kt={class:"px-3 py-3 text-sm text-gray-900 font-medium text-gray-900 truncate"},Nt={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},St={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},Ct={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500 min-w-36"},Tt={key:1},zt=t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Ot=[zt],Rt={__name:"TDSReport",props:["data","organization","customers","fromDate","toDate","organizationId","customerId"],setup(l){const N=l,b=L({}),i=g(""),d=g(""),S=g("ALL CUSTOMERS"),a=g(N.organizationId),r=g(N.customerId),_=g(""),$=s=>{const o=new Date(s),e={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",e)},A=s=>{let o=s.toFixed(2).toString(),[e,u]=o.split("."),c=e.substring(e.length-3),w=e.substring(0,e.length-3);return w!==""&&(c=","+c),`${w.replace(/\B(?=(\d{2})+(?!\d))/g,",")+c}.${u}`},f=(s,o,e,u,c)=>{_.value=s,b.get(route("tds.report",{search:s,organization_id:o,customer_id:e,from_date:u,to_date:c}),{preserveState:!0})},I=()=>{let s="";switch(a.value){case 1:s="MC";break;case 2:s="HC";break;case 3:s="NOX";break;default:s="All_Organizations";break}const o=S.value.replace(/\s+/g,"_"),e=`TDS_Data_Report_${s}_${o}`,u={organization_id:a.value||"",customer_id:r.value||"",from_date:i.value||"",to_date:d.value||""},w=`/export-tds-report?${new URLSearchParams(u).toString()}`;fetch(w,{method:"GET"}).then(m=>{if(!m.ok)throw new Error("Network response was not ok");return m.blob()}).then(m=>{const C=window.URL.createObjectURL(new Blob([m])),v=document.createElement("a");v.href=C,v.setAttribute("download",`${e}.xlsx`),document.body.appendChild(v),v.click(),document.body.removeChild(v)}).catch(m=>{console.error("Error exporting data:",m)})},U=()=>{f(_.value,a.value,r.value,i.value,d.value)},B=()=>{f(_.value,a.value,r.value,i.value,d.value)},E=(s,o)=>{a.value=s,f(_.value,a.value,r.value,i.value,d.value)},R=(s,o)=>{r.value=s,S.value=o,f(_.value,a.value,r.value,i.value,d.value)};return(s,o)=>(p(),x(z,null,[n(k(j),{title:"TDS Data Report"}),n(X,null,{default:T(()=>[t("div",Q,[t("div",W,[Y,t("div",D,[t("div",tt,[t("div",et,[ot,t("input",{id:"search-field",onInput:o[0]||(o[0]=e=>f(e.target.value,a.value,r.value,i.value,d.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),t("div",st,[n(H,{href:s.route("reports")},{default:T(()=>[F(" Back ")]),_:1},8,["href"])])])]),t("div",at,[t("div",nt,[t("div",lt,[rt,n(y,{for:"customer_id",value:"Filters"})]),t("div",it,[t("button",{onClick:I},[t("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,dt)])])]),t("div",ct,[t("div",mt,[n(y,{for:"customer_id",value:"Organization Name"}),t("div",ut,[n(J,{options:l.organization,modelValue:a.value,"onUpdate:modelValue":o[1]||(o[1]=e=>a.value=e),onOnchange:E},null,8,["options","modelValue"])])]),t("div",gt,[n(y,{for:"customer_id",value:"Customer Name"}),t("div",pt,[n(Z,{options:l.customers,modelValue:r.value,"onUpdate:modelValue":o[2]||(o[2]=e=>r.value=e),onOnchange:R},null,8,["options","modelValue"])])]),t("div",ht,[n(y,{for:"date",value:"From Date"}),O(t("input",{"onUpdate:modelValue":o[3]||(o[3]=e=>i.value=e),class:M(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":k(b).errors.from_date}]),type:"date",onChange:U},null,34),[[V,i.value]])]),t("div",_t,[n(y,{for:"date",value:"To Date"}),O(t("input",{"onUpdate:modelValue":o[4]||(o[4]=e=>d.value=e),class:M(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":k(b).errors.to_date}]),type:"date",onChange:B},null,34),[[V,d.value]])])])]),t("div",ft,[t("div",vt,[t("table",xt,[yt,l.data.data&&l.data.data.length>0?(p(),x("tbody",wt,[(p(!0),x(z,null,P(l.data.data,(e,u)=>(p(),x("tr",{key:e.id,class:"odd:bg-white even:bg-gray-50 border-b"},[t("td",bt,h($(e.date)??"-"),1),t("td",kt,h(e.customers.customer_name??"-")+" : "+h(e.customers.city??"-"),1),t("td",Nt,h(e!=null&&e.invoice_data?e.invoice_data.map(c=>c.invoice_no).join(", "):e.invoice_no),1),t("td",St,h(e!=null&&e.bank_info?(e==null?void 0:e.bank_info.bank_name)+"-"+(e==null?void 0:e.bank_info.account_number):"-"),1),t("td",Ct,h(A(e.tds_amount)??"-"),1)]))),128))])):(p(),x("tbody",Tt,Ot))])])]),l.data.data&&l.data.data.length>0?(p(),q(K,{key:0,class:"mt-6",links:l.data.links},null,8,["links"])):G("",!0)])]),_:1})],64))}};export{Rt as default};
