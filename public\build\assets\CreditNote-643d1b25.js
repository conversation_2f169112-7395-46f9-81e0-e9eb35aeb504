import{_ as u}from"./AdminLayout-595ad5a7.js";import{h as f,r as x,o as s,c as a,a as r,u as g,w as i,F as c,Z as p,b as e,g as _,i as y,f as n,e as b}from"./app-97275a91.js";import{_ as v}from"./CreateButton-91ea7c7b.js";/* empty css                                                              */import{_ as w}from"./Pagination-5e2f223d.js";const N={class:"animate-top"},k={class:"flex justify-between items-center"},C=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Credit Note Report")],-1),B={class:"flex mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},M={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},E={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},T=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),V={class:"flex ml-6"},A={class:"mt-8 overflow-x-auto sm:rounded-lg"},I={class:"shadow sm:rounded-lg"},O={class:"w-full text-sm text-left rtl:text-right text-gray-500"},R=e("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[e("tr",{class:"border-b-2"},[e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," INVOICE NUMBER "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," CUSTOMER NAME "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," AMOUNT (₹) "),e("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," ACTION ")])],-1),S={key:0},Z={__name:"CreditNote",props:["data","category","organization","customers","salesuser","permissions","invoicetypes"],setup(t){const m=f({});x("");const h=o=>{m.get(route("credit.report",{search:o}),{preserveState:!0})};return(o,d)=>(s(),a(c,null,[r(g(p),{title:"Credit Note Report"}),r(u,null,{default:i(()=>[e("div",N,[e("div",k,[C,e("div",B,[e("div",M,[e("div",E,[T,e("input",{id:"search-field",onInput:d[0]||(d[0]=l=>h(l.target.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),e("div",V,[r(v,{href:o.route("reports")},{default:i(()=>[_(" Back ")]),_:1},8,["href"])])])])]),e("div",A,[e("div",I,[e("table",O,[R,t.data.data&&t.data.data.length>0?(s(),a("tbody",S,[(s(!0),a(c,null,y(t.data.data,(l,z)=>(s(),a("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:l.id}))),128))])):n("",!0)])])]),t.data.data&&t.data.data.length>0?(s(),b(w,{key:0,class:"mt-6",links:t.data.links},null,8,["links"])):n("",!0)]),_:1})],64))}};export{Z as default};
