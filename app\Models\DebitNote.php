<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DebitNote extends Model
{
    use HasFactory;
    use ActivityTrait;

    protected $table = 'debit_notes';

    protected static $logName = 'DebitNote';

    public function getLogDescription(string $event): string
    {
        $companyName = $this->company?->name ?? 'Unknown Company';

        return "DebitNote <strong>{$this->debit_note_no}</strong> has been {$event} for <strong>{$companyName}</strong> by";
    }

    protected static $logAttributes = [
        'invoice_type',
        'organization_id',
        'company_id',
        'purchase_invoice_id',
        'debit_note_no',
        'credit_note_number',
        'date',
        'sub_total',
        'igst',
        'cgst',
        'sgst',
        'discount_before_tax',
        'overall_discount',
        'total_discount',
        'total_gst',
        'total_amount',
        'reason',
        'created_by',
        'updated_by',
    ];

    protected $fillable = [
        'invoice_type',
        'organization_id',
        'company_id',
        'purchase_invoice_id',
        'debit_note_no',
        'credit_note_number',
        'date',
        'sub_total',
        'igst',
        'cgst',
        'sgst',
        'discount_before_tax',
        'overall_discount',
        'total_discount',
        'total_gst',
        'total_amount',
        'reason',
        'created_by',
        'updated_by',
    ];

    public function debitNoteDetails()
    {
        return $this->hasMany(PurchaseOrderReceiveDetails::class, 'debit_note_id', 'id')->where('is_receive', 'yes');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id', 'id');
    }

    public function purchaseInvoice()
    {
        return $this->belongsTo(PurchaseOrderReceives::class, 'purchase_invoice_id', 'id');
    }

    public function users()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }
}
