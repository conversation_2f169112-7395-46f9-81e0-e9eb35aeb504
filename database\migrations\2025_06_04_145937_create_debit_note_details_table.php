<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('debit_note_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('debit_note_id')->constrained('debit_notes')->onDelete('cascade');
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->foreignId('serial_number_id')->nullable()->constrained('serial_numbers')->onDelete('cascade');
            $table->foreignId('purchase_order_receive_detail_id')->nullable()->constrained('purchase_order_receive_details')->onDelete('cascade');
            $table->string('hsn_code')->nullable();
            $table->integer('qty');
            $table->decimal('price', 16, 2);
            $table->decimal('total_price', 16, 2);
            $table->decimal('gst', 5, 2);
            $table->decimal('gst_amount', 16, 2);
            $table->decimal('total_gst_amount', 16, 2);
            $table->decimal('discount', 16, 2)->default(0);
            $table->decimal('discount_amount', 16, 2)->default(0);
            $table->decimal('total_amount', 16, 2);
            $table->string('batch')->nullable();
            $table->date('expiry_date')->nullable();
            $table->text('description')->nullable();
            $table->integer('created_by');
            $table->integer('updated_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('debit_note_details');
    }
};
