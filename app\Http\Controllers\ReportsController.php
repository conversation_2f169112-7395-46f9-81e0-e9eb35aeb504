<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Exports\EngineerBusinessExport;
use App\Exports\SnsStockExport;
use App\Exports\SnsSalesExport;
use App\Exports\CustomerSnsSalesExport;
use App\Exports\GstSalesDataExport;
use App\Exports\GstPurchaseDataExport;
use App\Exports\TdsReportExport;
use App\Exports\HsnSalesSummaryExport;
use App\Exports\SalesReportExport;
use App\Exports\PurchaseReportExport;
use App\Exports\PendingAmountexport;
use App\Exports\CustomerTransactionReportExport;
use App\Models\Company;
use App\Models\Customer;
use App\Models\PaymentReceive;
use App\Models\Invoice;
use App\Models\InvoiceDetail;
use App\Models\CustomerTransaction;
use App\Models\CustomerCredit;
use App\Models\CreditNote;
use App\Models\DebitNote;
use App\Models\User;
use App\Models\Product;
use App\Models\PurchaseOrderReceives;
use App\Models\Organization;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Illuminate\Support\Facades\Auth;
use App\Traits\QueryTrait;
use Config;

class ReportsController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:List Reports')->only(['index']);
        $this->middleware('permission:Engineer Business Report')->only(['engineerBusiness', 'exportEngineerBusiness']);
        $this->middleware('permission:SNS Stock Report')->only(['snsStock', 'exportSnsStock']);

        $this->middleware('permission:HSN Sales Summary')->only(['hsnSalesSummary', 'exportHsnSalesSummary']);
        $this->middleware('permission:TDS Data Report')->only(['tdsReport', 'exportTdsReport']);
        $this->middleware('permission:SNS Sales Report(Company)')->only(['snsSales', 'exportSnsSales']);
        $this->middleware('permission:SNS Sales Report(Customer)')->only(['customersnsSales', 'exportcustomerSnsSales']);
    }

    use QueryTrait;

    public function index(Request $request)
    {
        $permissions = [
            'canEngineerBusinessReport' => auth()->user()->can('Engineer Business Report'),
            'canSnsStockReport' => auth()->user()->can('SNS Stock Report'),
            'canGSTData' => auth()->user()->can('GST Data'),
            'canSalesReport' => auth()->user()->can('Sales Report'),
            'canPurchaseReport' => auth()->user()->can('Purchase Report'),
            'canHSNSalesSummary' => auth()->user()->can('HSN Sales Summary'),
            'canTDSDataReport' => auth()->user()->can('TDS Data Report'),
            'canSNSSalesReport(Company)' => auth()->user()->can('SNS Sales Report(Company)'),
            'canSNSSalesReport(Customer)' => auth()->user()->can('SNS Sales Report(Customer)'),
        ];
        return Inertia::render('Reports/Index', compact('permissions'));
    }

    public function hsnSalesSummary(Request $request)
    {
        $organizationId = $request->input('organization_id');
        $from_date = $request->input('from_date');
        $to_date = $request->input('to_date');

        $invoices = Invoice::with(['invoiceDetail' => function($query) {
         $query->select('id', 'invoice_id', 'product_id', 'qty');
                        }, 'invoiceDetail.product:id,hsn_code'])
          ->select('id', 'invoice_no', 'sub_total', 'cgst', 'sgst', 'igst', 'date')
          ->where(function ($query) use ($from_date, $to_date) {
         if ($from_date && $to_date) {
           $query->whereBetween('date', [$from_date, $to_date]);
          }
         });

        if ($organizationId) {
        $invoices->where('organization_id', $organizationId);
        }
        $data = $invoices->orderBy('id', 'asc')->paginate(20);
        $organization = Organization::select('id', 'name')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATIONS'];
        $organization->prepend($allOrganization);
        $data->withQueryString()->links();
        return Inertia::render('Reports/HsnSalesSummary', compact('data', 'invoices', 'organization', 'organizationId', 'from_date', 'to_date'));
    }

    public function exportHsnSalesSummary(Request $request)
    {
        $validated = $request->validate([
            'organization_id' => 'nullable|integer',
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date',
        ]);

        $organizationId = $validated['organization_id'] ?? null;
        $from_date = $validated['from_date'] ?? null;
        $to_date = $validated['to_date'] ?? null;

        $organizationName = null;
        if ($organizationId) {
            $organization = Organization::find($organizationId);
            $organizationName = $organization ? $organization->name : null;
        }

        $query = Invoice::with(['invoiceDetail' => function($query) {
            $query->select('id', 'invoice_id', 'product_id', 'qty', 'total_price', 'gst', 'total_gst_amount');
        }, 'invoiceDetail.product:id,hsn_code'])
        ->select('id', 'invoice_no', 'sub_total', 'cgst', 'sgst', 'igst', 'date')
        ->when($from_date && $to_date, function ($query) use ($from_date, $to_date) {
            $query->whereBetween('date', [$from_date, $to_date]);
        })
        ->when($organizationId, function ($query) use ($organizationId) {
            $query->where('organization_id', $organizationId);
        });

        $allData = $query->get()->toArray();
        return Excel::download(new HsnSalesSummaryExport($allData, $organizationName, $from_date, $to_date), 'hsn_sales_summary.xlsx');
    }


    public function engineerBusiness(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');

        //for take salesuserid for engineer
        if(auth()->user()->can('Filter Funnel') != true){
            $salesUserId = Auth::user()->id;
        } else {
            $salesUserId = $request->input('sales_user_id');
        }

        $from_date = $request->input('from_date');
        $to_date = $request->input('to_date');
        $query = Invoice::with('customers');

        if($organizationId) {
            $query->where('organization_id', $organizationId);
        }

        if($salesUserId) {
            $query->where('sales_user_id', $salesUserId);
        }

        if($from_date) {
            $query->whereDate('date', '>=', $from_date);
        }

        if($to_date) {
            $query->whereDate('date', '<=', $to_date);
        }

        if(auth()->user()->can('Filter Funnel') != true){
            $query->where('sales_user_id', Auth::user()->id);
        }

        if(!empty($search)){
            $query->where(function ($query) use ($search) {
                $query->whereHas('customers', function ($subquery) use ($search) {
                    $subquery->where('customer_name', 'like', "%$search%");
                })
                ->orWhere('invoice_no', 'like', "%$search%")
                ->orWhere('date', 'like', "%$search%")
                ->orWhere('total_amount', 'like', "%$search%");
            });
        }

        $permissions = [
            'canEngineerBusinessReport' => auth()->user()->can('Engineer Business Report'),
            'canFilterFunnel' => auth()->user()->can('Filter Funnel')
        ];

        $searchableFields = ['date', 'invoice_no', 'customers.customer_name', 'sub_total'];
        $this->searchAndSort($query, $request, $searchableFields);

        $total_data = $query->orderBy('id', 'desc')->get();
        $data = $query->paginate(20);
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $allSalesuser = ['id' => null, 'name' => 'ALL USERS'];
        $salesuser->prepend($allSalesuser);
        $organization  = Organization::select('id', 'name')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOrganization);
        $data->withQueryString()->links();
        return Inertia::render('Reports/EngineerSales', compact('data', 'total_data', 'permissions', 'salesuser', 'organization','from_date', 'to_date', 'organizationId', 'salesUserId'));
    }


    public function exportEngineerBusiness(Request $request){

        $validated = $request->validate([
            'organization_id' => 'nullable|integer',
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date',
            'sales_user_id' => 'nullable|integer',
        ]);

        $organizationId = $validated['organization_id'] ?? null;
        $from_date = $validated['from_date'] ?? null;
        $to_date = $validated['to_date'] ?? null;
        $salesuserId = $validated['sales_user_id'] ?? null;


        $organizationName = null;
        if ($organizationId != null) {
            $organization = Organization::find($organizationId);
            $organizationName = $organization ? $organization->name : "null";
        } else {
            $organizationName = "All Organization";
        }

        $salesuserName = null;
        if ($salesuserId) {
            $salesuser = User::find($salesuserId);
            $salesuserName = $salesuser ? $salesuser->first_name . ' ' . $salesuser->last_name : null;
        }

        $data = $request->input();
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $allSalesuser = ['id' => null, 'name' => 'ALL USERS'];
        $salesuser->prepend($allSalesuser);
        $query = Invoice::with('customers');
        if($data['organization_id'] != "") {
            $query->where('organization_id', $data['organization_id']);
        }
        if($data['sales_user_id'] != "null") {
            $query->where('sales_user_id', $data['sales_user_id']);
        }
        if(auth()->user()->can('Filter Funnel') != true){
            $query->where('sales_user_id', Auth::user()->id);
        }
        if($data['from_date'] != null) {
            $query->whereDate('date', '>=', $data['from_date']);
        }
        if($data['to_date'] != null) {
            $query->whereDate('date', '<=', $data['to_date']);
        }
        $allData = $query->get()->toArray();
        return Excel::download(new EngineerBusinessExport($allData,$organizationName, $salesuserName, $from_date, $to_date), 'abc.xlsx');
    }


    public function snsStock(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $companyId = $request->input('company_id');
        $from_date = $request->input('from_date');
        $to_date = $request->input('to_date');
        $query = Product::with(['salesProducts' => function ($query1) use ($organizationId, $from_date, $to_date) {
            if($organizationId) {
                $query1->where('organization_id', $organizationId);
            }
            // if($from_date) {
            //     $query1->whereDate('created_at', '>=', $from_date);
            // }
            // if($to_date) {
            //     $query1->whereDate('created_at', '<=', $to_date);
            // }
        }, 'company'])->whereHas('salesProducts');

        if($companyId) {
            $query->where('company_id', $companyId);
        }

        if(!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->orWhere('item_code', 'like', "$search%")
                ->orWhere('name', 'like', "$search%");
            });
        }

        $searchableFields = ['item_code', 'name', 'company.name', 'min_qty'];
        $this->searchAndSort($query, $request, $searchableFields);

        $data = $query->orderBy('id', 'asc')->paginate(20);
        $company  = Company::select('id', 'name')->orderByRaw('name')->get();
        $allCompany = ['id' => null, 'name' => 'ALL COMPANY'];
        $company->prepend($allCompany);
        $organization  = Organization::select('id', 'name')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOrganization);
        $data->withQueryString()->links();
        return Inertia::render('Reports/SnsStock', compact('data', 'company', 'organization', 'organizationId', 'companyId'));
    }

    public function exportSnsStock(Request $request){
        $data = $request->input();
        $organizationId = $data['organization_id'];
        $companyId = $data['company_id'];
        $from_date = $data['from_date'];
        $to_date = $data['to_date'];

        $organizationName = null;
        if ($organizationId) {
            $organization = Organization::find($organizationId);
            $organizationName = $organization ? $organization->name : null;
        }

        $companyName = null;
        if ($companyId) {
            $company = Company::find($companyId);
            $companyName = $company ? $company->name : null;
        }

        $query = Product::with(['salesProducts' => function ($query1) use ($organizationId, $from_date, $to_date) {
            if($organizationId != '') {
                $query1->where('organization_id', $organizationId);
            }
            // if($from_date != ''){
            //     $query1->whereDate('created_at', '>=', $from_date);
            // }
            // if($to_date != '') {
            //     $query1->whereDate('created_at', '<=', $to_date);
            // }
        }, 'company'])->whereHas('salesProducts');

        if($companyId) {
            $query->where('company_id', $companyId);
        }
        $allData = $query->get()->toArray();
        return Excel::download(new SnsStockExport($allData, $organizationName, $companyName, $from_date, $to_date), 'abcdd.xlsx');
    }


    public function snsSales(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $companyId = $request->input('company_id');
        $from_date = $request->input('from_date');
        $to_date = $request->input('to_date');

        // if(!empty($search)) {
        //     $query->where(function ($query) use ($search) {
        //         $query->orWhere('item_code', 'like', "$search%")
        //         ->orWhere('name', 'like', "$search%");
        //     });
        // }

        // $sales = DB::table('invoice_details')
        // ->join('invoice', 'invoice_details.invoice_id', '=', 'invoice.id')
        // ->join('products', 'invoice_details.product_id', '=', 'products.id')
        // ->select(
        //     'products.name as product_name',
        //     DB::raw('SUM(invoice_details.total_amount) as total_sales'),
        //     DB::raw('SUM(invoice_details.qty) as total_quantity')
        // )
        // ->where('invoice.organization_id', $organizationId)
        // ->where('products.company_id', $companyId)
        // ->whereBetween('invoice.date', [$from_date, $to_date])
        // ->groupBy('products.name')
        // ->get()->toArray();

        $query = Product::whereHas('invoiceDetails', function ($query) use ($organizationId, $from_date, $to_date) {
            $query->whereHas('invoice', function ($query) use ($organizationId, $from_date, $to_date) {
                if ($organizationId) {
                    $query->where('organization_id', $organizationId);
                }
                if ($from_date && $to_date) {
                    $query->whereBetween('date', [$from_date, $to_date]);
                }
            });
        })->when($companyId,function($q) use ($companyId){
           $q->where('company_id', $companyId);
        })->with(['invoiceDetails' => function ($query) use ($organizationId, $from_date, $to_date) {
                $query->whereHas('invoice', function ($query) use ($organizationId, $from_date, $to_date) {
                    if ($organizationId) {
                        $query->where('organization_id', $organizationId);
                    }
                    if ($from_date && $to_date) {
                        $query->whereBetween('date', [$from_date, $to_date]);
                    }
                });
        }, 'invoiceDetails.invoice.customers', 'company']);

        $searchableFields = ['item_code', 'name', 'company.name', 'min_qty'];
        $this->searchAndSort($query, $request, $searchableFields);

        $data = $query->orderBy('id', 'asc')->paginate(20);
        $company  = Company::select('id', 'name')->orderByRaw('name')->get();
        $allCompany = ['id' => null, 'name' => 'ALL COMPANY'];
        $company->prepend($allCompany);
        $organization  = Organization::select('id', 'name')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOrganization);
        $data->withQueryString()->links();
        return Inertia::render('Reports/SnsSales', compact('data', 'company', 'organization', 'organizationId', 'companyId'));
    }

    public function exportSnsSales(Request $request){
        $data = $request->input();
        $organizationId = $data['organization_id'];
        $companyId = $data['company_id'];
        $from_date = $data['from_date'];
        $to_date = $data['to_date'];

        $organizationName = null;
        if ($organizationId) {
            $organization = Organization::find($organizationId);
            $organizationName = $organization ? $organization->name : null;
        }

        $companyName = null;
        if ($companyId) {
            $company = Company::find($companyId);
            $companyName = $company ? $company->name : null;
        }

        $query = Product::whereHas('invoiceDetails', function ($query) use ($organizationId, $from_date, $to_date) {
            $query->whereHas('invoice', function ($query) use ($organizationId, $from_date, $to_date) {
                if ($organizationId != '') {
                    $query->where('organization_id', $organizationId);
                }
                if ($from_date != '' && $to_date != '') {
                    $query->whereBetween('date', [$from_date, $to_date]);
                }
            });
        })->when($companyId != '',function($q) use ($companyId){
           $q->where('company_id', $companyId);
        })->with(['invoiceDetails' => function ($query) use ($organizationId, $from_date, $to_date) {
                $query->whereHas('invoice', function ($query) use ($organizationId, $from_date, $to_date) {
                    if ($organizationId != '') {
                        $query->where('organization_id', $organizationId);
                    }
                    if ($from_date != '' && $to_date != '') {
                        $query->whereBetween('date', [$from_date, $to_date]);
                    }
                });
        }, 'invoiceDetails.invoice.customers', 'company']);
        $allData = $query->get()->toArray();
        return Excel::download(new SnsSalesExport($allData, $organizationName, $companyName, $from_date, $to_date), 'abcdd.xlsx');
    }

    public function customersnsSales(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $customerId =$request->input('customer_id');
        $from_date = $request->input('from_date');
        $to_date = $request->input('to_date');



        $query = Customer::whereHas('invoices.invoiceDetail', function ($query) use ($organizationId, $customerId, $from_date, $to_date) {
            $query->whereHas('invoice', function ($query) use ($organizationId, $customerId, $from_date, $to_date) {
                if ($organizationId) {
                    $query->where('organization_id', $organizationId);
                }
                if ($from_date && $to_date) {
                    $query->whereBetween('date', [$from_date, $to_date]);
                }
                if ($customerId) {
                    $query->where('customer_id', $customerId);
                }
            });
        })
        ->with(['invoices.invoiceDetail' => function ($query) use ($organizationId, $customerId, $from_date, $to_date) {
                $query->whereHas('invoice', function ($query) use ($organizationId, $customerId, $from_date, $to_date) {
                    if ($organizationId) {
                        $query->where('organization_id', $organizationId);
                    }
                    if ($from_date && $to_date) {
                        $query->whereBetween('date', [$from_date, $to_date]);
                    }
                    if ($customerId) {
                        $query->where('customer_id', $customerId);
                    }
                });
        }, 'invoices.invoiceDetail.product']);
        // ->get()->take(1)->toArray();
        // dd($products);
        // $products = Customer::with('invoices.invoiceDetail')->get()->take(5)->toArray();
        // dd($products);

        $searchableFields = ['customer_name'];
        $this->searchAndSort($query, $request, $searchableFields);

        $data = $query->orderBy('id', 'asc')->paginate(20);
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id')->orderByRaw('customer_name')->get();
        $allCustomers = ['id' => null, 'name' => 'ALL CUSTOMERS'];
        $customers->prepend($allCustomers);
        $organization  = Organization::select('id', 'name')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOrganization);
        $data->withQueryString()->links();
        return Inertia::render('Reports/SnssalesCustomer', compact('data',  'customers', 'organization', 'organizationId','customerId'));

    }

    public function exportcustomerSnsSales(Request $request) {
        $data = $request->input();
        $organizationId = $data['organization_id'];
        $customerId = $data['customer_id'];
        $from_date = $data['from_date'];
        $to_date = $data['to_date'];

        $organizationName = null;
        if ($organizationId) {
            $organization = Organization::find($organizationId);
            $organizationName = $organization ? $organization->name : null;
        }

        $customerName = null;
        if ($customerId) {
        $customer = Customer::find($customerId);
        $customerName = $customer ? $customer->customer_name : null;
    }

        $query = Customer::whereHas('invoices.invoiceDetail', function ($query) use ($organizationId, $customerId, $from_date, $to_date) {
            $query->whereHas('invoice', function ($query) use ($organizationId, $customerId, $from_date, $to_date) {
                if ($organizationId != '') {
                    $query->where('organization_id', $organizationId);
                }
                if ($from_date != '' && $to_date != '') {
                    $query->whereBetween('date', [$from_date, $to_date]);
                }
                if ($customerId != '') {
                    $query->where('customer_id', $customerId);
                }
            });
        })
        ->with(['invoices.invoiceDetail' => function ($query) use ($organizationId, $customerId, $from_date, $to_date) {
            $query->whereHas('invoice', function ($query) use ($organizationId, $customerId, $from_date, $to_date) {
                if ($organizationId != '') {
                    $query->where('organization_id', $organizationId);
                }
                if ($from_date != '' && $to_date != '') {
                    $query->whereBetween('date', [$from_date, $to_date]);
                }
                if ($customerId != '') {
                    $query->where('customer_id', $customerId);
                }
            });
        }, 'invoices.invoiceDetail.product']);

        $allData = $query->get()->toArray();

        return Excel::download(new CustomerSnsSalesExport($allData, $organizationName, $customerName, $from_date, $to_date), 'customer_sales_report.xlsx');
    }

    public function gstSalesData(Request $request)
    {
        $organizationId = $request->input('organization_id');
        $from_date = $request->input('from_date');
        $to_date = $request->input('to_date');
        $organization = Organization::select('id', 'name')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATIONS'];
        $organization->prepend($allOrganization);
        return Inertia::render('Reports/GstSalesData', [
            'organization' => $organization,
            'organizationId' => $organizationId,
            'from_date' => $from_date,
            'to_date' => $to_date
        ]);
    }

    public function exportGstSalesData(Request $request)
    {
            $validated = $request->validate([
                'organization_id' => 'nullable|integer',
                'from_date' => 'nullable|date',
                'to_date' => 'nullable|date',
            ]);

            $organizationId = $validated['organization_id'] ?? null;
            $from_date = $validated['from_date'] ?? null;
            $to_date = $validated['to_date'] ?? null;

            $organizationName = 'Organization';
            if ($organizationId) {
                $organization = Organization::find($organizationId);
                $organizationName = $organization ? $organization->name : $organizationName;
            }

            $query = Invoice::with([
                'invoiceDetails.product',
                'customers'
            ])
            ->when($from_date && $to_date, function ($query) use ($from_date, $to_date) {
                $query->whereBetween('date', [$from_date, $to_date]);
            })
            ->when($organizationId, function ($query) use ($organizationId) {
                $query->where('organization_id', $organizationId);
            })
            ->orderBy('date', 'asc');
            $allData = $query->get()->toArray();
            return Excel::download(new GstSalesDataExport($allData, $organizationName, $from_date, $to_date), 'GST_Sales_Data.xlsx');
    }

    public function gstPurchaseData(Request $request)
    {
        $organizationId = $request->input('organization_id');
        $from_date = $request->input('from_date');
        $to_date = $request->input('to_date');

        $organization = Organization::select('id', 'name')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATIONS'];
        $organization->prepend($allOrganization);

        return Inertia::render('Reports/GstPurchaseData', [
            'organization' => $organization,
            'organizationId' => $organizationId,
            'from_date' => $from_date,
            'to_date' => $to_date,
        ]);
    }

    public function exportGstPurchaseData(Request $request)
    {
        $organizationId = $request->input('organization_id');
        $from_date = $request->input('from_date');
        $to_date = $request->input('to_date');
        $organizationName = Organization::where('id', $organizationId)->value('name') ?? 'ALL ORGANIZATIONS';

        $query = PurchaseOrderReceives::with([
            'purchaseOrder' => function ($query) {
                $query->select('id', 'organization_id', 'company_id', 'sub_total', 'igst', 'sgst', 'cgst', 'date', 'total_gst', 'total_amount');
            },
            'purchaseOrder.company:id,name,gst_no,gst_type',
            'purchaseOrderReceiveDetails' => function ($query) {
                $query->select('id', 'purchase_order_receive_id', 'purchase_order_detail_id', 'product_id', 'receive_qty')
                    ->with([
                        'product:id,hsn_code',
                        'purchaseOrderDetail:id,gst',
                        'serialNumbers' => function ($query) {
                            $query->select('id', 'purchase_order_receive_detail_id', 'purchase_price', 'receive_qty');
                        }
                    ]);
            }
        ])
        ->where('type', 'invoice')
        ->select('id', 'purchase_order_id', 'customer_invoice_date', 'customer_invoice_no', 'total_price', 'total_gst_amount', 'total_amount');

        // Apply organization filter to the main query through the purchaseOrder relationship
        if ($organizationId) {
            $query->whereHas('purchaseOrder', function ($query) use ($organizationId) {
                $query->where('organization_id', $organizationId);
            });
        }

        if ($from_date && $to_date) {
            $query->whereBetween('customer_invoice_date', [$from_date, $to_date])->orderBy('customer_invoice_date', 'asc');
        }
        $allData = $query->get()->toArray();

        return Excel::download(new GstPurchaseDataExport($allData, $from_date, $to_date, $organizationName), 'GST_Purchase_Data.xlsx');
    }


    public function salesReport(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');

        $query = Invoice::with('customers');

        if ($organizationId) {
            $query->where('organization_id', $organizationId);
        }
        if (!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->whereHas('customers', function ($subquery) use ($search) {
                    $subquery->where('customer_name', 'like', "%$search%");
                })
                ->orWhere('invoice_no', 'like', "%$search%")
                ->orWhere('date', 'like', "%$search%")
                ->orWhere('status', 'like', "%$search%")
                ->orWhere('total_amount', 'like', "%$search%");
            });
        }

        if ($fromDate && $toDate) {
            $query->whereBetween('date', [$fromDate, $toDate]);
        }

        $searchableFields = ['invoice_no', 'invoice_type', 'category', 'customers.customer_name', 'date', 'total_amount'];
        $this->searchAndSort($query, $request, $searchableFields);

        $total_data = $query->orderBy('date')->get();
        $data = $query->paginate(20);
        $data->withQueryString()->links();
        $organization = Organization::select('id', 'name')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOrganization);
        return Inertia::render('Reports/SalesReport', compact('data', 'total_data',  'organization', 'organizationId', 'fromDate', 'toDate'));
    }

    public function exportSalesReport(Request $request)
    {
        $validated = $request->validate([
            'organization_id' => 'nullable|integer',
            'customer_id' => 'nullable|integer',
            'category' => 'nullable|string',
            'invoice_type' => 'nullable|string',
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date',
            'search' => 'nullable|string',
        ]);

        $organizationId = $validated['organization_id'] ?? null;
        $customerId = $validated['customer_id'] ?? null;
        $category = $validated['category'] ?? null;
        $invoiceType = $validated['invoice_type'] ?? null;
        $fromDate = $validated['from_date'] ?? null;
        $toDate = $validated['to_date'] ?? null;
        $search = $validated['search'] ?? null;

        $organizationName = Organization::find($organizationId)->name ?? 'All Organizations';

        $query = Invoice::with('customers');

        if ($organizationId) {
            $query->where('organization_id', $organizationId);
        }

        if ($customerId) {
            $query->where('customer_id', $customerId);
        }

        if ($category) {
            $query->where('category', 'like', "%$category%");
        }

        if ($invoiceType) {
            $query->where('invoice_type', 'like', "%$invoiceType%");
        }

        if (!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->whereHas('customers', function ($subquery) use ($search) {
                    $subquery->where('customer_name', 'like', "%$search%");
                })
                ->orWhere('invoice_no', 'like', "%$search%")
                ->orWhere('date', 'like', "%$search%")
                ->orWhere('status', 'like', "%$search%")
                ->orWhere('total_amount', 'like', "%$search%");
            });
        }

        if ($fromDate && $toDate) {
            $query->whereBetween('date', [$fromDate, $toDate]);
        }

        $salesData = $query->get();

        return Excel::download(new SalesReportExport($salesData, $organizationName, $fromDate, $toDate), 'sales_report.xlsx');
    }

    public function purchasereport(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $companyId = $request->input('company_id');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');

        $query = PurchaseOrderReceives::with('purchaseOrder.company', 'purchaseOrder.organization')
            ->whereHas('purchaseOrder', function ($query) {
                $query->where('type', 'invoice');
            })
            ->when($companyId, function ($query) use ($companyId) {
                $query->whereHas('purchaseOrder', function ($query) use ($companyId) {
                    $query->where('company_id', $companyId);
                });
            })
            ->when($organizationId, function ($query) use ($organizationId) {
                $query->whereHas('purchaseOrder', function ($query) use ($organizationId) {
                    $query->where('organization_id', $organizationId);
                });
            });

        if (!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->whereHas('purchaseOrder', function ($subquery) use ($search) {
                    $subquery->where('po_number', 'like', "%$search%");
                })
                ->orWhere('customer_invoice_no', 'like', "%$search%")
                ->orWhere('customer_invoice_date', 'like', "%$search%")
                ->orWhere('status', 'like', "%$search%");
            });
        }

        if ($fromDate && $toDate) {
            $query->whereBetween('customer_invoice_date', [$fromDate, $toDate]);
        }

        $searchableFields = ['customer_invoice_no', 'purchaseOrder.po_number', 'purchaseOrder.company.name', 'customer_invoice_date', 'total_amount'];
        $this->searchAndSort($query, $request, $searchableFields);

        $total_data = $query->orderBy('customer_invoice_date')->get();
        $data = $query->paginate(20);
        $data->withQueryString()->links();
        $organization = Organization::select('id', 'name')->get();
        $companies = Company::select('name', 'id')->orderByRaw('name')->get();

        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOrganization);
        $allCompany = ['id' => null, 'name' => 'ALL COMPANY'];
        $companies->prepend($allCompany);
        // dd($organization);

        return Inertia::render('Reports/PurchaseReport', compact('data', 'total_data', 'organization', 'companies', 'organizationId', 'companyId', 'fromDate', 'toDate'));
    }

    public function exportPurchaseReport(Request $request)
    {
        $validated = $request->validate([
            'company_id' => 'nullable|integer',
            'organization_id' => 'nullable|integer',
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date',
            'search' => 'nullable|string',
        ]);

        $companyId = $validated['company_id'] ?? null;
        $organizationId = $validated['organization_id'] ?? null;
        $fromDate = $validated['from_date'] ?? null;
        $toDate = $validated['to_date'] ?? null;
        $search = $validated['search'] ?? null;
        $organizationName = Organization::where('id', $organizationId)->value('name') ?? 'ALL ORGANIZATIONS';


        $query = PurchaseOrderReceives::with('purchaseOrder.company', 'purchaseOrder.organization')
            ->whereHas('purchaseOrder', function ($query) {
                $query->where('type', 'invoice');
            })
            ->when($companyId, function ($query) use ($companyId) {
                $query->whereHas('purchaseOrder', function ($query) use ($companyId) {
                    $query->where('company_id', $companyId);
                });
            })
            ->when($organizationId, function ($query) use ($organizationId) {
                $query->whereHas('purchaseOrder', function ($query) use ($organizationId) {
                    $query->where('organization_id', $organizationId);
                });
            })
            ->when($fromDate && $toDate, function ($query) use ($fromDate, $toDate) {
                $query->whereBetween('customer_invoice_date', [$fromDate, $toDate]);
            });

        if (!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->whereHas('purchaseOrder', function ($subquery) use ($search) {
                    $subquery->where('po_number', 'like', "%$search%");
                })
                ->orWhere('customer_invoice_no', 'like', "%$search%")
                ->orWhere('customer_invoice_date', 'like', "%$search%")
                ->orWhere('status', 'like', "%$search%");
            });
        }

        $purchaseReports = $query->get();

        return Excel::download(
            new PurchaseReportExport($purchaseReports, $fromDate, $toDate, $organizationName),
            'purchase_report.xlsx'
        );
    }

    public function tdsReport(Request $request)
    {
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $customerId = $request->input('customer_id');
        $query = PaymentReceive::with('customers', 'bankInfo');

        $organization  = Organization::select('id', 'name')->get();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id')->orderByRaw('customer_name')->get();
        $allOption = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOption);
        $allOption2 = ['id' => null, 'name' => 'ALL CUSTOMERS'];
        $customers->prepend($allOption2);

        if($customerId) {
            $query->where('customer_id', $customerId);
        }
        if($organizationId) {
            $query->where('organization_id', $organizationId);
        }

        if ($fromDate && $toDate) {
            $query->whereBetween('date', [$fromDate, $toDate]);
        }

        $query->where('tds_amount', '>' , 0);
        $data = $query->orderBy('id', 'desc')->paginate(10);

           return Inertia::render('Reports/TDSReport', compact(
               'data',
               'organization',
               'customers',
               'organizationId',
               'customerId',
               'fromDate',
               'toDate'
           ));
       }

       public function exportTdsReport(Request $request)
       {
           $validated = $request->validate([
               'organization_id' => 'nullable|integer',
               'customer_id' => 'nullable|integer',
               'from_date' => 'nullable|date',
               'to_date' => 'nullable|date',
               'search' => 'nullable|string',
           ]);

           $organizationId = $validated['organization_id'] ?? null;
           $customerId = $validated['customer_id'] ?? null;
           $fromDate = $validated['from_date'] ?? null;
           $toDate = $validated['to_date'] ?? null;
           $search = $validated['search'] ?? null;
           $organizationName = Organization::where('id', $organizationId)->value('name') ?? 'ALL ORGANIZATIONS';

           $customerName = null;
           if ($customerId) {
               $customer = Customer::find($customerId);
               $customerName = $customer ? $customer->customer_name : null;
           }

           $query = PaymentReceive::with('customers', 'bankInfo')
           ->when($organizationId, function ($query) use ($organizationId) {
               $query->where('organization_id', $organizationId);
           })
           ->when($customerId, function ($query) use ($customerId) {
               $query->where('customer_id', $customerId);
           })
           ->when($fromDate && $toDate, function ($query) use ($fromDate, $toDate) {
               $query->whereBetween('date', [$fromDate, $toDate]);
           })
           ->where('tds_amount', '>' , 0);

           if (!empty($search)) {
               $query->where(function ($query) use ($search) {
                   $query->where('payment_reference', 'like', "%$search%")
                       ->orWhere('payment_mode', 'like', "%$search%")
                       ->orWhereHas('customers', function ($subquery) use ($search) {
                           $subquery->where('customer_name', 'like', "%$search%");
                       })
                       ->orWhereHas('bankInfo', function ($subquery) use ($search) {
                           $subquery->where('bank_name', 'like', "%$search%");
                       });
               });
           }

           $tdsReports = $query->get();
           return Excel::download(
               export: new TdsReportExport($tdsReports, $fromDate, $toDate, $organizationName, $customerName),
               fileName: 'tds_report.xlsx'
           );
       }

       public function invoicePendingAmount(Request $request)
       {
           $search = $request->input('search');
           $organizationId = $request->input('organization_id');
            if(auth()->user()->can('Filter Funnel') != true){
                $salesUserId = Auth::user()->id;
            } else {
                $salesUserId = $request->input('sales_user_id');
            }
           $from_date = $request->input('from_date');
           $to_date = $request->input('to_date');

           $query = Invoice::query()
               ->select('invoice.*')
               ->leftJoin('customers', 'invoice.customer_id', '=', 'customers.id')
               ->with('customers')
               ->where('pending_amount', '>', 0);

           if ($organizationId) {
               $query->where('invoice.organization_id', $organizationId);
           }

           if ($salesUserId) {
               $query->where('invoice.sales_user_id', $salesUserId);
           }

           if ($from_date) {
               $query->whereDate('invoice.date', '>=', $from_date);
           }

           if ($to_date) {
               $query->whereDate('invoice.date', '<=', $to_date);
           }

           if (auth()->user()->can('Filter Funnel') != true) {
               $query->where('invoice.sales_user_id', Auth::user()->id);
           }

           if (!empty($search)) {
               $query->where(function ($query) use ($search) {
                   $query->whereHas('customers', function ($subquery) use ($search) {
                       $subquery->where('customer_name', 'like', "%$search%");
                   })
                   ->orWhere('invoice_no', 'like', "%$search%")
                   ->orWhere('date', 'like', "%$search%")
                   ->orWhere('total_amount', 'like', "%$search%");
               });
           }

           $permissions = [
               'canEngineerBusinessReport' => auth()->user()->can('Engineer Business Report'),
               'canFilterFunnel' => auth()->user()->can('Filter Funnel')
           ];

           $searchableFields = ['date', 'invoice_no', 'customers.customer_name', 'sub_total'];
           $this->searchAndSort($query, $request, $searchableFields);

           $query->orderBy('customers.customer_name', 'asc');

           $total_data = $query->get();
           $data = $query->paginate(20);
           $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
           $allSalesuser = ['id' => null, 'name' => 'ALL USERS'];
           $salesuser->prepend($allSalesuser);
           $organization  = Organization::select('id', 'name')->get();
           $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
           $organization->prepend($allOrganization);
           $data->withQueryString()->links();

           return Inertia::render('Reports/InvoicePendingAmount', compact('data', 'total_data', 'permissions', 'salesuser', 'organization','from_date', 'to_date', 'organizationId', 'salesUserId'));
       }

       public function invoicePendingAmountexport(Request $request)
       {
           $validated = $request->validate([
               'organization_id' => 'nullable|integer',
               'from_date' => 'nullable|date',
               'to_date' => 'nullable|date',
               'sales_user_id' => 'nullable|integer',
           ]);

           $organizationId = $validated['organization_id'] ?? null;
           $from_date = $validated['from_date'] ?? null;
           $to_date = $validated['to_date'] ?? null;
           $salesuserId = $validated['sales_user_id'] ?? null;

           $organizationName = null;
           if ($organizationId != null) {
               $organization = Organization::find($organizationId);
               $organizationName = $organization ? $organization->name : "null";
           } else {
               $organizationName = "All Organization";
           }

           $salesuserName = null;
           if ($salesuserId) {
               $salesuser = User::find($salesuserId);
               $salesuserName = $salesuser ? $salesuser->first_name . ' ' . $salesuser->last_name : null;
           }

           $data = $request->input();

           $salesuser = User::where('status', '1')
               ->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"), 'id')
               ->orderByRaw('first_name')
               ->get();

           $allSalesuser = ['id' => null, 'name' => 'ALL USERS'];
           $salesuser->prepend($allSalesuser);

           $query = Invoice::query()
               ->join('customers', 'invoice.customer_id', '=', 'customers.id')
               ->where('invoice.pending_amount', '>', 0)
               ->select('invoice.*')
               ->orderBy('customers.customer_name', 'asc');

           if ($data['organization_id'] != "") {
               $query->where('invoice.organization_id', $data['organization_id']);
           }

           if ($data['sales_user_id'] != "null") {
               $query->where('invoice.sales_user_id', $data['sales_user_id']);
           }

           if (!empty($data['from_date'])) {
               $query->whereDate('invoice.date', '>=', $data['from_date']);
           }

           if (!empty($data['to_date'])) {
               $query->whereDate('invoice.date', '<=', $data['to_date']);
           }

           if(auth()->user()->can('Filter Funnel') != true){
                $query->where('invoice.sales_user_id', Auth::user()->id);
            }


           $allData = $query->with('customers')->get()->toArray();

           return Excel::download(new PendingAmountexport($allData, $organizationName, $salesuserName, $from_date, $to_date), 'abc.xlsx');
       }

       public function customerinvoiceReport(Request $request)
       {
           $customerId = $request->input('customer_id');
           $organizationId = $request->input('organization_id');
           $fromDate = $request->input('from_date');
           $toDate = $request->input('to_date');

           $query = Invoice::query()
               ->join('customers', 'invoice.customer_id', '=', 'customers.id')
               ->where('invoice.pending_amount', '>', 0)
               ->select('invoice.*')
               ->orderBy('customers.customer_name', 'asc');

           if ($customerId) {
               $query->where('invoice.customer_id', $customerId);
           }

           if ($organizationId) {
               $query->where('invoice.organization_id', $organizationId);
           }

           if ($fromDate && $toDate) {
               $query->whereBetween('invoice.date', [$fromDate, $toDate]);
           }

           $data = $query->with('customer')
               ->get(['invoice.id','invoice.customer_id','invoice.organization_id','invoice.date','invoice.invoice_no','invoice.total_amount','invoice.paid_amount','invoice.pending_amount']);

           $customers = Customer::select('id', 'customer_name as name')->get();
           $customers->prepend(['id' => null, 'name' => 'ALL CUSTOMERS']);

           $organization = Organization::select('id', 'name')->get();
           $organization->prepend(['id' => null, 'name' => 'ALL ORGANIZATION']);

           return Inertia::render('Reports/CustomerTransactionReport', compact('data','organization','organizationId','customers','customerId','fromDate','toDate'));
       }

       public function customerinvoiceReportExport(Request $request)
       {
           $customerId = $request->input('customer_id');
           $organizationId = $request->input('organization_id');
           $fromDate = $request->input('from_date');
           $toDate = $request->input('to_date');

           $organizationName = null;
           if ($organizationId) {
               $organization = Organization::find($organizationId);
               $organizationName = $organization ? $organization->name : null;
           }

           $query = Invoice::query()
               ->join('customers', 'invoice.customer_id', '=', 'customers.id')
               ->where('invoice.pending_amount', '>', 0)
               ->select('invoice.*')
               ->orderBy('customers.customer_name', 'asc');

           if ($customerId) {
               $query->where('invoice.customer_id', $customerId);
           }

           if ($organizationId) {
               $query->where('invoice.organization_id', $organizationId);
           }

           if ($fromDate && $toDate) {
               $query->whereBetween('invoice.date', [$fromDate, $toDate]);
           }

           $transactionData = $query->with('customer', 'organization')->get();

           $creditData = CustomerCredit::where('unused_amount', '>', 0);

           if ($customerId) {
               $creditData->where('customer_id', $customerId);
           }

           if ($organizationId) {
               $creditData->where('organization_id', $organizationId);
           }

           if ($fromDate && $toDate) {
               $creditData->whereBetween('date', [$fromDate, $toDate]);
           }

           $creditDataResult = $creditData->get();

           return Excel::download(
               new CustomerTransactionReportExport($transactionData,$creditDataResult,$organizationName,$fromDate,$toDate),
               'customer_transaction_report.xlsx'
           );
       }

    public function debitNote(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $companyId = $request->input('company_id');
        $from_date = $request->input('from_date');
        $to_date = $request->input('to_date');

        $query = DebitNote::with('debitNoteDetails', 'company', 'organization', 'purchaseInvoice');

        if ($companyId) {
            $query->where('company_id', $companyId);
        }
        if ($organizationId) {
            $query->where('organization_id', $organizationId);
        }
        if ($from_date) {
            $query->whereDate('date', '>=', $from_date);
        }
        if ($to_date) {
            $query->whereDate('date', '<=', $to_date);
        }

        if (!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->whereHas('company', function ($subquery) use ($search) {
                    $subquery->where('name', 'like', "%$search%");
                })
                ->orWhere('debit_note_no', 'like', "%$search%")
                ->orWhere('date', 'like', "%$search%")
                ->orWhere('total_amount', 'like', "%$search%");
            });
        }

        $searchableFields = ['debit_note_no', 'date', 'company.name', 'total_amount'];
        $this->searchAndSort($query, $request, $searchableFields);

        $total_data = $query->orderBy('date')->get();
        $data = $query->paginate(20);
        $data->withQueryString()->links();
        $organization = Organization::select('id', 'name')->get();
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOrganization);
        $companies = Company::select('name', 'id')->orderByRaw('name')->get();
        $allCompanies = ['id' => null, 'name' => 'ALL COMPANIES'];
        $companies->prepend($allCompanies);

        $permissions = [
            // 'canCreateDebitNote' => auth()->user()->can('Create Debit Note'),
            // 'canDeleteDebitNote' => auth()->user()->can('Delete Debit Note')
        ];

        return Inertia::render('Reports/DebitNote', compact('data', 'total_data', 'permissions', 'organization', 'companies', 'organizationId', 'companyId', 'from_date', 'to_date'));
    }
}

