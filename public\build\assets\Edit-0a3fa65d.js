import{_ as o}from"./AdminLayout-a6b1643c.js";import i from"./DeleteUserForm-2dd31a4f.js";import m from"./UpdatePasswordForm-57872a3d.js";import r from"./UpdateProfileInformationForm-855bf924.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-03250c83.js";import"./DangerButton-b2c666d1.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-564dc17f.js";import"./InputLabel-28ecec2a.js";import"./Modal-6b35e01b.js";/* empty css                                                              */import"./SecondaryButton-af5ac4b2.js";import"./TextInput-374b3fdd.js";import"./PrimaryButton-e6f8c536.js";import"./TextArea-0cb791f6.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
