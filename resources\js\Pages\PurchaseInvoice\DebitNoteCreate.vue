<script setup>
import { ref, onMounted, watch, computed, onBeforeMount } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import CreateButton from '@/Components/CreateButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import InputLabel from '@/Components/InputLabel.vue';
import InputError from '@/Components/InputError.vue';
import { Head } from '@inertiajs/vue3';
import { useForm } from 'laravel-precognition-vue-inertia';

const props = defineProps(['data', 'debit_no', 'selectedSerialNumbersIds']);

const selectedProduct = ref([
    {
        organization_id: '',
        company_id: '',
        product_id: '',
        purchase_detail_id: '',
        qty: '',
        purchase_price: '',
        total_price: '',
        total_gst_amount: ''
    }
])

onBeforeMount(() => {
    selectedProduct.value = props.data[0].purchase_order_receive_details.map(item => ({
        organization_id: props.data[0].purchase_order.organization_id,
        company_id: props.data[0].purchase_order.company_id,
        product_id: item.product_id,
        purchase_detail_id: item.id,
        qty: '',
        purchase_price: item.serial_numbers[0].purchase_price,
        total_price: '',
        total_gst_amount: ''
    }));
});

const form = useForm('post', '/debitnotesave', {
    organization_id: props.data[0].purchase_order.organization_id,
    company_id: props.data[0].purchase_order.company_id,
    purchase_invoice_id: props.data[0].id,
    debit_note_no: props.debit_no[props.data[0].purchase_order.organization_id],
    credit_note_number: '',
    date: new Date().toISOString().split('T')[0],
    reason: '',
    selectedProductItem: [],
    sub_total: '',
    cgst: '',
    sgst: '',
    igst: '',
    total_gst: '',
    total_amount: ''
});

const submit = () => {
    form.sub_total = totalPrice.value;
    form.total_gst = totalGstAmount.value;
    form.cgst = cgstAmount.value;
    form.sgst = sgstAmount.value;
    form.igst = igstAmount.value;
    form.total_amount = totalAmount.value;
    form.selectedProductItem = selectedProduct.value.filter(item => item.qty > 0);
    form.submit({
        preserveScroll: true,
        onSuccess: () => form.reset(),
    });
};

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
};

const calculateAmount = (product, index) => {
    const price = parseFloat(selectedProduct.value[index].purchase_price) || 0;
    const gst = parseFloat(product.purchase_order_detail?.gst) || 0;
    const qty = parseFloat(selectedProduct.value[index].qty) || 0;
    const total_price = price * qty;
    const total_gst_amount = price * qty * (gst / 100);
    selectedProduct.value[index].total_price = isNaN(total_price) ? '' : parseFloat(total_price).toFixed(2);
    selectedProduct.value[index].total_gst_amount = isNaN(total_gst_amount) ? '' : parseFloat(total_gst_amount).toFixed(2);
    return total_price + total_gst_amount;
};

const updateAmount = (product, index) => {
    calculateAmount(product, index);
};

const totalAmount = computed(() => {
    return selectedProduct.value.reduce((total, product) => {
        const price = parseFloat(product.total_price) || 0;
        const gst = parseFloat(product.total_gst_amount) || 0;
        return total + price + gst;
    }, 0);
});

const totalGstAmount = computed(() => {
    return selectedProduct.value.reduce((total, product) => {
        return total + (product.total_gst_amount ? parseFloat(product.total_gst_amount) : 0);
    }, 0);
});

const totalPrice = computed(() => {
    return selectedProduct.value.reduce((total, product) => {
        return total + (product.total_price ? parseFloat(product.total_price) : 0);
    }, 0);
});

const cgstAmount = computed(() => {
    if (props.data[0].purchase_order.company.gst_type === 'CGST/SGST') {
        return totalGstAmount.value / 2;
    }
    return 0;
});

const sgstAmount = computed(() => {
    if (props.data[0].purchase_order.company.gst_type === 'CGST/SGST') {
        return totalGstAmount.value / 2;
    }
    return 0;
});

const igstAmount = computed(() => {
    if (props.data[0].purchase_order.company.gst_type === 'IGST') {
        return totalGstAmount.value;
    }
    return 0;
});

const formatAmount = (amount) => {
     if (amount == null || isNaN(amount)) {
        return "0.00";  // Default value
    }

    let amountStr = Number(amount).toFixed(2);
    // let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
    let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const currentDate = computed(() => {
    const today = new Date();
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return today.toLocaleDateString('en-US', options);
});

const goBack = () => {
    window.history.back();
};

</script>

<template>
    <Head title="Create Debit Note"/>
    <AdminLayout>
        <div class="animate-top">
        <form @submit.prevent="submit" class="">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Create Debit Note</h1>
            </div>
            <div class="flex items-center space-x-4">
                <div>
                    <p class="text-sm font-semibold text-gray-900">{{ data[0].purchase_order.organization.name }}</p>
                </div>
                <div class="flex justify-end w-20">
                    <CreateButton :href="route('purchaseinvoice.index')">
                        Back
                    </CreateButton>
                </div>
            </div>
        </div>
        <div class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Debit Note Information</h2>
                    <div class="grid grid-cols-1 gap-x-6 sm:grid-cols-6 pb-2">
                        <div class="sm:col-span-2">
                            <InputLabel for="debit_note_no" value="Debit Note No" />
                            <TextInput
                                id="debit_note_no"
                                type="text"
                                v-model="form.debit_note_no"
                                readonly
                                class="bg-gray-100"
                            />
                            <InputError class="" :message="form.errors.debit_note_no" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="date" value="Date" />
                            <input
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                type="date" v-model="form.date"
                                @change="clearError('date')"
                                :class="{ 'error rounded-md': form.errors.date }"
                            />
                            <InputError v-if="form.invalid('date')" class="" :message="form.errors.date" />
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="credit_note_number" value="Credit Note Number" />
                            <TextInput
                                id="credit_note_number"
                                type="text"
                                v-model="form.credit_note_number"
                                @change="clearError('credit_note_number')"
                                :class="{ 'error rounded-md': form.errors.credit_note_number }"
                                required
                            />
                            <InputError v-if="form.invalid('credit_note_number')" class="" :message="form.errors.credit_note_number" />
                        </div>
                        <div class="sm:col-span-6 mt-4">
                            <InputLabel for="reason" value="Reason for Debit Note" />
                            <textarea
                                id="reason"
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                rows="4"
                                v-model="form.reason"
                                @change="clearError('reason')"
                                :class="{ 'error rounded-md': form.errors.reason }"
                                required
                                placeholder="Enter the reason for creating this debit note..."
                            ></textarea>
                            <InputError v-if="form.invalid('reason')" class="" :message="form.errors.reason" />
                        </div>
                    </div>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Purchase Invoice Information</h2>
                    <div class="space-y-3 bg-gray-50 p-4 rounded-lg">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Invoice No:</span>
                            <span class="font-medium text-sm">{{ data[0]?.customer_invoice_no || '-' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Date:</span>
                            <span class="font-medium text-sm">{{ formatDate(data[0]?.customer_invoice_date) || '-' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <p class="text-sm text-gray-600">GST No:</p>
                            <p class="font-medium text-sm">{{ data[0].purchase_order.company.gst_no ?? '-' }}</p>
                        </div>
                        <div class="flex justify-between">
                            <p class="text-sm text-gray-600">Email:</p>
                            <p class="font-medium text-sm">{{ data[0].purchase_order.company.email ?? '-' }}</p>
                        </div>
                        <div class="flex justify-between">
                            <p class="text-sm text-gray-600">Contact No:</p>
                            <p class="font-medium text-sm">{{ data[0].purchase_order.company.contact_no ?? '-' }}</p>
                        </div>
                        <div class="flex justify-between">
                            <p class="text-sm text-gray-600">City:</p>
                            <p class="font-medium text-sm">{{ data[0].purchase_order.company.city ?? '-' }}</p>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Total Amount:</span>
                            <span class="font-medium text-sm">₹{{ data[0]?.total_amount || 0 }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-if="data[0].purchase_order_receive_details.length > 0" class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border divide-y divide-gray-300 overflow-x-auto">
            <div class="grid grid-cols-1 gap-x-4 sm:grid-cols-12 pb-2" style="width: 140%;">
                <div class="sm:col-span-2">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Product</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Product Code</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Price (₹)</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">GST %</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">QTY</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Debit QTY</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Total Price (₹)</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">GST Amount (₹)</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm font-semibold text-gray-900 leading-6">Total Amount (₹)</p>
                </div>
            </div>
            <div class="grid grid-cols-1 gap-x-4 sm:grid-cols-12 items-center" style="width: 140%;" v-for="(product, index) in data[0].purchase_order_receive_details" :key="index">
                <div class="sm:col-span-2">
                    <p class="text-sm leading-5 text-gray-700">{{ product.product.name ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ product.product.item_code ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ product.serial_numbers[0].purchase_price ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ product.purchase_order_detail?.gst ?? '-' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ product.receive_qty ?? '-'}}</p>
                </div>
                <div class="sm:col-span-1 mb-2">
                    <TextInput
                        id="qty"
                        type="number"
                        v-model="selectedProduct[index].qty"
                        @input="updateAmount(product, index)"
                        @change="clearError('selectedProduct.' + index + '.qty')"
                        :class="{ 'error': form.errors[`selectedProduct.${index}.qty`] }"
                        min="0"
                        :max="product.receive_qty"
                    />
                    <p v-if="form.errors[`selectedProduct.${index}.qty`]" class="text-red-500 text-xs absolute">
                        {{ form.errors[`selectedProduct.${index}.qty`] }}
                    </p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ selectedProduct[index].total_price || '0.00' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ selectedProduct[index].total_gst_amount || '0.00' }}</p>
                </div>
                <div class="sm:col-span-1">
                    <p class="text-sm leading-5 text-gray-700">{{ ((parseFloat(selectedProduct[index].total_price) || 0) + (parseFloat(selectedProduct[index].total_gst_amount) || 0)).toFixed(2) }}</p>
                </div>
            </div>
        </div>
        <div v-if="data[0].purchase_order_receive_details.length > 0" class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                <div class="sm:col-span-3 space-y-2">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Amount Summary</h2>
                </div>
                <div class="sm:col-span-3">
                    <div class="inline-flex flex-col space-y-1 items-center justify-end w-full">
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm text-gray-700">Sub Total (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalPrice) }}</p>
                        </div>
                        <div v-if="data[0].purchase_order.company.gst_type === 'CGST/SGST'" class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm text-gray-700">CGST (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(cgstAmount) }}</p>
                        </div>
                        <div v-if="data[0].purchase_order.company.gst_type === 'CGST/SGST'" class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm text-gray-700">SGST (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(sgstAmount) }}</p>
                        </div>
                        <div v-if="data[0].purchase_order.company.gst_type === 'IGST'" class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm text-gray-700">IGST (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(igstAmount) }}</p>
                        </div>
                        <div class="inline-flex items-center justify-end w-full space-x-3">
                            <p class="text-sm text-gray-700">Total GST (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalGstAmount) }}</p>
                        </div>
                        <div class="inline-flex items-center justify-end w-full space-x-3 border-t pt-3">
                            <p class="font-semibold text-gray-900">Total Amount (₹):</p>
                            <p class="text-base font-semibold text-gray-900 w-32">{{ formatAmount(totalAmount) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-6 flex items-center justify-end gap-x-6">
            <SecondaryButton @click="goBack">Cancel</SecondaryButton>
            <PrimaryButton :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                Create Debit Note
            </PrimaryButton>
        </div>
        </form>
        </div>
    </AdminLayout>
</template>
