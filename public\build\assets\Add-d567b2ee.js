import{r as w,j as G,l as M,o as d,c as u,a as l,u as n,w as O,F as B,Z as se,b as s,t as c,f,d as oe,n as y,k as ae,v as ne,i as R,g as le,T as ie,s as re,x as de}from"./app-97275a91.js";import{_ as ue,a as ce}from"./AdminLayout-595ad5a7.js";import{_ as h}from"./InputLabel-eb73087c.js";import{P as me}from"./PrimaryButton-46ac4375.js";import{_ as F}from"./TextInput-11c46564.js";import{_ as _e}from"./TextArea-5e21e606.js";import{_ as pe}from"./RadioButton-1a065901.js";import{_ as E}from"./SearchableDropdown-9d1b12d3.js";import{u as ve}from"./index-05d29b1c.js";/* empty css                                                                          */import{_ as fe}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as ye}from"./Checkbox-c09a6665.js";const A=b=>(re("data-v-5fdc093a"),b=b(),de(),b),he={class:"h-screen animate-top"},ge={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},xe={class:"sm:flex sm:items-center"},be=A(()=>s("div",{class:"sm:flex-auto"},[s("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Receive Payment")],-1)),ke={class:"flex items-center justify-between"},we={key:0,class:"text-base font-semibold leading-6 text-gray-900"},Ve=["onSubmit"],Fe={class:"border-b border-gray-900/10 pb-12"},Ae={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},Ce={class:"sm:col-span-3"},Ne={class:"relative mt-2"},Se={class:"sm:col-span-3"},$e={class:"relative mt-2"},Ue={class:"sm:col-span-2"},Pe={class:"relative mt-2"},ze={key:0,class:"sm:col-span-3"},Te={class:"relative mt-2"},De={key:1,class:"sm:col-span-3"},Oe={key:2,class:"sm:col-span-2"},Be={key:3,class:"sm:col-span-1"},Ee={key:4,class:"sm:col-span-1"},Ie={key:5,class:"sm:col-span-1"},je={key:6,class:"sm:col-span-3"},Me={key:7,class:"sm:col-span-2"},Re={class:"mt-4 flex justify-start"},Le={class:"text-base font-semibold"},Ye={key:8,class:"sm:col-span-2"},qe={key:9,class:"sm:col-span-2"},He={key:10,class:"sm:col-span-2"},Ze={class:"relative mt-2"},Ge={key:11,class:"sm:col-span-3"},Je={class:"sm:col-span-6"},Ke={class:"overflow-x-auto divide-y divide-gray-300 w-full"},Qe=A(()=>s("div",{class:"w-full"},[s("thead",{class:"w-full"},[s("tr",{class:""},[s("th",{scope:"col",class:"w-8"}),s("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),s("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),s("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1)),We={style:{"overflow-y":"auto","max-height":"318px"}},Xe={class:"divide-y divide-gray-300 bg-white"},et={class:"whitespace-nowrap px-2 text-sm text-gray-900"},tt={class:"text-sm text-gray-900 leading-6 py-1.5"},st={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},ot={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},at={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},nt={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},lt={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},it={key:0,class:"text-red-500 text-xs absolute"},rt={class:"whitespace-nowrap px-2 text-sm text-gray-900"},dt={class:"sm:col-span-2"},ut={class:"mt-2 p-3 bg-gray-50 rounded-md"},ct={class:"space-y-2 text-sm"},mt={class:"flex items-center gap-2"},_t=A(()=>s("hr",{class:"my-2"},null,-1)),pt={class:"flex justify-between items-center font-semibold"},vt=A(()=>s("span",null,"Settlement:",-1)),ft={class:"flex flex-col"},yt={class:"flex justify-between items-center font-semibold"},ht=A(()=>s("span",null,"Advance Amount:",-1)),gt={key:0,class:"text-red-500 text-xs mt-1"},xt={key:12,class:"sm:col-span-6"},bt={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},kt=A(()=>s("thead",null,[s("tr",null,[s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1)),wt={class:"divide-y divide-gray-300 bg-white"},Vt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},Ft={class:"whitespace-nowrap py-3 text-sm text-gray-900"},At={class:"flex flex-col"},Ct={class:"text-sm text-gray-900"},Nt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},St={class:"whitespace-nowrap py-3 text-sm text-gray-900"},$t={class:"flex mt-6 items-center justify-between"},Ut={class:"ml-auto flex items-center justify-end gap-x-6"},Pt=A(()=>s("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),zt={key:0,class:"text-sm text-gray-600"},Tt={__name:"Add",props:["paymentType","bankinfo","organization","customers","invoices","credit"],setup(b){const N=b;w([]);const t=ve("post","/receipt",{organization_id:"",customer_id:"",payment_type:"",date:"",note:"",amount:0,tds_amount:0,discount_amount:0,round_off:0,check_number:"",bank_name:"",org_bank_id:"",invoice:[],settled_amount:"",advance_amount:"",is_credit:"",credit_data:[],total_unused_amount:""}),S=w(""),J=()=>{t.settled_amount=U.value,t.advance_amount=P.value,t.total_unused_amount=$.value,t.is_credit=_.value,t.invoice=g.value,t.credit_data=k.value,t.submit({preserveScroll:!0,onSuccess:()=>t.reset()})},K=(a,o)=>{S.value=o,t.payment_type=a,t.errors.payment_type=null,o==="Cash"?t.note="Cash":t.note==="Cash"&&(t.note="")},T=w([]),k=w([]),$=w(""),L=w([]),Q=(a,o)=>{const e=N.bankinfo.filter(r=>r.organization_id===a);L.value=e,t.customer_id&&Y(t.customer_id,a);const i=N.credit.filter(r=>r.organization_id===a&&r.customer_id===t.customer_id);k.value=i,$.value=k.value.reduce((r,m)=>r+m.unused_amount,0),t.organization_id=a,t.errors.organization_id=null},W=(a,o)=>{Y(a,t.organization_id);const e=N.credit.filter(i=>i.customer_id===a&&i.organization_id===t.organization_id);k.value=e,$.value=k.value.reduce((i,r)=>i+r.unused_amount,0),t.customer_id=a,t.errors.customer_id=null},Y=(a,o)=>{if(!a||!o){T.value=[];return}const e=N.customers.find(m=>m.id===a),i=e==null?void 0:e.party_id,r=N.invoices.filter(m=>{const x=m.organization_id===o;return m.invoice_type==="sales"?x&&m.customer_id===a:m.invoice_type==="purchase"&&i?x&&m.party_id===i:!1});T.value=r},X=(a,o)=>{t.org_bank_id=a,t.errors.org_bank_id=null},U=G(()=>g.value.reduce((a,o)=>{if(o.check&&o.amount){const e=parseFloat(o.amount);return o.invoice_type==="sales"?a+e:a-e}return a},0)),P=G(()=>{const a=parseFloat(t.amount||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0),o=parseFloat(t.round_off||0),e=U.value;return a-e-o}),I=()=>{},V=a=>{let o=a.toFixed(2).toString(),[e,i]=o.split("."),r=e.substring(e.length-3),m=e.substring(0,e.length-3);return m!==""&&(r=","+r),`${m.replace(/\B(?=(\d{2})+(?!\d))/g,",")+r}.${i}`},q=a=>{const o=new Date(a),e={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",e)},_=w("No"),ee=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],te=a=>{const o=_.value==="Yes"?parseFloat($.value||0):parseFloat(t.amount||0)+parseFloat(t.round_off||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0);if(!g.value[a].check){g.value[a].amount=0;return}const e=g.value.filter(v=>v.check),i=e.some(v=>v.invoice_type==="sales"),r=e.some(v=>v.invoice_type==="purchase");if(i&&r&&e.length>1&&H(o,e))return;let m=o;g.value.forEach((v,D)=>{if(v.check&&D!==a&&parseFloat(v.amount||0)>0){const z=parseFloat(v.amount||0);v.invoice_type==="sales"?m-=z:v.invoice_type==="purchase"&&(m+=z)}});const x=parseFloat(g.value[a].pending_amount||0),p=Math.min(x,Math.max(0,m));g.value[a].amount=p.toFixed(2)},H=(a,o)=>{const e=o.filter(p=>p.invoice_type==="sales"),i=o.filter(p=>p.invoice_type==="purchase"),r=e.reduce((p,v)=>p+parseFloat(v.pending_amount||0),0),m=i.reduce((p,v)=>p+parseFloat(v.pending_amount||0),0),x=r-m;return Math.abs(a-Math.abs(x))<=1?(o.forEach(p=>{p.amount=parseFloat(p.pending_amount||0).toFixed(2)}),!0):x>0&&a>=x?(o.forEach(p=>{p.amount=parseFloat(p.pending_amount||0).toFixed(2)}),!0):!1},g=w([{id:"",date:"",invoice_no:"",total_amount:"",pending_amount:"",check:!1,amount:0}]),j=()=>{g.value=T.value.map(a=>({id:a.id,date:a.date,invoice_no:a.invoice_no,total_amount:parseFloat(a.total_amount||0).toFixed(2),pending_amount:parseFloat(a.pending_amount||0).toFixed(2),invoice_type:a.invoice_type||"sales",check:!1,amount:"0.00"}))};M(T,()=>{j()}),M(_,()=>{j()}),M(()=>t.amount,()=>{if(_.value==="No"){j();const a=g.value.filter(i=>i.check),o=a.some(i=>i.invoice_type==="sales"),e=a.some(i=>i.invoice_type==="purchase");if(o&&e&&a.length>1){const i=parseFloat(t.amount||0)+parseFloat(t.round_off||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0);H(i,a)}}});const C=a=>{t.errors[a]=null,t.errors.settled_amount=null};return(a,o)=>(d(),u(B,null,[l(n(se),{title:"Receipt"}),l(ue,null,{default:O(()=>[s("div",he,[s("div",ge,[s("div",xe,[be,s("div",ke,[k.value.length>0?(d(),u("div",we," Credits Available: ₹"+c(V($.value)),1)):f("",!0)])]),s("form",{onSubmit:oe(J,["prevent"]),class:""},[s("div",Fe,[s("div",Ae,[s("div",Ce,[l(h,{for:"payment_type",value:"Organization"}),s("div",Ne,[l(E,{options:b.organization,modelValue:n(t).organization_id,"onUpdate:modelValue":o[0]||(o[0]=e=>n(t).organization_id=e),onOnchange:Q,class:y({"error rounded-md":n(t).errors.organization_id})},null,8,["options","modelValue","class"])])]),s("div",Se,[l(h,{for:"payment_type",value:"Customer"}),s("div",$e,[l(E,{options:b.customers,modelValue:n(t).customer_id,"onUpdate:modelValue":o[1]||(o[1]=e=>n(t).customer_id=e),onOnchange:W,class:y({"error rounded-md":n(t).errors.customer_id})},null,8,["options","modelValue","class"])])]),s("div",Ue,[l(h,{for:"role_id",value:"Payment Through Credit ?"}),s("div",Pe,[l(pe,{modelValue:_.value,"onUpdate:modelValue":o[2]||(o[2]=e=>_.value=e),options:ee},null,8,["modelValue"])])]),_.value=="No"?(d(),u("div",ze,[l(h,{for:"payment_type",value:"Payment Type"}),s("div",Te,[l(E,{options:b.paymentType,modelValue:n(t).payment_type,"onUpdate:modelValue":o[3]||(o[3]=e=>n(t).payment_type=e),onOnchange:K,class:y({"error rounded-md":n(t).errors.payment_type})},null,8,["options","modelValue","class"])])])):f("",!0),_.value=="No"?(d(),u("div",De,[l(h,{for:"date",value:"Payment Date"}),ae(s("input",{"onUpdate:modelValue":o[4]||(o[4]=e=>n(t).date=e),class:y(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":n(t).errors.date}]),type:"date",onChange:o[5]||(o[5]=e=>C("date"))},null,34),[[ne,n(t).date]])])):f("",!0),_.value=="No"?(d(),u("div",Oe)):f("",!0),_.value=="No"?(d(),u("div",Be,[l(h,{for:"tds_amount",value:"TDS Amount"}),l(F,{type:"text",onChange:o[6]||(o[6]=e=>C("tds_amount")),onInput:o[7]||(o[7]=e=>I()),modelValue:n(t).tds_amount,"onUpdate:modelValue":o[8]||(o[8]=e=>n(t).tds_amount=e),class:y({"error rounded-md":n(t).errors.tds_amount})},null,8,["modelValue","class"])])):f("",!0),_.value=="No"?(d(),u("div",Ee,[l(h,{for:"discount_amount",value:"Discount Amount"}),l(F,{type:"text",onChange:o[9]||(o[9]=e=>C("discount_amount")),onInput:o[10]||(o[10]=e=>I()),modelValue:n(t).discount_amount,"onUpdate:modelValue":o[11]||(o[11]=e=>n(t).discount_amount=e),class:y({"error rounded-md":n(t).errors.discount_amount})},null,8,["modelValue","class"])])):f("",!0),_.value=="No"?(d(),u("div",Ie,[l(h,{for:"round_off",value:"Round Off"}),l(F,{type:"text",onChange:o[12]||(o[12]=e=>C("round_off")),modelValue:n(t).round_off,"onUpdate:modelValue":o[13]||(o[13]=e=>n(t).round_off=e),class:y({"error rounded-md":n(t).errors.round_off})},null,8,["modelValue","class"])])):f("",!0),_.value=="No"?(d(),u("div",je,[l(h,{for:"amount",value:"Amount"}),l(F,{id:"amount",type:"text",onChange:o[14]||(o[14]=e=>C("amount")),onInput:o[15]||(o[15]=e=>I()),modelValue:n(t).amount,"onUpdate:modelValue":o[16]||(o[16]=e=>n(t).amount=e),class:y({"error rounded-md":n(t).errors.amount})},null,8,["modelValue","class"])])):f("",!0),_.value=="No"?(d(),u("div",Me,[l(h,{for:"advance",value:"Advance(Ref) Amount"}),s("div",Re,[s("p",Le,c(V(P.value)),1)])])):f("",!0),S.value=="Cheque"&&_.value=="No"?(d(),u("div",Ye,[l(h,{for:"check_number",value:"Cheque Number"}),l(F,{id:"check_number",type:"text",modelValue:n(t).check_number,"onUpdate:modelValue":o[17]||(o[17]=e=>n(t).check_number=e),class:y({"error rounded-md":n(t).errors["data.check_number"]})},null,8,["modelValue","class"])])):f("",!0),S.value=="Cheque"&&_.value=="No"?(d(),u("div",qe,[l(h,{for:"bank_name",value:"Bank Name"}),l(F,{id:"bank_name",type:"text",modelValue:n(t).bank_name,"onUpdate:modelValue":o[18]||(o[18]=e=>n(t).bank_name=e),class:y({"error rounded-md":n(t).errors["data.bank_name"]})},null,8,["modelValue","class"])])):f("",!0),S.value!="Cash"&&_.value=="No"?(d(),u("div",He,[l(h,{for:"org_bank_id",value:"Our Bank"}),s("div",Ze,[l(E,{options:L.value,modelValue:n(t).org_bank_id,"onUpdate:modelValue":o[19]||(o[19]=e=>n(t).org_bank_id=e),onOnchange:X,class:y({"error rounded-md":n(t).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):f("",!0),S.value!="Cash"&&_.value=="No"?(d(),u("div",Ge)):f("",!0),s("div",Je,[s("table",Ke,[Qe,s("div",We,[s("tbody",Xe,[(d(!0),u(B,null,R(g.value,(e,i)=>(d(),u("tr",{key:i},[s("td",et,[s("div",tt,[l(ye,{name:"check",checked:e.check,"onUpdate:checked":r=>e.check=r,onChange:r=>te(i)},null,8,["checked","onUpdate:checked","onChange"])])]),s("td",st,[s("span",{class:y([e.invoice_type==="sales"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},c(e.invoice_type==="sales"?"SALES":"PURCHASE"),3)]),s("td",ot,c(e.invoice_no),1),s("td",at,c(e.total_amount),1),s("td",nt,c(e.pending_amount),1),s("td",lt,[l(F,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":r=>e.amount=r,onChange:r=>C("invoice."+i+".amount"),class:y({error:n(t).errors[`invoice.${i}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),n(t).errors[`invoice.${i}.amount`]?(d(),u("p",it,c(n(t).errors[`invoice.${i}.amount`]),1)):f("",!0)]),s("td",rt,c(q(e.date)),1)]))),128))])])])]),s("div",dt,[l(h,{for:"note",value:"Net Settlement Summary"}),s("div",ut,[s("div",ct,[(d(!0),u(B,null,R(g.value.filter(e=>e.check),e=>(d(),u("div",{key:e.id,class:"flex justify-between items-center"},[s("div",mt,[s("span",{class:y([e.invoice_type==="sales"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800","inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium"])},c(e.invoice_type==="sales"?"S":"P"),3),s("span",null,c(e.invoice_no),1)]),s("span",{class:y([e.invoice_type==="sales"?"text-green-600":"text-blue-600","font-medium"])},c(e.invoice_type==="sales"?"+":"-")+"₹"+c(V(parseFloat(e.amount||0))),3)]))),128)),_t,s("div",pt,[vt,s("div",ft,[s("span",{class:y(U.value>=0?"text-green-600":"text-red-600")}," ₹"+c(V(Math.abs(U.value)))+" "+c(U.value>=0?"(Receive)":"(Pay)"),3)])]),s("div",yt,[ht,s("span",{class:y(P.value>=0?"text-green-600":"text-red-600")},c(P.value>0?"+":"")+"₹"+c(V(parseFloat(P.value||0))),3)])])]),n(t).errors.settled_amount?(d(),u("p",gt,c(n(t).errors.settled_amount),1)):f("",!0)]),_.value=="No"?(d(),u("div",xt,[l(h,{for:"note",value:"Note"}),l(_e,{id:"note",type:"text",rows:2,modelValue:n(t).note,"onUpdate:modelValue":o[20]||(o[20]=e=>n(t).note=e)},null,8,["modelValue"])])):f("",!0)]),k.value.length>0&&_.value=="Yes"?(d(),u("table",bt,[kt,s("tbody",wt,[(d(!0),u(B,null,R(k.value,(e,i)=>{var r,m,x,p,v,D,z,Z;return d(),u("tr",{key:i},[s("td",Vt,c(q(e.date)),1),s("td",Ft,[s("div",At,[s("div",Ct,c((m=(r=e.paymentreceive)==null?void 0:r.bank_info)!=null&&m.bank_name?(p=(x=e.paymentreceive)==null?void 0:x.bank_info)==null?void 0:p.bank_name:"Cash")+" - "+c((D=(v=e.paymentreceive)==null?void 0:v.bank_info)!=null&&D.account_number?(Z=(z=e.paymentreceive)==null?void 0:z.bank_info)==null?void 0:Z.account_number:""),1)])]),s("td",Nt,c(V(e.amount)),1),s("td",St,c(V(e.unused_amount)),1)])}),128))])])):f("",!0)]),s("div",$t,[s("div",Ut,[l(ce,{href:a.route("receipt.index")},{svg:O(()=>[Pt]),_:1},8,["href"]),l(me,{disabled:n(t).processing},{default:O(()=>[le("Save")]),_:1},8,["disabled"]),l(ie,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:O(()=>[n(t).recentlySuccessful?(d(),u("p",zt,"Saved.")):f("",!0)]),_:1})])])],40,Ve)])])]),_:1})],64))}},Zt=fe(Tt,[["__scopeId","data-v-5fdc093a"]]);export{Zt as default};
