import{K as _,o as g,c,a,u as s,w as u,F as x,Z as b,b as n,d as C,g as $,T as U,f as k}from"./app-16701445.js";import{_ as N,a as L}from"./AdminLayout-e15be38d.js";import{_ as d}from"./InputError-11376965.js";import{_ as i}from"./InputLabel-d69efee6.js";import{P as S}from"./PrimaryButton-eddb8b77.js";import{_ as r}from"./TextInput-764e3400.js";import{_ as w}from"./TextArea-b68da786.js";import{_ as v}from"./FileUpload-6a456abf.js";import{u as F}from"./index-10107770.js";import"./_plugin-vue_export-helper-c27b6911.js";const O={class:"animate-top bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},h=n("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Update Organization",-1),j=["onSubmit"],z={class:"border-b border-gray-900/10 pb-4"},B={class:"mt-5 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},P={class:"sm:col-span-4"},T={class:"sm:col-span-2"},A={class:"sm:col-span-1"},E={class:"sm:col-span-1"},D={class:"sm:col-span-2"},I={class:"sm:col-span-2"},G={class:"sm:col-span-3"},K={class:"sm:col-span-3"},M={class:"sm:col-span-2"},R={class:"sm:col-span-2"},Z={class:"sm:col-span-2"},q={class:"sm:col-span-2"},H={class:"sm:col-span-2"},J={class:"sm:col-span-6"},Q={class:"flex mt-6 items-center justify-between"},W={class:"ml-auto flex items-center justify-end gap-x-6"},X=n("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1),Y={key:0,class:"text-sm text-gray-600"},ue={__name:"Edit",props:{data:{type:Object},filepath:{type:Object}},setup(ee){const l=_().props.data,p=_().props.filepath.view,e=F("post","/organization",{id:l.id,name:l?l.name:"",address_line_1:l?l.address_line_1:"",address_line_2:l?l.address_line_2:"",pincode:l?l.pincode:"",city:l?l.city:"",state:l?l.state:"",gst_no:l?l.gst_no:"",pan_no:l?l.pan_no:"",drug_licence_no:l?l.drug_licence_no:"",email:l?l.email:"",contact_no:l?l.contact_no:"",remarks:l?l.remarks:"",logo:l?p+l.logo:"/uploads/companyprofile/defaultimg.png",signature:l?p+l.signature:"/uploads/companyprofile/defaultimg.png"}),f=()=>e.submit({preserveScroll:!0,resetOnSuccess:!1}),y=m=>{e.logo=m},V=m=>{e.signature=m};return(m,o)=>(g(),c(x,null,[a(s(b),{title:"Organization"}),a(N,null,{default:u(()=>[n("div",O,[h,n("form",{onSubmit:C(f,["prevent"]),class:""},[n("div",z,[n("div",B,[n("div",P,[a(i,{for:"name",value:"Name"}),a(r,{id:"name",type:"text",modelValue:s(e).name,"onUpdate:modelValue":o[0]||(o[0]=t=>s(e).name=t),autocomplete:"name",onChange:o[1]||(o[1]=t=>s(e).validate("name"))},null,8,["modelValue"]),a(d,{class:"",message:s(e).errors.name},null,8,["message"])]),n("div",T,[a(i,{for:"email",value:"Email"}),a(r,{id:"email",type:"text",modelValue:s(e).email,"onUpdate:modelValue":o[2]||(o[2]=t=>s(e).email=t),autocomplete:"email",onChange:o[3]||(o[3]=t=>s(e).validate("email"))},null,8,["modelValue"]),a(d,{class:"",message:s(e).errors.email},null,8,["message"])]),n("div",A,[a(i,{for:"gst_no",value:"GST Number"}),a(r,{id:"gst_no",type:"text",modelValue:s(e).gst_no,"onUpdate:modelValue":o[4]||(o[4]=t=>s(e).gst_no=t),maxLength:"15",autocomplete:"gst_no",onChange:o[5]||(o[5]=t=>s(e).validate("gst_no"))},null,8,["modelValue"]),a(d,{class:"",message:s(e).errors.gst_no},null,8,["message"])]),n("div",E,[a(i,{for:"pan_no",value:"PAN Number"}),a(r,{id:"pan_no",type:"text",modelValue:s(e).pan_no,"onUpdate:modelValue":o[6]||(o[6]=t=>s(e).pan_no=t),maxLength:"15",autocomplete:"pan_no",onChange:o[7]||(o[7]=t=>s(e).validate("pan_no"))},null,8,["modelValue"]),a(d,{class:"",message:s(e).errors.pan_no},null,8,["message"])]),n("div",D,[a(i,{for:"drug_licence_no",value:"DrugLicence Number"}),a(r,{id:"drug_licence_no",type:"text",modelValue:s(e).drug_licence_no,"onUpdate:modelValue":o[8]||(o[8]=t=>s(e).drug_licence_no=t),autocomplete:"drug_licence_no",onChange:o[9]||(o[9]=t=>s(e).validate("drug_licence_no"))},null,8,["modelValue"]),a(d,{class:"",message:s(e).errors.drug_licence_no},null,8,["message"])]),n("div",I,[a(i,{for:"contact_no",value:"Contact Number"}),a(r,{id:"contact_no",type:"text",modelValue:s(e).contact_no,"onUpdate:modelValue":o[10]||(o[10]=t=>s(e).contact_no=t),numeric:!0,maxLength:"10",autocomplete:"contact_no",onChange:o[11]||(o[11]=t=>s(e).validate("contact_no"))},null,8,["modelValue"]),a(d,{class:"",message:s(e).errors.contact_no},null,8,["message"])]),n("div",G,[a(i,{for:"address_line_1",value:"Address Line 1"}),a(r,{id:"address_line_1",type:"text",modelValue:s(e).address_line_1,"onUpdate:modelValue":o[12]||(o[12]=t=>s(e).address_line_1=t),autocomplete:"address_line_1",onChange:o[13]||(o[13]=t=>s(e).validate("address_line_1"))},null,8,["modelValue"]),a(d,{class:"",message:s(e).errors.address_line_1},null,8,["message"])]),n("div",K,[a(i,{for:"address_line_2",value:"Address Line 2"}),a(r,{id:"address_line_2",type:"text",modelValue:s(e).address_line_2,"onUpdate:modelValue":o[14]||(o[14]=t=>s(e).address_line_2=t),autocomplete:"address_line_2"},null,8,["modelValue"]),a(d,{class:"",message:s(e).errors.address_line_2},null,8,["message"])]),n("div",M,[a(i,{for:"city",value:"City"}),a(r,{id:"city",type:"text",modelValue:s(e).city,"onUpdate:modelValue":o[15]||(o[15]=t=>s(e).city=t),autocomplete:"city",onChange:o[16]||(o[16]=t=>s(e).validate("city"))},null,8,["modelValue"]),a(d,{class:"",message:s(e).errors.city},null,8,["message"])]),n("div",R,[a(i,{for:"state",value:"State"}),a(r,{id:"state",type:"text",modelValue:s(e).state,"onUpdate:modelValue":o[17]||(o[17]=t=>s(e).state=t),autocomplete:"state",onChange:o[18]||(o[18]=t=>s(e).validate("state"))},null,8,["modelValue"]),a(d,{class:"",message:s(e).errors.state},null,8,["message"])]),n("div",Z,[a(i,{for:"pincode",value:"Pincode"}),a(r,{id:"pincode",type:"text",numeric:!0,maxLength:"6",modelValue:s(e).pincode,"onUpdate:modelValue":o[19]||(o[19]=t=>s(e).pincode=t),autocomplete:"pincode",onChange:o[20]||(o[20]=t=>s(e).validate("pincode"))},null,8,["modelValue"]),a(d,{class:"",message:s(e).errors.pincode},null,8,["message"])]),n("div",q,[a(i,{for:"logo",value:"Company Logo"}),a(v,{label:"Company Logo",inputId:"logo",inputName:"logo",fileUrl:s(e).logo,onFile:y},null,8,["fileUrl"])]),n("div",H,[a(i,{for:"signature",value:"Company signature"}),a(v,{label:"Company signature",inputId:"signature",inputName:"signature",fileUrl:s(e).signature,onFile:V},null,8,["fileUrl"])]),n("div",J,[a(i,{for:"remarks",value:"Remarks"}),a(w,{id:"remarks",type:"text",rows:3,modelValue:s(e).remarks,"onUpdate:modelValue":o[21]||(o[21]=t=>s(e).remarks=t),autocomplete:"remarks",onChange:o[22]||(o[22]=t=>s(e).validate("remarks"))},null,8,["modelValue"]),a(d,{class:"",message:s(e).errors.remarks},null,8,["message"])])])]),n("div",Q,[n("div",W,[a(L,{href:m.route("organization.index")},{svg:u(()=>[X]),_:1},8,["href"]),a(S,{disabled:s(e).processing},{default:u(()=>[$("Save")]),_:1},8,["disabled"]),a(U,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:u(()=>[s(e).recentlySuccessful?(g(),c("p",Y,"Saved.")):k("",!0)]),_:1})])])],40,j)])]),_:1})],64))}};export{ue as default};
