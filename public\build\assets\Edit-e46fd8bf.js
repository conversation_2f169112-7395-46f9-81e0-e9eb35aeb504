import{r as j,C as z,j as P,A as Q,o as p,c as g,a as m,u as l,w as q,F as C,Z as G,b as e,d as M,t as _,g as D,n as f,e as I,f as x,k as L,v as Y,i as T,s as Z,x as H}from"./app-b320a640.js";import{_ as J}from"./AdminLayout-aac65a75.js";import{_ as K}from"./CreateButton-7506df4f.js";import{P as W}from"./PrimaryButton-cb5bb104.js";import{_ as w}from"./TextInput-cb7ba6f7.js";import{_ as X}from"./DateInput-6241ce46.js";import{_ as F}from"./InputLabel-946d937b.js";import{_ as ee}from"./SearchableDropdown-4997ffb6.js";import{u as te}from"./index-c4301439.js";import{_ as se}from"./_plugin-vue_export-helper-c27b6911.js";const n=u=>(Z("data-v-2d26e99d"),u=u(),H(),u),oe={class:"animate-top"},ae=["onSubmit"],re={class:"sm:flex sm:items-center"},le=n(()=>e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Receive Product")],-1)),ce={class:"flex items-center space-x-4"},ie={class:"text-sm font-semibold text-gray-900"},ne={class:"flex justify-end w-20"},de={class:"mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},_e={class:"inline-flex items-start space-x-6 justify-start w-full"},ue={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},me={class:"inline-flex items-center justify-start w-full space-x-2"},pe=n(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Company Name:",-1)),ve={class:"text-sm leading-6 text-gray-700"},ge={class:"inline-flex items-center justify-start w-full space-x-2"},he=n(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"GST No:",-1)),ye={class:"text-sm leading-6 text-gray-700"},fe={class:"inline-flex items-center justify-start w-full space-x-2"},xe=n(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Email:",-1)),be={class:"text-sm leading-6 text-gray-700"},we={class:"inline-flex items-center justify-start w-full space-x-2"},Ve=n(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1)),Pe={class:"text-sm leading-6 text-gray-700"},$e={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},qe={class:"inline-flex items-center justify-start w-full space-x-2"},Ce=n(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Receive No:",-1)),Fe={class:"text-sm leading-6 text-gray-700"},ke={class:"inline-flex items-center justify-start w-full space-x-2"},Ne=n(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Number:",-1)),Se={class:"text-sm leading-6 text-gray-700"},Ue={class:"inline-flex items-center justify-start w-full space-x-2"},je=n(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Date:",-1)),De={class:"text-sm leading-6 text-gray-700"},Ie={key:0,class:"mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},Te={class:"grid grid-cols-1 gap-x-6 sm:grid-cols-12 pb-2"},Ae={class:"sm:col-span-4"},Be={class:"sm:col-span-4"},Oe={class:"sm:col-span-4"},Ee={class:"relative mt-2"},Re={key:1,class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border divide-y divide-gray-300 overflow-x-auto"},ze={class:"grid grid-cols-1 gap-x-4 sm:grid-cols-12 pb-2",style:{width:"140%"}},Qe=n(()=>e("div",{class:"sm:col-span-2"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Product")],-1)),Ge={class:"sm:col-span-1"},Me={key:0,class:"text-sm font-semibold text-gray-900 leading-6"},Le={key:1,class:"text-sm font-semibold text-gray-900 leading-6"},Ye=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Price (₹)")],-1)),Ze=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"GST %")],-1)),He=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"QTY")],-1)),Je=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Received QTY")],-1)),Ke=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"MRP (₹)")],-1)),We=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Purchase Price (₹)")],-1)),Xe=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"QTY")],-1)),et=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Batch")],-1)),tt=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Total Amount")],-1)),st={class:"sm:col-span-2"},ot={class:"text-sm leading-5 text-gray-700"},at={class:"sm:col-span-1"},rt={class:"text-sm leading-5 text-gray-700"},lt={class:"sm:col-span-1"},ct={class:"text-sm leading-5 text-gray-700"},it={class:"sm:col-span-1"},nt={class:"text-sm leading-5 text-gray-700"},dt={class:"sm:col-span-1"},_t={class:"text-sm leading-5 text-gray-700"},ut={class:"sm:col-span-1"},mt={class:"text-sm leading-5 text-gray-700"},pt={class:"sm:col-span-1 mb-2"},vt={class:"sm:col-span-1 mb-2"},gt={class:"sm:col-span-1 mb-2"},ht={key:0,class:"text-red-500 text-xs absolute"},yt={class:"flex sm:col-span-1 mb-2"},ft={class:"sm:col-span-1"},xt={class:"text-sm leading-5 text-gray-700"},bt={key:0,class:"sm:col-span-9 mb-2"},wt={class:"sm:col-span-3"},Vt={class:"sm:col-span-3"},Pt={class:"sm:col-span-3"},$t={key:0,class:"text-red-500 text-xs absolute"},qt={key:2,class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Ct={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Ft=n(()=>e("div",{class:"sm:col-span-3 space-y-2"},null,-1)),kt={class:"sm:col-span-3"},Nt={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},St={class:"inline-flex items-center justify-end w-full space-x-3"},Ut=n(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),jt={class:"text-base font-semibold text-gray-900 w-32"},Dt={class:"inline-flex items-center justify-end w-full space-x-3"},It=n(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Total GST (₹):",-1)),Tt={class:"text-base font-semibold text-gray-900 w-32"},At={class:"inline-flex items-center justify-end w-full space-x-3"},Bt=n(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Round Off (₹):",-1)),Ot={class:"w-32"},Et={class:"inline-flex items-center justify-end w-full space-x-3"},Rt=n(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),zt={class:"text-base font-semibold text-gray-900 w-32"},Qt={key:3,class:"flex mt-6 items-center justify-between"},Gt={class:"ml-auto flex items-center justify-end gap-x-6"},Mt={__name:"Edit",props:["data","purchase_order_receives","salesuser"],setup(u){const h=u,i=j([{organization_id:"",company_id:"",product_id:"",purchase_order_detail_id:"",purchase_order_id:"",qty:"",po_receive_number:"",total_qty:"",received_qty:"",mrp:"",purchase_price:"",total_amount:"",total_price:"",total_gst_amount:""}]);z(()=>{i.value=h.data[0].purchase_order_detail_for_receive.map(a=>({organization_id:h.data[0].organization_id,company_id:h.data[0].company_id,product_id:a.product_id,purchase_order_detail_id:a.id,purchase_order_id:h.data[0].id,po_receive_number:h.purchase_order_receives[0].po_receive_number,total_qty:a.qty,received_qty:a.receive_qty,receive_qty:"",total_batch:"",mrp:"",purchase_price:"",total_amount:"",total_price:"",total_gst_amount:""}))});const o=te("post","/purchaseinvoice",{purchase_order_receive_id:h.purchase_order_receives[0].id,purchase_order_id:h.purchase_order_receives[0].purchase_order_id,receivedProduct:[],created_by:h.purchase_order_receives[0].created_by,total_price:"",total_gst_amount:"",total_amount:"",round_off:"0.00",customer_invoice_no:h.purchase_order_receives[0].customer_invoice_no,customer_invoice_date:h.purchase_order_receives[0].customer_invoice_date,category:h.data[0].category,type:h.data[0].type}),A=()=>{parseFloat(o.round_off)>.99||(o.total_amount=N.value,o.total_gst_amount=S.value,o.total_price=U.value,o.receivedProduct=i.value.map((s,d)=>({...s,productDetails:$.value[d]||[]})),o.submit({preserveScroll:!0,onSuccess:()=>o.reset()}))},b=a=>{o.errors[a]=null},$=j([]),B=a=>{o.errors["receivedProduct."+a+".receive_qty"]=null;const s=i.value[a].total_batch,d=i.value[a].total_qty,c=i.value[a].received_qty,t=[];let r;if(s&&!isNaN(s)){s>d-c?r=d-c:r=s;for(let v=0;v<r;v++)t.push({batch:"",expiry_date:"",qty:""})}i.value[a].total_batch=r,$.value[a]=t};P(()=>{const a=new Date,s={year:"numeric",month:"long",day:"numeric"};return a.toLocaleDateString("en-US",s)});const O=(a,s)=>{o.created_by=a,o.errors.created_by=null},E=(a,s)=>{const d=parseFloat(i.value[s].purchase_price),c=parseFloat(a.gst)||0,t=parseFloat(i.value[s].receive_qty),r=d*t*(1+c/100),v=d*t,y=d*t*(c/100);return i.value[s].total_price=isNaN(v)?"":parseFloat(v).toFixed(2),i.value[s].total_gst_amount=isNaN(y)?"":parseFloat(y).toFixed(2),isNaN(r)?"":parseFloat(r).toFixed(2)},k=(a,s)=>{i.value[s].total_amount=E(a,s)},N=P(()=>{const a=i.value.reduce((c,t)=>c+(t.total_amount?parseFloat(t.total_amount):0),0),s=parseFloat(o.round_off)||0,d=a+s;return parseFloat(d.toFixed(2))});P(()=>{const a=parseFloat(o.round_off);return a>=0&&a<=.99});const S=P(()=>i.value.reduce((a,s)=>a+(s.total_gst_amount?parseFloat(s.total_gst_amount):0),0)),U=P(()=>i.value.reduce((a,s)=>a+(s.total_price?parseFloat(s.total_price):0),0)),R=a=>{const s=new Date(a),d={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",d)},V=a=>{let s=a.toFixed(2).toString(),[d,c]=s.split("."),t=d.substring(d.length-3),r=d.substring(0,d.length-3);return r!==""&&(t=","+t),`${r.replace(/\B(?=(\d{2})+(?!\d))/g,",")+t}.${c}`};return(a,s)=>{const d=Q("InputError");return p(),g(C,null,[m(l(G),{title:"Receive Purchase"}),m(J,null,{default:q(()=>[e("div",oe,[e("form",{onSubmit:M(A,["prevent"]),class:""},[e("div",re,[le,e("div",ce,[e("div",null,[e("p",ie,_(u.data[0].organization.name),1)]),e("div",ne,[m(K,{href:a.route("purchaseinvoice.index")},{default:q(()=>[D(" Back ")]),_:1},8,["href"])])])]),e("div",de,[e("div",_e,[e("div",ue,[e("div",me,[pe,e("p",ve,_(u.data[0].company.name??"-"),1)]),e("div",ge,[he,e("p",ye,_(u.data[0].company.gst_no??"-"),1)]),e("div",fe,[xe,e("p",be,_(u.data[0].company.email??"-"),1)]),e("div",we,[Ve,e("p",Pe,_(u.data[0].company.contact_no??"-"),1)])]),e("div",$e,[e("div",qe,[Ce,e("p",Fe,_(h.purchase_order_receives[0].po_receive_number??"-"),1)]),e("div",ke,[Ne,e("p",Se,_(u.data[0].po_number??"-"),1)]),e("div",Ue,[je,e("p",De,_(R(u.data[0].date)??"-"),1)])])])]),u.data[0].purchase_order_detail_for_receive.length>0?(p(),g("div",Ie,[e("div",Te,[e("div",Ae,[m(F,{for:"customer_invoice_no",value:"Company Invoice No"}),m(w,{id:"customer_invoice_no",type:"text",modelValue:l(o).customer_invoice_no,"onUpdate:modelValue":s[0]||(s[0]=c=>l(o).customer_invoice_no=c),onChange:s[1]||(s[1]=c=>b("customer_invoice_no")),class:f({"error rounded-md":l(o).errors.customer_invoice_no})},null,8,["modelValue","class"]),l(o).invalid("customer_invoice_no")?(p(),I(d,{key:0,class:"",message:l(o).errors.customer_invoice_no},null,8,["message"])):x("",!0)]),e("div",Be,[m(F,{for:"customer_invoice_date",value:"Company Invoice Date"}),L(e("input",{class:f(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":l(o).errors.customer_invoice_date}]),type:"date","onUpdate:modelValue":s[2]||(s[2]=c=>l(o).customer_invoice_date=c),onChange:s[3]||(s[3]=c=>b("customer_invoice_date"))},null,34),[[Y,l(o).customer_invoice_date]]),l(o).invalid("customer_invoice_date")?(p(),I(d,{key:0,class:"",message:l(o).errors.customer_invoice_date},null,8,["message"])):x("",!0)]),e("div",Oe,[m(F,{for:"company_name",value:"Received By:"}),e("div",Ee,[m(ee,{options:u.salesuser,modelValue:l(o).created_by,"onUpdate:modelValue":s[4]||(s[4]=c=>l(o).created_by=c),onOnchange:O,class:f({"error rounded-md":l(o).errors.created_by})},null,8,["options","modelValue","class"])])])])])):x("",!0),u.data[0].purchase_order_detail_for_receive.length>0?(p(),g("div",Re,[e("div",ze,[Qe,e("div",Ge,[u.data[0].category=="Service"?(p(),g("p",Me,"Part No")):(p(),g("p",Le,"Product Code"))]),Ye,Ze,He,Je,Ke,We,Xe,et,tt]),(p(!0),g(C,null,T(u.data[0].purchase_order_detail_for_receive,(c,t)=>(p(),g("div",{class:"grid grid-cols-1 gap-x-4 sm:grid-cols-12 items-center",style:{width:"140%"},key:t},[e("div",st,[e("p",ot,_(c.product.name??"-"),1)]),e("div",at,[e("p",rt,_(c.product.item_code??"-"),1)]),e("div",lt,[e("p",ct,_(V(c.price)??"-"),1)]),e("div",it,[e("p",nt,_(V(c.gst)??"-"),1)]),e("div",dt,[e("p",_t,_(c.qty??"-"),1)]),e("div",ut,[e("p",mt,_(c.receive_qty??"-"),1)]),e("div",pt,[m(w,{id:"gst",type:"text",modelValue:i.value[t].mrp,"onUpdate:modelValue":r=>i.value[t].mrp=r,class:f({error:l(o).errors[`receivedProduct.${t}.mrp`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","class"])]),e("div",vt,[m(w,{id:"gst",type:"text",modelValue:i.value[t].purchase_price,"onUpdate:modelValue":r=>i.value[t].purchase_price=r,onInput:r=>k(c,t),onChange:r=>b("receivedProduct."+t+".purchase_price"),class:f({error:l(o).errors[`receivedProduct.${t}.purchase_price`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),e("div",gt,[m(w,{id:"gst",type:"text",modelValue:i.value[t].receive_qty,"onUpdate:modelValue":r=>i.value[t].receive_qty=r,numeric:!0,onChange:r=>b(`receivedProduct.${t}.receive_qty`),onInput:r=>k(c,t),class:f({error:l(o).errors[`receivedProduct.${t}.receive_qty`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","onChange","onInput","class"]),l(o).errors[`receivedProduct.${t}.receive_qty`]?(p(),g("p",ht,_(l(o).errors[`receivedProduct.${t}.receive_qty`]),1)):x("",!0)]),e("div",yt,[m(w,{id:"gst",type:"text",numeric:!0,modelValue:i.value[t].total_batch,"onUpdate:modelValue":r=>i.value[t].total_batch=r,onChange:r=>B(t),class:f({error:l(o).errors[`receivedProduct.${t}.total_batch`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),e("div",ft,[e("p",xt,_(i.value[t].total_amount),1)]),$.value[t]?(p(),g("div",bt,[(p(!0),g(C,null,T($.value[t],(r,v)=>(p(),g("div",{key:v,class:"grid grid-cols-1 gap-x-6 sm:grid-cols-12 items-center"},[e("div",wt,[m(w,{type:"text",modelValue:r.batch,"onUpdate:modelValue":y=>r.batch=y,placeholder:"Batch",onChange:y=>b("receivedProduct."+t+".productDetails."+v+".batch"),class:f({error:l(o).errors[`receivedProduct.${t}.productDetails.${v}.batch`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),e("div",Vt,[m(X,{modelValue:r.expiry_date,"onUpdate:modelValue":y=>r.expiry_date=y,onChange:y=>b("receivedProduct."+t+".productDetails."+v+".expiry_date"),class:f({error:l(o).errors[`receivedProduct.${t}.productDetails.${v}.expiry_date`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),e("div",Pt,[m(w,{type:"text",modelValue:r.qty,"onUpdate:modelValue":y=>r.qty=y,placeholder:"Qty",onChange:y=>b("receivedProduct."+t+".productDetails."+v+".qty"),class:f({error:l(o).errors[`receivedProduct.${t}.productDetails.${v}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])])]))),128)),l(o).errors[`receivedProduct.${t}.productDetails`]?(p(),g("p",$t,_(l(o).errors[`receivedProduct.${t}.productDetails`]),1)):x("",!0)])):x("",!0)]))),128))])):x("",!0),u.data[0].purchase_order_detail_for_receive.length>0?(p(),g("div",qt,[e("div",Ct,[Ft,e("div",kt,[e("div",Nt,[e("div",St,[Ut,e("p",jt,_(V(U.value)),1)]),e("div",Dt,[It,e("p",Tt,_(V(S.value)),1)]),e("div",At,[Bt,e("div",Ot,[m(w,{id:"round_off",type:"text",modelValue:l(o).round_off,"onUpdate:modelValue":s[5]||(s[5]=c=>l(o).round_off=c),onChange:s[6]||(s[6]=c=>b("round_off")),class:f({"error rounded-md":l(o).errors.round_off}),min:"0"},null,8,["modelValue","class"])])]),e("div",Et,[Rt,e("p",zt,_(V(N.value)),1)])])])])])):x("",!0),u.data[0].purchase_order_detail_for_receive.length>0?(p(),g("div",Qt,[e("div",Gt,[m(W,{class:f(["",{"opacity-25":l(o).processing}]),disabled:l(o).processing},{default:q(()=>[D(" Submit ")]),_:1},8,["class","disabled"])])])):x("",!0)],40,ae)])]),_:1})],64)}}},ss=se(Mt,[["__scopeId","data-v-2d26e99d"]]);export{ss as default};
