import{_ as ht,b as mt,a as R}from"./AdminLayout-e15be38d.js";import{_ as pt}from"./CreateButton-cebe4e7b.js";import{_ as w}from"./InputLabel-d69efee6.js";import{_ as z}from"./SearchableDropdown-c456ce8e.js";import{_ as O}from"./SecondaryButton-1012464f.js";import{P as F}from"./PrimaryButton-eddb8b77.js";import{D as ft}from"./DangerButton-9b74ae84.js";import{M as I}from"./Modal-754de2c3.js";import{_ as bt}from"./Pagination-ffdaf57f.js";import{_ as gt}from"./TextArea-b68da786.js";import{C as xt}from"./CheckboxWithLabel-82ad3b88.js";import{_ as yt}from"./SimpleDropdown-6f25da81.js";import"./html2canvas.esm-cb10c2d0.js";import{r as u,l as vt,o as c,c as h,a as n,u as x,w as i,F as M,Z as wt,b as t,g as f,f as g,i as E,e as k,t as a,n as y,s as kt,x as Ct}from"./app-16701445.js";import{_ as jt}from"./ArrowIcon-769e919f.js";import{s as St}from"./sortAndSearch-d1b7f68b.js";import{_ as Mt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              *//* empty css                                                                          */const o=r=>(kt("data-v-6b6fb937"),r=r(),Ct(),r),Jt={class:"animate-top"},Nt={class:"sm:flex sm:items-center"},Vt=o(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Jobcard")],-1)),Bt={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},Ot={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},It={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},Et=o(()=>t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),Lt={key:0,class:"flex justify-end"},$t={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},Ut={class:"flex justify-between mb-2"},At={class:"flex"},Tt=o(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),Pt=["src"],Wt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Rt={class:"sm:col-span-4"},zt={class:"relative mt-2"},Ft={class:"mt-8 overflow-x-auto sm:rounded-lg"},Gt={class:"shadow sm:rounded-lg"},Ht={class:"w-full text-sm text-left rtl:text-right text-gray-500"},qt={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Kt={class:"border-b-2"},Xt=["onClick"],Yt={key:0},Zt={class:"px-4 py-2.5 min-w-40"},Qt={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap truncate"},Dt={scope:"row",class:"px-4 py-2.5 whitespace-normal min-w-52 truncate"},te={class:"px-4 py-2.5 min-w-48"},ee={class:"px-4 py-2.5 min-w-52"},se={class:"px-4 py-2.5 min-w-32"},oe={class:"px-4 py-2.5 min-w-32"},le={class:"px-4 py-2.5"},ae={class:"flex justify-center item-center w-full"},ne={class:"flex flex-1 items-center px-4 py-2.5"},re={class:"items-center px-4 py-2.5"},ie={class:"flex items-center justify-start gap-4"},de=o(()=>t("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)),ce=o(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),ue=o(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),_e=["onClick"],he=o(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),me=o(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)),pe=[he,me],fe=o(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),be=o(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," View Jobcard ",-1)),ge=["onClick"],xe=o(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M6 2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm2 6h8m-4 4h4"})],-1)),ye=o(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," Generate PDF ",-1)),ve=[xe,ye],we=["onClick"],ke=o(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M6 18L18 6M6 6l12 12"})],-1)),Ce=o(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," Close Jobcard ",-1)),je=[ke,Ce],Se={key:1},Me=o(()=>t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),Je=[Me],Ne={class:"p-6"},Ve=o(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),Be={class:"mt-6 flex justify-end"},Oe={class:"p-6"},Ie={class:"container1 p-2",id:"pdf-content"},Ee=o(()=>t("div",{class:"header",style:{"align-items":"start","justify-content":"center","text-align":"center"}},[t("div",{style:{"align-items":"center","justify-content":"space-between","margin-bottom":"10px"}},[t("p",{style:{"font-size":"20px"}},[t("strong",null,"Maintenance Workshop")])])],-1)),Le={style:{display:"flex","justify-content":"space-between"}},$e={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},Ue=o(()=>t("p",{style:{"margin-bottom":"4px"}},[t("strong")],-1)),Ae={style:{display:"flex"}},Te=o(()=>t("p",{style:{width:"100px"}},[t("strong",null,"Hospital Name")],-1)),Pe={style:{display:"flex"}},We=o(()=>t("p",{style:{width:"100px"}},[t("strong",null,"Address")],-1)),Re={style:{display:"flex"}},ze=o(()=>t("p",{style:{width:"100px"}},[t("strong",null,"Contact Number")],-1)),Fe={class:"invoice-details",style:{"margin-bottom":"20px","justify-items":"start",width:"320px"}},Ge={style:{display:"flex"}},He=o(()=>t("p",{style:{width:"100px"}},[t("strong",null,"Job Number")],-1)),qe={style:{display:"flex"}},Ke=o(()=>t("p",{style:{width:"100px"}},[t("strong",null,"Job Date")],-1)),Xe={style:{display:"flex"}},Ye=o(()=>t("p",{style:{width:"100px"}},[t("strong",null,"Engineer")],-1)),Ze=o(()=>t("thead",null,[t("tr",null,[t("th",null,"Equipment Name"),t("th",null,"Model"),t("th",null,"Serial No")])],-1)),Qe={class:""},De={class:""},ts={style:{display:"flex"}},es=o(()=>t("p",null,[t("strong",null,"Problem Description")],-1)),ss={class:"mt-2"},os={style:{display:"flex"}},ls=o(()=>t("p",null,[t("strong",null,"Accessories")],-1)),as={class:"sm:col-span-6 mt-2"},ns=o(()=>t("p",{style:{width:"200px"}},[t("strong",null,"Checklist")],-1)),rs={class:"grid sm:grid-cols-6 relative",style:{"margin-bottom":"20px",width:"500px"}},is={style:{display:"flex","justify-content":"space-between","margin-top":"20px"}},ds={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"400"}},cs={style:{display:"flex"}},us=o(()=>t("p",null,[t("strong",null,"Close Note")],-1)),_s={class:"",style:{"margin-bottom":"20px","justify-items":"end",width:"260px"}},hs={style:{display:"flex"}},ms=o(()=>t("p",null,[t("strong",null,"Close Date")],-1)),ps={class:"mt-6 px-4 flex justify-end"},fs={class:"flex flex-col justify-end space-y-6"},bs={class:"flex items-center space-x-2"},gs={class:"flex justify-end"},xs={class:"w-36"},ys={class:"p-6"},vs=o(()=>t("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Close Jobcard",-1)),ws={class:"border-b border-gray-900/10 pb-12"},ks={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Cs={class:"sm:col-span-6"},js={class:"relative mt-2"},Ss={class:"sm:col-span-6"},Ms={class:"mt-6 px-4 flex justify-end"},Js={class:"w-36"},Ns={__name:"List",props:["data","permissions","checklist","jobStatus","statusId","jobFilterStatus","pagetypes","notifications"],setup(r){const J=r,{form:b,search:Vs,sort:G,fetchData:Bs,sortKey:H,sortDirection:q,updateParams:K}=St("jobcard.index",{status:J.statusId}),N=u(!1),L=u(null),V=u(!1),X=u("custom"),d=u([]),$=u([]),C=u(!1),Y=u("custom2"),p=u(J.statusId),U=u("");vt([p],()=>{K({status:p.value})});const Z=[{field:"job_card_number",label:"JOB NUMBER",sortable:!0},{field:"hospital_name",label:"HOSPITAL NAME",sortable:!0},{field:"product_name",label:"PRODUCT",sortable:!0,multiFieldSort:["product_name","product_code"]},{field:"serial_no",label:"SERIAL NUMBER",sortable:!0},{field:"users.first_name",label:"ENGINEER",sortable:!0},{field:"date",label:"DATE",sortable:!0},{field:"time_ago",label:"TIME",sortable:!0},{field:"warranty_status",label:"WARRANTY STATUS",sortable:!0},{field:"status",label:"STATUS",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],Q=s=>{L.value=s,N.value=!0},B=()=>{N.value=!1},D=()=>{b.delete(route("jobcard.destroy",{id:L.value}),{onSuccess:()=>B()})},tt=s=>{const l=J.data.data.find(e=>e.id===s);d.value=l,$.value=l.job_card_checks.map(e=>e.job_card_checklist_id),V.value=!0},A=()=>{V.value=!1},m={job_status:"",close_note:"",id:""},et=s=>{m.job_status="",m.close_note="",C.value=!0,m.id=s},T=()=>{C.value=!1},st=()=>{b.post(route("jobcard.close",{data:m}),{onSuccess:()=>{b.reset(),C.value=!1},onError:s=>{}})},ot=(s,l)=>{p.value=s,P(U.value,p.value)},lt=()=>{const s="Jobcard_Report_"+new Date().toISOString().split("T")[0],l={status:p.value},S=`/export-jobcard-report?${new URLSearchParams(l).toString()}`;fetch(S,{method:"GET"}).then(_=>{if(!_.ok)throw new Error("Network response was not ok");return _.blob()}).then(_=>{const _t=window.URL.createObjectURL(new Blob([_])),v=document.createElement("a");v.href=_t,v.setAttribute("download",s+".xlsx"),document.body.appendChild(v),v.click(),document.body.removeChild(v)}).catch(_=>{console.error("Error exporting data:",_)})},at=(s,l)=>{m.job_status=s,b.errors["data.job_status"]=null},nt=s=>{switch(s){case"Open":return"bg-blue-100";case"Close":return"bg-green-100"}},rt=s=>{switch(s){case"Open":return"text-blue-600";case"Close":return"text-green-600"}},it=s=>{switch(s){case"Out Of Warranty":return"bg-red-100";case"amc":return"bg-cyan-100";case"cmc":return"bg-green-100";case"warranty":return"bg-blue-100";default:return"bg-gray-100"}},dt=s=>{switch(s){case"Out Of Warranty":return"text-red-600";case"amc":return"text-cyan-600";case"cmc":return"text-green-600";case"warranty":return"text-blue-600";default:return"text-gray-600"}},P=(s,l)=>{U.value=s,b.get(route("jobcard.index",{search:s,status:l}),{preserveState:!0})},W=s=>{const l=new Date(s),e={year:"numeric",month:"short",day:"numeric"};return l.toLocaleDateString("en-US",e)},j=u("portrait"),ct=(s,l)=>{j.value=s},ut=(s,l)=>{window.open(`/jobcard/download/${s}/${l}`,"_blank")};return(s,l)=>(c(),h(M,null,[n(x(wt),{title:"Jobcard"}),n(ht,{notifications:r.notifications},{default:i(()=>[t("div",Jt,[t("div",Nt,[Vt,t("div",Bt,[t("div",Ot,[t("div",It,[Et,t("input",{id:"search-field",onInput:l[0]||(l[0]=e=>P(e.target.value,s.organizationId,s.customerId,s.salesUserId,s.categoryId,s.createdBy,p.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),r.permissions.canCreateJobcard?(c(),h("div",Lt,[n(pt,{href:s.route("jobcard.create")},{default:i(()=>[f(" Create Jobcard ")]),_:1},8,["href"])])):g("",!0)])]),t("div",$t,[t("div",Ut,[t("div",At,[Tt,n(w,{for:"customer_id",value:"Filters"})]),t("button",{onClick:lt},[t("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,Pt)])]),t("div",Wt,[t("div",Rt,[n(w,{for:"customer_id",value:"Status"}),t("div",zt,[n(yt,{options:r.jobFilterStatus,modelValue:p.value,"onUpdate:modelValue":l[1]||(l[1]=e=>p.value=e),onOnchange:ot},null,8,["options","modelValue"])])])])]),t("div",Ft,[t("div",Gt,[t("table",Ht,[t("thead",qt,[t("tr",Kt,[(c(),h(M,null,E(Z,(e,S)=>t("th",{key:S,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:_=>x(G)(e.field,e.sortable)},[f(a(e.label)+" ",1),e.sortable?(c(),k(jt,{key:0,isSorted:x(H)===e.field,direction:x(q)},null,8,["isSorted","direction"])):g("",!0)],8,Xt)),64))])]),r.data.data&&r.data.data.length>0?(c(),h("tbody",Yt,[(c(!0),h(M,null,E(r.data.data,(e,S)=>(c(),h("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",Zt,a(e.job_card_number),1),t("td",Qt,a(e.hospital_name??"-"),1),t("td",Dt,a(e.product_name??"")+" "+a(e.product_code??""),1),t("td",te,a(e.serial_no??"-"),1),t("td",ee,a(e.users.first_name)+" "+a(e.users.last_name),1),t("td",se,a(W(e.date)),1),t("td",oe,a(e.time_ago),1),t("td",le,[t("div",ae,[t("div",{class:y(["flex rounded-full px-4 py-1",it(e.warranty_status)])},[t("span",{class:y(["text-sm font-semibold",dt(e.warranty_status)])},a(e.warranty_status??"-"),3)],2)])]),t("td",ne,[t("div",{class:y(["flex rounded-full px-4 py-1",nt(e.status)])},[t("span",{class:y(["text-sm font-semibold",rt(e.status)])},a(e.status),3)],2)]),t("td",re,[t("div",ie,[n(mt,{align:"right",width:"48"},{trigger:i(()=>[de]),content:i(()=>[e.status=="Open"&&r.permissions.canEditJobcard?(c(),k(R,{key:0,href:s.route("jobcard.edit",{id:e.id})},{svg:i(()=>[ce]),text:i(()=>[ue]),_:2},1032,["href"])):g("",!0),e.status=="Open"&&r.permissions.canDeleteJobcard?(c(),h("button",{key:1,type:"button",onClick:_=>Q(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},pe,8,_e)):g("",!0),r.permissions.canViewJobcard?(c(),k(R,{key:2,href:s.route("jobcard.show",{id:e.id})},{svg:i(()=>[fe]),text:i(()=>[be]),_:2},1032,["href"])):g("",!0),t("button",{type:"button",onClick:_=>tt(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},ve,8,ge),e.status=="Open"&&r.permissions.canCreateJobcard?(c(),h("button",{key:3,type:"button",onClick:_=>et(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},je,8,we)):g("",!0)]),_:2},1024)])])]))),128))])):(c(),h("tbody",Se,Je))])])]),r.data.data&&r.data.data.length>0?(c(),k(bt,{key:0,class:"mt-6",links:r.data.links},null,8,["links"])):g("",!0)]),n(I,{show:N.value,onClose:B},{default:i(()=>[t("div",Ne,[Ve,t("div",Be,[n(O,{onClick:B},{default:i(()=>[f(" Cancel ")]),_:1}),n(ft,{class:"ml-3",onClick:D},{default:i(()=>[f(" Delete ")]),_:1})])])]),_:1},8,["show"]),n(I,{show:V.value,onClose:A,maxWidth:X.value},{default:i(()=>[t("div",Oe,[t("div",Ie,[Ee,t("div",Le,[t("div",$e,[Ue,t("div",Ae,[Te,t("p",null,": "+a(d.value.hospital_name),1)]),t("div",Pe,[We,t("p",null,": "+a(d.value.address),1)]),t("div",Re,[ze,t("p",null,": "+a(d.value.contact_no),1)])]),t("div",Fe,[t("div",Ge,[He,t("p",null,": "+a(d.value.job_card_number),1)]),t("div",qe,[Ke,t("p",null,": "+a(W(d.value.date)),1)]),t("div",Xe,[Ye,t("p",null,": "+a(d.value.users.first_name)+" "+a(d.value.users.last_name),1)])])]),t("table",null,[Ze,t("tbody",null,[t("tr",Qe,[t("td",null,a(d.value.product_name??"-"),1),t("td",null,a(d.value.product_code??"-"),1),t("td",null,a(d.value.serial_no??"-"),1)])])]),t("div",null,[t("div",De,[t("div",ts,[es,t("p",null,": "+a(d.value.problem_description??"NA"),1)])]),t("div",ss,[t("div",os,[ls,t("p",null,": "+a(d.value.accessories??"NA"),1)])])]),t("div",as,[ns,t("div",rs,[(c(!0),h(M,null,E(r.checklist,e=>(c(),k(xt,{key:e.id,checked:$.value,value:e.id,label:e.type,"onUpdate:checked":s.updateChecked},null,8,["checked","value","label","onUpdate:checked"]))),128))])]),t("div",is,[t("div",ds,[t("div",cs,[us,t("p",null,": "+a(d.value.problem_description),1)])]),t("div",_s,[t("div",hs,[ms,t("p",null,": "+a(d.value.close_date),1)])])])]),t("div",ps,[t("div",fs,[t("div",bs,[n(w,{for:"customer_id",value:"Page Type :"}),n(z,{options:r.pagetypes,modelValue:j.value,"onUpdate:modelValue":l[2]||(l[2]=e=>j.value=e),onOnchange:ct},null,8,["options","modelValue"])]),t("div",gs,[n(O,{onClick:A},{default:i(()=>[f(" Cancel ")]),_:1}),t("div",xs,[n(F,{class:"ml-3 w-20",onClick:l[3]||(l[3]=e=>ut(d.value.id,j.value))},{default:i(()=>[f(" Generate Pdf ")]),_:1})])])])])])]),_:1},8,["show","maxWidth"]),n(I,{show:C.value,onClose:T,maxWidth:Y.value},{default:i(()=>[t("div",ys,[vs,t("div",ws,[t("div",ks,[t("div",Cs,[n(w,{for:"role_id",value:"Job Status"}),t("div",js,[n(z,{options:r.jobStatus,modelValue:m.job_status,"onUpdate:modelValue":l[4]||(l[4]=e=>m.job_status=e),onOnchange:at,class:y({"error rounded-md":x(b).errors["data.job_status"]})},null,8,["options","modelValue","class"])])]),t("div",Ss,[n(w,{for:"close_note",value:"Close Note"}),n(gt,{id:"close_note",type:"text",rows:3,modelValue:m.close_note,"onUpdate:modelValue":l[5]||(l[5]=e=>m.close_note=e),class:y({"error rounded-md":x(b).errors["data.close_note"]})},null,8,["modelValue","class"])])])]),t("div",Ms,[n(O,{onClick:T},{default:i(()=>[f(" Cancel ")]),_:1}),t("div",Js,[n(F,{class:"ml-3 w-20",onClick:st},{default:i(()=>[f(" Save ")]),_:1})])])])]),_:1},8,["show","maxWidth"])]),_:1},8,["notifications"])],64))}},Zs=Mt(Ns,[["__scopeId","data-v-6b6fb937"]]);export{Zs as default};
