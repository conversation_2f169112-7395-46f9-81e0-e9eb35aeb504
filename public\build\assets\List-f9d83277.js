import{r as h,l as Q,o,c as i,a as l,u as f,w as r,F as I,Z as X,b as e,g,f as u,k as B,v as T,n as C,i as U,e as E,t as d,E as ee}from"./app-97275a91.js";import{s as te}from"./sortAndSearch-6f0bc414.js";import{_ as se,b as oe,a as le}from"./AdminLayout-595ad5a7.js";import{_ as ae}from"./CreateButton-91ea7c7b.js";import{_ as $}from"./SecondaryButton-d0c53c3f.js";import{P as ne}from"./PrimaryButton-46ac4375.js";import{D as ie}from"./DangerButton-36669f8b.js";import{_ as re}from"./SimpleDropdown-f072c5ba.js";import{_ as S}from"./InputLabel-eb73087c.js";import{M as L}from"./Modal-48c075e7.js";import{_ as de}from"./Pagination-5e2f223d.js";import{_ as ce}from"./ArrowIcon-572ff5c4.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const ue={class:"animate-top"},me={class:"sm:flex sm:items-center"},_e=e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Daily Plan")],-1),he={class:"flex justify-end items-center"},fe={class:"ml-6 flex space-x-6 mt-4 sm:mt-0 w-64"},ge={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},pe=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),ve={key:0,class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},ye={class:"flex justify-end"},xe={key:0,class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},we={class:"flex mb-2"},be=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),ke={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center"},Ce={class:"sm:col-span-4"},Se={class:"sm:col-span-4"},Me={class:"sm:col-span-4"},Pe={class:"relative mt-2"},Ie={class:"mt-8 overflow-x-auto sm:rounded-lg"},Be={class:"shadow sm:rounded-lg"},Ee={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Ve={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Ae={class:"border-b-2"},Ne=["onClick"],Te={key:0},Ue={key:0,class:"px-4 py-2.5 min-w-36"},$e={scope:"row",class:"px-4 py-2.5 min-w-32"},Le={class:"whitespace-normal px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},je={class:""},Oe={class:""},ze={scope:"row",class:"px-4 py-2.5 min-w-28"},Fe={class:"px-4 py-2.5 min-w-28"},We={class:"px-4 py-2.5 min-w-40"},De={class:"px-4 py-2.5 min-w-52"},Re={class:"flex flex-1 items-center px-4 py-2.5"},Ye={class:"px-4 py-2.5 min-w-32"},Ge={class:"items-center px-4 py-2.5"},He={class:"flex items-center justify-start gap-4"},Ke={key:0,type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},Ze=e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})],-1),qe=[Ze],Je=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),Qe=e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),Xe=["onClick"],et=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),tt=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),st=[et,tt],ot=["onClick"],lt=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1),at=e("span",{class:"text-sm text-gray-700 leading-5"}," Close Plan ",-1),nt=[lt,at],it={key:1},rt=e("tr",{class:"bg-white"},[e("td",{colspan:"10",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),dt=[rt],ct={class:"p-6"},ut=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),mt={class:"mt-6 flex justify-end"},_t={class:"p-6"},ht=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you want to Close this plan? ",-1),ft={class:"mt-6 flex justify-end space-x-3"},gt={class:"w-32"},Nt={__name:"List",props:["data","user","userId","userInfo","permissions"],setup(a){const p=a,{form:v,search:pt,sort:j,fetchData:vt,sortKey:O,sortDirection:z,updateParams:F}=te("weeklyplan.index",{user_id:p.userId,from_date:p.from_date,to_date:p.to_date}),y=h(""),c=h(p.userId),m=h(""),_=h("");Q([c,m,_],()=>{F({user_id:c.value,from_date:m.value,to_date:_.value})});const W=[{field:"users.first_name",label:"ENGINEER",sortable:!0,visible:p.userInfo.role_id=="1"},{field:"date",label:"DATE",sortable:!0,visible:!0},{field:"customer_name",label:"CUSTOMER NAME",sortable:!0,multiFieldSort:["customer_name","dr_name"],visible:!0},{field:"place",label:"CITY",sortable:!0,visible:!0},{field:"company",label:"COMPANY",sortable:!0,visible:!0},{field:"product",label:"PRODUCT",sortable:!0,visible:!0},{field:"order_value",label:"DISCUSSION",sortable:!1,visible:!0},{field:"status",label:"STATUS",sortable:!0,visible:!0},{field:"close_date",label:"CLOSE DATE",sortable:!0,visible:!0},{field:"action",label:"ACTION",sortable:!1,visible:!0}],x=(s,n,t,k)=>{y.value=s,v.get(route("weeklyplan.index",{search:s,user_id:n,from_date:t,to_date:k}),{preserveState:!0})},D=()=>{x(y.value,c.value,m.value,_.value)},R=()=>{x(y.value,c.value,m.value,_.value)},Y=(s,n)=>{c.value=s,x(y.value,c.value,m.value,_.value)},M=h(!1),w=h(null),G=s=>{w.value=s,M.value=!0},P=()=>{M.value=!1},H=()=>{v.delete(route("weeklyplan.destroy",{id:w.value}),{onSuccess:()=>P()})},b=h(!1),K=s=>{w.value=s,b.value=!0},V=()=>{b.value=!1},Z=()=>{const s=window.location.href;b.value=!1,v.post(route("weeklyplan.changeStatus",{id:w.value}),{onSuccess:()=>{window.location.href=s}})},q=s=>{switch(s){case"Close":return"bg-green-100";default:return"bg-blue-100"}},J=s=>{switch(s){case"Close":return"text-green-600";default:return"text-blue-600"}},A=s=>{const n=new Date(s),t={year:"numeric",month:"short",day:"numeric"};return n.toLocaleDateString("en-US",t)};return(s,n)=>(o(),i(I,null,[l(f(X),{title:"Weekly Plan"}),l(se,null,{default:r(()=>[e("div",ue,[e("div",me,[_e,e("div",he,[e("div",fe,[e("div",ge,[pe,e("input",{id:"search-field",onInput:n[0]||(n[0]=t=>x(t.target.value,c.value,m.value,_.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),a.permissions.canCreateWeeklyPlanning?(o(),i("div",ve,[e("div",ye,[l(ae,{href:s.route("weeklyplan.create")},{default:r(()=>[g(" Add Plan ")]),_:1},8,["href"])])])):u("",!0)])]),a.permissions.canFilterWeeklyPlanning?(o(),i("div",xe,[e("div",we,[be,l(S,{for:"customer_id",value:"Filters"})]),e("div",ke,[e("div",Ce,[l(S,{for:"date",value:"From Date"}),B(e("input",{"onUpdate:modelValue":n[1]||(n[1]=t=>m.value=t),class:C(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":f(v).errors.from_date}]),type:"date",onChange:D},null,34),[[T,m.value]])]),e("div",Se,[l(S,{for:"date",value:"To Date"}),B(e("input",{"onUpdate:modelValue":n[2]||(n[2]=t=>_.value=t),class:C(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":f(v).errors.to_date}]),type:"date",onChange:R},null,34),[[T,_.value]])]),e("div",Me,[l(S,{for:"customer_id",value:"Engineer Name"}),e("div",Pe,[l(re,{options:a.user,modelValue:c.value,"onUpdate:modelValue":n[3]||(n[3]=t=>c.value=t),onOnchange:Y},null,8,["options","modelValue"])])])])])):u("",!0),e("div",Ie,[e("div",Be,[e("table",Ee,[e("thead",Ve,[e("tr",Ae,[(o(),i(I,null,U(W,(t,k)=>B(e("th",{key:k,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:N=>f(j)(t.field,t.sortable)},[g(d(t.label)+" ",1),t.sortable?(o(),E(ce,{key:0,isSorted:f(O)===t.field,direction:f(z)},null,8,["isSorted","direction"])):u("",!0)],8,Ne),[[ee,t.visible]])),64))])]),a.data.data&&a.data.data.length>0?(o(),i("tbody",Te,[(o(!0),i(I,null,U(a.data.data,(t,k)=>(o(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[a.userInfo.role_id=="1"?(o(),i("td",Ue,d(t.users.first_name??"")+" "+d(t.users.last_name??""),1)):u("",!0),e("td",$e,d(A(t.date)??"-"),1),e("td",Le,[e("div",je,d(t.customer_name??""),1),e("div",Oe,d(t.dr_name??""),1)]),e("td",ze,d(t.place??"-"),1),e("td",Fe,d(t.company??"-"),1),e("td",We,d(t.product??"-"),1),e("td",De,d(t.brief_discussion??"-"),1),e("td",Re,[e("div",{class:C(["flex rounded-full px-4 py-1",q(t.status)])},[e("span",{class:C(["text-sm font-semibold",J(t.status)])},d(t.status),3)],2)]),e("td",Ye,d(t.close_date?A(t.close_date):"-"),1),e("td",Ge,[e("div",He,[l(oe,{align:"right",width:"48"},{trigger:r(()=>[t.status=="Open"?(o(),i("button",Ke,qe)):u("",!0)]),content:r(()=>[a.permissions.canEditWeeklyPlanning?(o(),E(le,{key:0,href:s.route("weeklyplan.edit",{id:t.id})},{svg:r(()=>[Je]),text:r(()=>[Qe]),_:2},1032,["href"])):u("",!0),a.permissions.canDeleteWeeklyPlanning?(o(),i("button",{key:1,type:"button",onClick:N=>G(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},st,8,Xe)):u("",!0),a.permissions.canCloseWeeklyPlanning?(o(),i("button",{key:2,type:"button",onClick:N=>K(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},nt,8,ot)):u("",!0)]),_:2},1024)])])]))),128))])):(o(),i("tbody",it,dt))])])]),a.data.data&&a.data.data.length>0?(o(),E(de,{key:1,class:"mt-6",links:a.data.links},null,8,["links"])):u("",!0)]),l(L,{show:M.value,onClose:P},{default:r(()=>[e("div",ct,[ut,e("div",mt,[l($,{onClick:P},{default:r(()=>[g(" Cancel ")]),_:1}),l(ie,{class:"ml-3",onClick:H},{default:r(()=>[g(" Delete ")]),_:1})])])]),_:1},8,["show"]),l(L,{show:b.value,onClose:V},{default:r(()=>[e("div",_t,[ht,e("div",ft,[l($,{onClick:V},{default:r(()=>[g(" Cancel ")]),_:1}),e("div",gt,[l(ne,{onClick:Z,type:"button"},{default:r(()=>[g("Approve")]),_:1})])])])]),_:1},8,["show"])]),_:1})],64))}};export{Nt as default};
