import{h as p,r as s}from"./app-b7a94f67.js";function g(v,i={}){const c=p({}),u=s(""),r=s("id"),a=s("desc"),o=s({...i}),m=e=>{o.value={...o.value,...e}},n=(e={})=>{const t={...o.value,...e,search:u.value,sort_by:r.value,sort_direction:a.value},l=new URLSearchParams(window.location.search).get("page");l&&(t.page=l),c.get(route(v,t),{preserveState:!0,replace:!0})};return{form:c,search:u,sort:(e,t=!0)=>{t&&(r.value===e?a.value=a.value==="asc"?"desc":"asc":(a.value="asc",r.value=e),n())},fetchData:n,sortKey:r,sortDirection:a,updateParams:m}}export{g as s};
