import{_ as o}from"./AdminLayout-0f1fdf67.js";import i from"./DeleteUserForm-0cfb3b7e.js";import m from"./UpdatePasswordForm-cfcec6fa.js";import r from"./UpdateProfileInformationForm-857ce7ff.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-b7a94f67.js";import"./DangerButton-a612a79a.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-86b88c86.js";import"./InputLabel-11b5d690.js";import"./Modal-e44dcdf0.js";/* empty css                                                              */import"./SecondaryButton-c893313c.js";import"./TextInput-fea73171.js";import"./PrimaryButton-4ffecd1c.js";import"./TextArea-500c5ac8.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
