import{r as x,o as r,c as d,a as i,u as h,w as u,F as C,Z as X,b as e,k as Y,v as Z,N as J,g as w,f as g,i as L,e as M,t as n,n as l,s as Q,x as W}from"./app-97275a91.js";import{_ as D,b as ee,a as te}from"./AdminLayout-595ad5a7.js";import{_ as se}from"./CreateButton-91ea7c7b.js";import{_ as N}from"./InputLabel-eb73087c.js";import{_ as le}from"./SecondaryButton-d0c53c3f.js";import{_ as ae}from"./_plugin-vue_export-helper-c27b6911.js";import{D as oe}from"./DangerButton-36669f8b.js";import{M as ne}from"./Modal-48c075e7.js";import{_ as re}from"./Pagination-5e2f223d.js";import{_ as ie}from"./SimpleDropdown-f072c5ba.js";import"./html2canvas.esm-6a1541b2.js";import{_ as ce}from"./ArrowIcon-572ff5c4.js";import{s as de}from"./sortAndSearch-6f0bc414.js";/* empty css                                                              */const c=a=>(Q("data-v-8366caa9"),a=a(),W(),a),ue={class:"animate-top"},me={class:"sm:flex sm:items-center"},he=c(()=>e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Maintenance Contract")],-1)),_e={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},ge={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},fe={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},pe=c(()=>e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),xe={key:0,class:"flex justify-end"},ye={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},we={class:"flex justify-between mb-2"},be={class:"flex"},ve=c(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),ke=["src"],Ce={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Me={class:"sm:col-span-4"},Se={class:"relative mt-2"},Ae={class:"mt-8 overflow-x-auto sm:rounded-lg"},Te={class:"shadow sm:rounded-lg"},Ee={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Le={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Ne={class:"border-b-2"},Be=["onClick"],$e={key:0},Ie={class:"flex items-center justify-start gap-4"},Oe={key:0,type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},Ue=c(()=>e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})],-1)),Ve=[Ue],Pe=c(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),je=c(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),ze=["onClick"],Re=c(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.42.271L12 0l-.057.072A2.25 2.25 0 0010.5 1.085c.002-.001.003-.001.004-.001z"})],-1)),Fe=c(()=>e("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1)),qe=[Re,Fe],Ge={key:1},He=c(()=>e("tr",{class:"bg-white"},[e("td",{colspan:"12",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),Ke=[He],Xe={class:"p-6"},Ye=c(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),Ze={class:"mt-6 flex justify-end"},Je={__name:"List",props:["data","permissions","status","statusId"],setup(a){const{form:S,search:b,sort:B,fetchData:A,sortKey:$,sortDirection:I}=de("maintenance-contract.index"),T=x(null),v=x(!1),E=x(""),O=[{field:"hospital_name",label:"HOSPITAL NAME",sortable:!0},{field:"product_name",label:"PRODUCT",sortable:!0},{field:"price",label:"PRICE (₹)",sortable:!0},{field:"contract_start_date",label:"CONTRACT DATE",sortable:!0,multiFieldSort:["contract_start_date","contract_end_date"]},{field:"pm_date_1",label:"PM DATE 1",sortable:!0},{field:"pm_date_2",label:"PM DATE 2",sortable:!0},{field:"pm_date_3",label:"PM DATE 3",sortable:!0},{field:"pm_date_4",label:"PM DATE 4",sortable:!0},{field:"maintenance_type",label:"TYPE",sortable:!0},{field:"status",label:"STATUS",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],U=s=>{T.value=s,v.value=!0},k=()=>{v.value=!1},V=()=>{S.delete(route("maintenance-contract.destroy",{id:T.value}),{onSuccess:()=>k()})},P=s=>{switch(s){case"Open":return"bg-blue-100";case"Close":return"bg-red-100"}},j=s=>{switch(s){case"Open":return"text-blue-600";case"Close":return"text-red-600"}},z=s=>{switch(s){case"AMC":return"bg-cyan-100";case"CMC":return"bg-green-100"}},R=s=>{switch(s){case"AMC":return"text-cyan-600";case"CMC":return"text-green-600"}},F=(s,o)=>{E.value=s,S.get(route("maintenance-contract.index",{search:s,status:o}),{preserveState:!0})},q=x("MAINTENANCE DATA"),G=()=>{const s=q.value.replace(/\s+/g,"_"),o={status:f.value},y=`/export-maintenance-contract?${new URLSearchParams(o).toString()}`;fetch(y,{method:"GET"}).then(m=>{if(!m.ok)throw new Error("Network response was not ok");return m.blob()}).then(m=>{const K=window.URL.createObjectURL(new Blob([m])),p=document.createElement("a");p.href=K,p.setAttribute("download",`${s}.xlsx`),document.body.appendChild(p),p.click(),document.body.removeChild(p)}).catch(m=>{console.error("Error exporting data:",m)})},_=s=>{const o=new Date(s),t={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",t)},f=x("Open"),H=(s,o)=>{f.value=s,F(E.value,f.value)};return(s,o)=>(r(),d(C,null,[i(h(X),{title:"Maintenance Contract"}),i(D,null,{default:u(()=>[e("div",ue,[e("div",me,[he,e("div",_e,[e("div",ge,[e("div",fe,[pe,Y(e("input",{id:"search-field","onUpdate:modelValue":o[0]||(o[0]=t=>J(b)?b.value=t:null),onInput:o[1]||(o[1]=(...t)=>h(A)&&h(A)(...t)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,544),[[Z,h(b)]])])]),a.permissions.canCreateMaintenance?(r(),d("div",xe,[i(se,{href:s.route("maintenance-contract.create")},{default:u(()=>[w(" Create Maintenance Contract ")]),_:1},8,["href"])])):g("",!0)])]),e("div",ye,[e("div",we,[e("div",be,[ve,i(N,{for:"customer_id",value:"Filters"})]),e("button",{onClick:G},[e("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,ke)])]),e("div",Ce,[e("div",Me,[i(N,{for:"customer_id",value:"Status"}),e("div",Se,[i(ie,{options:a.status,modelValue:f.value,"onUpdate:modelValue":o[2]||(o[2]=t=>f.value=t),onOnchange:H},null,8,["options","modelValue"])])])])]),e("div",Ae,[e("div",Te,[e("table",Ee,[e("thead",Le,[e("tr",Ne,[(r(),d(C,null,L(O,(t,y)=>e("th",{key:y,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:m=>h(B)(t.field,t.sortable)},[w(n(t.label)+" ",1),t.sortable?(r(),M(ce,{key:0,isSorted:h($)===t.field,direction:h(I)},null,8,["isSorted","direction"])):g("",!0)],8,Be)),64))])]),a.data.data&&a.data.data.length>0?(r(),d("tbody",$e,[(r(!0),d(C,null,L(a.data.data,(t,y)=>(r(),d("tr",{class:l(["odd:bg-white even:bg-gray-50 border-b",{"bg-yellow-100":t.highlight}]),key:t.id},[e("td",{class:l(["whitespace-normal px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap truncate min-w-48",{"text-yellow-600":t.highlight}])},n(t.hospital_name??"-")+" - "+n(t.city??"-"),3),e("td",{class:l(["whitespace-normal px-4 py-2.5 min-w-60 truncate",{"text-yellow-600":t.highlight}])},n(t.product_name??""),3),e("td",{scope:"row",class:l(["px-4 py-2.5 min-w-36 truncate",{"text-yellow-600":t.highlight}])},n(t.price??"-"),3),e("td",{class:l(["px-4 py-2.5 min-w-60",{"text-yellow-600":t.highlight}])},n(_(t.contract_start_date))+" - "+n(_(t.contract_end_date)),3),e("td",{class:l(["px-4 py-2.5 min-w-36",{"text-yellow-600":t.highlight}])},n(t.pm_date_1?_(t.pm_date_1):"-"),3),e("td",{class:l(["px-4 py-2.5 min-w-36",{"text-yellow-600":t.highlight}])},n(t.pm_date_2?_(t.pm_date_2):"-"),3),e("td",{class:l(["px-4 py-2.5 min-w-36",{"text-yellow-600":t.highlight}])},n(t.pm_date_3?_(t.pm_date_3):"-"),3),e("td",{class:l(["px-4 py-2.5 min-w-36",{"text-yellow-600":t.highlight}])},n(t.pm_date_4?_(t.pm_date_4):"-"),3),e("td",{class:l(["flex-1 items-center px-4 py-2.5",{"text-yellow-600":t.highlight}])},[e("div",{class:l(["flex rounded-full px-4 py-1",z(t.maintenance_type)])},[e("span",{class:l(["text-sm font-semibold",R(t.maintenance_type)])},n(t.maintenance_type),3)],2)],2),e("td",{class:l(["flex flex-1 items-center px-4 py-2.5",{"text-yellow-600":t.highlight}])},[e("div",{class:l(["flex rounded-full px-4 py-1",P(t.status)])},[e("span",{class:l(["text-sm font-semibold",j(t.status)])},n(t.status),3)],2)],2),e("td",{class:l(["items-center px-4 py-2.5",{"text-yellow-600":t.highlight}])},[e("div",Ie,[i(ee,{align:"right",width:"48"},{trigger:u(()=>[t.status!="Close"?(r(),d("button",Oe,Ve)):g("",!0)]),content:u(()=>[a.permissions.canEditMaintenance?(r(),M(te,{key:0,href:s.route("maintenance-contract.edit",{id:t.id})},{svg:u(()=>[Pe]),text:u(()=>[je]),_:2},1032,["href"])):g("",!0),a.permissions.canDeleteMaintenance?(r(),d("button",{key:1,type:"button",onClick:m=>U(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},qe,8,ze)):g("",!0)]),_:2},1024)])],2)],2))),128))])):(r(),d("tbody",Ge,Ke))])])]),a.data.data&&a.data.data.length>0?(r(),M(re,{key:0,class:"mt-6",links:a.data.links},null,8,["links"])):g("",!0)]),i(ne,{show:v.value,onClose:k},{default:u(()=>[e("div",Xe,[Ye,e("div",Ze,[i(le,{onClick:k},{default:u(()=>[w(" Cancel ")]),_:1}),i(oe,{class:"ml-3",onClick:V},{default:u(()=>[w(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}},ut=ae(Je,[["__scopeId","data-v-8366caa9"]]);export{ut as default};
