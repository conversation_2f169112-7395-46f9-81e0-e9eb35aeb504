import{K as X,r as m,l as Y,o as c,c as h,a as i,u as g,w as N,F as C,Z as H,b as e,g as z,k as O,v as V,n as $,i as M,e as T,f as I,t as x}from"./app-b7a94f67.js";import{_ as Z}from"./AdminLayout-0f1fdf67.js";import{_ as J}from"./CreateButton-fedd28a2.js";/* empty css                                                              */import{_ as Q}from"./Pagination-50283e81.js";import{_ as W}from"./SimpleDropdown-366207fb.js";import{_ as w}from"./InputLabel-11b5d690.js";import{_ as ee}from"./SearchableDropdown-711fb977.js";import{_ as te}from"./ArrowIcon-dce9e610.js";import{s as ae}from"./sortAndSearch-77279369.js";const oe={class:"animate-top"},se={class:"flex justify-between items-center"},ne=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"SNS STOCK REPORT")],-1),le={class:"flex mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},re={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},ie={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},de=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),ce={class:"flex ml-6"},me={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},ue={class:"flex justify-between mb-2"},_e={class:"flex"},pe=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),he={class:"inline-flex items-center space-x-4 justify-end w-full"},ge=["src"],fe={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center"},ve={class:"sm:col-span-3"},ye={class:"relative mt-2"},xe={class:"sm:col-span-3"},we={class:"relative mt-2"},be={class:"sm:col-span-3"},ke={class:"sm:col-span-3"},Ce={class:"mt-8 overflow-x-auto sm:rounded-lg"},Se={class:"shadow sm:rounded-lg"},Ne={class:"w-full text-sm text-left rtl:text-right text-gray-500"},ze={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Oe={class:"border-b-2"},Ve=["onClick"],$e={key:0},Me={scope:"row",class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},Te={scope:"row",class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},Ie={class:"px-4 py-2.5 min-w-52"},Re={class:"px-4 py-2.5 min-w-32"},Ue={key:1},Ae=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Be=[Ae],Ze={__name:"SnsStock",props:["data","organization","search","company","organizationId","companyId"],setup(d){const u=d,{form:b,search:Ee,sort:R,fetchData:De,sortKey:U,sortDirection:A,updateParams:B}=ae("sns-stock.report",{organization_id:u.organizationId,company_id:u.companyId,from_date:u.from_date,to_date:u.to_date});X().props.data.links.find(a=>a.active===!0);const s=m(u.organizationId),n=m(u.companyId),S=m("ALL COMPANY"),l=m(""),r=m(""),f=m("");Y([s,n,l,r],()=>{B({organization_id:s.value,conpany_id:n.value,from_date:l.value,to_date:r.value})});const E=[{field:"item_code",label:"PRODUCT CODE",sortable:!0},{field:"name",label:"PRODUCT NAME",sortable:!0},{field:"company.name",label:"COMPANY",sortable:!0},{field:"min_qty",label:"STOCK",sortable:!0}],v=(a,o,t,_,k)=>{f.value=a,b.get(route("sns-stock.report",{search:a,organization_id:o,company_id:t,from_date:_,to_date:k}),{preserveState:!0})},D=(a,o)=>{s.value=a,v(f.value,s.value,n.value,l.value,r.value)},L=(a,o)=>{n.value=a,S.value=o,v(f.value,s.value,n.value,l.value,r.value)};m([]);const j=()=>{let a="";switch(s.value){case 1:a="MC";break;case 2:a="HC";break;case 3:a="NOX";break;default:a="All_Organizations";break}const o=S.value.replace(/\s+/g,"_"),t=`SNS_Stock_Report_${a}_${o}`,_={organization_id:s.value,company_id:n.value,from_date:l.value,to_date:r.value},K=`/export-sns-stock-report?${new URLSearchParams(_).toString()}`;fetch(K,{method:"GET"}).then(p=>{if(!p.ok)throw new Error("Network response was not ok");return p.blob()}).then(p=>{const G=window.URL.createObjectURL(new Blob([p])),y=document.createElement("a");y.href=G,y.setAttribute("download",`${t}.xlsx`),document.body.appendChild(y),y.click(),document.body.removeChild(y)}).catch(p=>{console.error("Error exporting data:",p)})},q=()=>{v(f.value,s.value,n.value,l.value,r.value)},P=()=>{v(f.value,s.value,n.value,l.value,r.value)},F=a=>a&&a.length>0?a.reduce((o,t)=>o+(t.receive_qty-t.sell_qty),0):"-";return(a,o)=>(c(),h(C,null,[i(g(H),{title:"Stock Report"}),i(Z,null,{default:N(()=>[e("div",oe,[e("div",se,[ne,e("div",le,[e("div",re,[e("div",ie,[de,e("input",{id:"search-field",onInput:o[0]||(o[0]=t=>v(t.target.value,s.value,n.value,l.value,r.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),e("div",ce,[i(J,{href:a.route("reports")},{default:N(()=>[z(" Back ")]),_:1},8,["href"])])])]),e("div",me,[e("div",ue,[e("div",_e,[pe,i(w,{for:"customer_id",value:"Filters"})]),e("div",he,[e("button",{onClick:j},[e("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,ge)])])]),e("div",fe,[e("div",ve,[i(w,{for:"customer_id",value:"Organization Name"}),e("div",ye,[i(W,{options:d.organization,modelValue:s.value,"onUpdate:modelValue":o[1]||(o[1]=t=>s.value=t),onOnchange:D},null,8,["options","modelValue"])])]),e("div",xe,[i(w,{for:"customer_id",value:"Company Name"}),e("div",we,[i(ee,{options:d.company,modelValue:n.value,"onUpdate:modelValue":o[2]||(o[2]=t=>n.value=t),onOnchange:L},null,8,["options","modelValue"])])]),e("div",be,[i(w,{for:"date",value:"From Date"}),O(e("input",{"onUpdate:modelValue":o[3]||(o[3]=t=>l.value=t),class:$(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":g(b).errors.from_date}]),type:"date",onChange:q},null,34),[[V,l.value]])]),e("div",ke,[i(w,{for:"date",value:"To Date"}),O(e("input",{"onUpdate:modelValue":o[4]||(o[4]=t=>r.value=t),class:$(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":g(b).errors.to_date}]),type:"date",onChange:P},null,34),[[V,r.value]])])])]),e("div",Ce,[e("div",Se,[e("table",Ne,[e("thead",ze,[e("tr",Oe,[(c(),h(C,null,M(E,(t,_)=>e("th",{key:_,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:k=>g(R)(t.field,t.sortable)},[z(x(t.label)+" ",1),t.sortable?(c(),T(te,{key:0,isSorted:g(U)===t.field,direction:g(A)},null,8,["isSorted","direction"])):I("",!0)],8,Ve)),64))])]),d.data.data&&d.data.data.length>0?(c(),h("tbody",$e,[(c(!0),h(C,null,M(d.data.data,(t,_)=>(c(),h("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Me,x(t.item_code??"-"),1),e("th",Te,x(t.name??"-"),1),e("td",Ie,x(t.company.name??"-"),1),e("td",Re,x(F(t.sales_products)??"-"??"-"),1)]))),128))])):(c(),h("tbody",Ue,Be))])])]),d.data.data&&d.data.data.length>0?(c(),T(Q,{key:0,class:"mt-6",links:d.data.links},null,8,["links"])):I("",!0)])]),_:1})],64))}};export{Ze as default};
