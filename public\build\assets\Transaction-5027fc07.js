import{h as Y,r as x,j,m as L,o as r,c as l,a as c,u as D,w as F,F as w,Z as O,b as t,t as i,g as R,f as h,k as N,v as A,n as B,i as P}from"./app-4ea19997.js";import{_ as q}from"./AdminLayout-5eccc000.js";import{_ as G}from"./CreateButton-19955a3e.js";import{_ as X}from"./SimpleDropdown-6361db30.js";/* empty css                                                              */import{_ as b}from"./InputLabel-3a43d7c9.js";const Z={class:"animate-top"},H={class:"sm:flex sm:items-center"},J=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Customer Transaction")],-1),K={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},Q={class:"flex items-center space-x-4"},W={class:"text-lg font-semibold leading-7 text-gray-900"},ee={class:"flex justify-end w-20"},te={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},oe={class:"flex justify-between mb-2"},se={class:"flex"},ae=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),ne={class:"inline-flex items-center space-x-4 justify-end w-full"},re={key:0,class:"p-2 flex justify-end text-base font-semibold leading-6 text-gray-900"},le=["src"],ie={class:"grid grid-cols-1 sm:grid-cols-12 gap-x-6 gap-y-5 items-center"},de={class:"sm:col-span-4"},ce={class:"relative mt-2"},me={class:"sm:col-span-4"},ue={class:"sm:col-span-4"},_e={class:"mt-8 overflow-x-auto sm:rounded-lg"},pe={key:0,class:"shadow sm:rounded-lg"},ge={class:"w-full text-sm text-left rtl:text-right text-gray-500"},he={key:0,class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},fe=t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," DATE "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," NARRATION "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," DEBIT (₹) "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," CREDIT (₹) "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," BALANCE (₹) ")],-1),ye=[fe],xe={key:1},ve={class:"px-4 py-2.5 min-w-24"},we={class:"px-4 py-2.5 max-w-[24rem] font-medium text-gray-900"},be={class:"flex flex-col overflow-x-auto overflow-y-hidden whitespace-nowrap"},ke=["href"],De={class:"inline-block"},Se={key:0,class:"text-xs text-gray-700"},Ce={key:1,class:"text-xs text-gray-700"},Te={class:"px-4 py-2.5 min-w-28"},Fe={class:"px-4 py-2.5 min-w-28"},Ne={class:"px-4 py-2.5 min-w-36"},Ae={key:2},Be=t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),$e=[Be],Ye={__name:"Transaction",props:["data","customer","organization","organizationId","customerId","creditdata"],setup(f){const p=f,k=Y({}),$=x("customer tranasction"),I=()=>{const o=new Date,s=o.getMonth(),e=o.getFullYear();return`${s<3?e-1:e}-04-01`},z=()=>{const o=new Date,s=o.getFullYear(),e=String(o.getMonth()+1).padStart(2,"0"),n=String(o.getDate()).padStart(2,"0");return`${s}-${e}-${n}`},m=x(I()),g=x(z()),y=x(p.organizationId),E=o=>{k.get(route("customers.transaction",{id:p.customerId,organization_id:o}),{preserveState:!0})},M=(o,s)=>{y.value=o,E(y.value)},u=j(()=>{let o=0;p.data.forEach(e=>{const n=new Date(e.date),a=new Date(m.value);n<a&&(e.payment_type==="cr"?o+=parseFloat(e.amount):e.payment_type==="dr"&&(o-=parseFloat(e.amount)))});let s=p.data.filter(e=>{const n=new Date(e.date),a=new Date(m.value),_=new Date(g.value);if(m.value&&g.value)return n>=a&&n<=_;const d=!m.value||n>=a,T=!g.value||n<=_;return d&&T});return s=s.map(e=>{e.payment_type==="cr"?o+=parseFloat(e.amount):e.payment_type==="dr"&&(o-=parseFloat(e.amount));let n=o>=0?"cr":"dr",a=v(Math.abs(o))+" "+n;return{...e,balance:a}}),s}),S=()=>{};L(()=>{});const V=()=>{const o=$.value.replace(/\s+/g,"_"),s={customer_id:p.customerId||"",organization_id:y.value||"",from_date:m.value||"",to_date:g.value||""},n=`/export-customer-transaction?${new URLSearchParams(s).toString()}`;fetch(n,{method:"GET"}).then(a=>{if(!a.ok)throw new Error("Network response was not ok");return a.blob()}).then(a=>{const _=window.URL.createObjectURL(new Blob([a])),d=document.createElement("a");d.href=_,d.setAttribute("download",`${o}.xlsx`),document.body.appendChild(d),d.click(),document.body.removeChild(d)}).catch(a=>{console.error("Error exporting data:",a)})},v=o=>{let s=o.toFixed(2).toString(),[e,n]=s.split("."),a=e.substring(e.length-3),_=e.substring(0,e.length-3);return _!==""&&(a=","+a),`${_.replace(/\B(?=(\d{2})+(?!\d))/g,",")+a}.${n}`},U=o=>{const s=new Date(o),e={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",e)},C=x("");return C.value=p.creditdata.reduce((o,s)=>o+s.unused_amount,0),(o,s)=>(r(),l(w,null,[c(D(O),{title:"Customers"}),c(q,null,{default:F(()=>[t("div",Z,[t("div",H,[J,t("div",K,[t("div",Q,[t("h1",W,i(f.customer.customer_name)+" - "+i(f.customer.city),1),t("div",ee,[c(G,{href:o.route("customers.index")},{default:F(()=>[R(" Back ")]),_:1},8,["href"])])])])]),t("div",te,[t("div",oe,[t("div",se,[ae,c(b,{for:"customer_id",value:"Filters"})]),t("div",ne,[f.creditdata.length>0?(r(),l("div",re," Credits Available: ₹"+i(v(C.value)),1)):h("",!0),t("button",{onClick:V},[t("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,le)])])]),t("div",ie,[t("div",de,[c(b,{for:"customer_id",value:"Organization Name"}),t("div",ce,[c(X,{options:f.organization,modelValue:y.value,"onUpdate:modelValue":s[0]||(s[0]=e=>y.value=e),onOnchange:M},null,8,["options","modelValue"])])]),t("div",me,[c(b,{for:"date",value:"From Date"}),N(t("input",{"onUpdate:modelValue":s[1]||(s[1]=e=>m.value=e),class:B(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":D(k).errors.from_date}]),type:"date",onChange:S},null,34),[[A,m.value]])]),t("div",ue,[c(b,{for:"date",value:"To Date"}),N(t("input",{"onUpdate:modelValue":s[2]||(s[2]=e=>g.value=e),class:B(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":D(k).errors.to_date}]),type:"date",onChange:S},null,34),[[A,g.value]])])])]),t("div",_e,[u.value&&u.value.length>0?(r(),l("div",pe,[t("table",ge,[u.value&&u.value.length>0?(r(),l("thead",he,ye)):h("",!0),u.value&&u.value.length>0?(r(),l("tbody",xe,[(r(!0),l(w,null,P(u.value,(e,n)=>(r(),l("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",ve,i(U(e.date)??"-"),1),t("td",we,[t("div",be,[e.entity_type=="invoice"?(r(),l("a",{key:0,href:o.route("invoice.view",{id:e.entity_id}),class:"inline-block"},i(e.note??"-"),9,ke)):(r(),l(w,{key:1},[t("span",De,i(e.note??"-"),1),e.entity_type==="payment_receive"&&(e!=null&&e.payment_receive)?(r(),l(w,{key:0},[e.payment_receive.tds_amount>0?(r(),l("div",Se," TDS: ₹"+i(e.payment_receive.tds_amount),1)):h("",!0),e.payment_receive.discount_amount>0?(r(),l("div",Ce," Discount: ₹"+i(e.payment_receive.discount_amount),1)):h("",!0)],64)):h("",!0)],64))])]),t("td",Te,i(e.payment_type=="dr"?v(e.amount):"-"),1),t("td",Fe,i(e.payment_type=="cr"?v(e.amount):"-"),1),t("td",Ne,i(e.balance),1)]))),128))])):(r(),l("tbody",Ae,$e))])])):h("",!0)])])]),_:1})],64))}};export{Ye as default};
