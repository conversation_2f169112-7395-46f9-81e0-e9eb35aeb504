import{r as V,j as R,l as I,m as ee,o as u,c as p,a as d,u as l,w as E,F as M,Z as te,b as o,t as v,f,d as oe,n as w,k as ae,v as ne,i as q,g as se,T as ie}from"./app-4f4c883b.js";import{_ as le,a as re}from"./AdminLayout-d9d2bc31.js";import{_ as g}from"./InputLabel-468796e0.js";import{P as de}from"./PrimaryButton-3e579b0b.js";import{_ as O}from"./TextInput-21f4f57b.js";import{_ as ce}from"./TextArea-b7098398.js";import{_ as me}from"./RadioButton-ef33c90c.js";import{_ as D}from"./SearchableDropdown-ace42120.js";import{u as ue}from"./index-20fd5540.js";/* empty css                                                                          */import{_ as pe}from"./Checkbox-731cb89b.js";import"./_plugin-vue_export-helper-c27b6911.js";const _e={class:"h-screen animate-top"},ye={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},ve={class:"sm:flex sm:items-center"},fe=o("div",{class:"sm:flex-auto"},[o("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Payment")],-1),he={class:"flex items-center justify-between"},ge={key:0,class:"text-base font-semibold leading-6 text-gray-900"},xe=["onSubmit"],be={class:"border-b border-gray-900/10 pb-12"},we={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},ke={class:"sm:col-span-3"},Ve={class:"relative mt-2"},ze={class:"sm:col-span-3"},Fe={class:"relative mt-2"},Ae={class:"sm:col-span-2 hidden"},Ce={class:"relative mt-2"},Ne={key:0,class:"sm:col-span-3"},Se={class:"relative mt-2"},Ue={key:1,class:"sm:col-span-1"},Pe={key:2,class:"sm:col-span-2"},Te={key:3,class:"sm:col-span-2"},$e={class:"mt-4 flex justify-start"},Ee={class:"text-base font-semibold"},Oe={key:4,class:"sm:col-span-3"},De={key:5,class:"sm:col-span-3"},Be={key:6,class:"sm:col-span-3"},Ie={key:7,class:"sm:col-span-3"},Me={class:"relative mt-2"},je={class:"sm:col-span-6"},Le={class:"overflow-x-auto divide-y divide-gray-300 w-full"},Ye=o("div",{class:"w-full"},[o("thead",{class:"w-full"},[o("tr",{class:""},[o("th",{scope:"col",class:""}),o("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),o("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),o("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1),Re={style:{"overflow-y":"auto","max-height":"318px"}},qe={class:"divide-y divide-gray-300 bg-white"},He={class:"whitespace-nowrap px-2 text-sm text-gray-900"},Ze={class:"text-sm text-gray-900 leading-6 py-1.5"},Ge={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},Je={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Ke={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Qe={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},We={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},Xe={key:0,class:"text-red-500 text-xs absolute"},et={class:"whitespace-nowrap px-2 text-sm text-gray-900"},tt={class:"sm:col-span-2"},ot={class:"mt-4 flex justify-start"},at={class:"text-base font-semibold"},nt={key:0,class:"text-red-500 text-xs absolute"},st={key:8,class:"sm:col-span-6"},it={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},lt=o("thead",null,[o("tr",null,[o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1),rt={class:"divide-y divide-gray-300 bg-white"},dt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ct={class:"whitespace-nowrap py-3 text-sm text-gray-900"},mt={class:"flex flex-col"},ut={class:"text-sm text-gray-900"},pt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},_t={class:"whitespace-nowrap py-3 text-sm text-gray-900"},yt={class:"flex mt-6 items-center justify-between"},vt={class:"ml-auto flex items-center justify-end gap-x-6"},ft=o("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),ht={key:0,class:"text-sm text-gray-600"},Ut={__name:"Edit",props:["payment","paymentType","bankinfo","organization","companies","invoices","credit"],setup(N){const r=N;V([]);const S=V([]),H=r.bankinfo.filter(n=>n.organization_id===r.payment.organization_id);S.value=H;const t=ue("post","/payment",{id:r.payment.id,organization_id:r.payment.organization_id,company_id:r.payment.company_id,payment_type:r.payment.payment_type,date:r.payment.date,note:r.payment.note,amount:r.payment.amount,discount_amount:r.payment.discount_amount||0,round_off:r.payment.round_off||0,check_number:r.payment.check_number,org_bank_id:r.payment.org_bank_id,invoice:[],settled_amount:"",advance_amount:"",is_credit:"No",credit_data:[],total_unused_amount:""});t._method="PUT";const U=V(r.payment.payment_type),Z=()=>{t.settled_amount=B.value,t.advance_amount=L.value,t.total_unused_amount=F.value,t.is_credit=_.value,t.invoice=b.value,t.credit_data=x.value,t.submit({preserveScroll:!0,onSuccess:()=>{}})},G=(n,a)=>{U.value=n,t.payment_type=n,t.errors.payment_type=null,a==="Cash"?t.note="Cash":t.note==="Cash"&&(t.note="")},z=V([]),x=V([]),F=V(""),j=(n,a)=>{const e=r.bankinfo.filter(c=>c.organization_id===n);S.value=e;const s=r.invoices.filter(c=>c.purchase_order&&c.purchase_order.organization_id===n&&c.purchase_order.company_id===t.company_id);z.value=s;const i=r.credit.filter(c=>c.organization_id===n&&c.company_id===t.company_id);x.value=i,F.value=x.value.reduce((c,h)=>c+h.unused_amount,0),t.organization_id=n,t.errors.organization_id=null},J=(n,a)=>{t.company_id=n;const e=r.invoices.filter(i=>i.purchase_order&&i.purchase_order.organization_id===t.organization_id&&i.purchase_order.company_id===n);z.value=e;const s=r.credit.filter(i=>i.company_id===n&&i.organization_id===t.organization_id);x.value=s,F.value=x.value.reduce((i,c)=>i+c.unused_amount,0),t.errors.company_id=null},K=(n,a)=>{t.org_bank_id=n,t.errors.org_bank_id=null},B=R(()=>{const n=b.value.reduce((a,e)=>{if(e.check&&e.amount){const s=parseFloat(e.amount);return e.invoice_type==="purchase"?a+s:a-s}return a},0);return parseFloat(n.toFixed(2))}),L=R(()=>{const n=parseFloat(t.amount||0),a=parseFloat(t.round_off||0),e=B.value;return n>e?n-e-a:0}),A=n=>{let a=n.toFixed(2).toString(),[e,s]=a.split("."),i=e.substring(e.length-3),c=e.substring(0,e.length-3);return c!==""&&(i=","+i),`${c.replace(/\B(?=(\d{2})+(?!\d))/g,",")+i}.${s}`},Y=n=>{const a=new Date(n),e={year:"numeric",month:"short",day:"numeric"};return a.toLocaleDateString("en-US",e)},Q=(n,a)=>{const e=_.value==="Yes"?parseFloat(F.value||0):parseFloat(t.amount||0)+parseFloat(t.round_off||0);if(!b.value[a].check){b.value[a].amount=0;return}const s=b.value.filter(y=>y.check),i=s.some(y=>y.invoice_type==="purchase"),c=s.some(y=>y.invoice_type==="sales");if(i&&c&&s.length>1&&W(e,s))return;let h=e;b.value.forEach((y,$)=>{if(y.check&&$!==a&&parseFloat(y.amount||0)>0){const C=parseFloat(y.amount||0);y.invoice_type==="purchase"?h-=C:y.invoice_type==="sales"&&(h+=C)}});const m=parseFloat(b.value[a].pending_amount||0),k=Math.min(m,Math.max(0,h));b.value[a].amount=k.toFixed(2)},W=(n,a)=>{const e=a.filter(m=>m.invoice_type==="purchase"),s=a.filter(m=>m.invoice_type==="sales"),i=e.reduce((m,k)=>m+parseFloat(k.pending_amount||0),0),c=s.reduce((m,k)=>m+parseFloat(k.pending_amount||0),0),h=i-c;return Math.abs(n-Math.abs(h))<=1?(a.forEach(m=>{m.amount=parseFloat(m.pending_amount||0).toFixed(2)}),!0):h>0&&n>=h?(a.forEach(m=>{m.amount=parseFloat(m.pending_amount||0).toFixed(2)}),!0):!1},b=V([]),P=()=>{b.value=z.value.map(n=>{const a=n.invoice_type||"purchase",e=r.payment.invoice_data.some(i=>i.id===n.id&&(i.invoice_type||"purchase")===a),s=e?r.payment.invoice_data.find(i=>i.id===n.id&&(i.invoice_type||"purchase")===a).amount:0;return{id:n.id,date:n.customer_invoice_date||n.date,invoice_no:n.customer_invoice_no||n.invoice_no,total_amount:parseFloat(n.total_amount||0).toFixed(2),pending_amount:parseFloat(n.original_pending_amount||n.pending_amount||0).toFixed(2),invoice_type:a,check:e,amount:e?s.toString():"0.00"}})},_=V("No");I(z,()=>{P()}),I(_,()=>{P()}),I(()=>t.amount,()=>{_.value==="No"&&P()});const T=n=>{t.errors[n]=null,t.errors.settled_amount=null},X=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}];return ee(()=>{j(r.payment.organization_id);const n=r.invoices.filter(s=>s.purchase_order&&s.purchase_order.organization_id===r.payment.organization_id&&s.purchase_order.company_id===r.payment.company_id);z.value=n,console.log("pendinginvoice",z.value),P();const a=r.credit.filter(s=>s.organization_id===r.payment.organization_id&&s.company_id===r.payment.company_id);x.value=a,F.value=x.value.reduce((s,i)=>s+i.unused_amount,0);const e=r.bankinfo.filter(s=>s.organization_id===r.payment.organization_id);S.value=e}),(n,a)=>(u(),p(M,null,[d(l(te),{title:"Edit Payment"}),d(le,null,{default:E(()=>[o("div",_e,[o("div",ye,[o("div",ve,[fe,o("div",he,[x.value.length>0?(u(),p("div",ge," Credits Available: ₹"+v(A(F.value)),1)):f("",!0)])]),o("form",{onSubmit:oe(Z,["prevent"]),class:""},[o("div",be,[o("div",we,[o("div",ke,[d(g,{for:"payment_type",value:"Organization"}),o("div",Ve,[d(D,{options:N.organization,modelValue:l(t).organization_id,"onUpdate:modelValue":a[0]||(a[0]=e=>l(t).organization_id=e),onOnchange:j,class:w({"error rounded-md":l(t).errors.organization_id})},null,8,["options","modelValue","class"])])]),o("div",ze,[d(g,{for:"payment_type",value:"Company"}),o("div",Fe,[d(D,{options:N.companies,modelValue:l(t).company_id,"onUpdate:modelValue":a[1]||(a[1]=e=>l(t).company_id=e),onOnchange:J,class:w({"error rounded-md":l(t).errors.company_id})},null,8,["options","modelValue","class"])])]),o("div",Ae,[d(g,{for:"role_id",value:"Payment Through Credit ?"}),o("div",Ce,[d(me,{modelValue:_.value,"onUpdate:modelValue":a[2]||(a[2]=e=>_.value=e),options:X},null,8,["modelValue"])])]),_.value=="No"?(u(),p("div",Ne,[d(g,{for:"payment_type",value:"Payment Type"}),o("div",Se,[d(D,{options:N.paymentType,modelValue:l(t).payment_type,"onUpdate:modelValue":a[3]||(a[3]=e=>l(t).payment_type=e),onOnchange:G,class:w({"error rounded-md":l(t).errors.payment_type})},null,8,["options","modelValue","class"])])])):f("",!0),_.value=="No"?(u(),p("div",Ue,[d(g,{for:"round_off",value:"Round Off"}),d(O,{type:"text",onChange:a[4]||(a[4]=e=>T("round_off")),modelValue:l(t).round_off,"onUpdate:modelValue":a[5]||(a[5]=e=>l(t).round_off=e),class:w({"error rounded-md":l(t).errors.round_off})},null,8,["modelValue","class"])])):f("",!0),_.value=="No"?(u(),p("div",Pe,[d(g,{for:"amount",value:"Amount"}),d(O,{id:"amount",type:"text",onChange:a[6]||(a[6]=e=>T("amount")),modelValue:l(t).amount,"onUpdate:modelValue":a[7]||(a[7]=e=>l(t).amount=e),class:w({"error rounded-md":l(t).errors.amount})},null,8,["modelValue","class"])])):f("",!0),_.value=="No"?(u(),p("div",Te,[d(g,{for:"advance",value:"Advance(Ref) Amount"}),o("div",$e,[o("p",Ee,v(A(L.value)),1)])])):f("",!0),U.value=="check"&&_.value=="No"?(u(),p("div",Oe,[d(g,{for:"check_number",value:"Cheque Number"}),d(O,{id:"check_number",type:"text",modelValue:l(t).check_number,"onUpdate:modelValue":a[8]||(a[8]=e=>l(t).check_number=e),class:w({"error rounded-md":l(t).errors["data.check_number"]})},null,8,["modelValue","class"])])):f("",!0),_.value=="No"?(u(),p("div",De,[d(g,{for:"date",value:"Payment Date"}),ae(o("input",{"onUpdate:modelValue":a[9]||(a[9]=e=>l(t).date=e),class:w(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":l(t).errors.date}]),type:"date",onChange:a[10]||(a[10]=e=>T("date"))},null,34),[[ne,l(t).date]])])):f("",!0),U.value=="cash"&&_.value=="No"?(u(),p("div",Be)):f("",!0),U.value!="cash"&&_.value=="No"?(u(),p("div",Ie,[d(g,{for:"org_bank_id",value:"Our Bank"}),o("div",Me,[d(D,{options:S.value,modelValue:l(t).org_bank_id,"onUpdate:modelValue":a[11]||(a[11]=e=>l(t).org_bank_id=e),onOnchange:K,class:w({"error rounded-md":l(t).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):f("",!0),o("div",je,[o("table",Le,[Ye,o("div",Re,[o("tbody",qe,[(u(!0),p(M,null,q(b.value,(e,s)=>(u(),p("tr",{key:s},[o("td",He,[o("div",Ze,[d(pe,{name:"check",checked:e.check,"onUpdate:checked":i=>e.check=i,onChange:i=>Q(i,s)},null,8,["checked","onUpdate:checked","onChange"])])]),o("td",Ge,[o("span",{class:w([e.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},v(e.invoice_type==="purchase"?"PURCHASE":"SALES"),3)]),o("td",Je,v(e.invoice_no),1),o("td",Ke,v(e.total_amount),1),o("td",Qe,v(e.pending_amount),1),o("td",We,[d(O,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":i=>e.amount=i,onChange:i=>T("invoice."+s+".amount"),class:w({error:l(t).errors[`invoice.${s}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),l(t).errors[`invoice.${s}.amount`]?(u(),p("p",Xe,v(l(t).errors[`invoice.${s}.amount`]),1)):f("",!0)]),o("td",et,v(Y(e.date)),1)]))),128))])])])]),o("div",tt,[d(g,{for:"note",value:"Total Settled Amount"}),o("div",ot,[o("p",at,v(A(B.value)),1)]),l(t).errors.settled_amount?(u(),p("p",nt,v(l(t).errors.settled_amount),1)):f("",!0)]),_.value=="No"?(u(),p("div",st,[d(g,{for:"note",value:"Note"}),d(ce,{id:"note",type:"text",rows:2,modelValue:l(t).note,"onUpdate:modelValue":a[12]||(a[12]=e=>l(t).note=e)},null,8,["modelValue"])])):f("",!0)]),x.value.length>0&&_.value=="Yes"?(u(),p("table",it,[lt,o("tbody",rt,[(u(!0),p(M,null,q(x.value,(e,s)=>{var i,c,h,m,k,y,$,C;return u(),p("tr",{key:s},[o("td",dt,v(Y(e.date)),1),o("td",ct,[o("div",mt,[o("div",ut,v((c=(i=e.paymentpaid)==null?void 0:i.bank_info)!=null&&c.bank_name?(m=(h=e.paymentpaid)==null?void 0:h.bank_info)==null?void 0:m.bank_name:"Cash")+" - "+v((y=(k=e.paymentpaid)==null?void 0:k.bank_info)!=null&&y.account_number?(C=($=e.paymentpaid)==null?void 0:$.bank_info)==null?void 0:C.account_number:""),1)])]),o("td",pt,v(A(e.amount)),1),o("td",_t,v(A(e.unused_amount)),1)])}),128))])])):f("",!0)]),o("div",yt,[o("div",vt,[d(re,{href:n.route("payment.index")},{svg:E(()=>[ft]),_:1},8,["href"]),d(de,{disabled:l(t).processing},{default:E(()=>[se("Update")]),_:1},8,["disabled"]),d(ie,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:E(()=>[l(t).recentlySuccessful?(u(),p("p",ht,"Saved.")):f("",!0)]),_:1})])])],40,xe)])])]),_:1})],64))}};export{Ut as default};
