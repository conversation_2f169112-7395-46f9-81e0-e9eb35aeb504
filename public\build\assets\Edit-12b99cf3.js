import{K,r as v,o as c,c as p,a as n,u as t,w as u,F as b,Z,b as e,t as E,k as h,v as G,d as X,e as q,f,n as Y,P as x,i as j,g,T as ee}from"./app-ce7743ab.js";import{_ as se,a as te}from"./AdminLayout-6af2fc6a.js";import{_ as m}from"./InputError-473f1c1e.js";import{_ as r}from"./InputLabel-3aa35471.js";import{P as oe}from"./PrimaryButton-6ff8a943.js";import{_}from"./TextInput-65921831.js";import{_ as ae}from"./TextArea-5fab1749.js";import{_ as ne}from"./SearchableDropdown-6fd7fbbe.js";import{C as le}from"./CheckboxWithLabel-17a263c7.js";import{_ as B}from"./FileUpload-77d3b459.js";import{M as P}from"./Modal-599968f2.js";import{_ as ie}from"./FileViewer-7bb087a7.js";import{_ as N}from"./SecondaryButton-aec1a882.js";import{D as A}from"./DangerButton-ca58e5a5.js";import{u as de}from"./index-588ba5dc.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                                          *//* empty css                                                              */const re={class:"animate-top h-screen"},ce={class:"sm:flex sm:items-center"},ue=e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Jobcard")],-1),me={class:"w-auto"},pe={class:"flex space-x-2"},_e=e("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Jobcard Number:",-1),ve={class:"text-sm font-semibold text-gray-900 leading-6"},ge={class:"flex space-x-2 items-center"},he=e("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Date :",-1),fe=["disabled"],ye={class:"mt-8 bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},be=["onSubmit"],xe={class:"border-b border-gray-900/10 pb-12"},we={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},ke={class:"sm:col-span-4"},Ce={class:"sm:col-span-2"},Ve={class:"sm:col-span-2"},De={class:"sm:col-span-4"},Me={class:"sm:col-span-3"},$e={class:"sm:col-span-3"},Se={class:"sm:col-span-3"},Ue={class:"sm:col-span-3"},Ee={class:"sm:col-span-6"},qe={class:"sm:col-span-6"},je={class:"sm:col-span-3"},Pe={class:"relative mt-2"},Ne={class:"sm:col-span-12"},Oe={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Te={class:"sm:col-span-3"},Be={class:"mt-2 space-y-2"},Ae={class:"flex items-center space-x-2"},Le=["disabled"],Ie=e("span",null,"Warranty",-1),Qe={class:"flex items-center space-x-2"},Fe=["disabled"],Je=e("span",null,"Out of Warranty",-1),We={class:"flex items-center space-x-2"},He=["disabled"],Re=e("span",null,"AMC",-1),ze={class:"flex items-center space-x-2"},Ke=["disabled"],Ze=e("span",null,"CMC",-1),Ge={class:"sm:col-span-6"},Xe={class:"grid sm:grid-cols-6 relative mt-2"},Ye={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-4"},es={key:0,class:"bg-white p-1 shadow sm:rounded-lg border"},ss={class:"min-w-full divide-y divide-gray-300"},ts=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"QUOTATION DOCUMENT"),e("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1),os={class:"divide-y divide-gray-300 bg-white"},as={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},ns={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},ls=["onClick"],is=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),ds=[is],rs=["onClick"],cs=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),us=[cs],ms={key:1,class:"bg-white p-1 shadow sm:rounded-lg border"},ps={class:"min-w-full divide-y divide-gray-300"},_s=e("thead",{class:"bg-gray-50"},[e("tr",null,[e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"PO DOCUMENT"),e("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1),vs={class:"divide-y divide-gray-300 bg-white"},gs={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},hs={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},fs=["onClick"],ys=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),bs=[ys],xs=["onClick"],ws=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),ks=[ws],Cs={class:"flex mt-6 items-center justify-between"},Vs={class:"ml-auto flex items-center justify-end gap-x-6"},Ds=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Ms={key:0,class:"text-sm text-gray-600"},$s={class:"p-6"},Ss={class:"mt-6 px-4 flex justify-end"},Us={class:"p-6"},Es=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this PO document? ",-1),qs={class:"mt-6 flex justify-end"},js={class:"p-6"},Ps=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this quotation document? ",-1),Ns={class:"mt-6 flex justify-end"},et={__name:"Edit",props:["data","checklist","permissions","engineer","filePath"],setup(i){const l=K().props.data,y=v([]),w=v(!1),k=v(""),C=v(!1),V=v(null),D=v(!1),M=v(null);y.value=l.job_card_checks.map(d=>d.job_card_checklist_id);const s=de("post","/jobcard",{id:l.id,type:l.type,job_card_number:l.job_card_number,engineer_id:l.engineer_id,hospital_name:l.hospital_name,address:l.address,city:l.city,contact_no:l.contact_no,product_name:l.product_name,product_code:l.product_code,serial_no:l.serial_no,accessories:l.accessories,problem_description:l.problem_description,parts_required:l.parts_required,warranty_status:l.warranty_status,jobchecks:[],date:l.date,quotation_document:null,po_document:null,_method:"PUT"}),L=d=>{s.quotation_document=d},I=d=>{s.po_document=d},Q=d=>{y.value=d},F=()=>{s.jobchecks=y.value,s.post(route("jobcard.update",s.id),{preserveScroll:!0,onSuccess:()=>{s.quotation_document=null,s.po_document=null}})},J=(d,o)=>{s.engineer_id=d,s.errors.engineer_id=null},O=d=>{k.value=d,w.value=!0},T=()=>{w.value=!1,k.value=""},W=d=>{V.value=d,C.value=!0},H=()=>{s.get(route("removedocument",{id:V.value,name:"jobcardDocument"}),{onSuccess:()=>{$()}})},$=()=>{C.value=!1,V.value=null},R=d=>{M.value=d,D.value=!0},z=()=>{s.get(route("removedocument",{id:M.value,name:"jobcardDocument"}),{onSuccess:()=>{S()}})},S=()=>{D.value=!1,M.value=null};return(d,o)=>(c(),p(b,null,[n(t(Z),{title:"Jobcard Checklist"}),n(se,null,{default:u(()=>[e("div",re,[e("div",ce,[ue,e("div",me,[e("div",pe,[_e,e("span",ve,E(t(l).job_card_number),1)]),e("div",ge,[he,h(e("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":o[0]||(o[0]=a=>t(s).date=a),onChange:o[1]||(o[1]=a=>t(s).validate("date")),disabled:i.permissions.isServiceEngineer},null,40,fe),[[G,t(s).date]])])])]),e("div",ye,[e("form",{onSubmit:X(F,["prevent"]),class:""},[e("div",xe,[e("div",we,[e("div",ke,[n(r,{for:"hospital_name",value:"Hospital Name"}),n(_,{id:"hospital_name",hospital_name:"text",modelValue:t(s).hospital_name,"onUpdate:modelValue":o[2]||(o[2]=a=>t(s).hospital_name=a),autocomplete:"hospital_name",onChange:o[3]||(o[3]=a=>t(s).validate("hospital_name")),disabled:i.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),n(m,{class:"",message:t(s).errors.hospital_name},null,8,["message"])]),e("div",Ce,[n(r,{for:"contact_no",value:"Contact No"}),n(_,{id:"contact_no",contact_no:"text",modelValue:t(s).contact_no,"onUpdate:modelValue":o[4]||(o[4]=a=>t(s).contact_no=a),numeric:!0,autocomplete:"contact_no",onChange:o[5]||(o[5]=a=>t(s).validate("contact_no")),disabled:i.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),n(m,{class:"",message:t(s).errors.contact_no},null,8,["message"])]),e("div",Ve,[n(r,{for:"city",value:"City"}),n(_,{id:"city",city:"text",modelValue:t(s).city,"onUpdate:modelValue":o[6]||(o[6]=a=>t(s).city=a),autocomplete:"city",onChange:o[7]||(o[7]=a=>t(s).validate("city")),disabled:i.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),n(m,{class:"",message:t(s).errors.city},null,8,["message"])]),e("div",De,[n(r,{for:"address",value:"Address"}),n(_,{id:"address",type:"text",modelValue:t(s).address,"onUpdate:modelValue":o[8]||(o[8]=a=>t(s).address=a),onChange:o[9]||(o[9]=a=>t(s).validate("address")),disabled:i.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),n(m,{class:"",message:t(s).errors.address},null,8,["message"])]),e("div",Me,[n(r,{for:"product_name",value:"Equipment"}),n(_,{id:"product_name",product_name:"text",modelValue:t(s).product_name,"onUpdate:modelValue":o[10]||(o[10]=a=>t(s).product_name=a),autocomplete:"product_name",onChange:o[11]||(o[11]=a=>t(s).validate("product_name")),disabled:i.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),n(m,{class:"",message:t(s).errors.product_name},null,8,["message"])]),e("div",$e,[n(r,{for:"product_code",value:"Model"}),n(_,{id:"product_code",product_code:"text",modelValue:t(s).product_code,"onUpdate:modelValue":o[12]||(o[12]=a=>t(s).product_code=a),autocomplete:"product_code",onChange:o[13]||(o[13]=a=>t(s).validate("product_code")),disabled:i.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),n(m,{class:"",message:t(s).errors.product_code},null,8,["message"])]),e("div",Se,[n(r,{for:"serial_no",value:"Serial No"}),n(_,{id:"serial_no",serial_no:"text",modelValue:t(s).serial_no,"onUpdate:modelValue":o[14]||(o[14]=a=>t(s).serial_no=a),autocomplete:"serial_no",onChange:o[15]||(o[15]=a=>t(s).validate("serial_no")),disabled:i.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),n(m,{class:"",message:t(s).errors.serial_no},null,8,["message"])]),e("div",Ue,[n(r,{for:"accessories",value:"Accessories"}),n(_,{id:"accessories",accessories:"text",modelValue:t(s).accessories,"onUpdate:modelValue":o[16]||(o[16]=a=>t(s).accessories=a),autocomplete:"accessories",onChange:o[17]||(o[17]=a=>t(s).validate("accessories")),disabled:i.permissions.isServiceEngineer},null,8,["modelValue","disabled"]),t(s).invalid("accessories")?(c(),q(m,{key:0,class:"",message:t(s).errors.accessories},null,8,["message"])):f("",!0)]),e("div",Ee,[n(r,{for:"problem_description",value:"Description"}),n(ae,{id:"problem_description",type:"text",rows:3,modelValue:t(s).problem_description,"onUpdate:modelValue":o[18]||(o[18]=a=>t(s).problem_description=a),onChange:o[19]||(o[19]=a=>t(s).validate("problem_description"))},null,8,["modelValue"]),n(m,{class:"",message:t(s).errors.problem_description},null,8,["message"])]),e("div",qe,[n(r,{for:"parts_required",value:"Parts Required"}),n(_,{id:"parts_required",type:"text",modelValue:t(s).parts_required,"onUpdate:modelValue":o[20]||(o[20]=a=>t(s).parts_required=a),autocomplete:"parts_required",onChange:o[21]||(o[21]=a=>t(s).validate("parts_required"))},null,8,["modelValue"]),n(m,{class:"",message:t(s).errors.parts_required},null,8,["message"])]),e("div",je,[n(r,{for:"engineer_id",value:"Engineer Name"}),e("div",Pe,[n(ne,{options:i.engineer,modelValue:t(s).engineer_id,"onUpdate:modelValue":o[22]||(o[22]=a=>t(s).engineer_id=a),onOnchange:J,class:Y({"error rounded-md":t(s).errors.engineer_id}),disabled:i.permissions.isServiceEngineer},null,8,["options","modelValue","class","disabled"])])]),e("div",Ne,[e("div",Oe,[e("div",null,[n(r,{for:"quotation_document",value:"Upload Job Quotation Document"}),n(B,{inputId:"quotation_document",inputName:"quotation_document",onFile:L})]),e("div",null,[n(r,{for:"po_document",value:"Upload Job PO Document"}),n(B,{inputId:"po_document",inputName:"po_document",onFile:I})])])]),e("div",Te,[n(r,{for:"warranty_status",value:"Warranty Status"}),e("div",Be,[e("label",Ae,[h(e("input",{type:"radio",id:"warranty",value:"warranty","onUpdate:modelValue":o[23]||(o[23]=a=>t(s).warranty_status=a),onChange:o[24]||(o[24]=a=>t(s).validate("warranty_status")),class:"text-indigo-600 focus:ring-indigo-500 border-gray-300",disabled:i.permissions.isServiceEngineer},null,40,Le),[[x,t(s).warranty_status]]),Ie]),e("label",Qe,[h(e("input",{type:"radio",id:"out_of_warranty",value:"out_of_warranty","onUpdate:modelValue":o[25]||(o[25]=a=>t(s).warranty_status=a),onChange:o[26]||(o[26]=a=>t(s).validate("warranty_status")),class:"text-indigo-600 focus:ring-indigo-500 border-gray-300",disabled:i.permissions.isServiceEngineer},null,40,Fe),[[x,t(s).warranty_status]]),Je]),e("label",We,[h(e("input",{type:"radio",id:"amc",value:"amc","onUpdate:modelValue":o[27]||(o[27]=a=>t(s).warranty_status=a),onChange:o[28]||(o[28]=a=>t(s).validate("warranty_status")),class:"text-indigo-600 focus:ring-indigo-500 border-gray-300",disabled:i.permissions.isServiceEngineer},null,40,He),[[x,t(s).warranty_status]]),Re]),e("label",ze,[h(e("input",{type:"radio",id:"cmc",value:"cmc","onUpdate:modelValue":o[29]||(o[29]=a=>t(s).warranty_status=a),onChange:o[30]||(o[30]=a=>t(s).validate("warranty_status")),class:"text-indigo-600 focus:ring-indigo-500 border-gray-300",disabled:i.permissions.isServiceEngineer},null,40,Ke),[[x,t(s).warranty_status]]),Ze])]),t(s).invalid("warranty_status")?(c(),q(m,{key:0,message:t(s).errors.warranty_status},null,8,["message"])):f("",!0)]),e("div",Ge,[n(r,{for:"engineer_id",value:"Checklist"}),e("div",Xe,[(c(!0),p(b,null,j(i.checklist,a=>(c(),q(le,{key:a.id,checked:y.value,value:a.id,label:a.type,"onUpdate:checked":Q,disabled:i.permissions.isServiceEngineer},null,8,["checked","value","label","disabled"]))),128))])])]),e("div",Ye,[t(l).quotation_documents&&t(l).quotation_documents.length>0?(c(),p("div",es,[e("table",ss,[ts,e("tbody",os,[(c(!0),p(b,null,j(t(l).quotation_documents,a=>(c(),p("tr",{key:a.id},[e("td",as,E(a.orignal_name),1),e("td",ns,[e("button",{type:"button",onClick:U=>O(a.name)},ds,8,ls),e("button",{type:"button",onClick:U=>R(a.id)},us,8,rs)])]))),128))])])])):f("",!0),t(l).po_documents&&t(l).po_documents.length>0?(c(),p("div",ms,[e("table",ps,[_s,e("tbody",vs,[(c(!0),p(b,null,j(t(l).po_documents,a=>(c(),p("tr",{key:a.id},[e("td",gs,E(a.orignal_name),1),e("td",hs,[e("button",{type:"button",onClick:U=>O(a.name)},bs,8,fs),e("button",{type:"button",onClick:U=>W(a.id)},ks,8,xs)])]))),128))])])])):f("",!0)])]),e("div",Cs,[e("div",Vs,[n(te,{href:d.route("jobcard.index")},{svg:u(()=>[Ds]),_:1},8,["href"]),n(oe,{disabled:t(s).processing},{default:u(()=>[g("Save")]),_:1},8,["disabled"]),n(ee,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:u(()=>[t(s).recentlySuccessful?(c(),p("p",Ms,"Saved.")):f("",!0)]),_:1})])])],40,be)])]),n(P,{show:w.value,onClose:T,maxWidth:"xl"},{default:u(()=>[e("div",$s,[n(ie,{fileUrl:i.filePath.view+k.value},null,8,["fileUrl"]),e("div",Ss,[n(N,{onClick:T},{default:u(()=>[g("Close")]),_:1})])])]),_:1},8,["show"]),n(P,{show:C.value,onClose:$},{default:u(()=>[e("div",Us,[Es,e("div",qs,[n(N,{onClick:$},{default:u(()=>[g(" Cancel")]),_:1}),n(A,{class:"ml-3",onClick:H},{default:u(()=>[g(" Delete ")]),_:1})])])]),_:1},8,["show"]),n(P,{show:D.value,onClose:S},{default:u(()=>[e("div",js,[Ps,e("div",Ns,[n(N,{onClick:S},{default:u(()=>[g(" Cancel")]),_:1}),n(A,{class:"ml-3",onClick:z},{default:u(()=>[g(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{et as default};
