import{K as tt,r as b,C as bt,j as D,o as u,c as m,a as r,u as a,w as x,F as O,Z as Vt,b as t,t as y,k as Ct,v as St,d as Ft,n as p,f as v,i as et,g as I,e as kt,s as It,x as $t}from"./app-ce7743ab.js";import{_ as Tt,a as Pt}from"./AdminLayout-6af2fc6a.js";import{_ as Dt}from"./InputError-473f1c1e.js";import{_ as V}from"./InputLabel-3aa35471.js";import{P as st}from"./PrimaryButton-6ff8a943.js";import{_ as C}from"./TextInput-65921831.js";import{_ as z}from"./TextArea-5fab1749.js";import{_ as U}from"./SearchableDropdown-6fd7fbbe.js";import{D as ot}from"./DangerButton-ca58e5a5.js";import{_ as B}from"./SecondaryButton-aec1a882.js";import{M as E}from"./Modal-599968f2.js";import{_ as Ut}from"./FileViewer-7bb087a7.js";import{_ as Gt}from"./MultipleFileUpload-7a5f3a52.js";import{u as Mt}from"./index-588ba5dc.js";import{_ as jt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const c=f=>(It("data-v-b6c10f45"),f=f(),$t(),f),At={class:"animate-top"},Nt={class:"sm:flex sm:items-center"},qt=c(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Order")],-1)),Ot={class:"w-auto"},zt={class:"flex space-x-2 items-center"},Bt=c(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6"},"Order Number:",-1)),Et={class:"text-sm font-semibold text-gray-900 leading-6"},Lt={class:"flex space-x-2 items-center"},Wt=c(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Date :",-1)),Rt=["onSubmit"],Ht={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Kt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Qt={class:"sm:col-span-4"},Zt={class:"relative mt-2"},Jt={class:"sm:col-span-4"},Xt={class:"relative mt-2"},Yt={class:"sm:col-span-4"},te={class:"relative mt-2"},ee={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},se={class:"overflow-x-auto w-full"},oe={class:"overflow-x-auto divide-y divide-gray-300 w-full",style:{"margin-bottom":"160px"}},le=c(()=>t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Model",-1)),ae=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),ne=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),re=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),de=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),ie=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),ce={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ue={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},me={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},_e={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},pe={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ge=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),ve=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),ye=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),he={class:"divide-y divide-gray-300 bg-white"},xe={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-80"},fe={class:"relative mt-2"},we={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-96"},be={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ve={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ce={key:0,class:"text-red-500 text-xs absolute"},Se={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Fe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},ke={class:"text-sm text-gray-900 leading-6 mt-2 py-1.5"},Ie={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},$e={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Te={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Pe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},De={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ue={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ge={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-36"},Me={class:"flex space-x-2"},je={class:"text-sm text-gray-900 leading-6 py-1.5"},Ae=["onClick"],Ne=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),qe=[Ne],Oe={class:"flex items-center justify-between"},ze={class:"ml-auto flex items-center justify-end gap-x-6"},Be={key:0,class:"mt-6 bg-white p-1 shadow sm:rounded-lg border"},Ee={class:"min-w-full divide-y divide-gray-300"},Le=c(()=>t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"UPLOADED DOCUMENT "),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"REMOVE")])],-1)),We={class:"divide-y divide-gray-300 bg-white"},Re={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},He={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},Ke=["onClick"],Qe=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Ze=[Qe],Je=["onClick"],Xe=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),Ye=[Xe],ts=["onClick"],es=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),ss=[es],os={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},ls={class:"items-center justify-between"},as={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},ns={class:"inline-flex items-center justify-end w-full space-x-3"},rs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),ds={class:"text-base font-semibold text-gray-900 w-20"},is={class:"inline-flex items-center justify-end w-full space-x-3"},cs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),us={class:"w-40"},ms={class:"inline-flex items-center justify-end w-full space-x-3"},_s=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),ps={class:"text-base font-semibold text-gray-900 w-w-32"},gs={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},vs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),ys={class:"text-base font-semibold text-gray-900 w-w-32"},hs={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},xs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),fs={class:"text-base font-semibold text-gray-900 w-w-32"},ws={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},bs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),Vs={class:"text-base font-semibold text-gray-900 w-w-32"},Cs={class:"inline-flex items-center justify-end w-full space-x-3"},Ss=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),Fs={class:"text-base font-semibold text-gray-900 w-w-32"},ks={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Is={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},$s={class:"sm:col-span-10"},Ts={class:"flex space-x-4"},Ps={class:"w-full"},Ds=c(()=>t("div",{class:"w-full"},null,-1)),Us={class:"w-full"},Gs={class:"relative mt-2"},Ms={class:"sm:col-span-10"},js={class:"flex space-x-4"},As={class:"w-full"},Ns={class:"w-full"},qs={class:"w-full"},Os={class:"sm:col-span-10"},zs={class:"flex space-x-4"},Bs={class:"w-full"},Es={class:"w-full"},Ls={class:"flex mt-6 items-center justify-between"},Ws={class:"ml-auto flex items-center justify-end gap-x-6"},Rs=c(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),Hs={class:"p-6"},Ks=c(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this product? ",-1)),Qs={class:"mt-6 flex justify-end"},Zs={class:"p-6"},Js=c(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this document? ",-1)),Xs={class:"mt-6 flex justify-end"},Ys={class:"p-6"},to={class:"mt-6 px-4 flex justify-end"},eo={__name:"Edit",props:["customers","salesuser","products","filepath","category","organization"],setup(f){const lt=f,L=tt().props.filepath.view,i=tt().props.data[0],s=Mt("post","/orders",{note:i.note,date:i.date,selectedProductItem:[],customer_id:i.customer_id,sales_user_id:i.sales_user_id,organization_id:i.organization_id,category:i.category,total_amount:"",order_number:i.order_number,order_id:i.id,document:i.documents,cgst:i.cgst,sgst:i.sgst,igst:i.igst,total_gst:i.total_gst,sub_total:i.sub_total,total_discount:i.total_discount,overall_discount:i.overall_discount,validity:i.validity,delivery:i.delivery,payment_terms:i.payment_terms,warranty:i.warranty}),at=()=>{s.sub_total=H.value,s.total_discount=K.value,s.cgst=g.value=="CGST/SGST"?k.value/2:"0",s.sgst=g.value=="CGST/SGST"?k.value/2:"0",s.igst=g.value=="IGST"?k.value:"0",s.total_gst=k.value,s.total_amount=R.value,s.selectedProductItem=_.value,s.submit({preserveScroll:!0,onSuccess:()=>s.reset()})},_=b([{product_id:"",description:"",qty:"",price:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",discount:"",discount_amount:"",total_amount:"",order_details_id:"",delivered_qty:0}]);bt(()=>{_.value=i.order_details.map(o=>{var l,e;return{delivered_qty:o.delivered_qty,product_id:o.product_id,order_details_id:o.id,description:o.description,qty:o.qty,price:parseFloat(o.price).toFixed(2),total_price:parseFloat(o.total_price).toFixed(2),mrp:(e=(l=o.product)==null?void 0:l.serial_numbers[0])!=null&&e.mrp?parseFloat(o.product.serial_numbers[0].mrp).toFixed(2):"-",gst:parseFloat(o.gst).toFixed(2),sgst:parseFloat(o.gst/2).toFixed(2),gst_amount:parseFloat(o.gst_amount).toFixed(2),total_gst_amount:parseFloat(o.total_gst_amount).toFixed(2),total_amount:parseFloat(o.total_amount).toFixed(2),discount:parseFloat(o.discount).toFixed(2),discount_amount:parseFloat(o.discount_amount).toFixed(2)??"0"}})});const g=b(i.customers.gst_type),nt=(o,l,e,d)=>{var h;const n=lt.products.find(S=>S.id===o);n&&(_.value[e].product_id=n.id,_.value[e].price=parseFloat(n.price).toFixed(2),_.value[e].description=n.description,_.value[e].mrp=(h=n==null?void 0:n.serial_numbers[0])!=null&&h.mrp?parseFloat(n.serial_numbers[0].mrp).toFixed(2):"-",_.value[e].gst=parseFloat(n.gst).toFixed(2),_.value[e].sgst=parseFloat(n.gst/2).toFixed(2),_.value[e].discount="0.00",s.errors[`selectedProductItem.${e}.product_id`]=null,s.errors[`selectedProductItem.${e}.price`]=null,F(d))},rt=()=>{_.value.push({product_id:"",description:"",qty:"",price:"",gst:"",total_price:"",gst_amount:"",total_gst_amount:"",discount:"",discount_amount:"",total_amount:"",order_details_id:"",delivered_qty:0})},G=b(!1),W=b(null),dt=b(null),M=()=>{G.value=!1},it=()=>{s.get(route("removeproduct",{id:W.value,model:"OrderDetails"}),{onSuccess:()=>{M(),_.value.splice(index,1)}})},ct=(o,l)=>{l!==void 0&&l!=""?(W.value=l,dt.value=o,G.value=!0):_.value.splice(o,1)},ut=(o,l)=>{const e=parseFloat(o.price),d=parseFloat(o.discount)||0,n=g.value=="IGST"?parseFloat(o.gst):parseFloat(o.sgst*2),h=parseFloat(o.qty);let S=0,T=0;d>0?S=e*h:S=e*h*(1+n/100);const P=S*(d/100)||0,X=e*1*(n/100),q=(e*h-P)*(n/100);d>0?T=S-P+q:T=S-P;const Y=e*h;return o.total_price=isNaN(Y)?"":parseFloat(Y).toFixed(2),o.gst_amount=isNaN(X)?"":parseFloat(X).toFixed(2),o.total_gst_amount=isNaN(q)?"":parseFloat(q).toFixed(2),o.discount_amount=isNaN(P)?"":parseFloat(P).toFixed(2),isNaN(T)?"":parseFloat(T).toFixed(2)},F=(o,l)=>{o.total_amount=ut(o)},R=D(()=>{const o=Math.round(_.value.reduce((e,d)=>e+(d.total_amount?parseFloat(d.total_amount):0),0)),l=s.overall_discount?parseFloat(s.overall_discount):0;return o-l}),k=D(()=>_.value.reduce((o,l)=>o+(l.total_gst_amount?parseFloat(l.total_gst_amount):0),0)),H=D(()=>_.value.reduce((o,l)=>o+(l.total_price?parseFloat(l.total_price):0),0)),K=D(()=>{const o=_.value.reduce((e,d)=>e+(d.discount_amount?parseFloat(d.discount_amount):0),0),l=s.overall_discount?parseFloat(s.overall_discount):0;return o+l}),w=o=>{s.errors[o]=null};D(()=>{const o=new Date(i.date),l={year:"numeric",month:"long",day:"numeric"};return o.toLocaleDateString("en-US",l)});const mt=o=>{s.document=o},j=b(!1),Q=b(null),_t=o=>{Q.value=o,j.value=!0},pt=()=>{s.get(route("removedocument",{id:Q.value,name:"orderDocument"}),{onSuccess:()=>{A()}})},A=()=>{j.value=!1},gt=(o,l)=>{s.sales_user_id=o},N=b(!1),Z=b(null),vt=b("custom"),yt=o=>{Z.value=o,N.value=!0},J=()=>{N.value=!1},ht=o=>{const l=window.location.origin+L+o,e=document.createElement("a");e.href=l,e.setAttribute("download",o),document.body.appendChild(e),e.click(),document.body.removeChild(e)},$=o=>{let l=o.toFixed(2).toString(),[e,d]=l.split("."),n=e.substring(e.length-3),h=e.substring(0,e.length-3);return h!==""&&(n=","+n),`${h.replace(/\B(?=(\d{2})+(?!\d))/g,",")+n}.${d}`},xt=(o,l)=>{s.customer_id=o,s.errors.customer_id=null},ft=(o,l)=>{s.category=o,s.errors.category=null},wt=(o,l)=>{s.organization_id=o,s.errors.organization_id=null};return(o,l)=>(u(),m(O,null,[r(a(Vt),{title:"Orders"}),r(Tt,null,{default:x(()=>[t("div",At,[t("div",Nt,[qt,t("div",Ot,[t("div",zt,[Bt,t("span",Et,y(a(i).order_number),1)]),t("div",Lt,[Wt,Ct(t("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":l[0]||(l[0]=e=>a(s).date=e),onChange:l[1]||(l[1]=e=>a(s).validate("date"))},null,544),[[St,a(s).date]])])])]),t("form",{onSubmit:Ft(at,["prevent"]),class:""},[t("div",Ht,[t("div",Kt,[t("div",Qt,[r(V,{for:"company_name",value:"Organization"}),t("div",Zt,[r(U,{options:f.organization,modelValue:a(s).organization_id,"onUpdate:modelValue":l[2]||(l[2]=e=>a(s).organization_id=e),onOnchange:wt,class:p({"error rounded-md":a(s).errors.organization_id}),editMode:"editMode"},null,8,["options","modelValue","class"])])]),t("div",Jt,[r(V,{for:"customer_id",value:"Customer Name"}),t("div",Xt,[r(U,{options:f.customers,modelValue:a(s).customer_id,"onUpdate:modelValue":l[3]||(l[3]=e=>a(s).customer_id=e),onOnchange:xt,class:p({"error rounded-md":a(s).errors.customer_id})},null,8,["options","modelValue","class"])])]),t("div",Yt,[r(V,{for:"company_name",value:"Category"}),t("div",te,[r(U,{options:f.category,modelValue:a(s).category,"onUpdate:modelValue":l[4]||(l[4]=e=>a(s).category=e),onOnchange:ft,class:p({"error rounded-md":a(s).errors.category})},null,8,["options","modelValue","class"])])])])]),t("div",ee,[t("div",se,[t("table",oe,[t("thead",null,[t("tr",null,[le,ae,ne,re,de,ie,g.value=="IGST"?(u(),m("th",ce,"IGST (%)")):v("",!0),g.value=="IGST"?(u(),m("th",ue,"IGST (₹)")):v("",!0),g.value=="CGST/SGST"?(u(),m("th",me,"CGST (%)")):v("",!0),g.value=="CGST/SGST"?(u(),m("th",_e,"SGST (%)")):v("",!0),g.value=="CGST/SGST"?(u(),m("th",pe,"Total GST (₹)")):v("",!0),ge,ve,ye])]),t("tbody",he,[(u(!0),m(O,null,et(_.value,(e,d)=>(u(),m("tr",{key:d},[t("td",xe,[t("div",fe,[r(U,{options:f.products,modelValue:e.product_id,"onUpdate:modelValue":n=>e.product_id=n,onOnchange:(n,h)=>nt(n,h,d,e),onChange:l[5]||(l[5]=n=>a(s).validate("product_id")),class:p({"error rounded-md":a(s).errors[`selectedProductItem.${d}.product_id`]})},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class"])])]),t("td",we,[r(z,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":n=>e.description=n,autocomplete:"description",rows:2,onChange:n=>e.validate("description"),class:p({"error rounded-md":a(s).errors[`selectedProductItem.${d}.description`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),t("td",be,y(e.mrp??"-"),1),t("td",Ve,[r(C,{id:"qty",type:"text",numeric:!0,modelValue:e.qty,"onUpdate:modelValue":n=>e.qty=n,autocomplete:"qty",onInput:n=>F(e,d),onChange:n=>w("selectedProductItem."+d+".qty"),class:p({error:a(s).errors[`selectedProductItem.${d}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"]),a(s).errors[`selectedProductItem.${d}.qty1`]?(u(),m("p",Ce,y(a(s).errors[`selectedProductItem.${d}.qty1`]),1)):v("",!0)]),t("td",Se,[r(C,{id:"price",type:"text",modelValue:e.price,"onUpdate:modelValue":n=>e.price=n,autocomplete:"price",onInput:n=>F(e,d),onChange:n=>w("selectedProductItem."+d+".price"),class:p({error:a(s).errors[`selectedProductItem.${d}.price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Fe,[t("div",ke,y(e.total_price),1)]),g.value=="IGST"?(u(),m("td",Ie,[r(C,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":n=>e.gst=n,onInput:n=>F(e,d),onChange:n=>w("selectedProductItem."+d+".gst"),class:p({error:a(s).errors[`selectedProductItem.${d}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):v("",!0),g.value=="CGST/SGST"?(u(),m("td",$e,[r(C,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":n=>e.sgst=n,onInput:n=>F(e,d),onChange:n=>w("selectedProductItem."+d+".gst"),class:p({error:a(s).errors[`selectedProductItem.${d}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):v("",!0),g.value=="CGST/SGST"?(u(),m("td",Te,[r(C,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":n=>e.sgst=n,onInput:n=>F(e,d),onChange:n=>w("selectedProductItem."+d+".gst"),class:p({error:a(s).errors[`selectedProductItem.${d}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):v("",!0),t("td",Pe,y(e.total_gst_amount),1),t("td",De,[r(C,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":n=>e.discount=n,onInput:n=>F(e,d),onChange:n=>w("selectedProductItem."+d+".discount"),class:p({error:a(s).errors[`selectedProductItem.${d}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Ue,y(e.discount_amount),1),t("td",Ge,[t("div",Me,[t("div",je,y(e.total_amount),1),e.delivered_qty==0?(u(),m("button",{key:0,type:"button",class:"mt-1 flex",onClick:n=>ct(d,e.order_details_id)},qe,8,Ae)):v("",!0)])])]))),128))])])]),t("div",Oe,[t("div",ze,[r(st,{onClick:rt,type:"button"},{default:x(()=>[I("Add Product")]),_:1})])])]),a(i).documents&&a(i).documents.length>0?(u(),m("div",Be,[t("table",Ee,[Le,t("tbody",We,[(u(!0),m(O,null,et(a(i).documents,(e,d)=>(u(),m("tr",{key:a(i).id,class:""},[t("td",Re,y(e.orignal_name),1),t("td",He,[t("button",{type:"button",onClick:n=>_t(e.id)},Ze,8,Ke),t("button",{type:"button",onClick:n=>yt(e.name)},Ye,8,Je),t("button",{type:"button",onClick:n=>ht(e.name)},ss,8,ts)])]))),128))])])])):v("",!0),t("div",os,[t("div",ls,[t("div",as,[t("div",ns,[rs,t("p",ds,y($(H.value)),1)]),t("div",is,[cs,t("div",us,[r(C,{id:"overall_discount",type:"text",modelValue:a(s).overall_discount,"onUpdate:modelValue":l[6]||(l[6]=e=>a(s).overall_discount=e)},null,8,["modelValue"])])]),t("div",ms,[_s,t("p",ps,y($(K.value)),1)]),g.value=="IGST"?(u(),m("div",gs,[vs,t("p",ys,y($(k.value)),1)])):v("",!0),g.value=="CGST/SGST"?(u(),m("div",hs,[xs,t("p",fs,y($(k.value/2)),1)])):v("",!0),g.value=="CGST/SGST"?(u(),m("div",ws,[bs,t("p",Vs,y($(k.value/2)),1)])):v("",!0),t("div",Cs,[Ss,t("p",Fs,y($(R.value)),1)])])])]),t("div",ks,[t("div",Is,[t("div",$s,[t("div",Ts,[t("div",Ps,[r(V,{for:"note",value:"Upload Documents"}),r(Gt,{inputId:"document",inputName:"document",onFiles:mt})]),Ds,t("div",Us,[r(V,{for:"company_name",value:"Sales Person"}),t("div",Gs,[r(U,{options:f.salesuser,modelValue:a(s).sales_user_id,"onUpdate:modelValue":l[7]||(l[7]=e=>a(s).sales_user_id=e),onOnchange:gt,class:p({"error rounded-md":a(s).errors.sales_user_id})},null,8,["options","modelValue","class"])])])])]),t("div",Ms,[t("div",js,[t("div",As,[r(V,{for:"Validity",value:"Validity"}),r(C,{id:"price",type:"text",modelValue:a(s).validity,"onUpdate:modelValue":l[8]||(l[8]=e=>a(s).validity=e),onChange:l[9]||(l[9]=e=>w(a(s).errors.validity)),class:p({"error rounded-md":a(s).errors.validity})},null,8,["modelValue","class"])]),t("div",Ns,[r(V,{for:"delivery",value:"Delivery"}),r(C,{id:"price",type:"text",modelValue:a(s).delivery,"onUpdate:modelValue":l[10]||(l[10]=e=>a(s).delivery=e),onChange:l[11]||(l[11]=e=>w(a(s).errors.delivery)),class:p({"error rounded-md":a(s).errors.delivery})},null,8,["modelValue","class"])]),t("div",qs,[r(V,{for:"warranty",value:"Warranty"}),r(C,{id:"price",type:"text",modelValue:a(s).warranty,"onUpdate:modelValue":l[12]||(l[12]=e=>a(s).warranty=e),onChange:l[13]||(l[13]=e=>w(a(s).errors.warranty)),class:p({"error rounded-md":a(s).errors.warranty})},null,8,["modelValue","class"])])])]),t("div",Os,[t("div",zs,[t("div",Bs,[r(V,{for:"payment_terms",value:"Payment terms"}),r(z,{id:"price",type:"text",rows:4,modelValue:a(s).payment_terms,"onUpdate:modelValue":l[14]||(l[14]=e=>a(s).payment_terms=e),onChange:l[15]||(l[15]=e=>w(a(s).errors.payment_terms)),class:p({"error rounded-md":a(s).errors.payment_terms})},null,8,["modelValue","class"])]),t("div",Es,[r(V,{for:"note",value:"Note"}),r(z,{id:"note",type:"text",rows:4,modelValue:a(s).note,"onUpdate:modelValue":l[16]||(l[16]=e=>a(s).note=e),autocomplete:"note",onChange:l[17]||(l[17]=e=>a(s).validate("note"))},null,8,["modelValue"]),a(s).invalid("note")?(u(),kt(Dt,{key:0,class:"",message:a(s).errors.note},null,8,["message"])):v("",!0)])])])])]),t("div",Ls,[t("div",Ws,[r(Pt,{href:o.route("orders.index")},{svg:x(()=>[Rs]),_:1},8,["href"]),r(st,{disabled:a(s).processing},{default:x(()=>[I("Submit")]),_:1},8,["disabled"])])])],40,Rt)]),r(E,{show:G.value,onClose:M},{default:x(()=>[t("div",Hs,[Ks,t("div",Qs,[r(B,{onClick:M},{default:x(()=>[I(" Cancel")]),_:1}),r(ot,{class:"ml-3",onClick:it},{default:x(()=>[I(" Delete ")]),_:1})])])]),_:1},8,["show"]),r(E,{show:j.value,onClose:A},{default:x(()=>[t("div",Zs,[Js,t("div",Xs,[r(B,{onClick:A},{default:x(()=>[I(" Cancel")]),_:1}),r(ot,{class:"ml-3",onClick:pt},{default:x(()=>[I(" Delete ")]),_:1})])])]),_:1},8,["show"]),r(E,{show:N.value,onClose:J,maxWidth:vt.value},{default:x(()=>[t("div",Ys,[r(Ut,{fileUrl:a(L)+Z.value},null,8,["fileUrl"]),t("div",to,[r(B,{onClick:J},{default:x(()=>[I(" Cancel")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},xo=jt(eo,[["__scopeId","data-v-b6c10f45"]]);export{xo as default};
