import{o as l,c as _,a as o,u as t,w as y,F as k,Z as V,b as s,t as T,d as w,n as m,e as c,f as i,k as S,v as $,g as C,T as B}from"./app-21e66fd5.js";import{_ as N,a as P}from"./AdminLayout-db62264f.js";import{_ as u}from"./InputError-01d93b90.js";import{_ as p}from"./InputLabel-4a50badc.js";import{P as U}from"./PrimaryButton-ed35dcb4.js";import{_ as f}from"./TextInput-625f6add.js";import{_ as g}from"./SearchableDropdown-06b090a4.js";import{u as A}from"./index-c670349c.js";import"./_plugin-vue_export-helper-c27b6911.js";const D={class:"h-screen animate-top"},F={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},j={class:"sm:flex sm:items-center"},E=s("div",{class:"sm:flex-auto"},[s("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create Transaction")],-1),M={class:"text-sm font-semibold text-gray-900"},O=["onSubmit"],z={class:"border-b border-gray-900/10 pb-12"},Z={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},q={class:"sm:col-span-3"},G={class:"relative mt-2"},H={class:"sm:col-span-3"},I={class:"sm:col-span-3"},J={class:"sm:col-span-3"},K={class:"relative mt-2"},L={class:"sm:col-span-6"},Q={class:"flex mt-6 items-center justify-between"},R={class:"ml-auto flex items-center justify-end gap-x-6"},W=s("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),X={key:0,class:"text-sm text-gray-600"},de={__name:"Add",props:["paymentType","accounttype","bank"],setup(d){const e=A("post","/banktransaction",{org_bank_id:d.bank.id,payment_type:"",account_type:"",date:"",note:"",amount:""}),v=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()}),b=(r,a)=>{e.payment_type=r,e.errors.payment_type=null},x=(r,a)=>{e.account_type=r,e.errors.account_type=null},h=r=>{e.errors[r]=null};return(r,a)=>(l(),_(k,null,[o(t(V),{title:"Transactions"}),o(N,null,{default:y(()=>[s("div",D,[s("div",F,[s("div",j,[E,s("p",M,T(d.bank.bank_name),1)]),s("form",{onSubmit:w(v,["prevent"]),class:""},[s("div",z,[s("div",Z,[s("div",q,[o(p,{for:"payment_type",value:"Payment Type"}),s("div",G,[o(g,{options:d.paymentType,modelValue:t(e).payment_type,"onUpdate:modelValue":a[0]||(a[0]=n=>t(e).payment_type=n),onOnchange:b,class:m({"error rounded-md":t(e).errors.payment_type})},null,8,["options","modelValue","class"]),t(e).invalid("payment_type")?(l(),c(u,{key:0,class:"",message:t(e).errors.payment_type},null,8,["message"])):i("",!0)])]),s("div",H,[o(p,{for:"date",value:"Payment Date"}),S(s("input",{"onUpdate:modelValue":a[1]||(a[1]=n=>t(e).date=n),class:m(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":t(e).errors["data.date"]}]),type:"date"},null,2),[[$,t(e).date]]),t(e).invalid("date")?(l(),c(u,{key:0,class:"",message:t(e).errors.date},null,8,["message"])):i("",!0)]),s("div",I,[o(p,{for:"amount",value:"Amount"}),o(f,{id:"amount",type:"text",onChange:a[2]||(a[2]=n=>h("data.amount")),modelValue:t(e).amount,"onUpdate:modelValue":a[3]||(a[3]=n=>t(e).amount=n),class:m({"error rounded-md":t(e).errors["data.amount"]})},null,8,["modelValue","class"]),t(e).invalid("amount")?(l(),c(u,{key:0,class:"",message:t(e).errors.amount},null,8,["message"])):i("",!0)]),s("div",J,[o(p,{for:"account_type",value:"Account Type"}),s("div",K,[o(g,{options:d.accounttype,modelValue:t(e).account_type,"onUpdate:modelValue":a[4]||(a[4]=n=>t(e).account_type=n),onOnchange:x,class:m({"error rounded-md":t(e).errors.account_type})},null,8,["options","modelValue","class"]),t(e).invalid("account_type")?(l(),c(u,{key:0,class:"",message:t(e).errors.account_type},null,8,["message"])):i("",!0)])]),s("div",L,[o(p,{for:"note",value:"Narration"}),o(f,{id:"note",type:"text",modelValue:t(e).note,"onUpdate:modelValue":a[5]||(a[5]=n=>t(e).note=n),class:m({"error rounded-md":t(e).errors["data.note"]})},null,8,["modelValue","class"]),t(e).invalid("note")?(l(),c(u,{key:0,class:"",message:t(e).errors.note},null,8,["message"])):i("",!0)])])]),s("div",Q,[s("div",R,[o(P,{href:r.route("banktransaction.show",{id:d.bank.id})},{svg:y(()=>[W]),_:1},8,["href"]),o(U,{disabled:t(e).processing},{default:y(()=>[C("Save")]),_:1},8,["disabled"]),o(B,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:y(()=>[t(e).recentlySuccessful?(l(),_("p",X,"Saved.")):i("",!0)]),_:1})])])],40,O)])])]),_:1})],64))}};export{de as default};
