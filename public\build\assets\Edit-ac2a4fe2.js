import{_ as o}from"./AdminLayout-aac65a75.js";import i from"./DeleteUserForm-394b2c00.js";import m from"./UpdatePasswordForm-8f28b7df.js";import r from"./UpdateProfileInformationForm-b2487de1.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-b320a640.js";import"./DangerButton-dc982a69.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-257b182b.js";import"./InputLabel-946d937b.js";import"./Modal-b2e3ff36.js";/* empty css                                                              */import"./SecondaryButton-12775633.js";import"./TextInput-cb7ba6f7.js";import"./PrimaryButton-cb5bb104.js";import"./TextArea-5264b61a.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
