import{_ as w,b as v}from"./AdminLayout-595ad5a7.js";import{h as b,r as f,o as l,c as i,a as o,u as k,w as a,F as h,Z as E,b as t,g as m,i as S,e as N,f as C,t as c,s as I,x as R}from"./app-97275a91.js";import{_ as B}from"./CreateButton-91ea7c7b.js";import{_ as T}from"./SecondaryButton-d0c53c3f.js";import{D as V}from"./DangerButton-36669f8b.js";import{M}from"./Modal-48c075e7.js";import{_ as U}from"./Pagination-5e2f223d.js";import{_ as $}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const d=e=>(I("data-v-b4532e7f"),e=e(),R(),e),A={class:"animate-top"},O={class:"sm:flex sm:items-center"},j=d(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Uploaded Service Reports")],-1)),z={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},D={class:"flex justify-end w-20"},H={class:"mt-6 flow-root"},P={class:"-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},F={class:"overflow-hidden inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8",style:{"min-height":"500px"}},L={class:"p-1 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},G={class:"min-w-full divide-y divide-gray-300"},Y=d(()=>t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"DATE"),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"REPORT NAME"),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"REPORT TYPE "),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"SERVICE ENGINEER"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1)),Z={key:0,class:"divide-y divide-gray-300 bg-white"},q={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},J={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900"},K={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},Q={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},W={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},X={class:"flex items-center justify-start gap-4"},tt=d(()=>t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)),et={key:1},st=d(()=>t("tr",{class:"bg-white"},[t("td",{colspan:"5",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),ot=[st],at={class:"p-6"},lt=d(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),it={class:"mt-6 flex justify-end"},ct={__name:"History",props:["data","customer"],setup(e){const x=b({}),_=f(!1),y=f(null),n=()=>{_.value=!1},g=()=>{x.delete(route("service-reports.destroy",{id:y.value}),{onSuccess:()=>n()})},u=r=>{const p=new Date(r),s={year:"numeric",month:"short",day:"numeric"};return p.toLocaleDateString("en-US",s)};return(r,p)=>(l(),i(h,null,[o(k(E),{title:"Uploaded Service Reports"}),o(w,null,{default:a(()=>[t("div",A,[t("div",O,[j,t("div",z,[t("div",D,[o(B,{href:r.route("service-reports.show",{id:e.data.data[0].customer_id})},{default:a(()=>[m(" Back ")]),_:1},8,["href"])])])]),t("div",H,[t("div",P,[t("div",F,[t("div",L,[t("table",G,[Y,e.data.data[0].report_detail&&e.data.data[0].report_detail.length>0?(l(),i("tbody",Z,[(l(!0),i(h,null,S(e.data.data[0].report_detail,(s,dt)=>(l(),i("tr",{key:s.id,class:""},[t("td",q,c(u(s.date)??"-"),1),t("td",J,c(s.document_name??"-"),1),t("td",K,c(s.type??"-"),1),t("td",Q,c(s.engineer.first_name??"-")+" "+c(s.engineer.last_name??"-"),1),t("td",W,[t("div",X,[o(v,{align:"right",width:"48"},{trigger:a(()=>[tt]),content:a(()=>[]),_:1})])])]))),128))])):(l(),i("tbody",et,ot))])]),e.data.data&&e.data.data.length>0?(l(),N(U,{key:0,class:"mt-6",links:e.data.links},null,8,["links"])):C("",!0)])])])]),o(M,{show:_.value,onClose:n},{default:a(()=>[t("div",at,[lt,t("div",it,[o(T,{onClick:n},{default:a(()=>[m(" Cancel ")]),_:1}),o(V,{class:"ml-3",onClick:g},{default:a(()=>[m(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}},gt=$(ct,[["__scopeId","data-v-b4532e7f"]]);export{gt as default};
