import{K as f,o as c,c as p,a as e,u as r,w as d,F as x,Z as g,b as a,g as b,d as V,T as y,f as h}from"./app-6a429cee.js";import{_ as q}from"./AdminLayout-dc64724f.js";import{_ as u}from"./InputError-17731bba.js";import{_ as m}from"./InputLabel-5e6ac969.js";import{P}from"./PrimaryButton-c589c744.js";import{_ as U}from"./CreateButton-1a5625b4.js";import{_ as i}from"./TextInput-94a28154.js";import{u as j}from"./index-beae658c.js";import"./_plugin-vue_export-helper-c27b6911.js";const w={class:"h-screen animate-top"},S={class:"sm:flex sm:items-center"},B=a("div",{class:"sm:flex-auto"},[a("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Manage Prefix")],-1),C={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},N={class:"flex justify-end w-20"},O={class:"mt-8 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},T=["onSubmit"],$={class:"border-b border-gray-900/10 pb-4"},k={class:"mt-5 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},M={class:"sm:col-span-2"},D={class:"sm:col-span-2"},F={class:"sm:col-span-2"},I={class:"sm:col-span-2"},R={class:"sm:col-span-2"},E={class:"sm:col-span-2"},J={class:"sm:col-span-2"},K={class:"sm:col-span-2"},Q={class:"sm:col-span-2"},Z={class:"sm:col-span-2"},z={class:"sm:col-span-2"},A={class:"sm:col-span-2"},G={class:"flex mt-6 items-center justify-between"},H={class:"ml-auto flex items-center justify-end gap-x-6"},L={key:0,class:"text-sm text-gray-600"},te={__name:"ManagePrefix",props:["data"],setup(W){const n=f().props.data,t=_=>{const l=n.find(s=>s.type===_);return l?l.value:null},o=j("post","/saveprefix",{po_number:n?t("po_number"):"",po_receive_number:n?t("po_receive_number"):"",quotation_number:n?t("quotation_number"):"",order_number:n?t("order_number"):"",order_deliver_number:n?t("order_deliver_number"):"",challan_number:n?t("challan_number"):"",invoice_number:n?t("invoice_number"):"",retail_invoice_number:n?t("retail_invoice_number"):"",demo_challan_number:n?t("demo_challan_number"):"",job_card_number:n?t("job_card_number"):"",pi_number:n?t("pi_number"):"",credit_note_no:n?t("credit_note_no"):""}),v=()=>o.submit({preserveScroll:!0,resetOnSuccess:!1});return(_,l)=>(c(),p(x,null,[e(r(g),{title:"Manage Prefix"}),e(q,null,{default:d(()=>[a("div",w,[a("div",S,[B,a("div",C,[a("div",N,[e(U,{href:_.route("setting")},{default:d(()=>[b(" Back ")]),_:1},8,["href"])])])]),a("div",O,[a("form",{onSubmit:V(v,["prevent"]),class:""},[a("div",$,[a("div",k,[a("div",M,[e(m,{for:"po_number",value:"PO Prefix"}),e(i,{id:"po_number",type:"text",modelValue:r(o).po_number,"onUpdate:modelValue":l[0]||(l[0]=s=>r(o).po_number=s),autocomplete:"po_number",required:""},null,8,["modelValue"]),e(u,{class:"",message:r(o).errors.value},null,8,["message"])]),a("div",D,[e(m,{for:"po_receive_number",value:"PO Receive Prefix"}),e(i,{id:"po_receive_number",type:"text",modelValue:r(o).po_receive_number,"onUpdate:modelValue":l[1]||(l[1]=s=>r(o).po_receive_number=s),autocomplete:"po_receive_number",required:""},null,8,["modelValue"]),e(u,{class:"",message:r(o).errors.value},null,8,["message"])]),a("div",F,[e(m,{for:"quotation_number",value:"Quotation Prefix"}),e(i,{id:"quotation_number",type:"text",modelValue:r(o).quotation_number,"onUpdate:modelValue":l[2]||(l[2]=s=>r(o).quotation_number=s),autocomplete:"quotation_number",required:""},null,8,["modelValue"]),e(u,{class:"",message:r(o).errors.value},null,8,["message"])]),a("div",I,[e(m,{for:"order_number",value:"Order Prefix"}),e(i,{id:"order_number",type:"text",modelValue:r(o).order_number,"onUpdate:modelValue":l[3]||(l[3]=s=>r(o).order_number=s),autocomplete:"order_number",required:""},null,8,["modelValue"]),e(u,{class:"",message:r(o).errors.value},null,8,["message"])]),a("div",R,[e(m,{for:"order_deliver_number",value:"Order Deliver Prefix"}),e(i,{id:"order_deliver_number",type:"text",modelValue:r(o).order_deliver_number,"onUpdate:modelValue":l[4]||(l[4]=s=>r(o).order_deliver_number=s),autocomplete:"order_deliver_number",required:""},null,8,["modelValue"]),e(u,{class:"",message:r(o).errors.value},null,8,["message"])]),a("div",E,[e(m,{for:"challan_number",value:"Challan Prefix"}),e(i,{id:"challan_number",type:"text",modelValue:r(o).challan_number,"onUpdate:modelValue":l[5]||(l[5]=s=>r(o).challan_number=s),autocomplete:"challan_number",required:""},null,8,["modelValue"]),e(u,{class:"",message:r(o).errors.value},null,8,["message"])]),a("div",J,[e(m,{for:"invoice_number",value:"Tax Invoice Prefix"}),e(i,{id:"invoice_number",type:"text",modelValue:r(o).invoice_number,"onUpdate:modelValue":l[6]||(l[6]=s=>r(o).invoice_number=s),autocomplete:"invoice_number",required:""},null,8,["modelValue"]),e(u,{class:"",message:r(o).errors.value},null,8,["message"])]),a("div",K,[e(m,{for:"retail_invoice_number",value:"Retail Invoice Prefix"}),e(i,{id:"retail_invoice_number",type:"text",modelValue:r(o).retail_invoice_number,"onUpdate:modelValue":l[7]||(l[7]=s=>r(o).retail_invoice_number=s),autocomplete:"retail_invoice_number",required:""},null,8,["modelValue"]),e(u,{class:"",message:r(o).errors.value},null,8,["message"])]),a("div",Q,[e(m,{for:"demo_challan_number",value:"Demo Challan Prefix"}),e(i,{id:"demo_challan_number",type:"text",modelValue:r(o).demo_challan_number,"onUpdate:modelValue":l[8]||(l[8]=s=>r(o).demo_challan_number=s),autocomplete:"demo_challan_number",required:""},null,8,["modelValue"]),e(u,{class:"",message:r(o).errors.value},null,8,["message"])]),a("div",Z,[e(m,{for:"job_card_number",value:"Job Card Number"}),e(i,{id:"job_card_number",type:"text",modelValue:r(o).job_card_number,"onUpdate:modelValue":l[9]||(l[9]=s=>r(o).job_card_number=s),autocomplete:"job_card_number",required:""},null,8,["modelValue"]),e(u,{class:"",message:r(o).errors.value},null,8,["message"])]),a("div",z,[e(m,{for:"pi_number",value:"PI Prefix"}),e(i,{id:"pi_number",type:"text",modelValue:r(o).pi_number,"onUpdate:modelValue":l[10]||(l[10]=s=>r(o).pi_number=s),autocomplete:"pi_number",required:""},null,8,["modelValue"]),e(u,{class:"",message:r(o).errors.value},null,8,["message"])]),a("div",A,[e(m,{for:"credit_note_no",value:"Credit Note Prefix"}),e(i,{id:"credit_note_no",type:"text",modelValue:r(o).credit_note_no,"onUpdate:modelValue":l[11]||(l[11]=s=>r(o).credit_note_no=s),autocomplete:"credit_note_no",required:""},null,8,["modelValue"]),e(u,{class:"",message:r(o).errors.value},null,8,["message"])])])]),a("div",G,[a("div",H,[e(P,{disabled:r(o).processing},{default:d(()=>[b("Save")]),_:1},8,["disabled"]),e(y,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:d(()=>[r(o).recentlySuccessful?(c(),p("p",L,"Saved.")):h("",!0)]),_:1})])])],40,T)])])]),_:1})],64))}};export{te as default};
