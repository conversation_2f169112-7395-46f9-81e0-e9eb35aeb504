import{K as nt,r as h,C as $t,j as M,l as Ut,o as p,c as x,a as c,u as i,w,F as L,Z as Dt,b as t,t as _,k as Gt,v as Nt,d as jt,n as b,f as v,i as it,g as U,e as Mt,s as At,x as Ot,q as Et}from"./app-4c3f0163.js";import{_ as qt,a as Bt}from"./AdminLayout-36b0d46a.js";import{_ as ct}from"./InputError-64c2d172.js";import{_ as I}from"./InputLabel-d6414ecf.js";import{P as dt}from"./PrimaryButton-353715d1.js";import{_ as f}from"./TextInput-e8957d69.js";import{_ as zt}from"./TextArea-259ebf66.js";import{_ as G}from"./SearchableDropdown-84ab9b26.js";import{D as rt}from"./DangerButton-b3c50a37.js";import{_ as R}from"./SecondaryButton-d521cdbf.js";import{M as H}from"./Modal-61735c0a.js";import{_ as Lt}from"./FileViewer-e6911454.js";import{_ as Rt}from"./MultipleFileUpload-e24f2a14.js";import{u as Ht}from"./index-b332ae81.js";import{_ as Qt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const d=S=>(At("data-v-92b79fda"),S=S(),Ot(),S),Wt={class:"animate-top"},Kt={class:"sm:flex sm:items-center"},Xt=d(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Invoice")],-1)),Yt={class:"w-auto"},Zt={class:"flex space-x-2 items-center"},Jt={class:"text-sm font-semibold text-gray-900"},te={class:"flex space-x-2 items-center"},ee=["onSubmit"],se={class:"mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},oe={class:"inline-flex items-start space-x-6 justify-start w-full"},le={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},ae={class:"inline-flex items-center justify-start w-full space-x-6"},ne=d(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Customer:",-1)),ie={class:"relative w-full"},ce={class:"inline-flex items-center justify-start w-full space-x-2"},de=d(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"GST No:",-1)),re={class:"text-sm leading-6 text-gray-700"},ue={class:"inline-flex items-center justify-start w-full space-x-2"},_e=d(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Email:",-1)),me={class:"text-sm leading-6 text-gray-700"},pe={class:"inline-flex items-center justify-start w-full space-x-2"},xe=d(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1)),fe={class:"text-sm leading-6 text-gray-700"},ge={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},ve={class:"inline-flex items-center justify-start w-full space-x-2"},he=d(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Invoice Number:",-1)),ye={class:"text-sm leading-6 text-gray-700"},we={class:"inline-flex items-center justify-start w-full space-x-2"},be=d(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Category:",-1)),Ve={class:"relative mt-2"},Ce={class:"inline-flex items-center justify-start w-full space-x-2"},Ie=d(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Type:",-1)),Fe={class:"text-sm leading-6 text-gray-700"},Se={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border w-full"},Pe={class:"overflow-x-auto w-full"},Te={class:"overflow-x-auto divide-y divide-gray-300",style:{"margin-bottom":"160px"}},ke=d(()=>t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Product Name",-1)),$e=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Batch",-1)),Ue=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"HSN",-1)),De=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"EXP",-1)),Ge=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),Ne=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),je=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),Me=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),Ae=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Sell Price (₹)",-1)),Oe=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),Ee={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},qe={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Be={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ze={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Le={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Re=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),He=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),Qe=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),We={class:"divide-y divide-gray-300 bg-white"},Ke={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-96"},Xe={class:"relative mt-2"},Ye={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-60"},Ze={class:"relative mt-2"},Je={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ts={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},es={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},ss={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},os={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ls={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},as={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},ns={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},is={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},cs={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ds={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},rs={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},us={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},_s={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ms={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 flex space-x-2 min-w-48"},ps={class:"px-3 py-3 text-sm text-gray-900"},xs=["onClick"],fs=d(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),gs=[fs],vs={class:"flex items-center justify-between"},hs={key:0,class:""},ys=d(()=>t("span",{class:"text-lg font-semibold text-gray-900 leading-6"},"PREVIOUS INVOICE",-1)),ws=[ys],bs={class:"ml-auto flex items-center justify-end gap-x-6"},Vs={key:0,class:"flex justify-between"},Cs={class:"flex space-x-2 items-center"},Is=d(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"PRODUCT:",-1)),Fs={class:"text-sm font-semibold text-gray-700 leading-6"},Ss={class:"flex space-x-2 items-center"},Ps=d(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"HSN Code:",-1)),Ts={class:"text-sm font-semibold text-gray-700 leading-6"},ks={class:"flex space-x-2 items-center"},$s=d(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"QTY:",-1)),Us={class:"text-sm font-semibold text-gray-700 leading-6"},Ds={class:"flex space-x-2 items-center"},Gs=d(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"PRICE (₹):",-1)),Ns={class:"text-sm font-semibold text-gray-700 leading-6"},js={class:"flex space-x-2 items-center"},Ms=d(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"GST (%):",-1)),As={class:"text-sm font-semibold text-gray-700 leading-6"},Os={class:"flex space-x-2 items-center"},Es=d(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"TOTAL AMOUNT (₹):",-1)),qs={class:"text-sm font-semibold text-gray-700 leading-6"},Bs=d(()=>t("div",null,null,-1)),zs={key:0,class:"mt-6 bg-white p-1 shadow sm:rounded-lg border"},Ls={class:"min-w-full divide-y divide-gray-300"},Rs=d(()=>t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"UPLOADED DOCUMENT"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1)),Hs={class:"divide-y divide-gray-300 bg-white"},Qs={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},Ws={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},Ks=["onClick"],Xs=d(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Ys=[Xs],Zs=["onClick"],Js=d(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),to=[Js],eo=["onClick"],so=d(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),oo=[so],lo={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},ao={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},no={class:"sm:col-span-3 space-y-4"},io={class:"flex space-x-4"},co={class:"w-full"},ro={class:"w-full"},uo={class:"relative mt-2"},_o={class:"flex space-x-4"},mo={class:"w-full"},po={class:"w-full"},xo={class:"w-full"},fo={class:"flex space-x-4"},go={class:"w-full"},vo={class:"w-full"},ho={class:"w-full"},yo={class:"flex space-x-4"},wo={class:"w-full"},bo={class:"w-full"},Vo={class:"sm:col-span-3"},Co={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Io={class:"inline-flex items-center justify-end w-full space-x-3"},Fo=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),So={class:"text-base font-semibold text-gray-900 w-w-32"},Po={class:"inline-flex items-center justify-end w-full space-x-3"},To=d(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Discount On Sub Total(₹):",-1)),ko={class:"w-40"},$o={class:"inline-flex items-center justify-end w-full space-x-3"},Uo=d(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),Do={class:"w-40"},Go={class:"inline-flex items-center justify-end w-full space-x-3"},No=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),jo={class:"text-base font-semibold text-gray-900 w-w-32"},Mo={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},Ao=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),Oo={class:"text-base font-semibold text-gray-900 w-w-32"},Eo={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},qo=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),Bo={class:"text-base font-semibold text-gray-900 w-w-32"},zo={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},Lo=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),Ro={class:"text-base font-semibold text-gray-900 w-w-32"},Ho={class:"inline-flex items-center justify-end w-full space-x-3"},Qo=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),Wo={class:"text-base font-semibold text-gray-900 w-w-32"},Ko={class:"flex mt-6 items-center justify-between"},Xo={class:"ml-auto flex items-center justify-end gap-x-6"},Yo=d(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),Zo={class:"p-6"},Jo=d(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to Remove this product? ",-1)),tl={class:"mt-6 flex justify-end"},el={class:"p-6"},sl=d(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this document? ",-1)),ol={class:"mt-6 flex justify-end"},ll={class:"p-6"},al={class:"mt-6 px-4 flex justify-end"},nl={__name:"Edit",props:["customers","serialno","filepath","category","products","salesuser","invoice_details"],setup(S){const T=S,Q=nt().props.filepath.view,r=nt().props.data[0],W=h([]),ut=T.products.filter(s=>s.serial_numbers.some(e=>e.organization_id===r.organization_id));W.value=ut;const K=h([]),_t=T.serialno;K.value=_t;const u=h([{invoice_detail_id:"",editmode:"",serial_number_id:"",product_id:"",product_name:"",item_code:"",hsn_code:"",price:"",sell_price:"",discount:"",discount_amount:"",discount_before_tax_product:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",qty:"",expiry_date:"",mrp:"",description:""}]),mt=async(s,l,e)=>{o.errors[`selectedProductItem.${e}.product_id`]=null;const n=T.serialno.filter(a=>a.product_id===s&&a.organization_id===o.organization_id);K.value=n,u.value[e].product_id=s;try{const a=await Et.post("/api/invoices/previous",{customer_id:o.customer_id,organization_id:o.organization_id,product_id:s});a.data.success?V.value=a.data.data:V.value={product_name:"",hsn_code:"",qty:"",price:"",gst:"",total_amount:""}}catch(a){console.error("Error fetching previous invoice:",a),V.value={product_name:"",hsn_code:"",qty:"",price:"",gst:"",total_amount:""}}},V=h({product_name:"",hsn_code:"",qty:"",price:"",gst:"",total_amount:""});$t(()=>{u.value=r.invoice_detail.map(s=>({invoice_detail_id:s.id,editmode:"editMode",serial_number_id:s.serial_number_id,product_name:s.serialnumbers.product.name,hsn_code:s.serialnumbers.product.hsn_code,item_code:s.serialnumbers.product.item_code,discount:parseFloat(s.discount).toFixed(2),price:parseFloat(s.price).toFixed(2),total_price:parseFloat(s.total_price).toFixed(2),gst:parseFloat(s.gst).toFixed(2),sgst:parseFloat(s.gst/2).toFixed(2),gst_amount:parseFloat(s.gst_amount).toFixed(2),total_gst_amount:parseFloat(s.total_gst_amount).toFixed(2),total_amount:parseFloat(s.total_amount).toFixed(2),product_id:s.serialnumbers.product.id,qty:s.qty,expiry_date:s.serialnumbers.expiry_date,mrp:s.serialnumbers.mrp?parseFloat(s.serialnumbers.mrp).toFixed(2):"-",description:s.description,sell_price:parseFloat(s.price).toFixed(2),discount_amount:parseFloat(s.discount_amount).toFixed(2)??"0"}))});const g=h(r.customers.gst_type),pt=(s,l)=>{o.customer_id=s,o.errors.customer_id=null;const e=T.customers.find(n=>n.id===s);e&&(g.value=e.gst_type)},xt=()=>{u.value.push({invoice_detail_id:"",serial_number_id:"",product_name:"",hsn_code:""})},ft=(s,l,e)=>T.serialno.filter(a=>a.product_id===s&&a.organization_id===r.organization_id),gt=(s,l,e)=>{const n=T.serialno.find(a=>a.id===s);n&&(u.value[e].qty="",u.value[e].serial_number_id=n.id,u.value[e].product_name=n.product.name,u.value[e].item_code=n.product.item_code,u.value[e].expiry_date=n.expiry_date,u.value[e].mrp=n.mrp?parseFloat(n.mrp).toFixed(2):"-",u.value[e].price=parseFloat(n.purchase_price).toFixed(2),u.value[e].hsn_code=n.product.hsn_code,u.value[e].discount="0.00",u.value[e].sell_price="",u.value[e].total_price=parseFloat(n.purchase_price).toFixed(2),u.value[e].gst=parseFloat(n.product.gst).toFixed(2),u.value[e].sgst=parseFloat(n.product.gst/2).toFixed(2),u.value[e].gst_amount="",u.value[e].total_gst_amount="",u.value[e].total_amount="",u.value[e].description="",o.errors[`selectedProductItem.${e}.serial_number_id`]=null)},A=h(!1),X=h(null),vt=h(null),O=()=>{A.value=!1},ht=()=>{o.get(route("removeproduct",{id:X.value,model:"InvoiceDetail"}),{onSuccess:()=>{O(),u.value.splice(index,1)}})},yt=(s,l)=>{l!==void 0&&l!=""?(X.value=l,vt.value=s,A.value=!0):u.value.splice(s,1)},k=s=>{o.errors[s]=null},wt=s=>{o.document=s},E=h(!1),Y=h(null),bt=s=>{Y.value=s,E.value=!0},Vt=()=>{o.get(route("removedocument",{id:Y.value,name:"Invoice"}),{onSuccess:()=>{q()}})},q=()=>{E.value=!1},B=h(!1),Z=h(null),Ct=h("custom"),It=s=>{Z.value=s,B.value=!0},J=()=>{B.value=!1},Ft=s=>{const l=window.location.origin+Q+s,e=document.createElement("a");e.href=l,e.setAttribute("download",s),document.body.appendChild(e),e.click(),document.body.removeChild(e)},F=s=>{if(s==null||isNaN(s))return"0.00";let l=Number(s).toFixed(2),[e,n]=l.split("."),a=e.substring(e.length-3),m=e.substring(0,e.length-3);return m!==""&&(a=","+a),`${m.replace(/\B(?=(\d{2})+(?!\d))/g,",")+a}.${n}`},St=(s,l)=>{const e=parseFloat(s.sell_price),n=parseFloat(s.discount_before_tax_product)||0,a=parseFloat(s.discount)||0,m=g.value=="IGST"?s.gst:parseFloat(s.sgst*2),C=parseFloat(s.qty);let y=0,j=0;a>0||n>0?y=e*C:y=e*C*(1+m/100);const D=y*(a/100)||0,lt=e*1*(m/100),z=(e*C-D-n)*(m/100);a>0||n>0?j=y-D-n+z:j=y-D;const at=e*C;return s.total_price=isNaN(at)?"":parseFloat(at).toFixed(2),s.gst_amount=isNaN(lt)?"":parseFloat(lt).toFixed(2),s.total_gst_amount=isNaN(z)?"":parseFloat(z).toFixed(2),s.discount_amount=isNaN(D)?"":parseFloat(D).toFixed(2),s.gst=m,isNaN(j)?"":parseFloat(j).toFixed(2)},P=(s,l)=>{ot(),s.total_amount=St(s)},tt=M(()=>{const s=Math.round(u.value.reduce((e,n)=>e+(n.total_amount?parseFloat(n.total_amount):0),0)),l=o.overall_discount?parseFloat(o.overall_discount):0;return s-l}),$=M(()=>u.value.reduce((s,l)=>s+(l.total_gst_amount?parseFloat(l.total_gst_amount):0),0)),et=M(()=>u.value.reduce((s,l)=>s+(l.total_price?parseFloat(l.total_price):0),0)),st=M(()=>{const s=u.value.reduce((n,a)=>n+(a.discount_amount?parseFloat(a.discount_amount):0),0),l=o.overall_discount?parseFloat(o.overall_discount):0,e=o.discount_before_tax?parseFloat(o.discount_before_tax):0;return s+l+e}),o=Ht("post","/invoice",{organization_id:r.organization.id,date:r.date,invoice_id:r.id,category:r.category,note:r.note,sales_user_id:r.sales_user_id,selectedProductItem:[],customer_id:r.customer_id,invoice_no:r.invoice_no,document:r.documents,cgst:r.cgst,sgst:r.sgst,igst:r.igst,total_gst:r.total_gst,sub_total:r.sub_total,total_amount:r.total_amount,total_discount:r.total_discount,overall_discount:r.overall_discount,discount_before_tax:r.discount_before_tax,dispatch:r.dispatch,transport:r.transport,patient_name:r.patient_name,customer_po_date:r.customer_po_date,customer_po_number:r.customer_po_number,eway_bill:r.eway_bill,due_days:r.due_days,cr_dr_note:r.cr_dr_note}),Pt=(s,l)=>{o.sales_user_id=s},Tt=()=>{o.sub_total=et.value,o.invoice_no=T.invoice_no,o.cgst=g.value=="CGST/SGST"?$.value/2:"0",o.sgst=g.value=="CGST/SGST"?$.value/2:"0",o.igst=g.value=="IGST"?$.value:"0",o.total_gst=$.value,o.total_amount=tt.value,o.total_discount=st.value,o.selectedProductItem=u.value,o.submit({preserveScroll:!0,onSuccess:()=>o.reset()})},kt=(s,l)=>{o.category=s,o.errors.category=null},N=(s,l)=>{const e=l.length,n=l.reduce((C,y)=>C+(y.total_price?parseFloat(y.total_price):0),0),a=u.value.reduce((C,y)=>C+(y.total_price?parseFloat(y.total_price):0),0),m=s*n/a/e;l.forEach(C=>{C.discount_before_tax_product=m})},ot=()=>{const s=parseFloat(o.discount_before_tax)||0,l=u.value.filter(m=>m.gst==5&&m.total_price>0),e=u.value.filter(m=>m.gst==12&&m.total_price>0),n=u.value.filter(m=>m.gst==18&&m.total_price>0),a=u.value.filter(m=>m.gst==28&&m.total_price>0);N(s,l),N(s,e),N(s,n),N(s,a)};return Ut(()=>o.discount_before_tax,s=>{ot(),u.value.forEach(l=>{P(l)})}),(s,l)=>(p(),x(L,null,[c(i(Dt),{title:"Invoice"}),c(qt,null,{default:w(()=>[t("div",Wt,[t("div",Kt,[Xt,t("div",Yt,[t("div",Zt,[t("p",Jt,_(i(r).organization.name),1),t("div",te,[Gt(t("input",{class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":l[0]||(l[0]=e=>i(o).date=e),onChange:l[1]||(l[1]=e=>i(o).validate("date"))},null,544),[[Nt,i(o).date]])])])])]),t("form",{onSubmit:jt(Tt,["prevent"]),class:""},[t("div",se,[t("div",oe,[t("div",le,[t("div",ae,[ne,t("div",ie,[c(G,{options:S.customers,modelValue:i(o).customer_id,"onUpdate:modelValue":l[2]||(l[2]=e=>i(o).customer_id=e),onOnchange:pt,class:b({"error rounded-md":i(o).errors.customer_id})},null,8,["options","modelValue","class"])])]),t("div",ce,[de,t("p",re,_(i(r).customers.gst_no??"-"),1)]),t("div",ue,[_e,t("p",me,_(i(r).customers.email??"-"),1)]),t("div",pe,[xe,t("p",fe,_(i(r).customers.contact_no??"-"),1)])]),t("div",ge,[t("div",ve,[he,t("span",ye,_(i(r).invoice_no),1)]),t("div",we,[be,t("div",Ve,[c(G,{options:S.category,modelValue:i(o).category,"onUpdate:modelValue":l[3]||(l[3]=e=>i(o).category=e),onOnchange:kt,class:b({"error rounded-md":i(o).errors.category})},null,8,["options","modelValue","class"])])]),t("div",Ce,[Ie,t("p",Fe,_(i(r).invoice_type??"-"),1)])])])]),t("div",Se,[t("div",Pe,[t("table",Te,[t("thead",null,[t("tr",null,[ke,$e,Ue,De,Ge,Ne,je,Me,Ae,Oe,g.value=="IGST"?(p(),x("th",Ee,"IGST (%)")):v("",!0),g.value=="IGST"?(p(),x("th",qe,"IGST (₹)")):v("",!0),g.value=="CGST/SGST"?(p(),x("th",Be,"CGST (%)")):v("",!0),g.value=="CGST/SGST"?(p(),x("th",ze,"SGST (%)")):v("",!0),g.value=="CGST/SGST"?(p(),x("th",Le,"Total GST (₹)")):v("",!0),Re,He,Qe])]),t("tbody",We,[(p(!0),x(L,null,it(u.value,(e,n)=>(p(),x("tr",{key:n},[t("td",Ke,[t("div",Xe,[c(G,{options:W.value,modelValue:e.product_id,"onUpdate:modelValue":a=>e.product_id=a,onOnchange:(a,m)=>mt(a,m,n),onChange:l[4]||(l[4]=a=>i(o).validate("product_id")),class:b({"error rounded-md":i(o).errors[`selectedProductItem.${n}.product_id`]}),editMode:e.editmode},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class","editMode"])])]),t("td",Ye,[t("div",Ze,[c(G,{options:ft(e.product_id),modelValue:e.serial_number_id,"onUpdate:modelValue":a=>e.serial_number_id=a,onOnchange:(a,m)=>gt(a,m,n),onChange:l[5]||(l[5]=a=>i(o).validate("serial_number_id")),class:b({"error rounded-md":i(o).errors[`selectedProductItem.${n}.serial_number_id`]}),editMode:e.editmode},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class","editMode"])])]),t("td",Je,_(e.hsn_code??"-"),1),t("td",ts,_(e.expiry_date??"-"),1),t("td",es,[c(f,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":a=>e.description=a,onInput:a=>P(e,n),onChange:a=>k("selectedProductItem."+n+".description"),class:b({error:i(o).errors[`selectedProductItem.${n}.description`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",ss,_(e.mrp??"-"),1),t("td",os,_(e.price),1),t("td",ls,[c(f,{id:"qty",type:"text",modelValue:e.qty,"onUpdate:modelValue":a=>e.qty=a,onInput:a=>P(e,n),onChange:a=>k("selectedProductItem."+n+".qty"),class:b({error:i(o).errors[`selectedProductItem.${n}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"]),c(ct,{message:i(o).errors[`selectedProductItem.${n}.qty`]},null,8,["message"])]),t("td",as,[c(f,{id:"sell_price",type:"text",modelValue:e.sell_price,"onUpdate:modelValue":a=>e.sell_price=a,onInput:a=>P(e,n),onChange:a=>k("selectedProductItem."+n+".sell_price"),class:b({error:i(o).errors[`selectedProductItem.${n}.sell_price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",ns,_(e.total_price),1),g.value=="IGST"?(p(),x("td",is,[c(f,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":a=>e.gst=a,onInput:a=>P(e,n),onChange:a=>k("selectedProductItem."+n+".gst"),class:b({error:i(o).errors[`selectedProductItem.${n}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):v("",!0),g.value=="CGST/SGST"?(p(),x("td",cs,[c(f,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":a=>e.sgst=a,onInput:a=>P(e,n),onChange:a=>k("selectedProductItem."+n+".gst"),class:b({error:i(o).errors[`selectedProductItem.${n}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):v("",!0),g.value=="CGST/SGST"?(p(),x("td",ds,[c(f,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":a=>e.sgst=a,onInput:a=>P(e,n),onChange:a=>k("selectedProductItem."+n+".gst"),class:b({error:i(o).errors[`selectedProductItem.${n}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):v("",!0),t("td",rs,_(e.total_gst_amount),1),t("td",us,[c(f,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":a=>e.discount=a,onInput:a=>P(e,n),onChange:a=>k("selectedProductItem."+n+".discount"),class:b({error:i(o).errors[`selectedProductItem.${n}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",_s,_(e.discount_amount),1),t("td",ms,[t("div",ps,_(e.total_amount),1),t("button",{type:"button",class:"mt-1",onClick:a=>yt(n,e.invoice_detail_id)},gs,8,xs)])]))),128))])])]),t("div",vs,[V.value.product_name?(p(),x("div",hs,ws)):v("",!0),t("div",bs,[c(dt,{onClick:xt,type:"button"},{default:w(()=>[U("Add Product")]),_:1})])]),V.value.product_name?(p(),x("div",Vs,[t("div",null,[t("div",Cs,[Is,t("span",Fs,_(V.value.product_name),1)]),t("div",Ss,[Ps,t("span",Ts,_(V.value.hsn_code),1)]),t("div",ks,[$s,t("span",Us,_(V.value.qty),1)])]),t("div",null,[t("div",Ds,[Gs,t("span",Ns,_(F(V.value.price)),1)]),t("div",js,[Ms,t("span",As,_(F(V.value.gst)),1)]),t("div",Os,[Es,t("span",qs,_(F(V.value.total_amount)),1)])]),Bs])):v("",!0)]),i(r).documents&&i(r).documents.length>0?(p(),x("div",zs,[t("table",Ls,[Rs,t("tbody",Hs,[(p(!0),x(L,null,it(i(r).documents,(e,n)=>(p(),x("tr",{key:i(r).id,class:""},[t("td",Qs,_(e.orignal_name),1),t("td",Ws,[t("button",{type:"button",onClick:a=>bt(e.id)},Ys,8,Ks),t("button",{type:"button",onClick:a=>It(e.name)},to,8,Zs),t("button",{type:"button",onClick:a=>Ft(e.name)},oo,8,eo)])]))),128))])])])):v("",!0),t("div",lo,[t("div",ao,[t("div",no,[t("div",io,[t("div",co,[c(I,{for:"note",value:"Upload Documents"}),c(Rt,{inputId:"document",inputName:"document",uploadedFiles:i(o).document,onFiles:wt},null,8,["uploadedFiles"])]),t("div",ro,[c(I,{for:"sales_user_id",value:"Sales Person"}),t("div",uo,[c(G,{options:S.salesuser,modelValue:i(o).sales_user_id,"onUpdate:modelValue":l[6]||(l[6]=e=>i(o).sales_user_id=e),onOnchange:Pt,class:b({"error rounded-md":i(o).errors.sales_user_id})},null,8,["options","modelValue","class"])])])]),t("div",_o,[t("div",mo,[c(I,{for:"company_name",value:"Transport"}),c(f,{id:"gst",type:"text",modelValue:i(o).dispatch,"onUpdate:modelValue":l[7]||(l[7]=e=>i(o).dispatch=e)},null,8,["modelValue"])]),t("div",po,[c(I,{for:"company_name",value:"Dispatch"}),c(f,{id:"transport",type:"text",modelValue:i(o).transport,"onUpdate:modelValue":l[8]||(l[8]=e=>i(o).transport=e)},null,8,["modelValue"])]),t("div",xo,[c(I,{for:"eway_bill",value:"Eway Bill"}),c(f,{id:"eway_bill",type:"text",modelValue:i(o).eway_bill,"onUpdate:modelValue":l[9]||(l[9]=e=>i(o).eway_bill=e)},null,8,["modelValue"])])]),t("div",fo,[t("div",go,[c(I,{for:"company_name",value:"PO Number"}),c(f,{id:"gst",type:"text",modelValue:i(o).customer_po_number,"onUpdate:modelValue":l[10]||(l[10]=e=>i(o).customer_po_number=e)},null,8,["modelValue"])]),t("div",vo,[c(I,{for:"company_name",value:"PO Date"}),c(f,{id:"customer_po_date",type:"date",modelValue:i(o).customer_po_date,"onUpdate:modelValue":l[11]||(l[11]=e=>i(o).customer_po_date=e)},null,8,["modelValue"])]),t("div",ho,[c(I,{for:"due_days",value:"Due Days"}),c(f,{id:"due_days",type:"text",modelValue:i(o).due_days,"onUpdate:modelValue":l[12]||(l[12]=e=>i(o).due_days=e)},null,8,["modelValue"])])]),t("div",yo,[t("div",wo,[c(I,{for:"patient_name",value:"Patient Name"}),c(f,{id:"patient_name",type:"text",modelValue:i(o).patient_name,"onUpdate:modelValue":l[13]||(l[13]=e=>i(o).patient_name=e)},null,8,["modelValue"])]),t("div",bo,[c(I,{for:"cr_dr_note",value:"CR DR Note"}),c(f,{id:"cr_dr_note",type:"text",modelValue:i(o).cr_dr_note,"onUpdate:modelValue":l[14]||(l[14]=e=>i(o).cr_dr_note=e)},null,8,["modelValue"])])]),t("div",null,[c(I,{for:"note",value:"Note"}),c(zt,{id:"note",type:"text",modelValue:i(o).note,"onUpdate:modelValue":l[15]||(l[15]=e=>i(o).note=e),onChange:l[16]||(l[16]=e=>i(o).validate("note"))},null,8,["modelValue"]),i(o).invalid("note")?(p(),Mt(ct,{key:0,class:"",message:i(o).errors.note},null,8,["message"])):v("",!0)])]),t("div",Vo,[t("div",Co,[t("div",Io,[Fo,t("p",So,_(F(et.value)),1)]),t("div",Po,[To,t("div",ko,[c(f,{id:"discount_before_tax",type:"text",modelValue:i(o).discount_before_tax,"onUpdate:modelValue":l[17]||(l[17]=e=>i(o).discount_before_tax=e)},null,8,["modelValue"])])]),t("div",$o,[Uo,t("div",Do,[c(f,{id:"overall_discount",type:"text",modelValue:i(o).overall_discount,"onUpdate:modelValue":l[18]||(l[18]=e=>i(o).overall_discount=e)},null,8,["modelValue"])])]),t("div",Go,[No,t("p",jo,_(F(st.value)),1)]),g.value=="IGST"?(p(),x("div",Mo,[Ao,t("p",Oo,_(F($.value)),1)])):v("",!0),g.value=="CGST/SGST"?(p(),x("div",Eo,[qo,t("p",Bo,_(F($.value/2)),1)])):v("",!0),g.value=="CGST/SGST"?(p(),x("div",zo,[Lo,t("p",Ro,_(F($.value/2)),1)])):v("",!0),t("div",Ho,[Qo,t("p",Wo,_(F(tt.value)),1)])])])])]),t("div",Ko,[t("div",Xo,[c(Bt,{href:s.route("invoice.index")},{svg:w(()=>[Yo]),_:1},8,["href"]),c(dt,{disabled:i(o).processing},{default:w(()=>[U("Submit")]),_:1},8,["disabled"])])])],40,ee)]),c(H,{show:A.value,onClose:O},{default:w(()=>[t("div",Zo,[Jo,t("div",tl,[c(R,{onClick:O},{default:w(()=>[U(" Cancel ")]),_:1}),c(rt,{class:"ml-3",onClick:ht},{default:w(()=>[U(" Delete ")]),_:1})])])]),_:1},8,["show"]),c(H,{show:E.value,onClose:q},{default:w(()=>[t("div",el,[sl,t("div",ol,[c(R,{onClick:q},{default:w(()=>[U(" Cancel ")]),_:1}),c(rt,{class:"ml-3",onClick:Vt},{default:w(()=>[U(" Delete ")]),_:1})])])]),_:1},8,["show"]),c(H,{show:B.value,onClose:J,maxWidth:Ct.value},{default:w(()=>[t("div",ll,[c(Lt,{fileUrl:i(Q)+Z.value},null,8,["fileUrl"]),t("div",al,[c(R,{onClick:J},{default:w(()=>[U(" Cancel ")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},Vl=Qt(nl,[["__scopeId","data-v-92b79fda"]]);export{Vl as default};
