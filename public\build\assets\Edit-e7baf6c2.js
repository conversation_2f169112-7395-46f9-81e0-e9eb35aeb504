import{_ as o}from"./AdminLayout-d9d2bc31.js";import i from"./DeleteUserForm-1dfe30d6.js";import m from"./UpdatePasswordForm-9d659f53.js";import r from"./UpdateProfileInformationForm-5b3903b0.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-4f4c883b.js";import"./DangerButton-b7cb11b9.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-41a68047.js";import"./InputLabel-468796e0.js";import"./Modal-85d770f4.js";/* empty css                                                              */import"./SecondaryButton-69637431.js";import"./TextInput-21f4f57b.js";import"./PrimaryButton-3e579b0b.js";import"./TextArea-b7098398.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
