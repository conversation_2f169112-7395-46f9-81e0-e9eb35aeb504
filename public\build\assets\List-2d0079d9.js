import{_ as F,b as Y,a as K}from"./AdminLayout-e15be38d.js";import{_ as q}from"./CreateButton-cebe4e7b.js";import{_ as H}from"./SecondaryButton-1012464f.js";import{D as Z}from"./DangerButton-9b74ae84.js";import{M as G}from"./Modal-754de2c3.js";import{_ as J}from"./Pagination-ffdaf57f.js";import{_ as Q}from"./SearchableDropdownNew-7be1679d.js";import{_ as R}from"./SimpleDropdown-6f25da81.js";import{_ as w}from"./InputLabel-d69efee6.js";import{r as p,l as W,o as s,c as i,a,u as f,w as c,F as g,Z as X,b as t,g as y,i as k,e as C,f as z,t as d,n as D}from"./app-16701445.js";import{_ as tt}from"./ArrowIcon-769e919f.js";import{s as et}from"./sortAndSearch-d1b7f68b.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const ot={class:"animate-top"},st={class:"flex justify-between items-center"},nt=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Paid Payments")],-1),at={class:"flex justify-end"},lt=t("div",{class:"flex space-x-6 mt-4 sm:mt-0 w-64"},[t("div",{class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},[t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})]),t("input",{id:"search-field",class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"})])],-1),it={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},dt={class:"flex justify-end"},rt={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},ct={class:"flex mb-2"},mt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),_t={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},pt={class:"sm:col-span-4"},ht={class:"relative mt-2"},ut={class:"sm:col-span-4"},ft={class:"relative mt-2"},gt={class:"mt-8 overflow-x-auto sm:rounded-lg"},yt={class:"shadow sm:rounded-lg"},xt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},vt={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},bt={class:"border-b-2"},wt=["onClick"],kt={key:0},Ct={class:"px-4 py-2.5 min-w-32"},zt={scope:"row",class:"px-4 py-2.5 font-medium text-gray-900 min-w-52"},Mt={class:"px-4 py-2.5 min-w-36"},Nt={class:"px-4 py-2.5 min-w-52"},It={class:"px-4 py-2.5 min-w-36"},$t={key:0,class:"space-y-1"},At={class:"text-sm"},St={class:"text-xs text-gray-500"},Vt={key:1},Bt={class:"px-4 py-2.5 min-w-32"},Pt={class:"items-center px-4 py-2.5"},Ot={class:"flex items-center justify-start gap-4"},Lt=t("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),jt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),Et=t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),Tt=["onClick"],Ut=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),Ft=t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),Yt=[Ut,Ft],Kt={key:1},qt=t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Ht=[qt],Zt={class:"p-6"},Gt=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this transaction? ",-1),Jt={class:"mt-6 flex justify-end"},pe={__name:"List",props:["data","organization","companies","organizationId","companyId"],setup(r){const h=r,{form:M,search:Qt,sort:S,fetchData:Rt,sortKey:V,sortDirection:B,updateParams:P}=et("payment.index",{organization_id:h.organizationId,company_id:h.companyId}),x=p(!1),N=p(null),O=[{field:"date",label:"DATE",sortable:!0},{field:"company.name",label:"COMPANY NAME",sortable:!0},{field:"payment_type",label:"PAYMENT TYPE",sortable:!0},{field:"bank_info.bank_name",label:"BANK",sortable:!0},{field:"invoice_no",label:"INVOICE NO",sortable:!1},{field:"amount",label:"AMOUNT (₹)",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],L=o=>{N.value=o,x.value=!0},v=()=>{x.value=!1},j=()=>{M.delete(route("payment.destroy",{id:N.value}),{onSuccess:()=>v()})},E=o=>{const n=new Date(o),e={year:"numeric",month:"short",day:"numeric"};return n.toLocaleDateString("en-US",e)},I=o=>{let n=o.toFixed(2).toString(),[e,u]=n.split("."),l=e.substring(e.length-3),A=e.substring(0,e.length-3);return A!==""&&(l=","+l),`${A.replace(/\B(?=(\d{2})+(?!\d))/g,",")+l}.${u}`},m=p(h.organizationId),_=p(h.companyId),b=p("");W([m,_],()=>{P({organization_id:m.value,company_id:_.value})});const $=(o,n,e)=>{b.value=o,M.get(route("payment.index",{search:o,organization_id:n,company_id:e}),{preserveState:!0})},T=(o,n)=>{m.value=o,$(b.value,m.value,_.value)},U=(o,n)=>{_.value=o,$(b.value,m.value,_.value)};return(o,n)=>(s(),i(g,null,[a(f(X),{title:"Payment"}),a(F,null,{default:c(()=>[t("div",ot,[t("div",st,[nt,t("div",at,[lt,t("div",it,[t("div",dt,[a(q,{href:o.route("payment.create")},{default:c(()=>[y(" Make payment ")]),_:1},8,["href"])])])])]),t("div",rt,[t("div",ct,[mt,a(w,{for:"company_id",value:"Filters"})]),t("div",_t,[t("div",pt,[a(w,{for:"company_id",value:"Organization Name"}),t("div",ht,[a(R,{options:r.organization,modelValue:m.value,"onUpdate:modelValue":n[0]||(n[0]=e=>m.value=e),onOnchange:T},null,8,["options","modelValue"])])]),t("div",ut,[a(w,{for:"company_id",value:"Company Name"}),t("div",ft,[a(Q,{options:r.companies,modelValue:_.value,"onUpdate:modelValue":n[1]||(n[1]=e=>_.value=e),onOnchange:U},null,8,["options","modelValue"])])])])]),t("div",gt,[t("div",yt,[t("table",xt,[t("thead",vt,[t("tr",bt,[(s(),i(g,null,k(O,(e,u)=>t("th",{key:u,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:l=>f(S)(e.field,e.sortable)},[y(d(e.label)+" ",1),e.sortable?(s(),C(tt,{key:0,isSorted:f(V)===e.field,direction:f(B)},null,8,["isSorted","direction"])):z("",!0)],8,wt)),64))])]),r.data.data&&r.data.data.length>0?(s(),i("tbody",kt,[(s(!0),i(g,null,k(r.data.data,(e,u)=>(s(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",Ct,d(E(e.date)??"-"),1),t("td",zt,d(e.company.name??"-"),1),t("td",Mt,d(e.payment_type=="check"?"Cheque":e.payment_type??"-"),1),t("td",Nt,d(e!=null&&e.bank_info?(e==null?void 0:e.bank_info.bank_name)+"-"+(e==null?void 0:e.bank_info.account_number):"-"),1),t("td",It,[e!=null&&e.invoice_data&&e.invoice_data.length>0?(s(),i("div",$t,[(s(!0),i(g,null,k(e.invoice_data,l=>(s(),i("div",{key:l.id,class:"flex items-center gap-2"},[t("span",{class:D([l.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium"])},d(l.invoice_type==="purchase"?"P":"S"),3),t("span",At,d(l.invoice_no),1),t("span",St,"(₹"+d(I(l.amount))+")",1)]))),128))])):(s(),i("span",Vt,d(e.invoice_no||"-"),1))]),t("td",Bt,d(I(e.amount)??"-"),1),t("td",Pt,[t("div",Ot,[a(Y,{align:"right",width:"48"},{trigger:c(()=>[Lt]),content:c(()=>[e.invoice_data.length!=0?(s(),C(K,{key:0,href:o.route("payment.edit",e.id)},{svg:c(()=>[jt]),text:c(()=>[Et]),_:2},1032,["href"])):z("",!0),t("button",{type:"button",onClick:l=>L(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Yt,8,Tt)]),_:2},1024)])])]))),128))])):(s(),i("tbody",Kt,Ht))])])]),r.data.data&&r.data.data.length>0?(s(),C(J,{key:0,class:"mt-6",links:r.data.links},null,8,["links"])):z("",!0)]),a(G,{show:x.value,onClose:v},{default:c(()=>[t("div",Zt,[Gt,t("div",Jt,[a(H,{onClick:v},{default:c(()=>[y(" Cancel ")]),_:1}),a(Z,{class:"ml-3",onClick:j},{default:c(()=>[y(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{pe as default};
