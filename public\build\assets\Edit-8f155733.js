import{_ as o}from"./AdminLayout-595ad5a7.js";import i from"./DeleteUserForm-f5d01386.js";import m from"./UpdatePasswordForm-fc644883.js";import r from"./UpdateProfileInformationForm-7b35daa0.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-97275a91.js";import"./DangerButton-36669f8b.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-b3250228.js";import"./InputLabel-eb73087c.js";import"./Modal-48c075e7.js";/* empty css                                                              */import"./SecondaryButton-d0c53c3f.js";import"./TextInput-11c46564.js";import"./PrimaryButton-46ac4375.js";import"./TextArea-5e21e606.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
