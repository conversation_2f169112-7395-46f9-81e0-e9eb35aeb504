<script setup>
import { ref, onMounted, watch, computed  } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import { Head , useForm, usePage} from '@inertiajs/vue3';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import InputLabel from '@/Components/InputLabel.vue';
import ArrowIcon from '@/Components/ArrowIcon.vue';
import { sortAndSearch } from '@/Composables/sortAndSearch.js';

const props = defineProps(['data', 'permissions', 'organization', 'customers', 'organizationId', 'customerId']);
const { form, search, sort, fetchData, sortKey, sortDirection, updateParams } = sortAndSearch('creditnote.index', {
    organization_id: props.organizationId,
    customer_id: props.customerId,
});

// const form = useForm({});
const deleteModalVisible = ref(false);
const selectedUserId = ref(null);

const columns = [
    { field: 'credit_note_no',              label: 'CREDIT NUMBER',         sortable: true },
    { field: 'debit_note_number',           label: 'DEBIT NOTE NUMBER',     sortable: true },
    { field: 'invoice.invoice_no',          label: 'INVOICE NO',            sortable: true },
    { field: 'customers.customer_name',     label: 'CUSTOMER NAME',         sortable: true },
    { field: 'date',                        label: 'DATE',                  sortable: true },
    { field: 'total_amount',                label: 'AMOUNT (₹)',            sortable: true },
    { field: 'action',                      label: 'ACTION',                sortable: false },
];

const openDeleteModal = (userId) => {
  selectedUserId.value = userId;
  deleteModalVisible.value = true;
};

const closeModal = () => {
    deleteModalVisible.value = false;
};

const deletePO = () => {
    form.delete(route('creditnote.destroy',{id:selectedUserId.value}), {
        onSuccess: () => closeModal()
    });
};

const organizationId = ref(props.organizationId);
const customerId = ref(props.customerId);
const searchValue = ref('');

watch([organizationId, customerId ], () => {
    updateParams({
        organization_id: organizationId.value,
        customer_id: customerId.value,
    });
});

const handleSearchChange = (value, organizationId, customerId) => {
    searchValue.value = value;
    form.get(route('creditnote.index',{search:value,  organization_id: organizationId ,  customer_id: customerId }),  {
        preserveState: true,
        // replace: true,
    });
};

const setOrganization = (id, name) => {
    organizationId.value = id;
    handleSearchChange(searchValue.value , organizationId.value, customerId.value);
};

const setCustomers = (id, name) => {
    customerId.value = id;
    handleSearchChange(searchValue.value , organizationId.value , customerId.value);
};

const exportOrdersXls = () => {
    const xlsName = 'Orders_Report_' + new Date().toISOString().split('T')[0];
    const params = {
        organization_id: organizationId.value,customer_id: customerId.value
    };

    const queryString = new URLSearchParams(params).toString();
    const url = `/export-orders?${queryString}`;

    fetch(url, {
        method: 'GET',
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.blob();
    })
    .then(blob => {
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', xlsName + '.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    })
    .catch(error => {
        // Handle error
        console.error('Error exporting data:', error);
    });
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const truncateName = (name) => {
    return name && name.length > 40 ? name.substring(0, 40) + '...' : name;
};

const getRowHeight = (description) => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = description;
    tempDiv.style.width = '1000px';
    document.body.appendChild(tempDiv);
    const height = tempDiv.offsetHeight;
    document.body.removeChild(tempDiv);
    return height;
};

</script>

<template>
    <Head title="Credit Note"/>

    <AdminLayout>
        <div class="animate-top">
            <div class="flex justify-between items-center">
                <div class="items-start">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Credit Notes</h1>
                </div>
                <div class="flex justify-end">
                    <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                        <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                            <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                                <input id="search-field"  @input="handleSearchChange($event.target.value, organizationId, customerId)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                                <!-- <input id="search-field" v-model="search" @input="fetchData" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search"> -->
                        </div>
                    </div>
                    <div class="flex mt-4 sm:mt-0 sm:flex-none">
                        <div class="flex ml-6">
                            <CreateButton :href="route('reports')">
                                    Back
                            </CreateButton>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                <div class="flex justify-between mb-2">
                    <div class="flex">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                        </svg>
                        <InputLabel for="customer_id" value="Filters" />
                    </div>
                    <!-- <button @click="exportOrdersXls">
                        <img class="w-8 h-8" :src="'/uploads/img/export-excel.png'" alt="LOGO">
                    </button> -->

                </div>
                <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Organization Name" />
                        <div class="relative mt-2">
                            <SimpleDropdown :options="organization"
                            v-model="organizationId"
                            @onchange="setOrganization"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Customer Name" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="customers"
                            v-model="customerId"
                            @onchange="setCustomers"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                            <tr class="border-b-2">
                                <th v-for="(column, index) in columns" :key="index" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer" @click="sort(column.field, column.sortable)">
                                    {{ column.label }}
                                    <ArrowIcon :isSorted="sortKey === column.field" :direction="sortDirection" v-if="column.sortable" />
                                </th>
                            </tr>
                        </thead>
                        <tbody v-if="data.data && (data.data.length > 0)">
                            <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(poData, index) in data.data" :key="poData.id">
                                <td class="px-4 py-2.5 min-w-40">
                                    {{ poData.credit_note_no }}
                                </td>
                                <td class="px-4 py-2.5 min-w-44">
                                    {{ poData.debit_note_number }}
                                </td>
                                <td class="px-4 py-2.5 min-w-36">
                                    {{ poData.invoice.invoice_no ?? '-' }}
                                </td>
                                <th scope="row" class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap">
                                    {{ poData.customers.customer_name ?? '-' }} - {{ poData.customers.city ?? '-' }}
                                </th>
                                <td class="px-4 py-2.5 min-w-32">
                                    {{ formatDate(poData.date) }}
                                </td>
                                <td class="px-4 py-2.5 min-w-32">
                                    {{ formatAmount(poData.total_amount)}}
                                </td>
                                <td class="items-center px-4 py-2.5">
                                    <div class="flex items-center justify-start gap-4">
                                        <Dropdown :align="'right'" width="48">
                                            <template #trigger>
                                                <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                    <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                        <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                    </svg>
                                                </button>
                                            </template>
                                            <template #content>
                                                <ActionLink :href="route('creditnote.show',{id:poData.id})">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            View CreditNote
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <button  type="button" @click="openDeleteModal(poData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                        />
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Delete
                                                    </span>
                                                </button>
                                            </template>
                                        </Dropdown>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr class="bg-white">
                            <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                No data found.
                            </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>
        <Modal :show="deleteModalVisible" @close="closeModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete this credit note?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deletePO"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>

</template>

<style scoped>

</style>


