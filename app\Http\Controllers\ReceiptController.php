<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\BankTransaction;
use App\Models\Organization;
use App\Models\Customer;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\ReceiptRequest;
use App\Models\AccountType;
use App\Models\BankInfo;
use App\Models\Invoice;
use App\Models\CustomerTransaction;
use App\Models\PaymentReceive;
use App\Models\CustomerCredit;
use App\Models\CustomerCreditDetails;
use App\Models\PurchaseOrderReceives;
use App\Models\Company;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use App\Traits\QueryTrait;

class ReceiptController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:List Bank Transaction')->only(['index']);
        $this->middleware('permission:Create Bank Transaction')->only(['create', 'store']);
        $this->middleware('permission:Edit Bank Transaction')->only(['edit', 'update']);
        $this->middleware('permission:Delete Bank Transaction')->only('destroy');
    }

    use QueryTrait;

    public function index(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $customerId = $request->input('customer_id');
        $query = PaymentReceive::with('customers', 'bankInfo');

        $organization  = Organization::select('id', 'name')->get();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 28), " - ", city) as name, id')->orderByRaw('customer_name')->get();
        $allOption = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOption);
        $allOption2 = ['id' => null, 'name' => 'ALL CUSTOMERS'];
        $customers->prepend($allOption2);

        if($customerId) {
            $query->where('customer_id', $customerId);
        }
        if($organizationId) {
            $query->where('organization_id', $organizationId);
        }

        $searchableFields = ['customers.customer_name', 'invoice.invoice_no', 'payment_type', 'bank_info.bank_name', 'date', 'amount'];
        $this->searchAndSort($query, $request, $searchableFields);

        $data = $query->orderBy('id', 'desc')->paginate(20);
        $data->withQueryString()->links();
        return Inertia::render('Receipt/List', compact('data', 'organization', 'customers', 'customerId', 'organizationId'));
    }

    public function create(Request $request)
    {
        $organization  = Organization::select('id', 'name')->get();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 35), " - ", city) as name, id, party_id')->orderByRaw('customer_name')->get();
        $paymentType = Config::get('constants.paymentTypes');
        $bankinfo  = BankInfo::select('id', DB::raw("CONCAT(bank_name, ' - ', account_number) AS name"), 'organization_id')->get();

        // Get sales invoices (regular invoices)
        $salesInvoices = Invoice::where('invoice.status', '!=', 'Paid')
            ->select(DB::raw("CONCAT('SALES - ', invoice_no, '  ', 'Date', ' : ', DATE_FORMAT(date, '%d %b %Y'), '  ',  'Total Amount : ', total_amount) AS name"),
                'id', 'organization_id', 'customer_id', 'total_amount', 'invoice_no', 'date', 'pending_amount',
                DB::raw("'sales' as invoice_type"))
            ->orderBy('date', 'asc')->get();

        // Get purchase invoices (from PurchaseOrderReceives) with organization_id and company info
        $purchaseInvoices = PurchaseOrderReceives::with('purchaseOrder.company')
            ->where('purchase_order_receives.status', '!=', 'Paid')
            ->where('purchase_order_receives.total_amount', '>', 0)
            ->whereHas('purchaseOrder', function ($query) {
                $query->where('type', 'invoice');
            })
            ->join('purchase_orders', 'purchase_order_receives.purchase_order_id', '=', 'purchase_orders.id')
            ->join('companies', 'purchase_orders.company_id', '=', 'companies.id')
            ->select(DB::raw("CONCAT('PURCHASE - ', purchase_order_receives.customer_invoice_no, '  ', 'Date', ' : ', DATE_FORMAT(purchase_order_receives.customer_invoice_date, '%d %b %Y'), '  ',  'Total Amount : ', purchase_order_receives.total_amount) AS name"),
                'purchase_order_receives.id', 'purchase_orders.organization_id', 'companies.party_id as party_id', 'purchase_order_receives.total_amount',
                'purchase_order_receives.customer_invoice_no as invoice_no', 'purchase_order_receives.customer_invoice_date as date',
                'purchase_order_receives.pending_amount', DB::raw("'purchase' as invoice_type"))
            ->orderBy('purchase_order_receives.customer_invoice_date', 'asc')->get();

        // Combine both invoice types
        // $invoices = $salesInvoices->union($purchaseInvoices)->get();
        $invoices = $salesInvoices->concat($purchaseInvoices)->sortBy('date')->values();

        $credit = CustomerCredit::with('paymentreceive.bankInfo')->where('unused_amount', '>', 0)->get();
        return Inertia::render('Receipt/Add', compact('paymentType', 'organization', 'bankinfo', 'customers', 'invoices', 'credit'));
    }

    public function store(ReceiptRequest $request)
    {
        DB::beginTransaction();
        try {
            $data =  (array) $request->DTO();
            //payment edit
            if(isset($data['id'])){
                $paymentId = $data['id'];
                // Revert the effects of the original payment
                // Find the original payment
                $originalPayment = PaymentReceive::with('credit', 'credit.creditDetail')->find($paymentId);

                // 1. Revert invoice payments
                foreach ($originalPayment->invoice_data as $invoice) {
                    $invoiceDetail = Invoice::find($invoice['id']);
                    if ($invoiceDetail) {
                        $updateData['paid_amount'] = $invoiceDetail->paid_amount - $invoice['amount'];
                        $updateData['pending_amount'] = $invoiceDetail->pending_amount + $invoice['amount'];
                        if ($updateData['pending_amount'] <= 0) {
                            $updateData['status'] = 'Paid';
                        } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                            $updateData['status'] = 'Partially Paid';
                        } else {
                            $updateData['status'] = 'Unpaid';
                        }
                        $invoiceDetail->update($updateData);
                    }
                }

                // 2. Revert credit usage not required now
                // if ($originalPayment->credit) {
                //     $credit = $originalPayment->credit;
                //     foreach ($credit->creditDetail as $detail) {
                //         $invoice = Invoice::find($detail->invoice_id);
                //         if ($invoice) {
                //             $invoice->paid_amount -= $detail->amount;
                //             $invoice->pending_amount += $detail->amount;
                //             if ($invoice->paid_amount <= 0) {
                //                 $invoice->status = 'Unpaid';
                //             } elseif ($invoice->paid_amount > 0 && $invoice->pending_amount > 0) {
                //                 $invoice->status = 'Partially Paid';
                //             } else {
                //                 $invoice->status = 'Paid';
                //             }
                //             $invoice->save();
                //         }
                //         $detail->delete();
                //     }

                //     // Don't delete the credit record, just update it
                //     if ($originalPayment->advance_amount > 0) {
                //         $credit->unused_amount = $originalPayment->advance_amount;
                //         $credit->save();
                //     } else {
                //         $credit->delete();
                //     }
                // }

                // 3. Delete related bank transaction and customer transaction
                if ($originalPayment->payment_type == 'check' || $originalPayment->payment_type == 'NEFT') {
                    BankTransaction::where(['entity_id' => $paymentId, 'entity_type' => 'payment_receive'])->delete();
                }
                CustomerTransaction::where(['entity_id' => $paymentId, 'entity_type' => 'payment_receive'])->delete();

                $filteredInvoiceData = array_filter($data['invoice'], function ($invoice) {
                    return $invoice['check'] === true;  // Only include invoices where `check` is true
                });
                $data['invoice_data'] = array_values(array_map(function ($invoice) {
                    return ([
                        'id' => $invoice['id'],
                        'invoice_no' => $invoice['invoice_no'],
                        'amount' => $invoice['amount'],
                        'invoice_type' => $invoice['invoice_type'] ?? 'sales',
                    ]);
                }, $filteredInvoiceData));
                $invoice_nos = implode(',', array_map(function($item) {
                    return $item['invoice_no'];  // Return the 'invoice_no' field
                }, $data['invoice_data']));

                $data['created_by'] = $data['updated_by'] = auth()->id();
                $originalPayment->update($data);

                if ($originalPayment) {
                    foreach ($data['invoice_data'] as $invoice) {
                        $invoiceType = $invoice['invoice_type'] ?? 'sales';

                        if ($invoiceType === 'sales') {
                            $InvoiceDetail = Invoice::find($invoice['id']);
                            $updateData['paid_amount'] = $InvoiceDetail->paid_amount + $invoice['amount'];
                            $updateData['pending_amount'] = $InvoiceDetail->pending_amount - $invoice['amount'];
                            if ($updateData['pending_amount'] <= 0) {
                                $updateData['status'] = 'Paid';
                            } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                                $updateData['status'] = 'Partially Paid';
                            } else {
                                $updateData['status'] = 'Unpaid';
                            }
                            $InvoiceDetail->update($updateData);
                        } else if ($invoiceType === 'purchase') {
                            $PurchaseInvoiceDetail = PurchaseOrderReceives::find($invoice['id']);
                            $updateData['paid_amount'] = $PurchaseInvoiceDetail->paid_amount + $invoice['amount'];
                            $updateData['pending_amount'] = $PurchaseInvoiceDetail->pending_amount - $invoice['amount'];
                            if ($updateData['pending_amount'] <= 0) {
                                $updateData['status'] = 'Paid';
                            } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                                $updateData['status'] = 'Partially Paid';
                            } else {
                                $updateData['status'] = 'Unpaid';
                            }
                            $PurchaseInvoiceDetail->update($updateData);
                        }
                    }
                }

                // Create customer transaction and bank transaction
                $data['entity_id'] = $originalPayment->id;
                $data['entity_type'] = 'payment_receive';
                if ($data['payment_type'] == 'check') {
                    $data['payment_type'] = 'cr';
                    $data['note'] = 'Cheque No:' . $data['check_number'] . ' ' . 'Invoice No:' . $invoice_nos;
                    BankTransaction::create($data);
                } else if ($data['payment_type'] == 'NEFT') {
                    $data['payment_type'] = 'cr';
                    $data['note'] = 'NEFT' . ' ' . 'Invoice No:' . $invoice_nos;
                    BankTransaction::create($data);
                } else if ($data['payment_type'] == 'cash') {
                    $data['payment_type'] = 'cr';
                    $data['note'] = $data['note'] . ' ' . 'Invoice No:' . $invoice_nos;
                }

                if ($data['advance_amount'] > 0) {
                    // Check if there's an existing credit record
                    $existingCredit = CustomerCredit::where('payment_receive_id', $originalPayment->id)->first();
                    if ($existingCredit) {
                        $existingCredit->update([
                            'unused_amount' => $data['advance_amount'],
                            'amount' => $data['advance_amount']
                        ]);
                    } else {
                        $data['payment_receive_id'] = $originalPayment->id;
                        $data['unused_amount'] = $data['amount'] = $data['advance_amount'];
                        CustomerCredit::create($data);
                    }
                }
                $data['amount'] = $data['settled_amount'] + $data['advance_amount'];
                CustomerTransaction::create($data);
                DB::commit();
                return Redirect::to('/receipt')->with('success', 'Payment Updated Successfully');
            } else {
            //payment create
                $filteredInvoiceData = array_filter($data['invoice'], function ($invoice) {
                    return $invoice['check'] === true;  // Only include invoices where `check` is true
                });
                $data['invoice_data'] = array_values(array_map(function ($invoice) {
                    return ([
                        'id' => $invoice['id'],
                        'invoice_no' => $invoice['invoice_no'],
                        'amount' => $invoice['amount'],
                        'invoice_type' => $invoice['invoice_type'] ?? 'sales',
                    ]);
                }, $filteredInvoiceData));
                $invoice_nos = implode(',', array_map(function($item) {
                    return $item['invoice_no'];  // Return the 'invoice_no' field
                }, $data['invoice_data']));
                    //for advance payment settle
                if($data['is_credit'] == 'Yes'){
                    $credits = $data['credit_data'];
                    $invoiceData = $data['invoice_data'];
                    foreach ($invoiceData as $invoice) {
                        $remainingAmount = $invoice['amount'];
                        foreach ($credits as &$creditEntry) {
                            if ($remainingAmount <= 0) break;

                            $usableAmount = min($remainingAmount, $creditEntry['unused_amount']);

                            if ($usableAmount > 0) {
                                // 1. Update invoice based on type
                                $invoiceType = $invoice['invoice_type'] ?? 'sales';

                                if ($invoiceType === 'sales') {
                                    $InvoiceDetail = Invoice::find($invoice['id']);
                                    $updateData['paid_amount'] = $InvoiceDetail->paid_amount + $usableAmount;
                                    $updateData['pending_amount'] = $InvoiceDetail->pending_amount - $usableAmount;
                                    if ($updateData['pending_amount'] <= 0) {
                                        $updateData['status'] = 'Paid';
                                    } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                                        $updateData['status'] = 'Partially Paid';
                                    } else {
                                        $updateData['status'] = 'Unpaid';
                                    }
                                    $InvoiceDetail->update($updateData);
                                } else if ($invoiceType === 'purchase') {
                                    $PurchaseInvoiceDetail = PurchaseOrderReceives::find($invoice['id']);
                                    $updateData['paid_amount'] = $PurchaseInvoiceDetail->paid_amount + $usableAmount;
                                    $updateData['pending_amount'] = $PurchaseInvoiceDetail->pending_amount - $usableAmount;
                                    if ($updateData['pending_amount'] <= 0) {
                                        $updateData['status'] = 'Paid';
                                    } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                                        $updateData['status'] = 'Partially Paid';
                                    } else {
                                        $updateData['status'] = 'Unpaid';
                                    }
                                    $PurchaseInvoiceDetail->update($updateData);
                                }
                                // 2. Create credit details record
                                $creditDetail = [
                                    'invoice_id' => $invoice['id'],
                                    'credit_id' => $creditEntry['id'],
                                    'amount' => $usableAmount,
                                    'date' => date('Y-m-d'),
                                    'created_by' => auth()->id(),
                                    'updated_by' => auth()->id(),
                                ];
                                CustomerCreditDetails::create($creditDetail);
                                // 3. Deduct from credit's unused amount
                                $creditEntry['unused_amount'] -= $usableAmount;
                                CustomerCredit::where('id', $creditEntry['id'])->update([
                                    'unused_amount' => $creditEntry['unused_amount']
                                ]);
                                $remainingAmount -= $usableAmount;
                            }
                            $customerTransaction = CustomerTransaction::where(['entity_id' => $creditEntry['payment_receive_id'], 'entity_type' => 'payment_receive'])->first();
                            if ($customerTransaction) {
                                $existingNote = trim($customerTransaction->note ?? '');
                                $updatedNote = $existingNote . ' Invoice No: ' . $invoice_nos;
                                $customerTransaction->update(['note' => $updatedNote]);
                            }
                        }
                    }
                } else {
                    //for receive payment
                    $data['created_by'] = $data['updated_by'] = auth()->id();
                    $receivePayment = PaymentReceive::create($data);
                    if($receivePayment){
                        foreach($receivePayment->invoice_data as $invoice){
                            $invoiceType = $invoice['invoice_type'] ?? 'sales';

                            if ($invoiceType === 'sales') {
                                $InvoiceDetail = Invoice::find($invoice['id']);
                                $updateData['paid_amount'] = $InvoiceDetail->paid_amount + $invoice['amount'];
                                $updateData['pending_amount'] = $InvoiceDetail->pending_amount - $invoice['amount'];
                                if ($updateData['pending_amount'] <= 0) {
                                    $updateData['status'] = 'Paid';
                                } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                                    $updateData['status'] = 'Partially Paid';
                                } else {
                                    $updateData['status'] = 'Unpaid';
                                }
                                $InvoiceDetail->update($updateData);
                            } else if ($invoiceType === 'purchase') {
                                $PurchaseInvoiceDetail = PurchaseOrderReceives::find($invoice['id']);
                                $updateData['paid_amount'] = $PurchaseInvoiceDetail->paid_amount + $invoice['amount'];
                                $updateData['pending_amount'] = $PurchaseInvoiceDetail->pending_amount - $invoice['amount'];
                                if ($updateData['pending_amount'] <= 0) {
                                    $updateData['status'] = 'Paid';
                                } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                                    $updateData['status'] = 'Partially Paid';
                                } else {
                                    $updateData['status'] = 'Unpaid';
                                }
                                $PurchaseInvoiceDetail->update($updateData);
                            }
                        }
                    }
                    //customer Transaction
                    $data['entity_id'] = $receivePayment->id;
                    $data['entity_type'] = 'payment_receive';
                    if($data['payment_type'] == 'check'){
                        $data['payment_type'] = 'cr';
                        $data['note'] = 'Cheque No:' .$data['check_number'] .' '.'Invoice No:' .$invoice_nos;
                        $bankTransaction = BankTransaction::create($data);
                    } else if($data['payment_type'] == 'NEFT'){
                        $data['payment_type'] = 'cr';
                        $data['note'] = 'NEFT'. ' ' .'Invoice No:' .$invoice_nos;
                        $bankTransaction = BankTransaction::create($data);
                    } else if($data['payment_type'] == 'cash'){
                        $data['payment_type'] = 'cr';
                        $data['note'] = $data['note'].' '.'Invoice No:' .$invoice_nos;
                    }

                    if($data['advance_amount'] > 0){
                        $data['payment_receive_id'] = $receivePayment->id;
                        $data['unused_amount'] =  $data['amount'] = $data['advance_amount'];
                        CustomerCredit::create($data);
                    }
                    $data['amount'] = $data['settled_amount'] + $data['advance_amount'];
                    CustomerTransaction::create($data);
                }
                DB::commit();
                return Redirect::to('/receipt')->with('success', 'Payment Received Successfully');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit($id)
    {
        $payment = PaymentReceive::with('bankInfo')->find($id);

        if (!$payment) {
            return Redirect::to('/receipt')->with('error', 'Payment not found');
        }

        $organization = Organization::select('id', 'name')->get();
        $customers = Customer::where('status', '1')->selectRaw('CONCAT(SUBSTRING(customer_name, 1, 35), " - ", city) as name, id')->orderByRaw('customer_name')->get();
        $paymentType = Config::get('constants.paymentTypes');
        $bankinfo = BankInfo::select('id', DB::raw("CONCAT(bank_name, ' - ', account_number) AS name"), 'organization_id')->get();

        // Get customer's party_id for linking with companies
        $customer = Customer::find($payment->customer_id);
        $partyId = $customer->party_id;

        // Get sales invoices that are either paid in this payment or still have pending amounts
        $salesInvoices = Invoice::where(function($query) use ($payment) {
                // Include invoices that are not fully paid
                $query->where('invoice.status', '!=', 'Paid')
                      ->where('invoice.customer_id', $payment->customer_id)
                      ->where('invoice.organization_id', $payment->organization_id);
            })
            ->orWhereIn('id', collect($payment->invoice_data)->where('invoice_type', 'sales')->pluck('id'))
            ->select(DB::raw("CONCAT('SALES - ', invoice_no, '  ', 'Date', ' : ', DATE_FORMAT(date, '%d %b %Y'), '  ',  'Total Amount : ', total_amount) AS name"),
                    'id', 'organization_id', 'customer_id', 'total_amount', 'invoice_no', 'date', 'pending_amount',
                    DB::raw("'sales' as invoice_type"))
            ->orderBy('date', 'asc')->get();

        // Get purchase invoices for companies with the same party_id
        $purchaseInvoices = collect();
        if ($partyId) {
            $purchaseInvoices = PurchaseOrderReceives::with('purchaseOrder.company')
                ->whereHas('purchaseOrder', function ($query) use ($payment, $partyId) {
                    $query->where('type', 'invoice')
                          ->where('organization_id', $payment->organization_id)
                          ->whereHas('company', function ($companyQuery) use ($partyId) {
                              $companyQuery->where('party_id', $partyId);
                          });
                })
                ->where(function($query) {
                    $query->where('purchase_order_receives.status', '!=', 'Paid');
                })
                ->orWhereIn('purchase_order_receives.id', collect($payment->invoice_data)->where('invoice_type', 'purchase')->pluck('id'))
                ->join('purchase_orders', 'purchase_order_receives.purchase_order_id', '=', 'purchase_orders.id')
                ->join('companies', 'purchase_orders.company_id', '=', 'companies.id')
                ->select(DB::raw("CONCAT('PURCHASE - ', purchase_order_receives.customer_invoice_no, '  ', 'Date', ' : ', DATE_FORMAT(purchase_order_receives.customer_invoice_date, '%d %b %Y'), '  ',  'Total Amount : ', purchase_order_receives.total_amount) AS name"),
                    'purchase_order_receives.id', 'purchase_orders.organization_id', 'companies.party_id', 'purchase_order_receives.total_amount',
                    'purchase_order_receives.customer_invoice_no as invoice_no', 'purchase_order_receives.customer_invoice_date as date',
                    'purchase_order_receives.pending_amount', DB::raw("'purchase' as invoice_type"))
                ->orderBy('purchase_order_receives.customer_invoice_date', 'asc')->get();
        }

        // dd($purchaseInvoices);
        // Combine both invoice types
        // $invoices = $salesInvoices->union($purchaseInvoices)->get();
        $invoices = $salesInvoices->concat($purchaseInvoices)->sortBy('date')->values();
        // dd($invoices);


        // Get the original pending amount for each invoice by adding back the amount paid in this payment
        foreach ($invoices as $invoice) {
            foreach ($payment->invoice_data as $paidInvoice) {
                if ($invoice->id == $paidInvoice['id']) {
                    $invoice->original_pending_amount = $invoice->pending_amount + $paidInvoice['amount'];
                    $invoice->paid_in_this_payment = $paidInvoice['amount'];
                    break;
                }
            }
            if (!isset($invoice->original_pending_amount)) {
                $invoice->original_pending_amount = $invoice->pending_amount;
                $invoice->paid_in_this_payment = 0;
            }
        }

        // Get all credits for this customer
        $credit = CustomerCredit::with('paymentreceive.bankInfo')
            ->where('customer_id', $payment->customer_id)
            ->where('organization_id', $payment->organization_id)
            ->where('unused_amount', '>', 0)
            ->get();

        // Add the payment's own credit if it exists
        if ($payment->advance_amount > 0) {
            $ownCredit = CustomerCredit::with('paymentreceive.bankInfo')
                ->where('payment_receive_id', $payment->id)
                ->first();

            if ($ownCredit) {
                $credit->push($ownCredit);
            }
        }

        return Inertia::render('Receipt/Edit', compact('payment', 'paymentType', 'organization', 'bankinfo', 'customers', 'invoices', 'credit'));
    }

    // public function update(ReceiptRequest $request)
    // {
    //     DB::beginTransaction();
    //     try {
    //         $data = (array) $request->DTO();
    //         if ($data['is_credit'] == 'Yes') {
    //             $credits = $data['credit_data'];
    //             $invoiceData = $data['invoice_data'];
    //             foreach ($invoiceData as $invoice) {
    //                 $remainingAmount = $invoice['amount'];
    //                 foreach ($credits as &$creditEntry) {
    //                     if ($remainingAmount <= 0) break;

    //                     $usableAmount = min($remainingAmount, $creditEntry['unused_amount']);

    //                     if ($usableAmount > 0) {
    //                         // 1. Update invoice
    //                         $InvoiceDetail = Invoice::find($invoice['id']);
    //                         $updateData['paid_amount'] = $InvoiceDetail->paid_amount + $usableAmount;
    //                         $updateData['pending_amount'] = $InvoiceDetail->pending_amount - $usableAmount;
    //                         if ($updateData['pending_amount'] <= 0) {
    //                             $updateData['status'] = 'Paid';
    //                         } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
    //                             $updateData['status'] = 'Partially Paid';
    //                         } else {
    //                             $updateData['status'] = 'Unpaid';
    //                         }
    //                         $InvoiceDetail->update($updateData);

    //                         // 2. Create credit details record
    //                         $creditDetail = [
    //                             'invoice_id' => $invoice['id'],
    //                             'credit_id' => $creditEntry['id'],
    //                             'amount' => $usableAmount,
    //                             'date' => date('Y-m-d'),
    //                             'created_by' => auth()->id(),
    //                             'updated_by' => auth()->id(),
    //                         ];
    //                         CustomerCreditDetails::create($creditDetail);

    //                         // 3. Deduct from credit's unused amount
    //                         $creditEntry['unused_amount'] -= $usableAmount;
    //                         CustomerCredit::where('id', $creditEntry['id'])->update([
    //                             'unused_amount' => $creditEntry['unused_amount']
    //                         ]);
    //                         $remainingAmount -= $usableAmount;
    //                     }
    //                     $customerTransaction = CustomerTransaction::where(['entity_id' => $creditEntry['payment_receive_id'], 'entity_type' => 'payment_receive'])->first();
    //                     if ($customerTransaction) {
    //                         $existingNote = trim($customerTransaction->note ?? '');
    //                         $updatedNote = $existingNote . ' Invoice No: ' . $invoice_nos;
    //                         $customerTransaction->update(['note' => $updatedNote]);
    //                     }
    //                 }
    //             }
    //         } else {
    //         }
    //         DB::commit();
    //         return Redirect::to('/receipt')->with('success', 'Payment Updated Successfully');
    //     } catch (\Exception $e) {
    //         DB::rollBack();
    //         return Redirect::back()->with('error', $e->getMessage());
    //     }
    // }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $receivePayment = PaymentReceive::with('credit', 'credit.creditDetail')->find($id);
            foreach($receivePayment->invoice_data as $invoice){
                $invoiceType = $invoice['invoice_type'] ?? 'sales';

                if ($invoiceType === 'sales') {
                    // Handle sales invoices
                    $InvoiceDetail = Invoice::find($invoice['id']);
                    if ($InvoiceDetail) {
                        $updateData['paid_amount'] = $InvoiceDetail->paid_amount - $invoice['amount'];
                        $updateData['pending_amount'] = $InvoiceDetail->pending_amount + $invoice['amount'];
                        if ($updateData['pending_amount'] <= 0) {
                            $updateData['status'] = 'Paid';
                        } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                            $updateData['status'] = 'Partially Paid';
                        } else {
                            $updateData['status'] = 'Unpaid';
                        }
                        $InvoiceDetail->update($updateData);
                    }
                } elseif ($invoiceType === 'purchase') {
                    // Handle purchase invoices
                    $PurchaseDetail = PurchaseOrderReceives::find($invoice['id']);
                    if ($PurchaseDetail) {
                        $updateData['paid_amount'] = $PurchaseDetail->paid_amount - $invoice['amount'];
                        $updateData['pending_amount'] = $PurchaseDetail->pending_amount + $invoice['amount'];
                        if ($updateData['pending_amount'] <= 0) {
                            $updateData['status'] = 'Paid';
                        } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                            $updateData['status'] = 'Partially Paid';
                        } else {
                            $updateData['status'] = 'Unpaid';
                        }
                        $PurchaseDetail->update($updateData);
                    }
                }
            }

            if($receivePayment->credit) {
                $credit = $receivePayment->credit;
                foreach ($credit->creditDetail as $detail) {
                    $invoice = Invoice::find($detail->invoice_id);
                    if ($invoice) {
                        $invoice->paid_amount -= $detail->amount;
                        $invoice->pending_amount += $detail->amount;
                        if ($invoice->paid_amount <= 0) {
                            $invoice->status = 'Unpaid';
                        } elseif ($invoice->paid_amount > 0 && $invoice->pending_amount > 0) {
                            $invoice->status = 'Partially Paid';
                        } else {
                            $invoice->status = 'Paid';
                        }
                        $invoice->save();
                    }
                    $detail->delete();
                }
                $credit->delete();
            }
            if($receivePayment->payment_type == 'check' || $receivePayment->payment_type== 'NEFT'){
                $bankTransaction = BankTransaction::where(['entity_id' => $receivePayment->id , 'entity_type' => 'payment_receive'])->delete();
            }
            $customerTransaction = CustomerTransaction::where(['entity_id' => $receivePayment->id , 'entity_type' => 'payment_receive'])->delete();
            $receivePayment->delete();
            DB::commit();
            return Redirect::back()->with('success','Transaction Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }
}
