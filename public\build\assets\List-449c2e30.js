import{_ as x,b as u}from"./AdminLayout-36b0d46a.js";import{_ as f}from"./SecondaryButton-d521cdbf.js";import{D as _}from"./DangerButton-b3c50a37.js";import{M as y}from"./Modal-61735c0a.js";import{_ as w}from"./Pagination-52b28f25.js";import{h as v,r as b,o,c as i,a,u as k,w as l,F as c,Z as C,b as t,i as B,e as M,f as N,g as m,t as r}from"./app-4c3f0163.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const L={class:"sm:flex sm:items-center"},T=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Activity Logs")],-1),V={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200"},z=t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),A={class:"mt-8 flow-root"},E={class:"-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"},S={class:"overflow-hidden inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8",style:{"min-height":"500px"}},I={class:"p-1 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},$={class:"min-w-full divide-y divide-gray-300"},j=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"TYPE"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"EVENT"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"DESCRIPTION"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1),D={key:0,class:"divide-y divide-gray-300 bg-white"},O={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900"},F={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},P={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},H={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-500"},R={class:"flex justify-start gap-4"},U=t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Y=t("button",{type:"button",class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})]),t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ")],-1),Z={key:1},q=t("tr",{class:"bg-white"},[t("td",{colspan:"6",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),G=[q],J={class:"p-6"},K=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),Q={class:"mt-6 flex justify-end"},it={__name:"List",props:["data","search"],setup(e){const h=v({}),g=b(!1),p=n=>{h.get(route("activity-logs",{search:n}),{preserveState:!0})};return(n,d)=>(o(),i(c,null,[a(k(C),{title:"Activity Logs"}),a(x,null,{default:l(()=>[t("div",L,[T,t("div",V,[z,t("input",{id:"search-field",onInput:d[0]||(d[0]=s=>p(s.target.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),t("div",A,[t("div",E,[t("div",S,[t("div",I,[t("table",$,[j,e.data.data&&e.data.data.length>0?(o(),i("tbody",D,[(o(!0),i(c,null,B(e.data.data,(s,W)=>(o(),i("tr",{key:s.id,class:""},[t("td",O,r(s.log_name),1),t("td",F,r(s.event),1),t("td",P,r(s.properties),1),t("td",H,[t("div",R,[a(u,{align:"right",width:"48"},{trigger:l(()=>[U]),content:l(()=>[Y]),_:1})])])]))),128))])):(o(),i("tbody",Z,G))])]),e.data.data&&e.data.data.length>0?(o(),M(w,{key:0,class:"mt-6",links:e.data.links},null,8,["links"])):N("",!0)])])]),a(y,{show:g.value,onClose:n.closeModal},{default:l(()=>[t("div",J,[K,t("div",Q,[a(f,{onClick:n.closeModal},{default:l(()=>[m(" Cancel ")]),_:1},8,["onClick"]),a(_,{class:"ml-3",onClick:n.deleteUser},{default:l(()=>[m(" Delete ")]),_:1},8,["onClick"])])])]),_:1},8,["show","onClose"])]),_:1})],64))}};export{it as default};
