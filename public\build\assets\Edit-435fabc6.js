import{r as w,j as q,l as M,m as te,o as u,c as m,a as l,u as n,w as P,F as j,Z as oe,b as a,t as f,f as y,d as ae,n as h,k as se,v as ne,i as H,g as le,T as ie}from"./app-4f4c883b.js";import{_ as re,a as de}from"./AdminLayout-d9d2bc31.js";import{_ as g}from"./InputLabel-468796e0.js";import{P as ue}from"./PrimaryButton-3e579b0b.js";import{_ as V}from"./TextInput-21f4f57b.js";import{_ as me}from"./TextArea-b7098398.js";import{_ as ce}from"./RadioButton-ef33c90c.js";import{_ as E}from"./SearchableDropdown-ace42120.js";import{u as pe}from"./index-20fd5540.js";/* empty css                                                                          */import{_ as _e}from"./Checkbox-731cb89b.js";import"./_plugin-vue_export-helper-c27b6911.js";const ve={class:"h-screen animate-top"},ye={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},fe={class:"sm:flex sm:items-center"},ge=a("div",{class:"sm:flex-auto"},[a("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Payment")],-1),he={class:"flex items-center justify-between"},xe={key:0,class:"text-base font-semibold leading-6 text-gray-900"},be=["onSubmit"],ke={class:"border-b border-gray-900/10 pb-12"},we={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},Ve={class:"sm:col-span-3"},Fe={class:"relative mt-2"},Ne={class:"sm:col-span-3"},ze={class:"relative mt-2"},Ae={class:"sm:col-span-2 hidden"},Ce={class:"relative mt-2"},$e={key:0,class:"sm:col-span-3"},Se={class:"relative mt-2"},Ue={key:1,class:"sm:col-span-3"},Te={key:2,class:"sm:col-span-2"},Pe={key:3,class:"sm:col-span-1"},Ee={key:4,class:"sm:col-span-1"},De={key:5,class:"sm:col-span-1"},Oe={key:6,class:"sm:col-span-3"},Be={key:7,class:"sm:col-span-2"},Me={class:"mt-4 flex justify-start"},je={class:"text-base font-semibold"},Ie={key:8,class:"sm:col-span-2"},Le={key:9,class:"sm:col-span-2"},Re={key:10,class:"sm:col-span-2"},Ye={class:"relative mt-2"},qe={key:11,class:"sm:col-span-3"},He={class:"sm:col-span-6"},Ze={class:"overflow-x-auto divide-y divide-gray-300 w-full"},Ge=a("div",{class:"w-full"},[a("thead",{class:"w-full"},[a("tr",{class:""},[a("th",{scope:"col",class:""}),a("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),a("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),a("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),a("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),a("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),a("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1),Je={style:{"overflow-y":"auto","max-height":"318px"}},Ke={class:"divide-y divide-gray-300 bg-white"},Qe={class:"whitespace-nowrap px-2 text-sm text-gray-900"},We={class:"text-sm text-gray-900 leading-6 py-1.5"},Xe={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},et={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},tt={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},ot={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},at={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},st={key:0,class:"text-red-500 text-xs absolute"},nt={class:"whitespace-nowrap px-2 text-sm text-gray-900"},lt={class:"sm:col-span-2"},it={class:"mt-4 flex justify-start"},rt={class:"text-base font-semibold"},dt={key:0,class:"text-red-500 text-xs absolute"},ut={key:12,class:"sm:col-span-6"},mt={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},ct=a("thead",null,[a("tr",null,[a("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),a("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),a("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),a("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1),pt={class:"divide-y divide-gray-300 bg-white"},_t={class:"whitespace-nowrap py-3 text-sm text-gray-900"},vt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},yt={class:"flex flex-col"},ft={class:"text-sm text-gray-900"},gt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ht={class:"whitespace-nowrap py-3 text-sm text-gray-900"},xt={class:"flex mt-6 items-center justify-between"},bt={class:"ml-auto flex items-center justify-end gap-x-6"},kt=a("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),wt={key:0,class:"text-sm text-gray-600"},Dt={__name:"Edit",props:["payment","paymentType","bankinfo","organization","customers","invoices","credit"],setup(S){const r=S;w([]);const D=w([]),Z=r.bankinfo.filter(s=>s.organization_id===r.payment.organization_id);D.value=Z;const F=w(r.payment.payment_type),t=pe("post","/receipt",{id:r.payment.id,organization_id:r.payment.organization_id,customer_id:r.payment.customer_id,payment_type:r.payment.payment_type,date:r.payment.date,note:r.payment.note,amount:r.payment.amount,tds_amount:r.payment.tds_amount||0,discount_amount:r.payment.discount_amount||0,round_off:r.payment.round_off||0,check_number:r.payment.check_number,bank_name:r.payment.bank_name,org_bank_id:r.payment.org_bank_id,invoice:[],settled_amount:"",advance_amount:"",is_credit:"No",credit_data:[],total_unused_amount:""}),G=()=>{t.settled_amount=O.value,t.advance_amount=L.value,t.total_unused_amount=N.value,t.is_credit=p.value,t.invoice=k.value,t.credit_data=b.value,t.submit({preserveScroll:!0,onSuccess:()=>{}})},J=(s,o)=>{F.value=s,t.payment_type=s,t.errors.payment_type=null,o==="Cash"?t.note="Cash":t.note==="Cash"&&(t.note="")},A=w([]),b=w([]),N=w(""),I=(s,o)=>{const e=r.bankinfo.filter(d=>d.organization_id===s);D.value=e;const c=r.invoices.filter(d=>d.organization_id===s&&d.customer_id===t.customer_id);A.value=c;const i=r.credit.filter(d=>d.organization_id===s&&d.customer_id===t.customer_id);b.value=i,N.value=b.value.reduce((d,x)=>d+x.unused_amount,0),t.organization_id=s,t.errors.organization_id=null},K=(s,o)=>{const e=r.invoices.filter(i=>i.customer_id===s&&i.organization_id===t.organization_id);A.value=e;const c=r.credit.filter(i=>i.customer_id===s&&i.organization_id===t.organization_id);b.value=c,N.value=b.value.reduce((i,d)=>i+d.unused_amount,0),t.customer_id=s,t.errors.customer_id=null},Q=(s,o)=>{t.org_bank_id=s,t.errors.org_bank_id=null},O=q(()=>k.value.reduce((s,o)=>{if(o.check&&o.amount){const e=parseFloat(o.amount);return o.invoice_type==="sales"?s+e:s-e}return s},0)),L=q(()=>{const s=parseFloat(t.amount||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0),o=parseFloat(t.round_off||0),e=O.value;return s-e-o}),B=()=>{},C=s=>{let o=s.toFixed(2).toString(),[e,c]=o.split("."),i=e.substring(e.length-3),d=e.substring(0,e.length-3);return d!==""&&(i=","+i),`${d.replace(/\B(?=(\d{2})+(?!\d))/g,",")+i}.${c}`},R=s=>{const o=new Date(s),e={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",e)},p=w("No"),W=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],X=s=>{const o=p.value==="Yes"?parseFloat(N.value||0):parseFloat(t.amount||0)+parseFloat(t.round_off||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0);if(!k.value[s].check){k.value[s].amount=0;return}const e=k.value.filter(v=>v.check),c=e.some(v=>v.invoice_type==="sales"),i=e.some(v=>v.invoice_type==="purchase");if(c&&i&&e.length>1&&ee(o,e))return;let d=o;k.value.forEach((v,T)=>{if(v.check&&T!==s&&parseFloat(v.amount||0)>0){const $=parseFloat(v.amount||0);v.invoice_type==="sales"?d-=$:v.invoice_type==="purchase"&&(d+=$)}});const x=parseFloat(k.value[s].pending_amount||0),_=Math.min(x,Math.max(0,d));k.value[s].amount=_.toFixed(2)},ee=(s,o)=>{const e=o.filter(_=>_.invoice_type==="sales"),c=o.filter(_=>_.invoice_type==="purchase"),i=e.reduce((_,v)=>_+parseFloat(v.pending_amount||0),0),d=c.reduce((_,v)=>_+parseFloat(v.pending_amount||0),0),x=i-d;return Math.abs(s-Math.abs(x))<=1?(o.forEach(_=>{_.amount=parseFloat(_.pending_amount||0).toFixed(2)}),!0):x>0&&s>=x?(o.forEach(_=>{_.amount=parseFloat(_.pending_amount||0).toFixed(2)}),!0):!1},k=w([]),U=()=>{k.value=A.value.map(s=>{const o=s.invoice_type||"sales",e=r.payment.invoice_data.some(i=>i.id===s.id&&(i.invoice_type||"sales")===o),c=e?r.payment.invoice_data.find(i=>i.id===s.id&&(i.invoice_type||"sales")===o).amount:0;return{id:s.id,date:s.date,invoice_no:s.invoice_no,total_amount:parseFloat(s.total_amount||0).toFixed(2),pending_amount:parseFloat(s.original_pending_amount||s.pending_amount||0).toFixed(2),invoice_type:o,check:e,amount:e?c.toString():"0.00"}})};M(A,()=>{U()}),M(p,()=>{U()}),M(()=>t.amount,()=>{p.value==="No"&&U()});const z=s=>{t.errors[s]=null,t.errors.settled_amount=null};return te(()=>{I(r.payment.organization_id);const s=r.invoices.filter(e=>e.organization_id===r.payment.organization_id&&e.customer_id===r.payment.customer_id);A.value=s,U();const o=r.credit.filter(e=>e.organization_id===r.payment.organization_id&&e.customer_id===r.payment.customer_id);b.value=o,N.value=b.value.reduce((e,c)=>e+c.unused_amount,0)}),(s,o)=>(u(),m(j,null,[l(n(oe),{title:"Edit Receipt"}),l(re,null,{default:P(()=>[a("div",ve,[a("div",ye,[a("div",fe,[ge,a("div",he,[b.value.length>0?(u(),m("div",xe," Credits Available: ₹"+f(C(N.value)),1)):y("",!0)])]),a("form",{onSubmit:ae(G,["prevent"]),class:""},[a("div",ke,[a("div",we,[a("div",Ve,[l(g,{for:"payment_type",value:"Organization"}),a("div",Fe,[l(E,{options:S.organization,modelValue:n(t).organization_id,"onUpdate:modelValue":o[0]||(o[0]=e=>n(t).organization_id=e),onOnchange:I,class:h({"error rounded-md":n(t).errors.organization_id})},null,8,["options","modelValue","class"])])]),a("div",Ne,[l(g,{for:"payment_type",value:"Customer"}),a("div",ze,[l(E,{options:S.customers,modelValue:n(t).customer_id,"onUpdate:modelValue":o[1]||(o[1]=e=>n(t).customer_id=e),onOnchange:K,class:h({"error rounded-md":n(t).errors.customer_id})},null,8,["options","modelValue","class"])])]),a("div",Ae,[l(g,{for:"role_id",value:"Payment Through Credit ?"}),a("div",Ce,[l(ce,{modelValue:p.value,"onUpdate:modelValue":o[2]||(o[2]=e=>p.value=e),options:W},null,8,["modelValue"])])]),p.value=="No"?(u(),m("div",$e,[l(g,{for:"payment_type",value:"Payment Type"}),a("div",Se,[l(E,{options:S.paymentType,modelValue:n(t).payment_type,"onUpdate:modelValue":o[3]||(o[3]=e=>n(t).payment_type=e),onOnchange:J,class:h({"error rounded-md":n(t).errors.payment_type})},null,8,["options","modelValue","class"])])])):y("",!0),p.value=="No"?(u(),m("div",Ue,[l(g,{for:"date",value:"Payment Date"}),se(a("input",{"onUpdate:modelValue":o[4]||(o[4]=e=>n(t).date=e),class:h(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":n(t).errors.date}]),type:"date",onChange:o[5]||(o[5]=e=>z("date"))},null,34),[[ne,n(t).date]])])):y("",!0),p.value=="No"?(u(),m("div",Te)):y("",!0),p.value=="No"?(u(),m("div",Pe,[l(g,{for:"tds_amount",value:"TDS Amount"}),l(V,{type:"text",onChange:o[6]||(o[6]=e=>z("tds_amount")),onInput:o[7]||(o[7]=e=>B()),modelValue:n(t).tds_amount,"onUpdate:modelValue":o[8]||(o[8]=e=>n(t).tds_amount=e),class:h({"error rounded-md":n(t).errors.tds_amount})},null,8,["modelValue","class"])])):y("",!0),p.value=="No"?(u(),m("div",Ee,[l(g,{for:"discount_amount",value:"Discount Amount"}),l(V,{type:"text",onChange:o[9]||(o[9]=e=>z("discount_amount")),onInput:o[10]||(o[10]=e=>B()),modelValue:n(t).discount_amount,"onUpdate:modelValue":o[11]||(o[11]=e=>n(t).discount_amount=e),class:h({"error rounded-md":n(t).errors.discount_amount})},null,8,["modelValue","class"])])):y("",!0),p.value=="No"?(u(),m("div",De,[l(g,{for:"round_off",value:"Round Off"}),l(V,{type:"text",onChange:o[12]||(o[12]=e=>z("round_off")),modelValue:n(t).round_off,"onUpdate:modelValue":o[13]||(o[13]=e=>n(t).round_off=e),class:h({"error rounded-md":n(t).errors.round_off})},null,8,["modelValue","class"])])):y("",!0),p.value=="No"?(u(),m("div",Oe,[l(g,{for:"amount",value:"Amount"}),l(V,{id:"amount",type:"text",onChange:o[14]||(o[14]=e=>z("amount")),onInput:o[15]||(o[15]=e=>B()),modelValue:n(t).amount,"onUpdate:modelValue":o[16]||(o[16]=e=>n(t).amount=e),class:h({"error rounded-md":n(t).errors.amount})},null,8,["modelValue","class"])])):y("",!0),p.value=="No"?(u(),m("div",Be,[l(g,{for:"advance",value:"Advance(Ref) Amount"}),a("div",Me,[a("p",je,f(C(L.value)),1)])])):y("",!0),F.value=="check"||F.value=="Cheque"&&p.value=="No"?(u(),m("div",Ie,[l(g,{for:"check_number",value:"Cheque Number"}),l(V,{id:"check_number",type:"text",modelValue:n(t).check_number,"onUpdate:modelValue":o[17]||(o[17]=e=>n(t).check_number=e),class:h({"error rounded-md":n(t).errors["data.check_number"]})},null,8,["modelValue","class"])])):y("",!0),F.value=="check"&&p.value=="No"?(u(),m("div",Le,[l(g,{for:"bank_name",value:"Bank Name"}),l(V,{id:"bank_name",type:"text",modelValue:n(t).bank_name,"onUpdate:modelValue":o[18]||(o[18]=e=>n(t).bank_name=e),class:h({"error rounded-md":n(t).errors["data.bank_name"]})},null,8,["modelValue","class"])])):y("",!0),F.value!="cash"&&p.value=="No"?(u(),m("div",Re,[l(g,{for:"org_bank_id",value:"Our Bank"}),a("div",Ye,[l(E,{options:D.value,modelValue:n(t).org_bank_id,"onUpdate:modelValue":o[19]||(o[19]=e=>n(t).org_bank_id=e),onOnchange:Q,class:h({"error rounded-md":n(t).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):y("",!0),F.value!="cash"&&p.value=="No"?(u(),m("div",qe)):y("",!0),a("div",He,[a("table",Ze,[Ge,a("div",Je,[a("tbody",Ke,[(u(!0),m(j,null,H(k.value,(e,c)=>(u(),m("tr",{key:c},[a("td",Qe,[a("div",We,[l(_e,{name:"check",checked:e.check,"onUpdate:checked":i=>e.check=i,onChange:i=>X(c)},null,8,["checked","onUpdate:checked","onChange"])])]),a("td",Xe,[a("span",{class:h([e.invoice_type==="sales"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},f(e.invoice_type==="sales"?"SALES":"PURCHASE"),3)]),a("td",et,f(e.invoice_no),1),a("td",tt,f(e.total_amount),1),a("td",ot,f(e.pending_amount),1),a("td",at,[l(V,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":i=>e.amount=i,onChange:i=>z("invoice."+c+".amount"),class:h({error:n(t).errors[`invoice.${c}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),n(t).errors[`invoice.${c}.amount`]?(u(),m("p",st,f(n(t).errors[`invoice.${c}.amount`]),1)):y("",!0)]),a("td",nt,f(R(e.date)),1)]))),128))])])])]),a("div",lt,[l(g,{for:"note",value:"Total Settled Amount"}),a("div",it,[a("p",rt,f(C(O.value)),1)]),n(t).errors.settled_amount?(u(),m("p",dt,f(n(t).errors.settled_amount),1)):y("",!0)]),p.value=="No"?(u(),m("div",ut,[l(g,{for:"note",value:"Note"}),l(me,{id:"note",type:"text",rows:2,modelValue:n(t).note,"onUpdate:modelValue":o[20]||(o[20]=e=>n(t).note=e)},null,8,["modelValue"])])):y("",!0)]),b.value.length>0&&p.value=="Yes"?(u(),m("table",mt,[ct,a("tbody",pt,[(u(!0),m(j,null,H(b.value,(e,c)=>{var i,d,x,_,v,T,$,Y;return u(),m("tr",{key:c},[a("td",_t,f(R(e.date)),1),a("td",vt,[a("div",yt,[a("div",ft,f((d=(i=e.paymentreceive)==null?void 0:i.bank_info)!=null&&d.bank_name?(_=(x=e.paymentreceive)==null?void 0:x.bank_info)==null?void 0:_.bank_name:"Cash")+" - "+f((T=(v=e.paymentreceive)==null?void 0:v.bank_info)!=null&&T.account_number?(Y=($=e.paymentreceive)==null?void 0:$.bank_info)==null?void 0:Y.account_number:""),1)])]),a("td",gt,f(C(e.amount)),1),a("td",ht,f(C(e.unused_amount)),1)])}),128))])])):y("",!0)]),a("div",xt,[a("div",bt,[l(de,{href:s.route("receipt.index")},{svg:P(()=>[kt]),_:1},8,["href"]),l(ue,{disabled:n(t).processing},{default:P(()=>[le("Update")]),_:1},8,["disabled"]),l(ie,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:P(()=>[n(t).recentlySuccessful?(u(),m("p",wt,"Saved.")):y("",!0)]),_:1})])])],40,be)])])]),_:1})],64))}};export{Dt as default};
