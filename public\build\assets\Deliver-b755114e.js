import{j as P,o as m,c as _,a as n,u as o,w as f,F as y,Z as D,b as e,t as s,g as h,f as b,i as V,d as q,n as $,s as C,x as N}from"./app-16701445.js";import{_ as k}from"./AdminLayout-e15be38d.js";import{_ as I}from"./CreateButton-cebe4e7b.js";import{P as B}from"./PrimaryButton-eddb8b77.js";import{_ as w}from"./TextInput-764e3400.js";import{_ as O}from"./InputLabel-d69efee6.js";import{u as T}from"./index-10107770.js";import{_ as U}from"./_plugin-vue_export-helper-c27b6911.js";const i=t=>(C("data-v-b4117f70"),t=t(),N(),t),E=["onSubmit"],F={class:"animate-top"},z={class:"sm:flex sm:items-center"},L=i(()=>e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Deliver Order")],-1)),Q={class:"flex items-center space-x-4"},Y={class:"text-sm font-semibold text-gray-900"},G={class:"flex justify-end w-20"},M={class:"mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},Z={class:"inline-flex items-start space-x-6 justify-start w-full"},A={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},H={class:"inline-flex items-center justify-start w-full space-x-2"},J=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Customer:",-1)),K={class:"text-sm leading-6 text-gray-700"},R={class:"inline-flex items-center justify-start w-full space-x-2"},W=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"GST No:",-1)),X={class:"text-sm leading-6 text-gray-700"},ee={class:"inline-flex items-center justify-start w-full space-x-2"},te=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Email:",-1)),se={class:"text-sm leading-6 text-gray-700"},ae={class:"inline-flex items-center justify-start w-full space-x-2"},ie=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1)),oe={class:"text-sm leading-6 text-gray-700"},de={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},le={class:"inline-flex items-center justify-start w-full space-x-2"},re=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Order Number:",-1)),ne={class:"text-sm leading-6 text-gray-700"},ce={class:"inline-flex items-center justify-start w-full space-x-2"},me=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Order Date:",-1)),_e={class:"text-sm leading-6 text-gray-700"},ue={class:"inline-flex items-center justify-start w-full space-x-2"},xe=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Category:",-1)),fe={class:"text-sm leading-6 text-gray-700"},ve={class:"inline-flex items-center justify-start w-full space-x-2"},pe=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Sales Person:",-1)),ge={class:"text-sm leading-6 text-gray-700"},ye={class:"sm:col-span-6 mb-2"},he={class:"flex items-center space-x-2"},be={key:0,class:"text-red-600 text-sm mt-1"},we={class:"mt-6 bg-white p-4 shadow sm:p-8 sm:rounded-lg border divide-y divide-gray-300"},je=i(()=>e("div",{class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10 pb-2"},[e("div",{class:"sm:col-span-3"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Product")]),e("div",{class:"sm:col-span-2"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"QTY")]),e("div",{class:"sm:col-span-2"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Delivered Product")]),e("div",{class:"sm:col-span-3"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Deliver QTY")])],-1)),Se={class:"sm:col-span-3"},Pe={class:"text-sm leading-5 text-gray-700"},De={class:"sm:col-span-2"},Ve={class:"text-sm leading-5 text-gray-700"},qe={class:"sm:col-span-2"},$e={class:"text-sm leading-5 text-gray-700"},Ce={class:"sm:col-span-3 mb-2"},Ne={key:0,class:"text-red-500 text-xs"},ke={class:"flex mt-6 items-center justify-between"},Ie={class:"ml-auto flex items-center justify-end gap-x-6"},Be={__name:"Deliver",props:["data","order_deliver_number"],setup(t){var p;const u=t,x=P(()=>u.data[0].pending_order_details.map(l=>({delivered_qty:"",order_details_id:l.id,order_id:u.data[0].id,order_deliver_number:u.order_deliver_number}))),a=T("post","/saveorderdeliver",{invoice_number:"",deliveredProduct:[],order_id:(p=u.value)==null?void 0:p.id}),j=()=>{a.deliveredProduct=x.value,a.submit({preserveScroll:!0,onSuccess:()=>a.reset()})},v=l=>{a.errors[l]=null},S=l=>{const c=new Date(l),d={year:"numeric",month:"short",day:"numeric"};return c.toLocaleDateString("en-US",d)};return(l,c)=>(m(),_(y,null,[n(o(D),{title:"Orders"}),n(k,null,{default:f(()=>[e("form",{onSubmit:q(j,["prevent"]),class:""},[e("div",F,[e("div",z,[L,e("div",Q,[e("div",null,[e("p",Y,s(t.data[0].organization.name),1)]),e("div",G,[n(I,{href:l.route("orders.index")},{default:f(()=>[h(" Back ")]),_:1},8,["href"])])])]),e("div",M,[e("div",Z,[e("div",A,[e("div",H,[J,e("p",K,s(t.data[0].customers.customer_name??"-"),1)]),e("div",R,[W,e("p",X,s(t.data[0].customers.gst_no??"-"),1)]),e("div",ee,[te,e("p",se,s(t.data[0].customers.email??"-"),1)]),e("div",ae,[ie,e("p",oe,s(t.data[0].customers.contact_no??"-"),1)])]),e("div",de,[e("div",le,[re,e("p",ne,s(t.data[0].order_number??"-"),1)]),e("div",ce,[me,e("p",_e,s(S(t.data[0].date)??"-"),1)]),e("div",ue,[xe,e("p",fe,s(t.data[0].category??"-"),1)]),e("div",ve,[pe,e("p",ge,s(t.data[0].users.first_name??"-")+" "+s(t.data[0].users.last_name??"-"),1)]),e("div",ye,[e("div",he,[n(O,{for:"invoice_number",value:"Invoice Number:"}),n(w,{id:"invoice_number",type:"text",onInput:c[0]||(c[0]=d=>v("invoice_number")),modelValue:o(a).invoice_number,"onUpdate:modelValue":c[1]||(c[1]=d=>o(a).invoice_number=d),class:"flex-1"},null,8,["modelValue"])]),o(a).errors.invoice_number?(m(),_("div",be,s(o(a).errors.invoice_number),1)):b("",!0)])])])]),e("div",we,[je,(m(!0),_(y,null,V(t.data[0].pending_order_details,(d,r)=>(m(),_("div",{class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10 items-center",key:r},[e("div",Se,[e("p",Pe,s(d.product.name),1)]),e("div",De,[e("p",Ve,s(d.qty),1)]),e("div",qe,[e("p",$e,s(d.delivered_qty),1)]),e("div",Ce,[n(w,{id:"gst",type:"text",numeric:!0,modelValue:x.value[r].delivered_qty,"onUpdate:modelValue":g=>x.value[r].delivered_qty=g,autocomplete:"delivered_qty",onChange:g=>v("deliveredProduct."+r+".delivered_qty"),class:$({error:o(a).errors[`deliveredProduct.${r}.delivered_qty`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),o(a).errors[`deliveredProduct.${r}.delivered_qty`]?(m(),_("p",Ne,s(o(a).errors[`deliveredProduct.${r}.delivered_qty`]),1)):b("",!0)])]))),128))]),e("div",ke,[e("div",Ie,[n(B,{disabled:o(a).processing},{default:f(()=>[h("Submit")]),_:1},8,["disabled"])])])])],40,E)]),_:1})],64))}},Ye=U(Be,[["__scopeId","data-v-b4117f70"]]);export{Ye as default};
