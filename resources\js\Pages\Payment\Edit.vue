<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SvgLink from '@/Components/ActionLink.vue';
import RadioButton from '@/Components/RadioButton.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import { useForm } from 'laravel-precognition-vue-inertia';
import CheckboxWithLabel from '@/Components/CheckboxWithLabel.vue';
import Checkbox from '@/Components/Checkbox.vue';
import { defineProps, ref, computed, watch, onMounted } from 'vue';
import { Head } from '@inertiajs/vue3';

const props = defineProps(['payment', 'paymentType', 'bankinfo', 'organization', 'companies', 'invoices', 'credit']);

const checkedValues = ref([]);
const bankinfo = ref([]);
const bank = props.bankinfo.filter(bank => bank.organization_id === props.payment.organization_id);
bankinfo.value = bank;

const form = useForm('post', '/payment', {
    id: props.payment.id,
    organization_id: props.payment.organization_id,
    company_id: props.payment.company_id,
    payment_type: props.payment.payment_type,
    date: props.payment.date,
    note: props.payment.note,
    amount: props.payment.amount,
    discount_amount: props.payment.discount_amount || 0,
    round_off: props.payment.round_off || 0,
    check_number: props.payment.check_number,
    org_bank_id: props.payment.org_bank_id,
    invoice: [],
    settled_amount: '',
    advance_amount: '',
    is_credit: 'No',
    credit_data: [],
    total_unused_amount: ''
});

// Add _method field for Laravel to recognize this as a PUT request
form._method = 'PUT';

const payment_type = ref(props.payment.payment_type);

const submit = () => {
    form.settled_amount = settledAmount.value;
    form.advance_amount = advanceAmount.value;
    form.total_unused_amount = totalUnusedAmount.value;
    form.is_credit = creditOption.value,
    form.invoice = invoice.value;
    form.credit_data = creditData.value;
    form.submit({
        preserveScroll: true,
        onSuccess: () => {
            // Redirect handled by controller
        },
    });
};

const setPaymentType = (id, name) => {
    payment_type.value = id;
    form.payment_type = id;
    form.errors.payment_type = null;
    if (name === "Cash") {
        form.note = "Cash";
    } else if (form.note === "Cash") {
        form.note = "";
    }
};

const pendinginvoice = ref([]);
const creditData = ref([]);
const totalUnusedAmount = ref('');

const setOrganization = (id, name) => {
    const bank = props.bankinfo.filter(bank => bank.organization_id === id);
    bankinfo.value = bank;
    const finalInvoice = props.invoices.filter(invoice =>
        invoice.purchase_order &&
        invoice.purchase_order.organization_id === id &&
        invoice.purchase_order.company_id === form.company_id
    );
    pendinginvoice.value = finalInvoice;
    const credit = props.credit.filter(credit => credit.organization_id === id && credit.company_id === form.company_id);
    creditData.value = credit
    totalUnusedAmount.value = creditData.value.reduce((sum, item) => sum + item.unused_amount, 0);
    form.organization_id = id;
    form.errors.organization_id = null;
};

const setCustomer = (id, name) => {
    form.company_id = id;
    const finalInvoice = props.invoices.filter(invoice =>
        invoice.purchase_order &&
        invoice.purchase_order.organization_id === form.organization_id &&
        invoice.purchase_order.company_id === id
    );
    pendinginvoice.value = finalInvoice;
    const credit = props.credit.filter(credit => credit.company_id === id && credit.organization_id === form.organization_id);
    creditData.value = credit
    totalUnusedAmount.value = creditData.value.reduce((sum, item) => sum + item.unused_amount, 0);
    form.errors.company_id = null;
};

const setBankInfo = (id, name) => {
    form.org_bank_id = id;
    form.errors.org_bank_id = null;
};

const updateChecked = (newCheckedValues) => {
    const filteredInvoices = props.invoices.filter(invoice => newCheckedValues.includes(invoice.id));
    const totalAmount = filteredInvoices.reduce((sum, invoice) => sum + invoice.total_amount, 0);
    settledAmount.value = totalAmount;
    checkedValues.value = newCheckedValues;
};

const settledAmount = computed(() => {
    const total = invoice.value.reduce((total, product) => {
        if (product.check && product.amount) {
            const amount = parseFloat(product.amount);
            // For purchase invoices, add to total (debit - money we pay)
            // For sales invoices, subtract from total (credit - money they owe us)
            return product.invoice_type === 'purchase'
                ? total + amount
                : total - amount;
        }
        return total;
    }, 0);
    return parseFloat(total.toFixed(2));
});

const advanceAmount = computed(() => {
    const totalEntered = parseFloat(form.amount || 0);
    const roundOff = parseFloat(form.round_off || 0);
    const settled = settledAmount.value;
    return totalEntered > settled ? (totalEntered - settled - roundOff) : 0;
});

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
    let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const changeEvent = (event, index) => {
    // Calculate total available amount
    const totalAvailableAmount =
        creditOption.value === 'Yes'
            ? parseFloat(totalUnusedAmount.value || 0)
            : (parseFloat(form.amount || 0) + parseFloat(form.round_off || 0));

    // If unchecked, reset amount to 0
    if (!invoice.value[index].check) {
        invoice.value[index].amount = 0;
        return;
    }

    // Check if this is a net settlement scenario (both purchase and sales invoices are checked)
    const checkedInvoices = invoice.value.filter(inv => inv.check);
    const hasPurchase = checkedInvoices.some(inv => inv.invoice_type === 'purchase');
    const hasSales = checkedInvoices.some(inv => inv.invoice_type === 'sales');

    // If we have both types, try net settlement
    if (hasPurchase && hasSales && checkedInvoices.length > 1) {
        const netSettlementApplied = handleNetSettlement(totalAvailableAmount, checkedInvoices);
        if (netSettlementApplied) {
            return; // Net settlement was applied, we're done
        }
    }

    // Otherwise, use the enhanced individual allocation logic
    // Calculate remaining amount after accounting for NET effect of already allocated amounts
    let remainingAmount = totalAvailableAmount;

    // Calculate net effect of already allocated amounts
    invoice.value.forEach((item, i) => {
        if (item.check && i !== index && parseFloat(item.amount || 0) > 0) {
            const amount = parseFloat(item.amount || 0);
            if (item.invoice_type === 'purchase') {
                // Purchase invoices reduce available amount (money going out)
                remainingAmount -= amount;
            } else if (item.invoice_type === 'sales') {
                // Sales invoices increase available amount (money coming back)
                remainingAmount += amount;
            }
        }
    });

    // Apply remaining amount to the current invoice
    const pending = parseFloat(invoice.value[index].pending_amount || 0);
    const applied = Math.min(pending, Math.max(0, remainingAmount));
    invoice.value[index].amount = applied.toFixed(2);
};

const handleNetSettlement = (totalAvailableAmount, checkedInvoices) => {
    // Calculate total pending amounts for each type
    const purchaseInvoices = checkedInvoices.filter(inv => inv.invoice_type === 'purchase');
    const salesInvoices = checkedInvoices.filter(inv => inv.invoice_type === 'sales');

    const purchaseTotal = purchaseInvoices.reduce((sum, inv) => sum + parseFloat(inv.pending_amount || 0), 0);
    const salesTotal = salesInvoices.reduce((sum, inv) => sum + parseFloat(inv.pending_amount || 0), 0);

    // Calculate net amount (purchase - sales)
    const netAmount = purchaseTotal - salesTotal;

    // Perfect net settlement scenario
    if (Math.abs(totalAvailableAmount - Math.abs(netAmount)) <= 1) {
        // Perfect net settlement - settle all checked invoices to their full amounts
        checkedInvoices.forEach(inv => {
            inv.amount = parseFloat(inv.pending_amount || 0).toFixed(2);
        });
        return true; // Indicate that net settlement was applied
    }

    // Check if the available amount can cover the net settlement
    if (netAmount > 0 && totalAvailableAmount >= netAmount) {
        // We can do a full net settlement
        checkedInvoices.forEach(inv => {
            inv.amount = parseFloat(inv.pending_amount || 0).toFixed(2);
        });
        return true;
    }

    return false; // Use regular distribution
};

const invoice = ref([]);

const setupInvoiceList = () => {
    invoice.value = pendinginvoice.value.map(detail => {
        // Check if this invoice was part of the original payment (match by both ID and type)
        const invoiceType = detail.invoice_type || 'purchase';
        const isPaidInThisPayment = props.payment.invoice_data.some(
            paidInvoice => paidInvoice.id === detail.id && (paidInvoice.invoice_type || 'purchase') === invoiceType
        );

        const paidAmount = isPaidInThisPayment
            ? props.payment.invoice_data.find(paidInvoice =>
                paidInvoice.id === detail.id && (paidInvoice.invoice_type || 'purchase') === invoiceType
              ).amount
            : 0;

        return {
            id: detail.id,
            date: detail.customer_invoice_date || detail.date,
            invoice_no: detail.customer_invoice_no || detail.invoice_no,
            total_amount: parseFloat(detail.total_amount || 0).toFixed(2),
            pending_amount: parseFloat(detail.original_pending_amount || detail.pending_amount || 0).toFixed(2),
            invoice_type: invoiceType,
            check: isPaidInThisPayment,
            amount: isPaidInThisPayment ? paidAmount.toString() : '0.00',
        };
    });
};

const creditOption = ref('No');

watch(pendinginvoice, () => {
    setupInvoiceList();
});

watch(creditOption, () => {
    setupInvoiceList();
});

watch(() => form.amount, () => {
    if (creditOption.value === 'No') {
        setupInvoiceList();
    }
});

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
    form.errors.settled_amount = null;
};


const radioOptions = [
    { value: 'Yes', label: 'Yes' },
    { value: 'No', label: 'No' }
];

// Initialize data on component mount
onMounted(() => {
    // Set initial values based on the payment data
    setOrganization(props.payment.organization_id);

    // Initialize the invoice list
    const finalInvoice = props.invoices.filter(invoice =>
        invoice.purchase_order &&
        invoice.purchase_order.organization_id === props.payment.organization_id &&
        invoice.purchase_order.company_id === props.payment.company_id
    );
    pendinginvoice.value = finalInvoice;
    setupInvoiceList();

    // Initialize credit data
    const credit = props.credit.filter(credit =>
        credit.organization_id === props.payment.organization_id &&
        credit.company_id === props.payment.company_id
    );
    creditData.value = credit;
    totalUnusedAmount.value = creditData.value.reduce((sum, item) => sum + item.unused_amount, 0);

    // Set bank info
    const bank = props.bankinfo.filter(bank => bank.organization_id === props.payment.organization_id);
    bankinfo.value = bank;
});
</script>

<template>
    <Head title="Edit Payment" />
    <AdminLayout>
        <div class="h-screen animate-top">
        <div class="bg-white p-4 shadow sm:p-8 sm:rounded-lg border">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Edit Payment</h1>
                </div>
                <div class="flex items-center justify-between">
                    <div class="text-base font-semibold leading-6 text-gray-900" v-if="creditData.length > 0">
                        Credits Available:  ₹{{ formatAmount(totalUnusedAmount) }}
                    </div>
                </div>
            </div>
            <form @submit.prevent="submit" class="">
                <div class="border-b border-gray-900/10 pb-12">
                    <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8">
                        <div class="sm:col-span-3">
                            <InputLabel for="payment_type" value="Organization"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="organization"
                                    v-model="form.organization_id"
                                    @onchange="setOrganization"
                                    :class="{ 'error rounded-md': form.errors.organization_id }"
                                />
                            </div>
                        </div>
                        <div class="sm:col-span-3">
                            <InputLabel for="payment_type" value="Company"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="companies"
                                    v-model="form.company_id"
                                    @onchange="setCustomer"
                                    :class="{ 'error rounded-md': form.errors.company_id }"
                                />
                            </div>
                        </div>
                        <div class="sm:col-span-2 hidden">
                            <InputLabel for="role_id" value="Payment Through Credit ?" />
                            <div class="relative mt-2">
                                <RadioButton
                                    v-model="creditOption"
                                    :options="radioOptions"
                                />
                            </div>
                        </div>
                        <div v-if="creditOption =='No'" class="sm:col-span-3">
                            <InputLabel for="payment_type" value="Payment Type"/>
                            <div class="relative mt-2">
                                <SearchableDropdown :options="paymentType"
                                    v-model="form.payment_type"
                                    @onchange="setPaymentType"
                                    :class="{ 'error rounded-md': form.errors.payment_type }"
                                />
                            </div>
                        </div>
                        <div v-if="creditOption =='No'" class="sm:col-span-1">
                            <InputLabel for="round_off" value="Round Off" />
                            <TextInput
                                type="text"
                                @change="clearError('round_off')"
                                v-model="form.round_off"
                                :class="{ 'error rounded-md': form.errors.round_off }"
                            />
                        </div>
                        <div v-if="creditOption =='No'" class="sm:col-span-2">
                            <InputLabel for="amount" value="Amount" />
                            <TextInput
                                id="amount"
                                type="text"
                                @change="clearError('amount')"
                                v-model="form.amount"
                                :class="{ 'error rounded-md': form.errors.amount }"
                            />
                        </div>
                        <div v-if="creditOption =='No'" class="sm:col-span-2">
                            <InputLabel for="advance" value="Advance(Ref) Amount" />
                            <div class="mt-4 flex justify-start">
                                <p class="text-base font-semibold">{{ formatAmount(advanceAmount) }}</p>
                            </div>
                        </div>
                        <div v-if="payment_type == 'check' && creditOption =='No'" class="sm:col-span-3">
                            <InputLabel for="check_number" value="Cheque Number" />
                            <TextInput
                                id="check_number"
                                type="text"
                                v-model="form.check_number"
                                :class="{ 'error rounded-md': form.errors[`data.check_number`] }"
                            />
                        </div>
                        <div v-if="creditOption =='No'" class="sm:col-span-3">
                            <InputLabel for="date" value="Payment Date" />
                            <input
                                v-model="form.date"
                                class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                type="date"
                                @change="clearError('date')"
                                :class="{ 'error rounded-md': form.errors.date }"
                            />
                        </div>
                        <div v-if="payment_type == 'cash' && creditOption =='No'" class="sm:col-span-3">
                        </div>
                        <div v-if="payment_type != 'cash' && creditOption =='No'" class="sm:col-span-3">
                            <InputLabel for="org_bank_id" value="Our Bank" />
                            <div class="relative mt-2">
                                <SearchableDropdown :options="bankinfo"
                                v-model="form.org_bank_id"
                                @onchange="setBankInfo"
                                :class="{ 'error rounded-md': form.errors.org_bank_id }"
                                />
                            </div>
                        </div>

                        <div class="sm:col-span-6">
                              <table class="overflow-x-auto divide-y divide-gray-300 w-full">
                                    <div class="w-full">
                                        <thead class="w-full">
                                            <tr class="">
                                                <th scope="col" class="">
                                                </th>
                                                <th scope="col" class="whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"> Type </th>
                                                <th scope="col" class="whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"> Invoice No </th>
                                                <th scope="col" class="whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"> Total Amount (₹) </th>
                                                <th scope="col" class="whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"> Pending Amount (₹) </th>
                                                <th scope="col" class="whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"> Amount (₹)</th>
                                                <th scope="col" class="whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"> Date</th>
                                            </tr>
                                        </thead>
                                    </div>
                                    <div style="overflow-y: auto; max-height: 318px;">
                                    <tbody class="divide-y divide-gray-300 bg-white">
                                        <tr v-for="(product, index) in invoice" :key="index">
                                            <td class="whitespace-nowrap px-2 text-sm text-gray-900">
                                                <div class="text-sm text-gray-900 leading-6 py-1.5">
                                                    <Checkbox name="check" v-model:checked="product.check" @change="(event) => changeEvent(event, index)"/>
                                                </div>
                                            </td>
                                            <td class="whitespace-nowrap min-w-20 px-2 text-sm text-gray-900">
                                                <span :class="product.invoice_type === 'purchase' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'"
                                                      class="inline-flex items-center rounded-md px-2 py-1 text-xs font-medium">
                                                    {{ product.invoice_type === 'purchase' ? 'PURCHASE' : 'SALES' }}
                                                </span>
                                            </td>
                                            <td class="whitespace-nowrap min-w-32 px-2 text-sm text-gray-900">{{ product.invoice_no }}</td>
                                            <td class="whitespace-nowrap min-w-32 px-2 text-sm text-gray-900">{{ product.total_amount }}</td>
                                            <td class="whitespace-nowrap min-w-32 px-2 text-sm text-gray-900">{{ product.pending_amount }}</td>
                                            <td class="whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36">
                                                <TextInput
                                                    id="amount"
                                                    type="text"
                                                    v-model="product.amount"
                                                    @change="clearError('invoice.' + index + '.amount')"
                                                    :class="{ 'error': form.errors[`invoice.${index}.amount`] }"
                                                />
                                                <p v-if="form.errors[`invoice.${index}.amount`]" class="text-red-500 text-xs absolute">
                                                    {{ form.errors[`invoice.${index}.amount`] }}
                                                </p>
                                            </td>
                                            <td class="whitespace-nowrap px-2 text-sm text-gray-900">{{ formatDate(product.date) }}</td>
                                        </tr>
                                    </tbody>
                                    </div>
                              </table>
                        </div>
                        <div class="sm:col-span-2">
                            <InputLabel for="note" value="Total Settled Amount" />
                            <div class="mt-4 flex justify-start">
                                <p class="text-base font-semibold">{{ formatAmount(settledAmount) }}</p>
                            </div>
                             <p v-if="form.errors.settled_amount" class="text-red-500 text-xs absolute">
                                {{ form.errors.settled_amount }}
                            </p>
                        </div>
                        <div v-if="creditOption =='No'" class="sm:col-span-6">
                            <InputLabel for="note" value="Note" />
                            <TextArea
                                id="note"
                                type="text"
                                :rows="2"
                                v-model="form.note"
                            />
                        </div>
                    </div>
                    <table class="mt-5 overflow-x-auto divide-y divide-gray-300 w-full" v-if="creditData.length > 0 && creditOption =='Yes'">
                        <thead>
                            <tr>
                                <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Date</th>
                                <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Bank</th>
                                <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Credit Amount (₹)</th>
                                <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Available Credit (₹)</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-300 bg-white">
                            <tr v-for="(product, index) in creditData" :key="index">
                                <td class="whitespace-nowrap py-3 text-sm text-gray-900">{{ formatDate(product.date) }}</td>
                                <td class="whitespace-nowrap py-3 text-sm text-gray-900">
                                    <div class="flex flex-col">
                                        <div class="text-sm text-gray-900">
                                            {{ product.paymentpaid?.bank_info?.bank_name ? product.paymentpaid?.bank_info?.bank_name : 'Cash' }} -
                                            {{ product.paymentpaid?.bank_info?.account_number ? product.paymentpaid?.bank_info?.account_number : '' }}
                                        </div>
                                    </div>
                                </td>
                                <td class="whitespace-nowrap py-3 text-sm text-gray-900">{{ formatAmount(product.amount) }}</td>
                                <td class="whitespace-nowrap py-3 text-sm text-gray-900">{{ formatAmount(product.unused_amount) }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                    <SvgLink :href="route('payment.index')">
                          <template #svg>
                             <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
                          </template>
                    </SvgLink>

                    <PrimaryButton :disabled="form.processing">Update</PrimaryButton>

                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>
                </div>
            </form>
        </div>
        </div>
    </AdminLayout>
</template>
