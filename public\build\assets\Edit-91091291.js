import{r as b,j as q,l as j,m as X,o as m,c as u,a as r,u as i,w as O,F as E,Z as ee,b as t,t as c,f as y,d as te,n as v,k as oe,v as ae,i as B,g as ne,T as se}from"./app-b7a94f67.js";import{_ as ie,a as le}from"./AdminLayout-0f1fdf67.js";import{_ as h}from"./InputLabel-11b5d690.js";import{P as re}from"./PrimaryButton-4ffecd1c.js";import{_ as M}from"./TextInput-fea73171.js";import{_ as de}from"./TextArea-500c5ac8.js";import{_ as ce}from"./RadioButton-1b431749.js";import{_ as D}from"./SearchableDropdown-711fb977.js";import{u as me}from"./index-5a4eda7d.js";/* empty css                                                                          */import{_ as ue}from"./Checkbox-abe11c50.js";import"./_plugin-vue_export-helper-c27b6911.js";const pe={class:"h-screen animate-top"},_e={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},ye={class:"sm:flex sm:items-center"},ve=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Payment")],-1),fe={class:"flex items-center justify-between"},he={key:0,class:"text-base font-semibold leading-6 text-gray-900"},ge=["onSubmit"],xe={class:"border-b border-gray-900/10 pb-12"},be={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},we={class:"sm:col-span-3"},ke={class:"relative mt-2"},Ve={class:"sm:col-span-3"},ze={class:"relative mt-2"},Ae={class:"sm:col-span-2 hidden"},Fe={class:"relative mt-2"},Ce={key:0,class:"sm:col-span-3"},Ne={class:"relative mt-2"},Pe={key:1,class:"sm:col-span-1"},Se={key:2,class:"sm:col-span-2"},Ue={key:3,class:"sm:col-span-2"},$e={class:"mt-4 flex justify-start"},Te={class:"text-base font-semibold"},Oe={key:4,class:"sm:col-span-3"},Ee={key:5,class:"sm:col-span-3"},Me={key:6,class:"sm:col-span-3"},De={key:7,class:"sm:col-span-3"},je={class:"relative mt-2"},Be={class:"sm:col-span-6"},Ie={class:"overflow-x-auto divide-y divide-gray-300 w-full"},Le=t("div",{class:"w-full"},[t("thead",{class:"w-full"},[t("tr",{class:""},[t("th",{scope:"col",class:"w-8"}),t("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),t("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),t("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1),Re={style:{"overflow-y":"auto","max-height":"318px"}},Ye={class:"divide-y divide-gray-300 bg-white"},qe={class:"whitespace-nowrap px-2 text-sm text-gray-900"},He={class:"text-sm text-gray-900 leading-6 py-1.5"},Ze={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},Ge={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Je={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Ke={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Qe={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},We={key:0,class:"text-red-500 text-xs absolute"},Xe={class:"whitespace-nowrap px-2 text-sm text-gray-900"},et={class:"sm:col-span-2"},tt={class:"mt-2 p-3 bg-gray-50 rounded-md"},ot={class:"space-y-2 text-sm"},at={class:"flex items-center gap-2"},nt=t("hr",{class:"my-2"},null,-1),st={class:"flex justify-between items-center font-semibold"},it=t("span",null,"Settlement:",-1),lt={class:"flex justify-between items-center font-semibold"},rt=t("span",null,"Advance Amount:",-1),dt={key:0,class:"text-red-500 text-xs absolute"},ct={key:8,class:"sm:col-span-6"},mt={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},ut=t("thead",null,[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1),pt={class:"divide-y divide-gray-300 bg-white"},_t={class:"whitespace-nowrap py-3 text-sm text-gray-900"},yt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},vt={class:"flex flex-col"},ft={class:"text-sm text-gray-900"},ht={class:"whitespace-nowrap py-3 text-sm text-gray-900"},gt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},xt={class:"flex mt-6 items-center justify-between"},bt={class:"ml-auto flex items-center justify-end gap-x-6"},wt=t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),kt={key:0,class:"text-sm text-gray-600"},Et={__name:"Edit",props:["payment","paymentType","bankinfo","organization","companies","invoices","credit"],setup(N){const l=N;b([]);const P=b([]),H=l.bankinfo.filter(n=>n.organization_id===l.payment.organization_id);P.value=H;const o=me("post","/payment",{id:l.payment.id,organization_id:l.payment.organization_id,company_id:l.payment.company_id,payment_type:l.payment.payment_type,date:l.payment.date,note:l.payment.note,amount:l.payment.amount,discount_amount:l.payment.discount_amount||0,round_off:l.payment.round_off||0,check_number:l.payment.check_number,org_bank_id:l.payment.org_bank_id,invoice:[],settled_amount:"",advance_amount:"",is_credit:"No",credit_data:[],total_unused_amount:""});o._method="PUT";const S=b(l.payment.payment_type),Z=()=>{o.settled_amount=A.value,o.advance_amount=F.value,o.total_unused_amount=V.value,o.is_credit=_.value,o.invoice=f.value,o.credit_data=g.value,o.submit({preserveScroll:!0,onSuccess:()=>{}})},G=(n,a)=>{S.value=n,o.payment_type=n,o.errors.payment_type=null,a==="Cash"?o.note="Cash":o.note==="Cash"&&(o.note="")},z=b([]),g=b([]),V=b(""),I=(n,a)=>{const e=l.bankinfo.filter(p=>p.organization_id===n);P.value=e;const d=l.invoices.filter(p=>p.purchase_order&&p.purchase_order.organization_id===n&&p.purchase_order.company_id===o.company_id);z.value=d;const s=l.credit.filter(p=>p.organization_id===n&&p.company_id===o.company_id);g.value=s,V.value=g.value.reduce((p,k)=>p+k.unused_amount,0),o.organization_id=n,o.errors.organization_id=null},J=(n,a)=>{o.company_id=n;const e=l.invoices.filter(s=>s.purchase_order&&s.purchase_order.organization_id===o.organization_id&&s.purchase_order.company_id===n);z.value=e;const d=l.credit.filter(s=>s.company_id===n&&s.organization_id===o.organization_id);g.value=d,V.value=g.value.reduce((s,p)=>s+p.unused_amount,0),o.errors.company_id=null},K=(n,a)=>{o.org_bank_id=n,o.errors.org_bank_id=null},A=q(()=>{const n=f.value.reduce((a,e)=>{if(e.check&&e.amount){const d=parseFloat(e.amount);return e.invoice_type==="purchase"?a+d:a-d}return a},0);return parseFloat(n.toFixed(2))}),F=q(()=>{const n=parseFloat(o.amount||0),a=parseFloat(o.round_off||0),e=A.value;return n>e?n-e-a:0}),w=n=>{let a=n.toFixed(2).toString(),[e,d]=a.split("."),s=e.substring(e.length-3),p=e.substring(0,e.length-3);return p!==""&&(s=","+s),`${p.replace(/\B(?=(\d{2})+(?!\d))/g,",")+s}.${d}`},L=n=>{const a=new Date(n),e={year:"numeric",month:"short",day:"numeric"};return a.toLocaleDateString("en-US",e)},Q=(n,a)=>{if(!f.value[a].check){f.value[a].amount=0;return}const e=l.payment.invoice_data.find(d=>d.id===f.value[a].id&&(d.invoice_type||"purchase")===f.value[a].invoice_type);if(e)f.value[a].amount=parseFloat(e.amount).toFixed(2);else{let s=_.value==="Yes"?parseFloat(V.value||0):parseFloat(o.amount||0)+parseFloat(o.round_off||0);f.value.forEach((x,T)=>{if(x.check&&T!==a&&parseFloat(x.amount||0)>0){const C=parseFloat(x.amount||0);x.invoice_type==="purchase"?s-=C:x.invoice_type==="sales"&&(s+=C)}});const p=parseFloat(f.value[a].pending_amount||0),k=Math.min(p,Math.max(0,s));f.value[a].amount=k.toFixed(2)}},f=b([]),U=()=>{f.value=z.value.map(n=>{const a=n.invoice_type||"purchase",e=l.payment.invoice_data.some(s=>s.id===n.id&&(s.invoice_type||"purchase")===a),d=e?l.payment.invoice_data.find(s=>s.id===n.id&&(s.invoice_type||"purchase")===a).amount:0;return{id:n.id,date:n.customer_invoice_date||n.date,invoice_no:n.customer_invoice_no||n.invoice_no,total_amount:parseFloat(n.total_amount||0).toFixed(2),pending_amount:parseFloat(n.original_pending_amount||n.pending_amount||0).toFixed(2),invoice_type:a,check:e,amount:e?d.toString():"0.00"}})},_=b("No");j(z,()=>{U()}),j(_,()=>{U()}),j(()=>o.amount,()=>{_.value==="No"&&U()});const $=n=>{o.errors[n]=null,o.errors.settled_amount=null},W=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}];return X(()=>{I(l.payment.organization_id),z.value=l.invoices,U();const n=l.credit.filter(e=>e.organization_id===l.payment.organization_id&&e.company_id===l.payment.company_id);g.value=n,V.value=g.value.reduce((e,d)=>e+d.unused_amount,0);const a=l.bankinfo.filter(e=>e.organization_id===l.payment.organization_id);P.value=a}),(n,a)=>(m(),u(E,null,[r(i(ee),{title:"Edit Payment"}),r(ie,null,{default:O(()=>[t("div",pe,[t("div",_e,[t("div",ye,[ve,t("div",fe,[g.value.length>0?(m(),u("div",he," Credits Available: ₹"+c(w(V.value)),1)):y("",!0)])]),t("form",{onSubmit:te(Z,["prevent"]),class:""},[t("div",xe,[t("div",be,[t("div",we,[r(h,{for:"payment_type",value:"Organization"}),t("div",ke,[r(D,{options:N.organization,modelValue:i(o).organization_id,"onUpdate:modelValue":a[0]||(a[0]=e=>i(o).organization_id=e),onOnchange:I,class:v({"error rounded-md":i(o).errors.organization_id}),editMode:"editMode"},null,8,["options","modelValue","class"])])]),t("div",Ve,[r(h,{for:"payment_type",value:"Company"}),t("div",ze,[r(D,{options:N.companies,modelValue:i(o).company_id,"onUpdate:modelValue":a[1]||(a[1]=e=>i(o).company_id=e),onOnchange:J,class:v({"error rounded-md":i(o).errors.company_id}),editMode:"editMode"},null,8,["options","modelValue","class"])])]),t("div",Ae,[r(h,{for:"role_id",value:"Payment Through Credit ?"}),t("div",Fe,[r(ce,{modelValue:_.value,"onUpdate:modelValue":a[2]||(a[2]=e=>_.value=e),options:W},null,8,["modelValue"])])]),_.value=="No"?(m(),u("div",Ce,[r(h,{for:"payment_type",value:"Payment Type"}),t("div",Ne,[r(D,{options:N.paymentType,modelValue:i(o).payment_type,"onUpdate:modelValue":a[3]||(a[3]=e=>i(o).payment_type=e),onOnchange:G,class:v({"error rounded-md":i(o).errors.payment_type})},null,8,["options","modelValue","class"])])])):y("",!0),_.value=="No"?(m(),u("div",Pe,[r(h,{for:"round_off",value:"Round Off"}),r(M,{type:"text",onChange:a[4]||(a[4]=e=>$("round_off")),modelValue:i(o).round_off,"onUpdate:modelValue":a[5]||(a[5]=e=>i(o).round_off=e),class:v({"error rounded-md":i(o).errors.round_off})},null,8,["modelValue","class"])])):y("",!0),_.value=="No"?(m(),u("div",Se,[r(h,{for:"amount",value:"Amount"}),r(M,{id:"amount",type:"text",onChange:a[6]||(a[6]=e=>$("amount")),modelValue:i(o).amount,"onUpdate:modelValue":a[7]||(a[7]=e=>i(o).amount=e),class:v({"error rounded-md":i(o).errors.amount})},null,8,["modelValue","class"])])):y("",!0),_.value=="No"?(m(),u("div",Ue,[r(h,{for:"advance",value:"Advance(Ref) Amount"}),t("div",$e,[t("p",Te,c(w(F.value)),1)])])):y("",!0),S.value=="check"&&_.value=="No"?(m(),u("div",Oe,[r(h,{for:"check_number",value:"Cheque Number"}),r(M,{id:"check_number",type:"text",modelValue:i(o).check_number,"onUpdate:modelValue":a[8]||(a[8]=e=>i(o).check_number=e),class:v({"error rounded-md":i(o).errors["data.check_number"]})},null,8,["modelValue","class"])])):y("",!0),_.value=="No"?(m(),u("div",Ee,[r(h,{for:"date",value:"Payment Date"}),oe(t("input",{"onUpdate:modelValue":a[9]||(a[9]=e=>i(o).date=e),class:v(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":i(o).errors.date}]),type:"date",onChange:a[10]||(a[10]=e=>$("date"))},null,34),[[ae,i(o).date]])])):y("",!0),S.value=="cash"&&_.value=="No"?(m(),u("div",Me)):y("",!0),S.value!="cash"&&_.value=="No"?(m(),u("div",De,[r(h,{for:"org_bank_id",value:"Our Bank"}),t("div",je,[r(D,{options:P.value,modelValue:i(o).org_bank_id,"onUpdate:modelValue":a[11]||(a[11]=e=>i(o).org_bank_id=e),onOnchange:K,class:v({"error rounded-md":i(o).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):y("",!0),t("div",Be,[t("table",Ie,[Le,t("div",Re,[t("tbody",Ye,[(m(!0),u(E,null,B(f.value,(e,d)=>(m(),u("tr",{key:d},[t("td",qe,[t("div",He,[r(ue,{name:"check",checked:e.check,"onUpdate:checked":s=>e.check=s,onChange:s=>Q(s,d)},null,8,["checked","onUpdate:checked","onChange"])])]),t("td",Ze,[t("span",{class:v([e.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},c(e.invoice_type==="purchase"?"PURCHASE":"SALES"),3)]),t("td",Ge,c(e.invoice_no),1),t("td",Je,c(e.total_amount),1),t("td",Ke,c(e.pending_amount),1),t("td",Qe,[r(M,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":s=>e.amount=s,onChange:s=>$("invoice."+d+".amount"),class:v({error:i(o).errors[`invoice.${d}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),i(o).errors[`invoice.${d}.amount`]?(m(),u("p",We,c(i(o).errors[`invoice.${d}.amount`]),1)):y("",!0)]),t("td",Xe,c(L(e.date)),1)]))),128))])])])]),t("div",et,[r(h,{for:"note",value:"Net Settlement Summary"}),t("div",tt,[t("div",ot,[(m(!0),u(E,null,B(f.value.filter(e=>e.check),e=>(m(),u("div",{key:e.id,class:"flex justify-between items-center"},[t("div",at,[t("span",{class:v([e.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium"])},c(e.invoice_type==="purchase"?"P":"S"),3),t("span",null,c(e.invoice_no),1)]),t("span",{class:v([e.invoice_type==="purchase"?"text-blue-600":"text-green-600","font-medium"])},c(e.invoice_type==="purchase"?"+":"-")+"₹"+c(w(parseFloat(e.amount||0))),3)]))),128)),nt,t("div",st,[it,t("span",{class:v(A.value>=0?"text-blue-600":"text-red-600")}," ₹"+c(w(Math.abs(A.value)))+" "+c(A.value>=0?"(Pay)":"(Receive)"),3)]),t("div",lt,[rt,t("span",{class:v(F.value>=0?"text-green-600":"text-red-600")},c(F.value>0?"+":"")+"₹"+c(w(parseFloat(F.value||0))),3)])])]),i(o).errors.settled_amount?(m(),u("p",dt,c(i(o).errors.settled_amount),1)):y("",!0)]),_.value=="No"?(m(),u("div",ct,[r(h,{for:"note",value:"Note"}),r(de,{id:"note",type:"text",rows:2,modelValue:i(o).note,"onUpdate:modelValue":a[12]||(a[12]=e=>i(o).note=e)},null,8,["modelValue"])])):y("",!0)]),g.value.length>0&&_.value=="Yes"?(m(),u("table",mt,[ut,t("tbody",pt,[(m(!0),u(E,null,B(g.value,(e,d)=>{var s,p,k,x,T,C,R,Y;return m(),u("tr",{key:d},[t("td",_t,c(L(e.date)),1),t("td",yt,[t("div",vt,[t("div",ft,c((p=(s=e.paymentpaid)==null?void 0:s.bank_info)!=null&&p.bank_name?(x=(k=e.paymentpaid)==null?void 0:k.bank_info)==null?void 0:x.bank_name:"Cash")+" - "+c((C=(T=e.paymentpaid)==null?void 0:T.bank_info)!=null&&C.account_number?(Y=(R=e.paymentpaid)==null?void 0:R.bank_info)==null?void 0:Y.account_number:""),1)])]),t("td",ht,c(w(e.amount)),1),t("td",gt,c(w(e.unused_amount)),1)])}),128))])])):y("",!0)]),t("div",xt,[t("div",bt,[r(le,{href:n.route("payment.index")},{svg:O(()=>[wt]),_:1},8,["href"]),r(re,{disabled:i(o).processing},{default:O(()=>[ne("Update")]),_:1},8,["disabled"]),r(se,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:O(()=>[i(o).recentlySuccessful?(m(),u("p",kt,"Saved.")):y("",!0)]),_:1})])])],40,ge)])])]),_:1})],64))}};export{Et as default};
