import{_ as C,b as A,a as I}from"./AdminLayout-aac65a75.js";import{_ as g}from"./CreateButton-7506df4f.js";import{_ as L}from"./SecondaryButton-12775633.js";import{D as M}from"./DangerButton-dc982a69.js";import{M as $}from"./Modal-b2e3ff36.js";import{_ as E}from"./Pagination-02bcc8c2.js";import{h as j,r as y,o as a,c as r,a as n,u as O,w as s,F as b,Z as T,b as t,g as c,f as d,i as z,e as k,t as m}from"./app-b320a640.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const P={class:"animate-top"},V={class:"sm:flex sm:items-center"},S=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Bank Info")],-1),U={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},F={class:"flex justify-end w-20"},G={key:0,class:"flex justify-end"},R={class:"mt-8 overflow-x-auto sm:rounded-lg"},Z={class:"shadow sm:rounded-lg"},D={class:"w-full text-sm text-left rtl:text-right text-gray-500"},H=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"BANK NAME"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"ACCOUNT NUMBER"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"OPENING BALANCE"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"ORGANIZATION"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"ACTION")])],-1),K={key:0},q={class:"px-4 py-2.5"},J={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},Q={class:"px-4 py-2.5"},W={class:"px-4 py-2.5"},X={class:"items-center px-4 py-2.5"},Y={class:"flex items-center justify-start gap-4"},tt=t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),et=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),st=t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),ot=["onClick"],at=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),nt=t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),lt=[at,nt],rt={key:1},it=t("tr",{class:"bg-white"},[t("td",{colspan:"8",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),ct=[it],dt={class:"p-6"},mt=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),ft={class:"mt-6 flex justify-end"},Bt={__name:"List",props:["data","permissions"],setup(o){const w=j({}),f=y(!1),h=y(null),v=l=>{h.value=l,f.value=!0},u=()=>{f.value=!1},B=()=>{w.delete(route("bankinfo.destroy",{id:h.value}),{onSuccess:()=>u()})},N=l=>{let _=l.toFixed(2).toString(),[e,p]=_.split("."),i=e.substring(e.length-3),x=e.substring(0,e.length-3);return x!==""&&(i=","+i),`${x.replace(/\B(?=(\d{2})+(?!\d))/g,",")+i}.${p}`};return(l,_)=>(a(),r(b,null,[n(O(T),{title:"Bank Info"}),n(C,null,{default:s(()=>[t("div",P,[t("div",V,[S,t("div",U,[t("div",F,[n(g,{href:l.route("setting")},{default:s(()=>[c(" Back ")]),_:1},8,["href"])]),o.permissions.canCreateBankInfo?(a(),r("div",G,[n(g,{href:l.route("bankinfo.create")},{default:s(()=>[c(" Add Bank ")]),_:1},8,["href"])])):d("",!0)])]),t("div",R,[t("div",Z,[t("table",D,[H,o.data.data&&o.data.data.length>0?(a(),r("tbody",K,[(a(!0),r(b,null,z(o.data.data,(e,p)=>(a(),r("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",q,m(e.bank_name??"-"),1),t("td",J,m(e.account_number??"-"),1),t("td",Q,m(N(e.balance)??"-"),1),t("td",W,m(e.organization.name??"-"),1),t("td",X,[t("div",Y,[n(A,{align:"right",width:"48"},{trigger:s(()=>[tt]),content:s(()=>[o.permissions.canEditBankInfo?(a(),k(I,{key:0,href:l.route("bankinfo.edit",{id:e.id})},{svg:s(()=>[et]),text:s(()=>[st]),_:2},1032,["href"])):d("",!0),o.permissions.canDeleteBankInfo?(a(),r("button",{key:1,type:"button",onClick:i=>v(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},lt,8,ot)):d("",!0)]),_:2},1024)])])]))),128))])):(a(),r("tbody",rt,ct))])])]),o.data.data&&o.data.data.length>0?(a(),k(E,{key:0,class:"mt-6",links:o.data.links},null,8,["links"])):d("",!0)]),n($,{show:f.value,onClose:u},{default:s(()=>[t("div",dt,[mt,t("div",ft,[n(L,{onClick:u},{default:s(()=>[c(" Cancel ")]),_:1}),n(M,{class:"ml-3",onClick:B},{default:s(()=>[c(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{Bt as default};
