import{r as l,m as r,o as c,c as i}from"./app-4c3f0163.js";const p=["value"],m={__name:"DateInput",props:{modelValue:{required:!0}},emits:["update:modelValue"],setup(o,{expose:a,emit:n}){const e=l(null),s=t=>{const u=t.target.value;n("update:modelValue",u)};return r(()=>{e.value.hasAttribute("autofocus")&&e.value.focus()}),a({focus:()=>e.value.focus()}),(t,u)=>(c(),i("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date",value:o.modelValue,onInput:s,autocomplete:"off",ref_key:"input",ref:e},null,40,p))}};export{m as _};
