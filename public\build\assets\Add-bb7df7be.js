import{r as b,j as H,l as O,o as i,c as d,a as l,u as a,w as F,F as B,Z as te,b as s,t as p,f as _,d as oe,n as f,k as se,v as ae,i as Z,g as ne,T as le,s as re,x as ie}from"./app-b320a640.js";import{_ as de,a as ue}from"./AdminLayout-aac65a75.js";import{_ as v}from"./InputLabel-946d937b.js";import{P as me}from"./PrimaryButton-cb5bb104.js";import{_ as k}from"./TextInput-cb7ba6f7.js";import{_ as ce}from"./TextArea-5264b61a.js";import{_ as _e}from"./RadioButton-2f4bb735.js";import{_ as S}from"./SearchableDropdown-4997ffb6.js";import{u as pe}from"./index-c4301439.js";/* empty css                                                                          */import{_ as ve}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as fe}from"./Checkbox-124714ed.js";const U=g=>(re("data-v-924c9af6"),g=g(),ie(),g),ye={class:"h-screen animate-top"},ge={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},he={class:"sm:flex sm:items-center"},xe=U(()=>s("div",{class:"sm:flex-auto"},[s("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Receive Payment")],-1)),be={class:"flex items-center justify-between"},ke={key:0,class:"text-base font-semibold leading-6 text-gray-900"},we=["onSubmit"],Ve={class:"border-b border-gray-900/10 pb-12"},Ce={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},Ae={class:"sm:col-span-3"},Ne={class:"relative mt-2"},$e={class:"sm:col-span-3"},Fe={class:"relative mt-2"},Se={class:"sm:col-span-2"},Ue={class:"relative mt-2"},ze={key:0,class:"sm:col-span-3"},Te={class:"relative mt-2"},Pe={key:1,class:"sm:col-span-3"},De={key:2,class:"sm:col-span-2"},Oe={key:3,class:"sm:col-span-1"},Be={key:4,class:"sm:col-span-1"},Ie={key:5,class:"sm:col-span-1"},Ee={key:6,class:"sm:col-span-3"},je={key:7,class:"sm:col-span-2"},Re={class:"mt-4 flex justify-start"},Le={class:"text-base font-semibold"},Me={key:8,class:"sm:col-span-2"},Ye={key:9,class:"sm:col-span-2"},qe={key:10,class:"sm:col-span-2"},He={class:"relative mt-2"},Ze={key:11,class:"sm:col-span-3"},Ge={class:"sm:col-span-6"},Je={class:"overflow-x-auto divide-y divide-gray-300 w-full"},Ke=U(()=>s("div",{class:"w-full"},[s("thead",{class:"w-full"},[s("tr",{class:""},[s("th",{scope:"col",class:""}),s("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),s("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),s("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1)),Qe={style:{"overflow-y":"auto","max-height":"318px"}},We={class:"divide-y divide-gray-300 bg-white"},Xe={class:"whitespace-nowrap px-2 text-sm text-gray-900"},et={class:"text-sm text-gray-900 leading-6 py-1.5"},tt={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},ot={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},st={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},at={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},nt={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},lt={key:0,class:"text-red-500 text-xs absolute"},rt={class:"whitespace-nowrap px-2 text-sm text-gray-900"},it={class:"sm:col-span-2"},dt={class:"mt-4 flex justify-start"},ut={class:"text-base font-semibold"},mt={key:0,class:"text-red-500 text-xs absolute"},ct={key:12,class:"sm:col-span-6"},_t={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},pt=U(()=>s("thead",null,[s("tr",null,[s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1)),vt={class:"divide-y divide-gray-300 bg-white"},ft={class:"whitespace-nowrap py-3 text-sm text-gray-900"},yt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},gt={class:"flex flex-col"},ht={class:"text-sm text-gray-900"},xt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},bt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},kt={class:"flex mt-6 items-center justify-between"},wt={class:"ml-auto flex items-center justify-end gap-x-6"},Vt=U(()=>s("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),Ct={key:0,class:"text-sm text-gray-600"},At={__name:"Add",props:["paymentType","bankinfo","organization","customers","invoices","credit"],setup(g){const V=g;b([]);const e=pe("post","/receipt",{organization_id:"",customer_id:"",payment_type:"",date:"",note:"",amount:0,tds_amount:0,discount_amount:0,round_off:0,check_number:"",bank_name:"",org_bank_id:"",invoice:[],settled_amount:"",advance_amount:"",is_credit:"",credit_data:[],total_unused_amount:""}),C=b(""),G=()=>{e.settled_amount=z.value,e.advance_amount=j.value,e.total_unused_amount=A.value,e.is_credit=m.value,e.invoice=y.value,e.credit_data=h.value,e.submit({preserveScroll:!0,onSuccess:()=>e.reset()})},J=(n,o)=>{C.value=o,e.payment_type=n,e.errors.payment_type=null,o==="Cash"?e.note="Cash":e.note==="Cash"&&(e.note="")},$=b([]),h=b([]),A=b(""),I=b([]),K=(n,o)=>{const t=V.bankinfo.filter(r=>r.organization_id===n);I.value=t,e.customer_id&&E(e.customer_id,n);const u=V.credit.filter(r=>r.organization_id===n&&r.customer_id===e.customer_id);h.value=u,A.value=h.value.reduce((r,c)=>r+c.unused_amount,0),e.organization_id=n,e.errors.organization_id=null},Q=(n,o)=>{E(n,e.organization_id);const t=V.credit.filter(u=>u.customer_id===n&&u.organization_id===e.organization_id);h.value=t,A.value=h.value.reduce((u,r)=>u+r.unused_amount,0),e.customer_id=n,e.errors.customer_id=null},E=(n,o)=>{if(!n||!o){$.value=[];return}const t=V.customers.find(c=>c.id===n),u=t==null?void 0:t.party_id,r=V.invoices.filter(c=>{const x=c.organization_id===o;return c.invoice_type==="sales"?x&&c.customer_id===n:c.invoice_type==="purchase"&&u?x&&c.party_id===u:!1});$.value=r},W=(n,o)=>{e.org_bank_id=n,e.errors.org_bank_id=null},z=H(()=>y.value.reduce((n,o)=>n+(o.check&&o.amount?parseFloat(o.amount):0),0)),j=H(()=>{const n=parseFloat(e.amount||0)+parseFloat(e.discount_amount||0)+parseFloat(e.tds_amount||0),o=parseFloat(e.round_off||0),t=z.value;return n-t-o}),T=()=>{},N=n=>{let o=n.toFixed(2).toString(),[t,u]=o.split("."),r=t.substring(t.length-3),c=t.substring(0,t.length-3);return c!==""&&(r=","+r),`${c.replace(/\B(?=(\d{2})+(?!\d))/g,",")+r}.${u}`},R=n=>{const o=new Date(n),t={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",t)},m=b("No"),X=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],ee=n=>{const o=m.value==="Yes"?parseFloat(A.value||0):parseFloat(e.amount||0)+parseFloat(e.round_off||0)+parseFloat(e.discount_amount||0)+parseFloat(e.tds_amount||0);if(!y.value[n].check){y.value[n].amount=0;return}let t=o;y.value.forEach((c,x)=>{c.check&&x!==n&&(t-=parseFloat(c.amount||0))});const u=parseFloat(y.value[n].pending_amount||0),r=Math.min(u,t);y.value[n].amount=r.toFixed(2)},y=b([{id:"",date:"",invoice_no:"",total_amount:"",pending_amount:"",check:!1,amount:0}]),P=()=>{y.value=$.value.map(n=>({id:n.id,date:n.date,invoice_no:n.invoice_no,total_amount:parseFloat(n.total_amount||0).toFixed(2),pending_amount:parseFloat(n.pending_amount||0).toFixed(2),invoice_type:n.invoice_type||"sales",check:!1,amount:"0.00"}))};O($,()=>{P()}),O(m,()=>{P()}),O(()=>e.amount,()=>{m.value==="No"&&P()});const w=n=>{e.errors[n]=null,e.errors.settled_amount=null};return(n,o)=>(i(),d(B,null,[l(a(te),{title:"Receipt"}),l(de,null,{default:F(()=>[s("div",ye,[s("div",ge,[s("div",he,[xe,s("div",be,[h.value.length>0?(i(),d("div",ke," Credits Available: ₹"+p(N(A.value)),1)):_("",!0)])]),s("form",{onSubmit:oe(G,["prevent"]),class:""},[s("div",Ve,[s("div",Ce,[s("div",Ae,[l(v,{for:"payment_type",value:"Organization"}),s("div",Ne,[l(S,{options:g.organization,modelValue:a(e).organization_id,"onUpdate:modelValue":o[0]||(o[0]=t=>a(e).organization_id=t),onOnchange:K,class:f({"error rounded-md":a(e).errors.organization_id})},null,8,["options","modelValue","class"])])]),s("div",$e,[l(v,{for:"payment_type",value:"Customer"}),s("div",Fe,[l(S,{options:g.customers,modelValue:a(e).customer_id,"onUpdate:modelValue":o[1]||(o[1]=t=>a(e).customer_id=t),onOnchange:Q,class:f({"error rounded-md":a(e).errors.customer_id})},null,8,["options","modelValue","class"])])]),s("div",Se,[l(v,{for:"role_id",value:"Payment Through Credit ?"}),s("div",Ue,[l(_e,{modelValue:m.value,"onUpdate:modelValue":o[2]||(o[2]=t=>m.value=t),options:X},null,8,["modelValue"])])]),m.value=="No"?(i(),d("div",ze,[l(v,{for:"payment_type",value:"Payment Type"}),s("div",Te,[l(S,{options:g.paymentType,modelValue:a(e).payment_type,"onUpdate:modelValue":o[3]||(o[3]=t=>a(e).payment_type=t),onOnchange:J,class:f({"error rounded-md":a(e).errors.payment_type})},null,8,["options","modelValue","class"])])])):_("",!0),m.value=="No"?(i(),d("div",Pe,[l(v,{for:"date",value:"Payment Date"}),se(s("input",{"onUpdate:modelValue":o[4]||(o[4]=t=>a(e).date=t),class:f(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":a(e).errors.date}]),type:"date",onChange:o[5]||(o[5]=t=>w("date"))},null,34),[[ae,a(e).date]])])):_("",!0),m.value=="No"?(i(),d("div",De)):_("",!0),m.value=="No"?(i(),d("div",Oe,[l(v,{for:"tds_amount",value:"TDS Amount"}),l(k,{type:"text",onChange:o[6]||(o[6]=t=>w("tds_amount")),onInput:o[7]||(o[7]=t=>T()),modelValue:a(e).tds_amount,"onUpdate:modelValue":o[8]||(o[8]=t=>a(e).tds_amount=t),class:f({"error rounded-md":a(e).errors.tds_amount})},null,8,["modelValue","class"])])):_("",!0),m.value=="No"?(i(),d("div",Be,[l(v,{for:"discount_amount",value:"Discount Amount"}),l(k,{type:"text",onChange:o[9]||(o[9]=t=>w("discount_amount")),onInput:o[10]||(o[10]=t=>T()),modelValue:a(e).discount_amount,"onUpdate:modelValue":o[11]||(o[11]=t=>a(e).discount_amount=t),class:f({"error rounded-md":a(e).errors.discount_amount})},null,8,["modelValue","class"])])):_("",!0),m.value=="No"?(i(),d("div",Ie,[l(v,{for:"round_off",value:"Round Off"}),l(k,{type:"text",onChange:o[12]||(o[12]=t=>w("round_off")),modelValue:a(e).round_off,"onUpdate:modelValue":o[13]||(o[13]=t=>a(e).round_off=t),class:f({"error rounded-md":a(e).errors.round_off})},null,8,["modelValue","class"])])):_("",!0),m.value=="No"?(i(),d("div",Ee,[l(v,{for:"amount",value:"Amount"}),l(k,{id:"amount",type:"text",onChange:o[14]||(o[14]=t=>w("amount")),onInput:o[15]||(o[15]=t=>T()),modelValue:a(e).amount,"onUpdate:modelValue":o[16]||(o[16]=t=>a(e).amount=t),class:f({"error rounded-md":a(e).errors.amount})},null,8,["modelValue","class"])])):_("",!0),m.value=="No"?(i(),d("div",je,[l(v,{for:"advance",value:"Advance(Ref) Amount"}),s("div",Re,[s("p",Le,p(N(j.value)),1)])])):_("",!0),C.value=="Cheque"&&m.value=="No"?(i(),d("div",Me,[l(v,{for:"check_number",value:"Cheque Number"}),l(k,{id:"check_number",type:"text",modelValue:a(e).check_number,"onUpdate:modelValue":o[17]||(o[17]=t=>a(e).check_number=t),class:f({"error rounded-md":a(e).errors["data.check_number"]})},null,8,["modelValue","class"])])):_("",!0),C.value=="Cheque"&&m.value=="No"?(i(),d("div",Ye,[l(v,{for:"bank_name",value:"Bank Name"}),l(k,{id:"bank_name",type:"text",modelValue:a(e).bank_name,"onUpdate:modelValue":o[18]||(o[18]=t=>a(e).bank_name=t),class:f({"error rounded-md":a(e).errors["data.bank_name"]})},null,8,["modelValue","class"])])):_("",!0),C.value!="Cash"&&m.value=="No"?(i(),d("div",qe,[l(v,{for:"org_bank_id",value:"Our Bank"}),s("div",He,[l(S,{options:I.value,modelValue:a(e).org_bank_id,"onUpdate:modelValue":o[19]||(o[19]=t=>a(e).org_bank_id=t),onOnchange:W,class:f({"error rounded-md":a(e).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):_("",!0),C.value!="Cash"&&m.value=="No"?(i(),d("div",Ze)):_("",!0),s("div",Ge,[s("table",Je,[Ke,s("div",Qe,[s("tbody",We,[(i(!0),d(B,null,Z(y.value,(t,u)=>(i(),d("tr",{key:u},[s("td",Xe,[s("div",et,[l(fe,{name:"check",checked:t.check,"onUpdate:checked":r=>t.check=r,onChange:r=>ee(u)},null,8,["checked","onUpdate:checked","onChange"])])]),s("td",tt,[s("span",{class:f([t.invoice_type==="sales"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},p(t.invoice_type==="sales"?"SALES":"PURCHASE"),3)]),s("td",ot,p(t.invoice_no),1),s("td",st,p(t.total_amount),1),s("td",at,p(t.pending_amount),1),s("td",nt,[l(k,{id:"amount",type:"text",modelValue:t.amount,"onUpdate:modelValue":r=>t.amount=r,onChange:r=>w("invoice."+u+".amount"),class:f({error:a(e).errors[`invoice.${u}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),a(e).errors[`invoice.${u}.amount`]?(i(),d("p",lt,p(a(e).errors[`invoice.${u}.amount`]),1)):_("",!0)]),s("td",rt,p(R(t.date)),1)]))),128))])])])]),s("div",it,[l(v,{for:"note",value:"Total Settled Amount"}),s("div",dt,[s("p",ut,p(N(z.value)),1)]),a(e).errors.settled_amount?(i(),d("p",mt,p(a(e).errors.settled_amount),1)):_("",!0)]),m.value=="No"?(i(),d("div",ct,[l(v,{for:"note",value:"Note"}),l(ce,{id:"note",type:"text",rows:2,modelValue:a(e).note,"onUpdate:modelValue":o[20]||(o[20]=t=>a(e).note=t)},null,8,["modelValue"])])):_("",!0)]),h.value.length>0&&m.value=="Yes"?(i(),d("table",_t,[pt,s("tbody",vt,[(i(!0),d(B,null,Z(h.value,(t,u)=>{var r,c,x,D,L,M,Y,q;return i(),d("tr",{key:u},[s("td",ft,p(R(t.date)),1),s("td",yt,[s("div",gt,[s("div",ht,p((c=(r=t.paymentreceive)==null?void 0:r.bank_info)!=null&&c.bank_name?(D=(x=t.paymentreceive)==null?void 0:x.bank_info)==null?void 0:D.bank_name:"Cash")+" - "+p((M=(L=t.paymentreceive)==null?void 0:L.bank_info)!=null&&M.account_number?(q=(Y=t.paymentreceive)==null?void 0:Y.bank_info)==null?void 0:q.account_number:""),1)])]),s("td",xt,p(N(t.amount)),1),s("td",bt,p(N(t.unused_amount)),1)])}),128))])])):_("",!0)]),s("div",kt,[s("div",wt,[l(ue,{href:n.route("receipt.index")},{svg:F(()=>[Vt]),_:1},8,["href"]),l(me,{disabled:a(e).processing},{default:F(()=>[ne("Save")]),_:1},8,["disabled"]),l(le,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:F(()=>[a(e).recentlySuccessful?(i(),d("p",Ct,"Saved.")):_("",!0)]),_:1})])])],40,we)])])]),_:1})],64))}},Et=ve(At,[["__scopeId","data-v-924c9af6"]]);export{Et as default};
