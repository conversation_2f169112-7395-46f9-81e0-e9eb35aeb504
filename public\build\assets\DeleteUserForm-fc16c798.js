import{r as u,h as _,o as y,c as w,a as o,w as a,b as s,O as h,g as c,u as t,M as g,n as x}from"./app-16701445.js";import{D as m}from"./DangerButton-9b74ae84.js";import{_ as v}from"./InputError-********.js";import{_ as k}from"./InputLabel-d69efee6.js";import{M as D}from"./Modal-754de2c3.js";import{_ as C}from"./SecondaryButton-1012464f.js";import{_ as b}from"./TextInput-764e3400.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const V={class:"space-y-6"},B=s("header",null,[s("h2",{class:"text-lg font-medium text-gray-900"},"Delete Account"),s("p",{class:"text-sm text-gray-500"}," Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain. ")],-1),U={class:"p-6"},A=s("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete your account? ",-1),M=s("p",{class:"text-sm text-gray-500"}," Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account. ",-1),$={class:"mt-6"},K={class:"mt-6 flex justify-end"},G={__name:"DeleteUserForm",setup(N){const r=u(!1),l=u(null),e=_({password:""}),p=()=>{r.value=!0,h(()=>l.value.focus())},d=()=>{e.delete(route("profile.destroy"),{preserveScroll:!0,onSuccess:()=>n(),onError:()=>l.value.focus(),onFinish:()=>e.reset()})},n=()=>{r.value=!1,e.reset()};return(O,i)=>(y(),w("section",V,[B,o(m,{onClick:p},{default:a(()=>[c("Delete Account")]),_:1}),o(D,{show:r.value,onClose:n},{default:a(()=>[s("div",U,[A,M,s("div",$,[o(k,{for:"password",value:"Password",class:"sr-only"}),o(b,{id:"password",ref_key:"passwordInput",ref:l,modelValue:t(e).password,"onUpdate:modelValue":i[0]||(i[0]=f=>t(e).password=f),type:"password",class:"mt-1 block w-3/4",placeholder:"Password",onKeyup:g(d,["enter"])},null,8,["modelValue","onKeyup"]),o(v,{message:t(e).errors.password,class:"mt-2"},null,8,["message"])]),s("div",K,[o(C,{onClick:n},{default:a(()=>[c(" Cancel ")]),_:1}),o(m,{class:x(["ml-3",{"opacity-25":t(e).processing}]),disabled:t(e).processing,onClick:d},{default:a(()=>[c(" Delete Account ")]),_:1},8,["class","disabled"])])])]),_:1},8,["show"])]))}};export{G as default};
