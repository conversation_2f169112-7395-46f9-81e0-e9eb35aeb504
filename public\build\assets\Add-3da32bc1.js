import{r as V,j as H,l as B,o as c,c as d,a as r,u as n,w as O,F as I,Z as te,b as t,t as u,f as y,d as oe,n as g,k as se,v as ae,i as j,g as ne,T as le,s as ie,x as re}from"./app-21e66fd5.js";import{_ as ce,a as de}from"./AdminLayout-db62264f.js";import{_ as x}from"./InputLabel-4a50badc.js";import{P as ue}from"./PrimaryButton-ed35dcb4.js";import{_ as D}from"./TextInput-625f6add.js";import{_ as me}from"./TextArea-2c14c909.js";import{_ as pe}from"./RadioButton-b7067d6a.js";import{_ as E}from"./SearchableDropdown-06b090a4.js";import{u as _e}from"./index-c670349c.js";/* empty css                                                                          */import{_ as ve}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as fe}from"./Checkbox-4fdc0e7d.js";const C=b=>(ie("data-v-8035f97a"),b=b(),re(),b),ye={class:"h-screen animate-top"},he={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},ge={class:"sm:flex sm:items-center"},xe=C(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Make Payment")],-1)),be={class:"flex items-center justify-between"},we={key:0,class:"text-base font-semibold leading-6 text-gray-900"},ke=["onSubmit"],Ve={class:"border-b border-gray-900/10 pb-12"},Fe={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},Ce={class:"sm:col-span-3"},Se={class:"relative mt-2"},Ae={class:"sm:col-span-3"},Ne={class:"relative mt-2"},Pe={class:"sm:col-span-2"},Ue={class:"relative mt-2"},$e={key:0,class:"sm:col-span-3"},ze={class:"relative mt-2"},Te={key:1,class:"sm:col-span-1"},Oe={key:2,class:"sm:col-span-2"},Ie={key:3,class:"sm:col-span-2"},De={class:"mt-4 flex justify-start"},Ee={class:"text-base font-semibold"},Me={key:4,class:"sm:col-span-3"},Be={key:5,class:"sm:col-span-3"},je={key:6,class:"sm:col-span-3"},Le={key:7,class:"sm:col-span-3"},Re={class:"relative mt-2"},Ye={class:"sm:col-span-6"},qe={class:"overflow-x-auto divide-y divide-gray-300 w-full"},He=C(()=>t("div",{class:"w-full"},[t("thead",{class:"w-full"},[t("tr",{class:""},[t("th",{scope:"col",class:""}),t("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),t("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),t("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1)),Ze={style:{"overflow-y":"auto","max-height":"318px"}},Ge={class:"divide-y divide-gray-300 bg-white"},Je={class:"whitespace-nowrap px-2 text-sm text-gray-900"},Ke={class:"text-sm text-gray-900 leading-6 py-1.5"},Qe={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},We={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Xe={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},et={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},tt={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},ot={key:0,class:"text-red-500 text-xs absolute"},st={class:"whitespace-nowrap px-2 text-sm text-gray-900"},at={class:"sm:col-span-2"},nt={class:"mt-2 p-3 bg-gray-50 rounded-md"},lt={class:"space-y-2 text-sm"},it={class:"flex items-center gap-2"},rt=C(()=>t("hr",{class:"my-2"},null,-1)),ct={class:"flex justify-between items-center font-semibold"},dt=C(()=>t("span",null,"Net Settlement:",-1)),ut={key:0,class:"text-red-500 text-xs mt-1"},mt={key:8,class:"sm:col-span-6"},pt={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},_t=C(()=>t("thead",null,[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1)),vt={class:"divide-y divide-gray-300 bg-white"},ft={class:"whitespace-nowrap py-3 text-sm text-gray-900"},yt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ht={class:"flex flex-col"},gt={class:"text-sm text-gray-900"},xt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},bt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},wt={class:"flex mt-6 items-center justify-between"},kt={class:"ml-auto flex items-center justify-end gap-x-6"},Vt=C(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),Ft={key:0,class:"text-sm text-gray-600"},Ct={__name:"Add",props:["paymentType","bankinfo","organization","companies","invoices","credit"],setup(b){const S=b;V([]);const o=_e("post","/payment",{organization_id:"",company_id:"",payment_type:"",date:"",note:"",amount:"",discount_amount:0,round_off:0,check_number:"",org_bank_id:"",invoice:[],settled_amount:"",advance_amount:"",is_credit:"",credit_data:[],total_unused_amount:""}),U=V(""),Z=()=>{o.settled_amount=N.value,o.advance_amount=Y.value,o.total_unused_amount=A.value,o.is_credit=v.value,o.invoice=h.value,o.credit_data=w.value,o.submit({preserveScroll:!0,onSuccess:()=>o.reset()})},G=(a,s)=>{U.value=s,o.payment_type=a,o.errors.payment_type=null,s==="Cash"?o.note="Cash":o.note==="Cash"&&(o.note="")},$=V([]),L=V([]),w=V([]),A=V(""),J=(a,s)=>{o.organization_id=a,o.errors.organization_id=null;const e=S.bankinfo.filter(i=>i.organization_id===a);L.value=e,o.company_id&&R(o.company_id,a);const l=S.credit.filter(i=>i.organization_id===a&&i.company_id===o.company_id);w.value=l,A.value=w.value.reduce((i,m)=>i+m.unused_amount,0)},K=(a,s)=>{o.company_id=a,R(a,o.organization_id);const e=S.credit.filter(l=>l.company_id===a&&l.organization_id===o.organization_id);w.value=e,A.value=w.value.reduce((l,i)=>l+i.unused_amount,0),o.errors.company_id=null},R=(a,s)=>{if(!a||!s){$.value=[];return}const e=S.companies.find(m=>m.id===a),l=e==null?void 0:e.party_id,i=S.invoices.filter(m=>{const f=m.organization_id===s;return m.invoice_type==="purchase"?f&&m.company_id===a:m.invoice_type==="sales"&&l?f&&m.party_id===l:!1});$.value=i},Q=(a,s)=>{o.org_bank_id=a,o.errors.org_bank_id=null},N=H(()=>{const a=h.value.reduce((s,e)=>{if(e.check&&e.amount){const l=parseFloat(e.amount);return e.invoice_type==="purchase"?s+l:s-l}return s},0);return parseFloat(a.toFixed(2))}),Y=H(()=>{const a=parseFloat(o.amount||0),s=parseFloat(o.round_off||0),e=N.value;return a>e?a-e-s:0}),F=a=>{let s=a.toFixed(2).toString(),[e,l]=s.split("."),i=e.substring(e.length-3),m=e.substring(0,e.length-3);return m!==""&&(i=","+i),`${m.replace(/\B(?=(\d{2})+(?!\d))/g,",")+i}.${l}`},q=a=>{const s=new Date(a),e={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",e)},W=(a,s)=>{const e=v.value==="Yes"?parseFloat(A.value||0):parseFloat(o.amount||0)+parseFloat(o.round_off||0);if(s===void 0&&(s=h.value.findIndex(_=>_.check===a.target.checked)),s===-1||s>=h.value.length)return;if(!h.value[s].check){h.value[s].amount=0;return}const l=h.value.filter(_=>_.check),i=l.some(_=>_.invoice_type==="purchase"),m=l.some(_=>_.invoice_type==="sales");if(i&&m&&l.length>1&&X(e,l))return;let f=e;h.value.forEach((_,T)=>{if(_.check&&T!==s&&parseFloat(_.amount||0)>0){const P=parseFloat(_.amount||0);_.invoice_type==="purchase"?f-=P:_.invoice_type==="sales"&&(f+=P)}});const p=parseFloat(h.value[s].pending_amount||0),k=Math.min(p,Math.max(0,f));h.value[s].amount=k.toFixed(2)},X=(a,s)=>{const e=s.filter(p=>p.invoice_type==="purchase"),l=s.filter(p=>p.invoice_type==="sales"),i=e.reduce((p,k)=>p+parseFloat(k.pending_amount||0),0),m=l.reduce((p,k)=>p+parseFloat(k.pending_amount||0),0),f=i-m;return Math.abs(a-Math.abs(f))<=1?(s.forEach(p=>{p.amount=parseFloat(p.pending_amount||0).toFixed(2)}),!0):f>0&&a>=f?(s.forEach(p=>{p.amount=parseFloat(p.pending_amount||0).toFixed(2)}),!0):!1},h=V([{id:"",date:"",invoice_no:"",total_amount:"",pending_amount:"",check:!1,amount:0}]),v=V("No"),ee=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],M=()=>{h.value=$.value.map(a=>({id:a.id,date:a.date,invoice_no:a.invoice_no,total_amount:parseFloat(a.total_amount||0).toFixed(2),pending_amount:parseFloat(a.pending_amount||0).toFixed(2),invoice_type:a.invoice_type||"purchase",check:!1,amount:"0.00"}))};B($,()=>{M()}),B(v,()=>{M()}),B(()=>o.amount,()=>{v.value==="No"&&M()});const z=a=>{o.errors[a]=null,o.errors.settled_amount=null};return(a,s)=>(c(),d(I,null,[r(n(te),{title:"Payment"}),r(ce,null,{default:O(()=>[t("div",ye,[t("div",he,[t("div",ge,[xe,t("div",be,[w.value.length>0?(c(),d("div",we," Credits Available: ₹"+u(F(A.value)),1)):y("",!0)])]),t("form",{onSubmit:oe(Z,["prevent"]),class:""},[t("div",Ve,[t("div",Fe,[t("div",Ce,[r(x,{for:"payment_type",value:"Organization"}),t("div",Se,[r(E,{options:b.organization,modelValue:n(o).organization_id,"onUpdate:modelValue":s[0]||(s[0]=e=>n(o).organization_id=e),onOnchange:J,class:g({"error rounded-md":n(o).errors.organization_id})},null,8,["options","modelValue","class"])])]),t("div",Ae,[r(x,{for:"payment_type",value:"Company"}),t("div",Ne,[r(E,{options:b.companies,modelValue:n(o).company_id,"onUpdate:modelValue":s[1]||(s[1]=e=>n(o).company_id=e),onOnchange:K,class:g({"error rounded-md":n(o).errors.company_id})},null,8,["options","modelValue","class"])])]),t("div",Pe,[r(x,{for:"role_id",value:"Payment Through Credit ?"}),t("div",Ue,[r(pe,{modelValue:v.value,"onUpdate:modelValue":s[2]||(s[2]=e=>v.value=e),options:ee},null,8,["modelValue"])])]),v.value=="No"?(c(),d("div",$e,[r(x,{for:"payment_type",value:"Payment Type"}),t("div",ze,[r(E,{options:b.paymentType,modelValue:n(o).payment_type,"onUpdate:modelValue":s[3]||(s[3]=e=>n(o).payment_type=e),onOnchange:G,class:g({"error rounded-md":n(o).errors.payment_type})},null,8,["options","modelValue","class"])])])):y("",!0),v.value=="No"?(c(),d("div",Te,[r(x,{for:"round_off",value:"Round Off"}),r(D,{type:"text",onChange:s[4]||(s[4]=e=>z("round_off")),modelValue:n(o).round_off,"onUpdate:modelValue":s[5]||(s[5]=e=>n(o).round_off=e),class:g({"error rounded-md":n(o).errors.round_off})},null,8,["modelValue","class"])])):y("",!0),v.value=="No"?(c(),d("div",Oe,[r(x,{for:"amount",value:"Amount"}),r(D,{id:"amount",type:"text",onChange:s[6]||(s[6]=e=>z("amount")),modelValue:n(o).amount,"onUpdate:modelValue":s[7]||(s[7]=e=>n(o).amount=e),class:g({"error rounded-md":n(o).errors.amount})},null,8,["modelValue","class"])])):y("",!0),v.value=="No"?(c(),d("div",Ie,[r(x,{for:"advance",value:"Advance(Ref) Amount"}),t("div",De,[t("p",Ee,u(F(Y.value)),1)])])):y("",!0),U.value=="Cheque"&&v.value=="No"?(c(),d("div",Me,[r(x,{for:"check_number",value:"Cheque Number"}),r(D,{id:"check_number",type:"text",modelValue:n(o).check_number,"onUpdate:modelValue":s[8]||(s[8]=e=>n(o).check_number=e),class:g({"error rounded-md":n(o).errors["data.check_number"]})},null,8,["modelValue","class"])])):y("",!0),v.value=="No"?(c(),d("div",Be,[r(x,{for:"date",value:"Payment Date"}),se(t("input",{"onUpdate:modelValue":s[9]||(s[9]=e=>n(o).date=e),class:g(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":n(o).errors.date}]),type:"date",onChange:s[10]||(s[10]=e=>z("date"))},null,34),[[ae,n(o).date]])])):y("",!0),U.value=="Cash"&&v.value=="No"?(c(),d("div",je)):y("",!0),U.value!="Cash"&&v.value=="No"?(c(),d("div",Le,[r(x,{for:"org_bank_id",value:"Our Bank"}),t("div",Re,[r(E,{options:L.value,modelValue:n(o).org_bank_id,"onUpdate:modelValue":s[11]||(s[11]=e=>n(o).org_bank_id=e),onOnchange:Q,class:g({"error rounded-md":n(o).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):y("",!0),t("div",Ye,[t("table",qe,[He,t("div",Ze,[t("tbody",Ge,[(c(!0),d(I,null,j(h.value,(e,l)=>(c(),d("tr",{key:l},[t("td",Je,[t("div",Ke,[r(fe,{name:"check",checked:e.check,"onUpdate:checked":i=>e.check=i,onChange:i=>W(i,l)},null,8,["checked","onUpdate:checked","onChange"])])]),t("td",Qe,[t("span",{class:g([e.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},u(e.invoice_type==="purchase"?"PURCHASE":"SALES"),3)]),t("td",We,u(e.invoice_no),1),t("td",Xe,u(e.total_amount),1),t("td",et,u(e.pending_amount),1),t("td",tt,[r(D,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":i=>e.amount=i,onChange:i=>z("invoice."+l+".amount"),class:g({error:n(o).errors[`invoice.${l}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),n(o).errors[`invoice.${l}.amount`]?(c(),d("p",ot,u(n(o).errors[`invoice.${l}.amount`]),1)):y("",!0)]),t("td",st,u(q(e.date)),1)]))),128))])])])]),t("div",at,[r(x,{for:"note",value:"Settlement Summary"}),t("div",nt,[t("div",lt,[(c(!0),d(I,null,j(h.value.filter(e=>e.check),e=>(c(),d("div",{key:e.id,class:"flex justify-between items-center"},[t("div",it,[t("span",{class:g([e.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium"])},u(e.invoice_type==="purchase"?"P":"S"),3),t("span",null,u(e.invoice_no),1)]),t("span",{class:g([e.invoice_type==="purchase"?"text-blue-600":"text-green-600","font-medium"])},u(e.invoice_type==="purchase"?"+":"-")+"₹"+u(F(parseFloat(e.amount||0))),3)]))),128)),rt,t("div",ct,[dt,t("span",{class:g(N.value>=0?"text-blue-600":"text-red-600")}," ₹"+u(F(Math.abs(N.value)))+" "+u(N.value>=0?"(Pay)":"(Receive)"),3)])])]),n(o).errors.settled_amount?(c(),d("p",ut,u(n(o).errors.settled_amount),1)):y("",!0)]),v.value=="No"?(c(),d("div",mt,[r(x,{for:"note",value:"Note"}),r(me,{id:"note",type:"text",rows:2,modelValue:n(o).note,"onUpdate:modelValue":s[12]||(s[12]=e=>n(o).note=e)},null,8,["modelValue"])])):y("",!0)]),w.value.length>0&&v.value=="Yes"?(c(),d("table",pt,[_t,t("tbody",vt,[(c(!0),d(I,null,j(w.value,(e,l)=>{var i,m,f,p,k,_,T,P;return c(),d("tr",{key:l},[t("td",ft,u(q(e.date)),1),t("td",yt,[t("div",ht,[t("div",gt,u((m=(i=e.paymentpaid)==null?void 0:i.bank_info)!=null&&m.bank_name?(p=(f=e.paymentpaid)==null?void 0:f.bank_info)==null?void 0:p.bank_name:"Cash")+" - "+u((_=(k=e.paymentpaid)==null?void 0:k.bank_info)!=null&&_.account_number?(P=(T=e.paymentpaid)==null?void 0:T.bank_info)==null?void 0:P.account_number:""),1)])]),t("td",xt,u(F(e.amount)),1),t("td",bt,u(F(e.unused_amount)),1)])}),128))])])):y("",!0)]),t("div",wt,[t("div",kt,[r(de,{href:a.route("receipt.index")},{svg:O(()=>[Vt]),_:1},8,["href"]),r(ue,{disabled:n(o).processing},{default:O(()=>[ne("Save")]),_:1},8,["disabled"]),r(le,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:O(()=>[n(o).recentlySuccessful?(c(),d("p",Ft,"Saved.")):y("",!0)]),_:1})])])],40,ke)])])]),_:1})],64))}},Mt=ve(Ct,[["__scopeId","data-v-8035f97a"]]);export{Mt as default};
