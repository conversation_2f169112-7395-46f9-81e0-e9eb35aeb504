<template>
    <AdminLayout>
        <div class="px-2 sm:px-4 lg:px-8 py-4 sm:py-6 animate-top">
            <!-- Header -->
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
                <div class="items-start">
                    <h1 class="text-xl sm:text-2xl font-semibold leading-7 text-gray-900">Debit Notes</h1>
                </div>
                <div class="flex flex-col sm:flex-row gap-3 sm:gap-4">
                    <!-- Search -->
                    <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64">
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path>
                        </svg>
                        <input id="search-field" v-model="search" @input="fetchData" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div>
                    <SimpleDropdown
                        :options="organization"
                        v-model="organizationId"
                        @onchange="setOrganization"
                        placeholder="Select Organization"
                    />
                </div>
                <div>
                    <SimpleDropdown
                        :options="companies"
                        v-model="companyId"
                        @onchange="setCompany"
                        placeholder="Select Company"
                    />
                </div>
            </div>

            <!-- Desktop Table -->
            <div class="hidden lg:block mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3">Debit Note No</th>
                                <th scope="col" class="px-6 py-3">Company</th>
                                <th scope="col" class="px-6 py-3">Date</th>
                                <th scope="col" class="px-6 py-3">Total Amount</th>
                                <th scope="col" class="px-6 py-3">Reason</th>
                                <th scope="col" class="px-6 py-3">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-if="data.data && (data.data.length > 0)" class="odd:bg-white even:bg-gray-50 border-b" v-for="debitNote in data.data" :key="debitNote.id">
                                <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">
                                    {{ debitNote.debit_note_no ?? '-' }}
                                </td>
                                <td class="px-6 py-4">{{ debitNote.company?.name ?? '-' }}</td>
                                <td class="px-6 py-4">{{ debitNote.date ?? '-' }}</td>
                                <td class="px-6 py-4">₹{{ debitNote.total_amount ?? '-' }}</td>
                                <td class="px-6 py-4">{{ debitNote.reason ?? '-' }}</td>
                                <td class="px-6 py-4">
                                    <Dropdown :align="'right'" width="48">
                                        <template #trigger>
                                            <button type="button" title="Open details" class="p-2 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                <svg viewBox="0 0 24 24" class="w-5 h-5 fill-current">
                                                    <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                </svg>
                                            </button>
                                        </template>
                                        <template #content>
                                            <ActionLink :href="route('debitnote.show', {id: debitNote.id})">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">View</span>
                                                </template>
                                            </ActionLink>
                                            <button type="button" @click="openDeleteModal(debitNote.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full" v-if="permissions.canDeleteDebitNote">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">Delete</span>
                                            </button>
                                        </template>
                                    </Dropdown>
                                </td>
                            </tr>
                            <tr v-else>
                                <td colspan="6" class="px-6 py-4 text-center">
                                    <p class="text-sm font-semibold text-gray-900">No data found.</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Mobile Cards -->
            <div class="lg:hidden mt-6 space-y-4">
                <template v-if="data.data && (data.data.length > 0)">
                    <div v-for="debitNote in data.data" :key="debitNote.id" 
                         class="bg-white rounded-lg shadow-sm border p-4">
                        <div class="space-y-3">
                            <!-- Header -->
                            <div class="flex justify-between items-start">
                                <h3 class="font-medium text-gray-900 text-base">{{ debitNote.debit_note_no ?? '-' }}</h3>
                                <span class="text-sm text-gray-500">{{ debitNote.date ?? '-' }}</span>
                            </div>
                            
                            <!-- Details Grid -->
                            <div class="grid grid-cols-2 gap-3 text-sm">
                                <div>
                                    <span class="text-gray-500">Company:</span>
                                    <span class="ml-1 font-medium">{{ debitNote.company?.name ?? '-' }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">Amount:</span>
                                    <span class="ml-1 font-medium">₹{{ debitNote.total_amount ?? '-' }}</span>
                                </div>
                            </div>
                            
                            <!-- Reason (full width) -->
                            <div class="text-sm">
                                <span class="text-gray-500">Reason:</span>
                                <span class="ml-1 font-medium">{{ debitNote.reason ?? '-' }}</span>
                            </div>
                            
                            <!-- Actions -->
                            <div class="flex justify-end pt-2 border-t border-gray-100">
                                <Dropdown :align="'right'" width="48">
                                    <template #trigger>
                                        <button type="button" title="Open details" class="p-2 rounded hover:bg-gray-100 focus:bg-gray-100">
                                            <svg viewBox="0 0 24 24" class="w-5 h-5 fill-current">
                                                <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                            </svg>
                                        </button>
                                    </template>
                                    <template #content>
                                        <ActionLink :href="route('debitnote.show', {id: debitNote.id})">
                                            <template #svg>
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                            </template>
                                            <template #text>
                                                <span class="text-sm text-gray-700 leading-5">View</span>
                                            </template>
                                        </ActionLink>
                                        <button type="button" @click="openDeleteModal(debitNote.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full" v-if="permissions.canDeleteDebitNote">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
                                            </svg>
                                            <span class="text-sm text-gray-700 leading-5">Delete</span>
                                        </button>
                                    </template>
                                </Dropdown>
                            </div>
                        </div>
                    </div>
                </template>
                
                <!-- No data message for mobile -->
                <div v-else class="bg-white rounded-lg shadow-sm border p-8 text-center">
                    <p class="text-sm font-semibold text-gray-900">No data found.</p>
                </div>
            </div>

            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>

        <!-- Delete Modal -->
        <ConfirmationModal :show="deleteModal" @close="closeDeleteModal">
            <template #title>
                Delete Debit Note
            </template>
            <template #content>
                Are you sure you want to delete this debit note? This action cannot be undone.
            </template>
            <template #footer>
                <SecondaryButton @click="closeDeleteModal">
                    Cancel
                </SecondaryButton>
                <DangerButton class="ml-3" @click="deleteDebitNote" :class="{ 'opacity-25': form.processing }" :disabled="form.processing">
                    Delete
                </DangerButton>
            </template>
        </ConfirmationModal>
    </AdminLayout>
</template>

<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import { ref, onMounted } from 'vue';
import { router, useForm } from '@inertiajs/vue3';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import Dropdown from '@/Components/Dropdown.vue';
import ActionLink from '@/Components/ActionLink.vue';
import Pagination from '@/Components/Pagination.vue';
import ConfirmationModal from '@/Components/ConfirmationModal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';

const props = defineProps({
    data: Object,
    permissions: Object,
    organization: Array,
    companies: Array,
    organizationId: String,
    companyId: String
});

const search = ref('');
const organizationId = ref(props.organizationId);
const companyId = ref(props.companyId);
const deleteModal = ref(false);
const deleteId = ref(null);

const form = useForm({});

const fetchData = () => {
    router.get('/debitnote', {
        search: search.value,
        organization_id: organizationId.value,
        company_id: companyId.value
    }, {
        preserveState: true,
        replace: true
    });
};

const setOrganization = (value) => {
    organizationId.value = value;
    fetchData();
};

const setCompany = (value) => {
    companyId.value = value;
    fetchData();
};

const openDeleteModal = (id) => {
    deleteId.value = id;
    deleteModal.value = true;
};

const closeDeleteModal = () => {
    deleteModal.value = false;
    deleteId.value = null;
};

const deleteDebitNote = () => {
    form.delete(route('debitnote.destroy', deleteId.value), {
        onSuccess: () => {
            closeDeleteModal();
        }
    });
};
</script>
