<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\CompanyPoController;
use App\Http\Controllers\ChallanController;
use App\Http\Controllers\QuotationController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\OrganizationController;
use App\Http\Controllers\BankInfoController;
use App\Http\Controllers\WeeklyPlanController;
use App\Http\Controllers\FunnelController;
use App\Http\Controllers\BankTransactionController;
use App\Http\Controllers\AccountTypeController;
use App\Http\Controllers\ReportsController;
use App\Http\Controllers\PurchaseInvoiceController;
use App\Http\Controllers\ServiceReportController;
use App\Http\Controllers\JobCardController;
use App\Http\Controllers\JobCardCheckListController;
use App\Http\Controllers\MaintenanceContractController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\EmailTemplateController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ReceiptController;
use App\Http\Controllers\ProformaInvoiceController;
use App\Http\Controllers\MailConfigController;
use App\Http\Controllers\AddSmtpController;
use App\Http\Controllers\CreditNoteController;
use App\Http\Controllers\DebitNoteController;
use App\Http\Controllers\EmailTagController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Illuminate\Foundation\Http\Middleware\HandlePrecognitiveRequests;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return Inertia::render('Auth/Login', [
        'canLogin'          => Route::has('login'),
        'canRegister'       => Route::has('register'),
        'laravelVersion'    => Application::VERSION,
        'phpVersion'        => PHP_VERSION,
    ]);
});

Route::get('/dashboard',      [DashboardController::class, 'dashboard'])->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {

    Route::get('/profile',      [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile',    [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile',   [ProfileController::class, 'destroy'])->name('profile.destroy');

    //Roles
    Route::resource('roles', RoleController::class)->middleware([HandlePrecognitiveRequests::class]);

    //Users
    Route::resource('users', UserController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/users',              [UserController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('users.update');
    Route::post('/users/activation',    [UserController::class, 'activation'])->name('users.activation');

    //Customers
    Route::resource('customers', CustomerController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/customers',              [CustomerController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('customers.update');
    Route::post('/customers/activation',    [CustomerController::class, 'activation'])->name('customers.activation');
    Route::get('/customerstransaction/{id}',[CustomerController::class, 'customerTransaction'])->name('customers.transaction');
    Route::post('/customers/advancepayment',[CustomerController::class, 'advancePayment'])->name('customers.advancepayment');
    Route::get('/customerscredit/{id}',     [CustomerController::class, 'customerCredit'])->name('customers.credit');
    Route::get('/export-customer-transaction', [CustomerController::class, 'exportCustomerTransaction'])->name('export.customer.transaction');

    //Company
    Route::resource('companies', CompanyController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/companies',              [CompanyController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('companies.update');
    Route::post('/companies/activation',    [CompanyController::class, 'activation'])->name('companies.activation');
    Route::get('/companiestransaction/{id}',[CompanyController::class, 'companiesTransaction'])->name('companies.transaction');
    Route::post('/companies/advancepayment',[CompanyController::class, 'advancePayment'])->name('companies.advancepayment');
    Route::get('/companiescredit/{id}',     [CompanyController::class, 'companyCredit'])->name('companies.credit');
    Route::get('/company/{id}/transactions/export', [CompanyController::class, 'exportCompaniesTransaction'])->name('company.transactions.export');

    //Product
    Route::resource('products', ProductController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/products',               [ProductController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('products.update');
    Route::post('/products/activation',     [ProductController::class, 'activation'])->name('products.activation');
    Route::get('/products/{id}',            [ProductController::class, 'show'])->name('products.show');
    Route::get('/products/history/{id}',    [ProductController::class, 'history'])->name('products.history');
    Route::get('/product/logs/{id}',        [ProductController::class, 'logs'])->name('product.logs');
    Route::get('/stockadd/{id}',            [ProductController::class, 'stockAdd'])->name('stock.add');
    Route::post('/savestock',               [ProductController::class, 'saveStock'])->name('savestock');
    Route::get('/stockedit/{id}',           [ProductController::class, 'stockedit'])->name('stock.edit');
    Route::get('/remove-product-image/{id}', [ProductController::class, 'removeProductImage'])->name('productimage.remove');

    //Company PO
    Route::resource('companypo', CompanyPoController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::get('receivepo/{id}',            [CompanyPoController::class, 'receivePO'])->name('companypo.receivepo');
    Route::get('viewpo/{id}',               [CompanyPoController::class, 'viewPO'])->name('companypo.viewpo');
    Route::post('savereceivepo',            [CompanyPoController::class, 'saveReceivePO']);
    Route::post('/companypo/paymentpay',    [CompanyPoController::class, 'paymentPay'])->name('companypo.paymentpay');
    Route::get('/companypo/download/{id}/{type}',  [CompanyPoController::class, 'downloadPo'])->name('purchaseOrder.download');

    //Challan
    Route::resource('challan',      ChallanController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::get('/challan/close/{id}'          , [ChallanController::class, 'closeChallan'])->name('challan.close');
    Route::get('/challan/return/{id}/{detail}', [ChallanController::class, 'challanReturn'])->name('challan.return');
    Route::get('viewchallan/{id}',              [ChallanController::class, 'view'])->name('challan.view');
    Route::get('generate-invoice/{id}',         [ChallanController::class, 'generateChallan'])->name('challan.invoice');
    Route::get('generate-combine-invoice/{detail}', [ChallanController::class, 'generateCombineInvoice'])->name('challan.combine-invoice');
    Route::post('saveinvoice',                  [ChallanController::class, 'saveInvoice']);
    Route::get('/challan/foc/{id}',             [ChallanController::class, 'focChallan'])->name('challan.foc');
    Route::get('/challan/democlose/{id}',       [ChallanController::class, 'demoClose'])->name('challan.democlose');
    Route::get('/challan/download/{id}/{type}',        [ChallanController::class, 'downloadChallan'])->name('challan.download');
    Route::get('/challantransfer',              [ChallanController::class, 'challanTransfer'])->name('challantransfer');
    Route::get('/challantransferedit/{id}',     [ChallanController::class, 'challanTransferEdit'])->name('challantransfer.edit');

    //Quotation
    Route::resource('quotation', QuotationController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::get('viewquotation/{id}',        [QuotationController::class, 'view'])->name('quotation.view');
    Route::post('convertOrder',              [QuotationController::class, 'convertOrder']);
    Route::get('quotation-order/{id}',         [QuotationController::class, 'quotationToOrder'])->name('quotation.order');
    Route::get('convertoproforma-invoice/{id}',[QuotationController::class, 'convertPI'])->name('quotation.convertopi');
    Route::get('/quotation/reject/{id}',    [QuotationController::class, 'rejectQuotation'])->name('quotation.reject');
    Route::get('/quotation/download/{id}/{type}',  [QuotationController::class, 'downloadQuotation'])->name('quotation.download');

    //Orders
    Route::resource('orders',   OrderController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::get('vieworders/{id}',           [OrderController::class, 'view'])->name('orders.view');
    Route::get('orderdeliver/{id}',         [OrderController::class, 'orderDeliver'])->name('orders.deliver');
    Route::post('saveorderdeliver',         [OrderController::class, 'saveOrderDeliver'])->name('saveorderdeliver');
    Route::get('/export-orders',            [OrderController::class, 'exportOrders'])->name('export-orders');
    Route::get('/order/download/{id}/{type}',      [OrderController::class, 'downloadOrder'])->name('order.download');
    Route::get('generate-invoice-order/{id}', [OrderController::class, 'generateOrder'])->name('orders.generate');
    Route::post('/generate-invoice-order',     [OrderController::class, 'orderToInvoice']);

    //Proforma Invoice
    Route::resource('proforma-invoice',   ProformaInvoiceController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::get('viewproforma-invoice/{id}',           [ProformaInvoiceController::class, 'view'])->name('proforma-invoice.view');
    Route::get('/proforma-invoice/download/{id}/{type}',      [ProformaInvoiceController::class, 'downloadPI'])->name('proforma-invoice.download');
    Route::get('/proforma-invoice/reject/{id}',    [ProformaInvoiceController::class, 'rejectPi'])->name('pi.reject');

    //Invoice
    Route::resource('invoice',  InvoiceController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::get('viewinvoice/{id}',          [InvoiceController::class, 'view'])->name('invoice.view');
    Route::post('/invoice/paymentreceive',  [InvoiceController::class, 'receivePayment'])->name('invoice.paymentreceive');
    Route::get('/stocktransfer',            [InvoiceController::class, 'stockTransfer'])->name('stocktransfer');
    Route::get('/stocktransferedit/{id}',   [InvoiceController::class, 'stockTransferEdit'])->name('stocktransfer.edit');
    Route::get('/creditnoteadd/{id}',        [InvoiceController::class, 'creditNoteAdd'])->name('creditnote.add');
    Route::get('/removetransaction/{id}',   [InvoiceController::class, 'removeTransaction'])->name('removetransaction');
    Route::get('/invoice/download/{id}/{type}', [InvoiceController::class, 'downloadInvoice'])->name('invoices.download');
    Route::get('/challan-invoice-edit/{id}',    [InvoiceController::class, 'editCHLInvoice'])->name('challaninvoice.edit');
    Route::get('/invoicedetails/{customerId}/{productId}',  [InvoiceController::class, 'getInvoiceDetails'])->name('invoiceDetails');
    Route::post('editchallaninvoice',           [InvoiceController::class, 'editChallanInvoice']);
    Route::post('creditnotesave',                [InvoiceController::class, 'creditNoteSave']);
    Route::delete('/stock-transfer/{id}', [InvoiceController::class, 'destroyStockTransfer'])->name('stock-transfer.destroy');

    //Organization
    Route::resource('organization',OrganizationController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/organization',   [OrganizationController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('organization.update');

    //Bank Info
    Route::resource('bankinfo', BankInfoController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/bankinfo',   [BankInfoController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('bankinfo.update');

    //Account Types
    Route::resource('account-type',AccountTypeController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/account-type',   [AccountTypeController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('account-type.update');
    Route::get('/account-transactions/{id}', [AccountTypeController::class, 'view'])->name('account-type.transactions');
    Route::get('/export-account-type-transactions/{id}', [AccountTypeController::class, 'exportAccountTransactions'])->name('account-type.export-transactions')->middleware(['auth', 'verified']);

    //Jobcard
    Route::resource('jobcard',          JobCardController::class)->middleware([HandlePrecognitiveRequests::class]);
    // Route::patch('/jobcard',           [JobCardController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('jobcard.update');
    Route::post('/jobcard/close',      [JobCardController::class, 'closeJobcard'])->name('jobcard.close');
    Route::get('/export-jobcard-report', [JobCardController::class, 'exportJobcard'])->name('export-jobcard-report');
    Route::get('/jobcard/download/{id}/{type}', [JobCardController::class, 'downloadJobCard'])->name('jobcard.download');
    Route::delete('/jobcard/document/{id}', [JobCardController::class, 'deleteDocument'])->name('jobcard.document.delete');

    //Notifications
    Route::post('/notifications/{id}/mark-as-read', function($id) {
        auth()->user()->notifications()->where('id', $id)->update(['read_at' => now()]);
        return response()->json(['success' => true]);
    })->name('notifications.mark-as-read');

    Route::post('/notifications/mark-all-as-read', function() {
        auth()->user()->unreadNotifications->markAsRead();
        return response()->json(['success' => true]);
    })->name('notifications.mark-all-as-read');

    //Jobcard Checklist
    Route::resource('jobcard-checklist',    JobCardCheckListController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/jobcard-checklist',      [JobCardCheckListController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('jobcard-checklist.update');

    //Maintenance Contract
    Route::resource('maintenance-contract',    MaintenanceContractController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/maintenance-contract',      [MaintenanceContractController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('maintenance-contract.update');
    Route::get('/export-maintenance-contract',      [MaintenanceContractController::class, 'exportMaintenance'])->name('export-maintenance-contract');


    //Bank Transaction
    Route::resource('banktransaction',BankTransactionController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/banktransaction',        [BankTransactionController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('banktransaction.update');
    Route::get('/banktransaction/{id}',     [BankTransactionController::class, 'show'])->name('banktransaction.show');
    Route::get('/internalbanktransfer',     [BankTransactionController::class, 'internalTransfer'])->name('internalbanktransfer');
    Route::post('/saveinternalbanktransfer',[BankTransactionController::class, 'saveInternalTransfer'])->name('saveinternalbanktransfer');
    Route::get('/editinternalbanktransfer/{id}', [BankTransactionController::class, 'editInternalTransfer'])->name('editinternalbanktransfer');
    Route::post('/updateinternalbanktransfer',[BankTransactionController::class, 'updateInternalTransfer'])->name('updateinternalbanktransfer');
    Route::get('/export-bank-transaction/{id}', [BankTransactionController::class, 'exportBankTransaction'])->name('banktransaction.export') ->middleware(['auth']);

    //Payment
    Route::resource('payment',  PaymentController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/payment', [PaymentController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('payment.update');

    //Receipt
    Route::resource('receipt',  ReceiptController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/receipt', [ReceiptController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('receipt.update');

    //Purchase Invoice
    Route::resource('purchaseinvoice',  PurchaseInvoiceController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::get('viewpurchaseinvoice/{id}',        [PurchaseInvoiceController::class, 'view'])->name('purchaseinvoice.view');
    Route::post('/purchaseinvoice/makepayment',           [PurchaseInvoiceController::class, 'makePayment'])->name('purchaseinvoice.makepayment');
    Route::get('purchase-receive-update-pending-amount',  [PurchaseInvoiceController::class, 'updatePendingAmount']);
    Route::get('/purchaseinvoice/convert-to-invoice/{id}', [PurchaseInvoiceController::class, 'convertToInvoice'])->name('purchaseinvoice.convert-to-invoice');
    Route::post('/purchaseinvoice/save-convert-to-invoice', [PurchaseInvoiceController::class, 'saveConvertToInvoice'])->name('purchaseinvoice.save-convert-to-invoice');

    //Service Report
    Route::resource('service-reports',  ServiceReportController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/service-reports',            [ServiceReportController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('service-reports.update');
    Route::get('/service-reports/{id}',         [ServiceReportController::class, 'show'])->name('service-reports.show');
    Route::get('/service-reports-history/{id}', [ServiceReportController::class, 'view'])->name('service-reports.view');
    Route::get('/upload-service-report/{id}',   [ServiceReportController::class, 'uploadServiceReport'])->name('upload-service-report');
    Route::post('/save-upload-service-report',  [ServiceReportController::class, 'saveUploadServiceReport'])->name('save-upload-service-report');


    //Weekly Plan
    Route::resource('weeklyplan',WeeklyPlanController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/weeklyplan',           [WeeklyPlanController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('weeklyplan.update');
    Route::post('/weeklyplan/status',     [WeeklyPlanController::class, 'changeStatus'])->name('weeklyplan.changeStatus');

    //Funnel
    Route::resource('funnel',FunnelController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/funnel',                  [FunnelController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('funnel.update');
    Route::post('/funnel/changeStatus/{id}', [FunnelController::class, 'changeStatus'])->name('funnel.changeStatus');
    Route::get('/funnel', [FunnelController::class, 'index'])->name('funnel.index');

    //Credit Note
    Route::resource('creditnote',  CreditNoteController::class)->middleware([HandlePrecognitiveRequests::class]);

    //Debit Note
    Route::resource('debitnote',         DebitNoteController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::get('/debitnoteadd/{id}',        [PurchaseInvoiceController::class, 'debitNoteAdd'])->name('debitnote.add');
    Route::post('debitnotesave',            [PurchaseInvoiceController::class, 'debitNoteSave'])->name('debitnote.save');

    //Reports
    Route::get('/setting',                          [SettingController::class, 'index'])->name('setting');
    Route::get('/reports',                          [ReportsController::class,  'index'])->name('reports');
    Route::get('/engineer-business-report',         [ReportsController::class,  'engineerBusiness'])->name('engineer-business.report');
    Route::get('/export-engineer-business-report',  [ReportsController::class, 'exportEngineerBusiness'])->name('export-engineer-business-report');
    Route::get('/sns-stock-report',         [ReportsController::class,  'snsStock'])->name('sns-stock.report');
    Route::get('/export-sns-stock-report',  [ReportsController::class, 'exportSnsStock'])->name('export-sns-stock-report');
    Route::get('/sns-sales-report',         [ReportsController::class,  'snsSales'])->name('sns-sales.report');
    Route::get('/export-sns-sales-report',  [ReportsController::class, 'exportSnsSales'])->name('export-sns-sales-report');
    Route::get('/sns-cusomersales-report',  [ReportsController::class, 'customersnsSales'])->name('sns-cusomersales-report');
    Route::get('/export-sns-cusomersales-report',[ReportsController::class, 'exportcustomerSnsSales'])->name('export-sns-cusomersales-report');
    Route::get('/gst-sales-data',           [ReportsController::class, 'gstSalesData'])->name('gst-sales-data');
    Route::get('/export-gst-sales-data',    [ReportsController::class, 'exportGstSalesData'])->name('export-gst-sales-data');
    Route::get('/gst-purchase-data',        [ReportsController::class, 'gstPurchaseData'])->name('gst-purchase-data');
    Route::get('/export-gst-purchase-data',[ReportsController::class, 'exportGstPurchaseData'])->name('export-gst-purchase-data');
    Route::get('/hsn-sales-summary-report', [ReportsController::class, 'hsnSalesSummary'])->name('hsn.sales.summary');
    Route::get('/export-hsn-sales-summary', [ReportsController::class, 'exportHsnSalesSummary'])->name('export-hsn-sales-summary');
    Route::get('/sales-report', [ReportsController::class, 'salesReport'])->name('sales.report');
    Route::get('/export-sales-report', [ReportsController::class, 'exportSalesReport'])->name('export.sales.report');
    Route::get('/purchase-report', [ReportsController::class, 'purchasereport'])->name('purchase.report');
    Route::get('/export-purchase-report', [ReportsController::class, 'exportPurchaseReport']);
    Route::get('/tds-report', [ReportsController::class, 'tdsReport'])->name('tds.report');
    Route::get('/export-tds-report', [ReportsController::class, 'exportTdsReport'])->name('export.tds.report');
    Route::get('/invoice-pending-amount', [ReportsController::class, 'invoicePendingAmount'])->name('invoice.pending-amount');
    Route::get('/export-invoice-pending-amount', [ReportsController::class, 'invoicePendingAmountexport'])->name('export-invoice-pending-amount');
    Route::get('/customer-transaction-report', [ReportsController::class, 'customerinvoiceReport'])->name('customer-transaction.report');
    Route::get('/export/customer-report', [ReportsController::class, 'customerinvoiceReportExport'])->name('export.customer.report');

    //Common
    Route::get('/removedocument/{id}/{name}',        [SettingController::class, 'removedocument'])->name('removedocument');
    Route::get('/removeproduct/{id}/{model}',        [SettingController::class, 'removeProduct'])->name('removeproduct');
    Route::get('/removetransferproduct/{id}',        [SettingController::class, 'removeTransferProduct'])->name('removetransferproduct');
    Route::get('/removechallantransferproduct/{id}', [SettingController::class, 'removeChallanTransferProduct'])->name('removechallantransferproduct');
    Route::get('/removeinvoicedetails/{id}',         [SettingController::class, 'removeInvoiceDetails'])->name('removeinvoicedetails');

    //SMTP
    Route::resource('mail-configs', MailConfigController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/mail-configs',   [MailConfigController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('mail-configs.update');

    //emailtemplate
    Route::resource('emailtemplates', EmailTemplateController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/emailtemplates',   [EmailTemplateController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('emailtemplates.update');

    //Email Tags
    Route::resource('email-tag',EmailTagController::class)->middleware([HandlePrecognitiveRequests::class]);
    Route::patch('/email-tag',   [EmailTagController::class, 'update'])->middleware([HandlePrecognitiveRequests::class])->name('email-tag.update');

    //Setting
    Route::get('/salesstock',       [SettingController::class, 'salesstock'])->name('salesstock');
    Route::get('/servicestock',     [SettingController::class, 'servicestock'])->name('servicestock');
    Route::get('/manage-prefix',    [SettingController::class, 'managePrefix'])->name('manage-prefix');
    Route::post('/saveprefix',      [SettingController::class, 'savePrefixInfo'])->name('savePrefixInfo');
    // Route::get('/activity',         [SettingController::class, 'activityLogs'])->name('activity');
    Route::get('/export-stock',     [SettingController::class, 'exportExcel'])->name('export-stock');
    Route::get('logs',              [SettingController::class, 'getAllLogs'])->name('logs');
    Route::get('/logs/load-more',   [SettingController::class, 'loadMoreLogs'])->name('logs.loadMore');
    Route::get('/number-setting', [SettingController::class, 'numberSetting'])->name('number-setting');
    Route::post('/settings/update-number/{id}', [SettingController::class, 'updateNumber'])->name('setting.updateNumber');

    Route::get('export-users', [UserController::class, 'exportExcel']);


    // Route::get('/reset-transactions', [InvoiceController::class, 'resetCustomerTransactions']);
    // Route::get('/reset-purchase-transactions', [InvoiceController::class, 'resetPurchaseTransactions']);


});


require __DIR__ . '/auth.php';



