import{r as b,j as I,l as B,m as ee,o as d,c as u,a as l,u as n,w as T,F as D,Z as te,b as o,t as m,f as v,d as oe,n as y,k as se,v as ae,i as j,g as ne,T as le}from"./app-97275a91.js";import{_ as ie,a as re}from"./AdminLayout-595ad5a7.js";import{_ as f}from"./InputLabel-eb73087c.js";import{P as de}from"./PrimaryButton-46ac4375.js";import{_ as w}from"./TextInput-11c46564.js";import{_ as ue}from"./TextArea-5e21e606.js";import{_ as me}from"./RadioButton-1a065901.js";import{_ as O}from"./SearchableDropdown-9d1b12d3.js";import{u as ce}from"./index-05d29b1c.js";/* empty css                                                                          */import{_ as _e}from"./Checkbox-c09a6665.js";import"./_plugin-vue_export-helper-c27b6911.js";const pe={class:"h-screen animate-top"},ve={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},ye={class:"sm:flex sm:items-center"},fe=o("div",{class:"sm:flex-auto"},[o("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Payment")],-1),ge={class:"flex items-center justify-between"},he={key:0,class:"text-base font-semibold leading-6 text-gray-900"},xe=["onSubmit"],be={class:"border-b border-gray-900/10 pb-12"},ke={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},we={class:"sm:col-span-3"},Ve={class:"relative mt-2"},Ae={class:"sm:col-span-3"},Ne={class:"relative mt-2"},Ce={class:"sm:col-span-2 hidden"},Fe={class:"relative mt-2"},ze={key:0,class:"sm:col-span-3"},$e={class:"relative mt-2"},Ue={key:1,class:"sm:col-span-3"},Se={key:2,class:"sm:col-span-2"},Pe={key:3,class:"sm:col-span-1"},Te={key:4,class:"sm:col-span-1"},De={key:5,class:"sm:col-span-1"},Oe={key:6,class:"sm:col-span-3"},Ee={key:7,class:"sm:col-span-2"},Me={class:"mt-4 flex justify-start"},Be={class:"text-base font-semibold"},je={key:8,class:"sm:col-span-2"},Re={key:9,class:"sm:col-span-2"},Le={key:10,class:"sm:col-span-2"},Ye={class:"relative mt-2"},qe={key:11,class:"sm:col-span-3"},He={class:"sm:col-span-6"},Ie={class:"overflow-x-auto divide-y divide-gray-300 w-full"},Ze=o("div",{class:"w-full"},[o("thead",{class:"w-full"},[o("tr",{class:""},[o("th",{scope:"col",class:"w-8"}),o("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),o("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),o("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1),Ge={style:{"overflow-y":"auto","max-height":"318px"}},Je={class:"divide-y divide-gray-300 bg-white"},Ke={class:"whitespace-nowrap px-2 text-sm text-gray-900"},Qe={class:"text-sm text-gray-900 leading-6 py-1.5"},We={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},Xe={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},et={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},tt={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},ot={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},st={key:0,class:"text-red-500 text-xs absolute"},at={class:"whitespace-nowrap px-2 text-sm text-gray-900"},nt={class:"sm:col-span-2"},lt={class:"mt-2 p-3 bg-gray-50 rounded-md"},it={class:"space-y-2 text-sm"},rt={class:"flex items-center gap-2"},dt=o("hr",{class:"my-2"},null,-1),ut={class:"flex justify-between items-center font-semibold"},mt=o("span",null,"Settlement:",-1),ct={class:"flex justify-between items-center font-semibold"},_t=o("span",null,"Advance Amount:",-1),pt={key:0,class:"text-red-500 text-xs absolute"},vt={key:12,class:"sm:col-span-6"},yt={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},ft=o("thead",null,[o("tr",null,[o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1),gt={class:"divide-y divide-gray-300 bg-white"},ht={class:"whitespace-nowrap py-3 text-sm text-gray-900"},xt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},bt={class:"flex flex-col"},kt={class:"text-sm text-gray-900"},wt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},Vt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},At={class:"flex mt-6 items-center justify-between"},Nt={class:"ml-auto flex items-center justify-end gap-x-6"},Ct=o("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Ft={key:0,class:"text-sm text-gray-600"},Rt={__name:"Edit",props:["payment","paymentType","bankinfo","organization","customers","invoices","credit"],setup(S){const r=S;b([]);const E=b([]),Z=r.bankinfo.filter(a=>a.organization_id===r.payment.organization_id);E.value=Z;const V=b(r.payment.payment_type),t=ce("post","/receipt",{id:r.payment.id,organization_id:r.payment.organization_id,customer_id:r.payment.customer_id,payment_type:r.payment.payment_type,date:r.payment.date,note:r.payment.note,amount:r.payment.amount,tds_amount:r.payment.tds_amount||0,discount_amount:r.payment.discount_amount||0,round_off:r.payment.round_off||0,check_number:r.payment.check_number,bank_name:r.payment.bank_name,org_bank_id:r.payment.org_bank_id,invoice:[],settled_amount:"",advance_amount:"",is_credit:"No",credit_data:[],total_unused_amount:""}),G=()=>{t.settled_amount=F.value,t.advance_amount=z.value,t.total_unused_amount=A.value,t.is_credit=c.value,t.invoice=g.value,t.credit_data=x.value,t.submit({preserveScroll:!0,onSuccess:()=>{}})},J=(a,s)=>{V.value=a,t.payment_type=a,t.errors.payment_type=null,s==="Cash"?t.note="Cash":t.note==="Cash"&&(t.note="")},C=b([]),x=b([]),A=b(""),R=(a,s)=>{const e=r.bankinfo.filter(p=>p.organization_id===a);E.value=e;const _=r.invoices.filter(p=>p.organization_id===a&&p.customer_id===t.customer_id);C.value=_;const i=r.credit.filter(p=>p.organization_id===a&&p.customer_id===t.customer_id);x.value=i,A.value=x.value.reduce((p,h)=>p+h.unused_amount,0),t.organization_id=a,t.errors.organization_id=null},K=(a,s)=>{const e=r.invoices.filter(i=>i.customer_id===a&&i.organization_id===t.organization_id);C.value=e;const _=r.credit.filter(i=>i.customer_id===a&&i.organization_id===t.organization_id);x.value=_,A.value=x.value.reduce((i,p)=>i+p.unused_amount,0),t.customer_id=a,t.errors.customer_id=null},Q=(a,s)=>{t.org_bank_id=a,t.errors.org_bank_id=null},F=I(()=>g.value.reduce((a,s)=>{if(s.check&&s.amount){const e=parseFloat(s.amount);return s.invoice_type==="sales"?a+e:a-e}return a},0)),z=I(()=>{const a=parseFloat(t.amount||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0),s=parseFloat(t.round_off||0),e=F.value;return a-e-s}),M=()=>{},k=a=>{let s=a.toFixed(2).toString(),[e,_]=s.split("."),i=e.substring(e.length-3),p=e.substring(0,e.length-3);return p!==""&&(i=","+i),`${p.replace(/\B(?=(\d{2})+(?!\d))/g,",")+i}.${_}`},L=a=>{const s=new Date(a),e={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",e)},c=b("No"),W=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],X=a=>{if(!g.value[a].check){g.value[a].amount=0;return}const s=r.payment.invoice_data.find(e=>e.id===g.value[a].id&&(e.invoice_type||"sales")===g.value[a].invoice_type);if(s)g.value[a].amount=parseFloat(s.amount).toFixed(2);else{let _=c.value==="Yes"?parseFloat(A.value||0):parseFloat(t.amount||0)+parseFloat(t.round_off||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0);g.value.forEach((h,$)=>{if(h.check&&$!==a&&parseFloat(h.amount||0)>0){const U=parseFloat(h.amount||0);h.invoice_type==="sales"?_-=U:h.invoice_type==="purchase"&&(_+=U)}});const i=parseFloat(g.value[a].pending_amount||0),p=Math.min(i,Math.max(0,_));g.value[a].amount=p.toFixed(2)}},g=b([]),P=()=>{g.value=C.value.map(a=>{const s=a.invoice_type||"sales",e=r.payment.invoice_data.some(i=>i.id===a.id&&(i.invoice_type||"sales")===s),_=e?r.payment.invoice_data.find(i=>i.id===a.id&&(i.invoice_type||"sales")===s).amount:0;return{id:a.id,date:a.date,invoice_no:a.invoice_no,total_amount:parseFloat(a.total_amount||0).toFixed(2),pending_amount:parseFloat(a.original_pending_amount||a.pending_amount||0).toFixed(2),invoice_type:s,check:e,amount:e?_.toString():"0.00"}})};B(C,()=>{P()}),B(c,()=>{P()}),B(()=>t.amount,()=>{c.value==="No"&&P()});const N=a=>{t.errors[a]=null,t.errors.settled_amount=null};return ee(()=>{R(r.payment.organization_id),C.value=r.invoices,P();const a=r.credit.filter(s=>s.organization_id===r.payment.organization_id&&s.customer_id===r.payment.customer_id);x.value=a,A.value=x.value.reduce((s,e)=>s+e.unused_amount,0)}),(a,s)=>(d(),u(D,null,[l(n(te),{title:"Edit Receipt"}),l(ie,null,{default:T(()=>[o("div",pe,[o("div",ve,[o("div",ye,[fe,o("div",ge,[x.value.length>0?(d(),u("div",he," Credits Available: ₹"+m(k(A.value)),1)):v("",!0)])]),o("form",{onSubmit:oe(G,["prevent"]),class:""},[o("div",be,[o("div",ke,[o("div",we,[l(f,{for:"payment_type",value:"Organization"}),o("div",Ve,[l(O,{options:S.organization,modelValue:n(t).organization_id,"onUpdate:modelValue":s[0]||(s[0]=e=>n(t).organization_id=e),onOnchange:R,class:y({"error rounded-md":n(t).errors.organization_id}),editMode:"editMode"},null,8,["options","modelValue","class"])])]),o("div",Ae,[l(f,{for:"payment_type",value:"Customer"}),o("div",Ne,[l(O,{options:S.customers,modelValue:n(t).customer_id,"onUpdate:modelValue":s[1]||(s[1]=e=>n(t).customer_id=e),onOnchange:K,class:y({"error rounded-md":n(t).errors.customer_id}),editMode:"editMode"},null,8,["options","modelValue","class"])])]),o("div",Ce,[l(f,{for:"role_id",value:"Payment Through Credit ?"}),o("div",Fe,[l(me,{modelValue:c.value,"onUpdate:modelValue":s[2]||(s[2]=e=>c.value=e),options:W},null,8,["modelValue"])])]),c.value=="No"?(d(),u("div",ze,[l(f,{for:"payment_type",value:"Payment Type"}),o("div",$e,[l(O,{options:S.paymentType,modelValue:n(t).payment_type,"onUpdate:modelValue":s[3]||(s[3]=e=>n(t).payment_type=e),onOnchange:J,class:y({"error rounded-md":n(t).errors.payment_type})},null,8,["options","modelValue","class"])])])):v("",!0),c.value=="No"?(d(),u("div",Ue,[l(f,{for:"date",value:"Payment Date"}),se(o("input",{"onUpdate:modelValue":s[4]||(s[4]=e=>n(t).date=e),class:y(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":n(t).errors.date}]),type:"date",onChange:s[5]||(s[5]=e=>N("date"))},null,34),[[ae,n(t).date]])])):v("",!0),c.value=="No"?(d(),u("div",Se)):v("",!0),c.value=="No"?(d(),u("div",Pe,[l(f,{for:"tds_amount",value:"TDS Amount"}),l(w,{type:"text",onChange:s[6]||(s[6]=e=>N("tds_amount")),onInput:s[7]||(s[7]=e=>M()),modelValue:n(t).tds_amount,"onUpdate:modelValue":s[8]||(s[8]=e=>n(t).tds_amount=e),class:y({"error rounded-md":n(t).errors.tds_amount})},null,8,["modelValue","class"])])):v("",!0),c.value=="No"?(d(),u("div",Te,[l(f,{for:"discount_amount",value:"Discount Amount"}),l(w,{type:"text",onChange:s[9]||(s[9]=e=>N("discount_amount")),onInput:s[10]||(s[10]=e=>M()),modelValue:n(t).discount_amount,"onUpdate:modelValue":s[11]||(s[11]=e=>n(t).discount_amount=e),class:y({"error rounded-md":n(t).errors.discount_amount})},null,8,["modelValue","class"])])):v("",!0),c.value=="No"?(d(),u("div",De,[l(f,{for:"round_off",value:"Round Off"}),l(w,{type:"text",onChange:s[12]||(s[12]=e=>N("round_off")),modelValue:n(t).round_off,"onUpdate:modelValue":s[13]||(s[13]=e=>n(t).round_off=e),class:y({"error rounded-md":n(t).errors.round_off})},null,8,["modelValue","class"])])):v("",!0),c.value=="No"?(d(),u("div",Oe,[l(f,{for:"amount",value:"Amount"}),l(w,{id:"amount",type:"text",onChange:s[14]||(s[14]=e=>N("amount")),onInput:s[15]||(s[15]=e=>M()),modelValue:n(t).amount,"onUpdate:modelValue":s[16]||(s[16]=e=>n(t).amount=e),class:y({"error rounded-md":n(t).errors.amount})},null,8,["modelValue","class"])])):v("",!0),c.value=="No"?(d(),u("div",Ee,[l(f,{for:"advance",value:"Advance(Ref) Amount"}),o("div",Me,[o("p",Be,m(k(z.value)),1)])])):v("",!0),V.value=="check"||V.value=="Cheque"&&c.value=="No"?(d(),u("div",je,[l(f,{for:"check_number",value:"Cheque Number"}),l(w,{id:"check_number",type:"text",modelValue:n(t).check_number,"onUpdate:modelValue":s[17]||(s[17]=e=>n(t).check_number=e),class:y({"error rounded-md":n(t).errors["data.check_number"]})},null,8,["modelValue","class"])])):v("",!0),V.value=="check"&&c.value=="No"?(d(),u("div",Re,[l(f,{for:"bank_name",value:"Bank Name"}),l(w,{id:"bank_name",type:"text",modelValue:n(t).bank_name,"onUpdate:modelValue":s[18]||(s[18]=e=>n(t).bank_name=e),class:y({"error rounded-md":n(t).errors["data.bank_name"]})},null,8,["modelValue","class"])])):v("",!0),V.value!="cash"&&c.value=="No"?(d(),u("div",Le,[l(f,{for:"org_bank_id",value:"Our Bank"}),o("div",Ye,[l(O,{options:E.value,modelValue:n(t).org_bank_id,"onUpdate:modelValue":s[19]||(s[19]=e=>n(t).org_bank_id=e),onOnchange:Q,class:y({"error rounded-md":n(t).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):v("",!0),V.value!="cash"&&c.value=="No"?(d(),u("div",qe)):v("",!0),o("div",He,[o("table",Ie,[Ze,o("div",Ge,[o("tbody",Je,[(d(!0),u(D,null,j(g.value,(e,_)=>(d(),u("tr",{key:_},[o("td",Ke,[o("div",Qe,[l(_e,{name:"check",checked:e.check,"onUpdate:checked":i=>e.check=i,onChange:i=>X(_)},null,8,["checked","onUpdate:checked","onChange"])])]),o("td",We,[o("span",{class:y([e.invoice_type==="sales"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},m(e.invoice_type==="sales"?"SALES":"PURCHASE"),3)]),o("td",Xe,m(e.invoice_no),1),o("td",et,m(e.total_amount),1),o("td",tt,m(e.pending_amount),1),o("td",ot,[l(w,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":i=>e.amount=i,onChange:i=>N("invoice."+_+".amount"),class:y({error:n(t).errors[`invoice.${_}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),n(t).errors[`invoice.${_}.amount`]?(d(),u("p",st,m(n(t).errors[`invoice.${_}.amount`]),1)):v("",!0)]),o("td",at,m(L(e.date)),1)]))),128))])])])]),o("div",nt,[l(f,{for:"note",value:"Net Settlement Summary"}),o("div",lt,[o("div",it,[(d(!0),u(D,null,j(g.value.filter(e=>e.check),e=>(d(),u("div",{key:e.id,class:"flex justify-between items-center"},[o("div",rt,[o("span",{class:y([e.invoice_type==="sales"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800","inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium"])},m(e.invoice_type==="sales"?"S":"P"),3),o("span",null,m(e.invoice_no),1)]),o("span",{class:y([e.invoice_type==="sales"?"text-green-600":"text-blue-600","font-medium"])},m(e.invoice_type==="sales"?"+":"-")+"₹"+m(k(parseFloat(e.amount||0))),3)]))),128)),dt,o("div",ut,[mt,o("span",{class:y(F.value>=0?"text-green-600":"text-red-600")}," ₹"+m(k(Math.abs(F.value)))+" "+m(F.value>=0?"(Receive)":"(Pay)"),3)]),o("div",ct,[_t,o("span",{class:y(z.value>=0?"text-green-600":"text-red-600")},m(z.value>0?"+":"")+"₹"+m(k(parseFloat(z.value||0))),3)])])]),n(t).errors.settled_amount?(d(),u("p",pt,m(n(t).errors.settled_amount),1)):v("",!0)]),c.value=="No"?(d(),u("div",vt,[l(f,{for:"note",value:"Note"}),l(ue,{id:"note",type:"text",rows:2,modelValue:n(t).note,"onUpdate:modelValue":s[20]||(s[20]=e=>n(t).note=e)},null,8,["modelValue"])])):v("",!0)]),x.value.length>0&&c.value=="Yes"?(d(),u("table",yt,[ft,o("tbody",gt,[(d(!0),u(D,null,j(x.value,(e,_)=>{var i,p,h,$,U,Y,q,H;return d(),u("tr",{key:_},[o("td",ht,m(L(e.date)),1),o("td",xt,[o("div",bt,[o("div",kt,m((p=(i=e.paymentreceive)==null?void 0:i.bank_info)!=null&&p.bank_name?($=(h=e.paymentreceive)==null?void 0:h.bank_info)==null?void 0:$.bank_name:"Cash")+" - "+m((Y=(U=e.paymentreceive)==null?void 0:U.bank_info)!=null&&Y.account_number?(H=(q=e.paymentreceive)==null?void 0:q.bank_info)==null?void 0:H.account_number:""),1)])]),o("td",wt,m(k(e.amount)),1),o("td",Vt,m(k(e.unused_amount)),1)])}),128))])])):v("",!0)]),o("div",At,[o("div",Nt,[l(re,{href:a.route("receipt.index")},{svg:T(()=>[Ct]),_:1},8,["href"]),l(de,{disabled:n(t).processing},{default:T(()=>[ne("Update")]),_:1},8,["disabled"]),l(le,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:T(()=>[n(t).recentlySuccessful?(d(),u("p",Ft,"Saved.")):v("",!0)]),_:1})])])],40,xe)])])]),_:1})],64))}};export{Rt as default};
