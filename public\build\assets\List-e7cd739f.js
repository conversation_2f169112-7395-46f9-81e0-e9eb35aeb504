import{_ as y,b as w,a as b}from"./AdminLayout-d9d2bc31.js";import{_ as p}from"./CreateButton-7995b8ff.js";import{_ as v}from"./SecondaryButton-69637431.js";import{D as k}from"./DangerButton-b7cb11b9.js";import{M as C}from"./Modal-85d770f4.js";import{_ as B}from"./Pagination-b0edb9e0.js";import{h as L,r as x,o as a,c as i,a as s,u as M,w as e,F as h,Z as N,b as t,g as r,i as T,e as $,f as j,t as _}from"./app-4f4c883b.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const E={class:"animate-top"},V={class:"sm:flex sm:items-center"},z=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Email Tags")],-1),A={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},I={class:"flex justify-end w-20"},D={class:"flex justify-end"},O={class:"mt-8 overflow-x-auto sm:rounded-lg"},S={class:"shadow sm:rounded-lg"},F={class:"w-full text-sm text-left rtl:text-right text-gray-500"},U=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"Tag NAME"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"DESCRIPTION"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"ACTION")])],-1),H={key:0},P={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},R=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1),Z=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1),q={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-normal text-gray-900"},G=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1),J=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1),K={class:"items-center px-4 py-2.5"},Q={class:"flex items-center justify-start gap-4"},W=t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),X=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),Y=t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),tt=["onClick"],et=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),st=t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),ot=[et,st],at={key:1},lt=t("tr",{class:"bg-white"},[t("td",{colspan:"8",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),nt=[lt],it={class:"p-6"},rt=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),ct={class:"mt-6 flex justify-end"},vt={__name:"List",props:["data"],setup(o){const u=L({}),c=x(!1),m=x(null),g=l=>{m.value=l,c.value=!0},d=()=>{c.value=!1},f=()=>{u.delete(route("email-tag.destroy",{id:m.value}),{onSuccess:()=>d()})};return(l,dt)=>(a(),i(h,null,[s(M(N),{title:"Email Tags"}),s(y,null,{default:e(()=>[t("div",E,[t("div",V,[z,t("div",A,[t("div",I,[s(p,{href:l.route("setting")},{default:e(()=>[r(" Back ")]),_:1},8,["href"])]),t("div",D,[s(p,{href:l.route("email-tag.create")},{default:e(()=>[r(" Add Tag ")]),_:1},8,["href"])])])]),t("div",O,[t("div",S,[t("table",F,[U,o.data.data&&o.data.data.length>0?(a(),i("tbody",H,[(a(!0),i(h,null,T(o.data.data,(n,mt)=>(a(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:n.id},[t("td",P,_(n.name??"-"),1),R,Z,t("td",q,_(n.description??"-"),1),G,J,t("td",K,[t("div",Q,[s(w,{align:"right",width:"48"},{trigger:e(()=>[W]),content:e(()=>[s(b,{href:l.route("email-tag.edit",{id:n.id})},{svg:e(()=>[X]),text:e(()=>[Y]),_:2},1032,["href"]),t("button",{type:"button",onClick:pt=>g(n.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},ot,8,tt)]),_:2},1024)])])]))),128))])):(a(),i("tbody",at,nt))])])]),o.data.data&&o.data.data.length>0?(a(),$(B,{key:0,class:"mt-6",links:o.data.links},null,8,["links"])):j("",!0)]),s(C,{show:c.value,onClose:d},{default:e(()=>[t("div",it,[rt,t("div",ct,[s(v,{onClick:d},{default:e(()=>[r(" Cancel ")]),_:1}),s(k,{class:"ml-3",onClick:f},{default:e(()=>[r(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{vt as default};
