import{_ as F,b as R,a as K}from"./AdminLayout-5eccc000.js";import{_ as Y}from"./CreateButton-19955a3e.js";import{_ as q}from"./SecondaryButton-79105c78.js";import{D as H}from"./DangerButton-5e5c1802.js";import{M as Z}from"./Modal-65b3f5d9.js";import{_ as G}from"./Pagination-e0ea25b7.js";import{_ as J}from"./SearchableDropdownNew-56a3c1f2.js";import{_ as Q}from"./SimpleDropdown-6361db30.js";import{_ as w}from"./InputLabel-3a43d7c9.js";import{r as u,l as W,o as a,c as _,a as n,u as g,w as i,F as k,Z as X,b as t,g as h,i as $,e as C,f as v,t as r}from"./app-4ea19997.js";import{_ as D}from"./ArrowIcon-151eb820.js";import{s as tt}from"./sortAndSearch-931279ce.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const et={class:"animate-top"},ot={class:"flex justify-between items-center"},st=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Receive Payments")],-1),nt={class:"flex justify-end"},at=t("div",{class:"flex space-x-6 mt-4 sm:mt-0 w-64"},[t("div",{class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},[t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})]),t("input",{id:"search-field",class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"})])],-1),lt={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},it={class:"flex justify-end"},rt={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},dt={class:"flex mb-2"},ct=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),mt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},_t={class:"sm:col-span-4"},ut={class:"relative mt-2"},ht={class:"sm:col-span-4"},ft={class:"relative mt-2"},pt={class:"mt-8 overflow-x-auto sm:rounded-lg"},gt={class:"shadow sm:rounded-lg"},vt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},xt={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},yt={class:"border-b-2"},bt=["onClick"],wt={key:0},kt={class:"px-4 py-2.5 min-w-32"},Ct={class:"px-4 py-2.5 min-w-52 text-sm flex flex-col font-medium text-gray-900"},zt={class:"tooltiptext text-xs"},Mt={class:"px-4 py-2.5 min-w-36"},Nt={class:"px-4 py-2.5 min-w-52"},It={class:"px-4 py-2.5 min-w-36"},$t={class:"px-4 py-2.5 min-w-32"},St={class:"items-center px-4 py-2.5"},Vt={class:"flex items-center justify-start gap-4"},At=t("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Bt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),Ot=t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),jt=["onClick"],Lt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),Et=t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),Pt=[Lt,Et],Tt={key:1},Ut=t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Ft=[Ut],Rt={class:"p-6"},Kt=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this transaction? ",-1),Yt={class:"mt-6 flex justify-end"},de={__name:"List",props:["data","organization","customers","organizationId","customerId"],setup(l){const f=l,{form:z,search:qt,sort:S,fetchData:Ht,sortKey:V,sortDirection:A,updateParams:B}=tt("receipt.index",{organization_id:f.organizationId,customer_id:f.customerId}),x=u(!1),M=u(null),O=[{field:"date",label:"DATE",sortable:!0},{field:"customers.customer_name",label:"CUSTOMER NAME",sortable:!0},{field:"payment_type",label:"PAYMENT TYPE",sortable:!0},{field:"bank_info.bank_name",label:"BANK",sortable:!0},{field:"invoice.invoice_no",label:"INVOICE NO",sortable:!1},{field:"amount",label:"AMOUNT (₹)",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],j=o=>{M.value=o,x.value=!0},y=()=>{x.value=!1},L=()=>{z.delete(route("receipt.destroy",{id:M.value}),{onSuccess:()=>y()})},E=o=>{const s=new Date(o),e={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",e)},P=o=>{let s=o.toFixed(2).toString(),[e,p]=s.split("."),m=e.substring(e.length-3),I=e.substring(0,e.length-3);return I!==""&&(m=","+m),`${I.replace(/\B(?=(\d{2})+(?!\d))/g,",")+m}.${p}`},d=u(f.organizationId),c=u(f.customerId),b=u("");W([d,c],()=>{B({organization_id:d.value,company_id:c.value})});const N=(o,s,e)=>{b.value=o,z.get(route("receipt.index",{search:o,organization_id:s,customer_id:e}),{preserveState:!0})},T=(o,s)=>{d.value=o,N(b.value,d.value,c.value)},U=(o,s)=>{c.value=o,N(b.value,d.value,c.value)};return(o,s)=>(a(),_(k,null,[n(g(X),{title:"Receipt"}),n(F,null,{default:i(()=>[t("div",et,[t("div",ot,[st,t("div",nt,[at,t("div",lt,[t("div",it,[n(Y,{href:o.route("receipt.create")},{default:i(()=>[h(" Receive payment ")]),_:1},8,["href"])])])])]),t("div",rt,[t("div",dt,[ct,n(w,{for:"customer_id",value:"Filters"})]),t("div",mt,[t("div",_t,[n(w,{for:"customer_id",value:"Organization Name"}),t("div",ut,[n(Q,{options:l.organization,modelValue:d.value,"onUpdate:modelValue":s[0]||(s[0]=e=>d.value=e),onOnchange:T},null,8,["options","modelValue"])])]),t("div",ht,[n(w,{for:"customer_id",value:"Customer Name"}),t("div",ft,[n(J,{options:l.customers,modelValue:c.value,"onUpdate:modelValue":s[1]||(s[1]=e=>c.value=e),onOnchange:U},null,8,["options","modelValue"])])])])]),t("div",pt,[t("div",gt,[t("table",vt,[t("thead",xt,[t("tr",yt,[(a(),_(k,null,$(O,(e,p)=>t("th",{key:p,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:m=>g(S)(e.field,e.sortable)},[h(r(e.label)+" ",1),e.sortable?(a(),C(D,{key:0,isSorted:g(V)===e.field,direction:g(A)},null,8,["isSorted","direction"])):v("",!0)],8,bt)),64))])]),l.data.data&&l.data.data.length>0?(a(),_("tbody",wt,[(a(!0),_(k,null,$(l.data.data,(e,p)=>(a(),_("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",kt,r(E(e.date)??"-"),1),t("td",Ct,[h(r(e.customers.customer_name??"-")+" ",1),t("span",zt,r(e.customers.city??"-"),1)]),t("td",Mt,r(e.payment_type=="check"?"Cheque":e.payment_type??"-"),1),t("td",Nt,r(e!=null&&e.bank_info?(e==null?void 0:e.bank_info.bank_name)+"-"+(e==null?void 0:e.bank_info.account_number):"-"),1),t("td",It,r(e!=null&&e.invoice_data?e.invoice_data.map(m=>m.invoice_no).join(", "):e.invoice_no),1),t("td",$t,r(P(e.amount)??"-"),1),t("td",St,[t("div",Vt,[n(R,{align:"right",width:"48"},{trigger:i(()=>[At]),content:i(()=>[e.invoice_data.length!=0?(a(),C(K,{key:0,href:o.route("receipt.edit",e.id)},{svg:i(()=>[Bt]),text:i(()=>[Ot]),_:2},1032,["href"])):v("",!0),e.invoice_id==null?(a(),_("button",{key:1,type:"button",onClick:m=>j(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Pt,8,jt)):v("",!0)]),_:2},1024)])])]))),128))])):(a(),_("tbody",Tt,Ft))])])]),l.data.data&&l.data.data.length>0?(a(),C(G,{key:0,class:"mt-6",links:l.data.links},null,8,["links"])):v("",!0)]),n(Z,{show:x.value,onClose:y},{default:i(()=>[t("div",Rt,[Kt,t("div",Yt,[n(q,{onClick:y},{default:i(()=>[h(" Cancel ")]),_:1}),n(H,{class:"ml-3",onClick:L},{default:i(()=>[h(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{de as default};
