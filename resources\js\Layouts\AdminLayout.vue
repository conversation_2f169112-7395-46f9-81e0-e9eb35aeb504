<script setup>
import {ref, onMounted} from 'vue';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import Dropdown from '@/Components/Dropdown.vue';
import DropdownLink from '@/Components/DropdownLink.vue';
import NavLink from '@/Components/NavLink.vue';
import SideMenu from '@/Components/SideMenu.vue';
import ToastNotificationVue from '@/Components/ToastNotification.vue'
import ToastNotificationSuccessVue from '@/Components/ToastNotificationSuccess.vue'
import ToastNotificationErrorVue from '@/Components/ToastNotificationError.vue'
import ToastNotificationWarningVue from '@/Components/ToastNotificationWarning.vue'
import ActionLink from '@/Components/ActionLink.vue';
import NotificationDropdown from '@/Components/NotificationDropdown.vue';
import {Link, useForm} from '@inertiajs/vue3';

const logoSrc = ref('/uploads/companyprofile/defaultimg.png');
const form = useForm({});
const updateLogoSrc = (value) => {
    logoSrc.value = value;
};

const visionlogo = ref('/uploads/companyprofile/visionlogo.png');

onMounted(async () => {
    try {
        const response = await fetch('/api/logo');
        if (response.ok) {
            const data = await response.json();
            if (data.logoUrl) {
                updateLogoSrc('/uploads/companyprofile/' + data.logoUrl);
            } else {
                updateLogoSrc('/uploads/companyprofile/defaultimg.png');
            }
        }
    } catch (error) {
        console.error('Error fetching logo:', error);
    }
});

const props = defineProps({
    notifications: {
        type: Array,
        default: () => []
    }
});

const hasPermission = (permission) => {
//    return this.$store.state.user.permissions.includes(permission);
};

</script>

<template>
    <div>
        <!-- Static sidebar for desktop -->
        <div class="lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col border-t border-r">
            <div class="bg-white flex h-16 px-6 items-center px-10 shrink-0 w-full mt-2">
                <img class="h-10" :src="visionlogo" alt="LOGO">
            </div>
            <!-- Sidebar component, swap this element with another sidebar if you like -->
            <div class="flex grow flex-col gap-y-2 overflow-y-auto bg-gray-900 mt-2">
                <nav class="flex flex-1 flex-col px-6">
                    <ul role="list" class="flex flex-1 flex-col gap-y-7">
                        <li>
                            <ul role="list" class="-mx-2 space-y-1">
                                <li>
                                    <SideMenu v-if="$can('Dashboard')" :href="route('dashboard')"
                                              :active="route().current('dashboard')">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                                 :class="route().current('dashboard') ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                 fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"/>
                                            </svg>
                                        </template>
                                        <template #name>Dashboard</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Customer')" :href="route('customers.index')"
                                              :active="(route().current('customers.index') || route().current('customers.create') || route().current('customers.edit') || route().current('customers.transaction') || route().current('customers.credit') ||route().current('service-reports.show') || route().current('service-reports.create') || route().current('service-reports.edit') || route().current('upload-service-report') || route().current('service-reports.view'))">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                                 :class="(route().current('customers.index') || route().current('customers.create') || route().current('customers.edit') || route().current('customers.transaction') || route().current('customers.credit') || route().current('service-reports.show') || route().current('service-reports.create') || route().current('service-reports.edit') || route().current('upload-service-report') || route().current('service-reports.view')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                 fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm3 14l-3-3-3 3M12 5a4 4 0 1 0 0 8 4 4 0 0 0 0-8z"></path>
                                            </svg>
                                        </template>
                                        <template #name>Customers</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Quotation')" :href="route('quotation.index')"
                                              :active="(route().current('quotation.index') || route().current('quotation.create') || route().current('quotation.edit') || route().current('quotation.view') || route().current('quotation.order'))">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                                :class="(route().current('quotation.index') || route().current('quotation.create') || route().current('quotation.edit') || route().current('quotation.view') || route().current('quotation.order')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M6 2h10l4 4v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"/>
                                                <path d="M14 2v4h4"/>
                                                <path d="M8 10h8"/>
                                                <path d="M8 14h6"/>
                                                <path d="M8 18h8"/>
                                            </svg>
                                        </template>
                                        <template #name>Quotation</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Orders')" :href="route('proforma-invoice.index')"
                                              :active="(route().current('proforma-invoice.index') || route().current('proforma-invoice.create') || route().current('proforma-invoice.edit') || route().current('proforma-invoice.view') || route().current('proforma-invoice.deliver'))">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                            :class="(route().current('proforma-invoice.index') || route().current('proforma-invoice.create') || route().current('proforma-invoice.edit') || route().current('proforma-invoice.view') || route().current('proforma-invoice.deliver')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                            fill="none" viewBox="0 0 32 32" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <rect x="5" y="4" width="14" height="20" rx="2"/>
                                            <path d="M7 4v2"/>
                                            <path d="M17 4v2"/>
                                            <path d="M7 8h10"/>
                                            <path d="M7 12h10"/>
                                            <path d="M7 16h6"/>
                                            <rect x="22" y="5" width="3" height="18" rx="1"/>
                                            <path d="M22 23h3v2h-3z"/>
                                        </svg>
                                        </template>
                                        <template #name>Proforma Invoice</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Orders')" :href="route('orders.index')"
                                              :active="(route().current('orders.index') || route().current('orders.create') || route().current('orders.edit') || route().current('orders.view') || route().current('orders.deliver') || route().current('orders.generate'))">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                            :class="(route().current('orders.index') || route().current('orders.create') || route().current('orders.edit') || route().current('orders.view') || route().current('orders.deliver') || route().current('orders.generate')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                            fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M9 3.5l2.5 2.5 5-5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <g transform="translate(0, 2)">
                                                <path d="M3 6h2l3.6 9.6a1 1 0 0 0 .94.7h8.92a1 1 0 0 0 .94-.7L21 8H6" />
                                                <circle cx="9" cy="20" r="2" />
                                                <circle cx="17" cy="20" r="2" />
                                            </g>
                                        </svg>
                                        </template>
                                        <template #name>Orders</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Company')" :href="route('companies.index')"
                                              :active="(route().current('companies.index') || route().current('companies.create') || route().current('companies.edit') || route().current('products.show') || route().current('products.create') || route().current('products.edit') || route().current('companies.transaction') || route().current('companies.credit'))">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                                :class="(route().current('companies.index') || route().current('companies.create') || route().current('companies.edit') || route().current('products.show') || route().current('products.create') || route().current('products.edit') || route().current('companies.transaction') || route().current('companies.credit')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M4 21V7a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v14" />
                                                <path d="M8 10h2M8 14h2M8 18h2M14 10h2M14 14h2M14 18h2" />
                                                <path d="M10 21v-4h4v4" />
                                                <path d="M2 21h20" />
                                            </svg>
                                        </template>
                                        <template #name>Company</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Companypo')" :href="route('companypo.index')"
                                              :active="(route().current('companypo.index') || route().current('companypo.create') || route().current('companypo.receivepo') || route().current('companypo.viewpo') || route().current('companypo.edit'))">
                                    <template #svg>
                                        <svg class="h-6 w-6 shrink-0"
                                                :class="(route().current('companypo.index') || route().current('companypo.create') || route().current('companypo.receivepo') || route().current('companypo.viewpo') || route().current('companypo.edit')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                            <path d="M6 2h10l4 4v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z" />
                                            <path d="M14 2v4h4" />
                                            <path d="M8 12h8l-1.5 4h-5z" />
                                            <circle cx="10" cy="18" r="1" />
                                            <circle cx="14" cy="18" r="1" />
                                        </svg>
                                        </template>
                                        <template #name>Company PO</template>
                                    </SideMenu>
                                </li>
                                <li>
                                 <SideMenu v-if="$can('List PurchaseInvoice')" :href="route('purchaseinvoice.index')" :active="(route().current('purchaseinvoice.index') || route().current('purchaseinvoice.view') || route().current('purchaseinvoice.edit') || route().current('purchaseinvoice.convert-to-invoice'))">
                                     <template #svg>
                                        <svg class="h-6 w-6 shrink-0"
                                            :class="(route().current('purchaseinvoice.index') || route().current('purchaseinvoice.view') || route().current('purchaseinvoice.edit') || route().current('purchaseinvoice.convert-to-invoice')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                            fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">

                                            <rect x="4" y="3" width="16" height="18" rx="2" ry="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M6 3v2M9 3v2M12 3v2M15 3v2M18 3v2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <rect x="5" y="7" width="3" height="3" rx="0.5" ry="0.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M5.8 8.5l0.8 0.8 1.5-1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M10 8.5h8" stroke-linecap="round" stroke-linejoin="round"/>
                                            <rect x="5" y="12" width="3" height="3" rx="0.5" ry="0.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M5.8 13.5l0.8 0.8 1.5-1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M10 13.5h8" stroke-linecap="round" stroke-linejoin="round"/>
                                            <rect x="5" y="17" width="3" height="3" rx="0.5" ry="0.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M5.8 18.5l0.8 0.8 1.5-1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M10 18.5h8" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                     </template>
                                     <template  #name>
                                         Purchase
                                     </template>
                                 </SideMenu>
                               </li>
                                <li>
                                    <SideMenu v-if="$can('List Challan')" :href="route('challan.index')"
                                              :active="(route().current('challan.index') || route().current('challan.create') || route().current('challan.edit') || route().current('challan.view') || route().current('challan.invoice') || route().current('challan.combine-invoice') || route().current('challantransfer.edit') || route().current('challantransfer'))">
                                              <template #svg>
                                                    <svg
                                                        class="h-6 w-6 shrink-0 transition-colors duration-200"
                                                        :class="{
                                                            'text-white': ['challan.index', 'challan.create', 'challan.edit', 'challan.view', 'challan.invoice', 'challan.combine-invoice', 'challantransfer.edit', 'challantransfer'].includes(route().current()),
                                                            'text-gray-700 group-hover:text-white': !['challan.index', 'challan.create', 'challan.edit', 'challan.view', 'challan.invoice', 'challan.combine-invoice', 'challantransfer.edit', 'challantransfer'].includes(route().current())
                                                        }"
                                                        viewBox="0 0 24 24"
                                                        fill="none"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        stroke="currentColor"
                                                        stroke-width="1.5"
                                                    >
                                                        <rect x="3" y="2" width="18" height="20" rx="2" ry="2" />
                                                        <path d="M3 6h14M3 10h12M3 14h10M3 18h8" stroke-linecap="round" />
                                                        <path d="M16 15l4 4m-5-3l3 3m-2-4l4 4M14 17l5 5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                                    </svg>
                                        </template>
                                        <template #name>Challan</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Users')" :href="route('users.index')"
                                              :active="(route().current('users.index') || route().current('users.create') || route().current('users.edit') )">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                                 :class="(route().current('users.index') || route().current('users.create') || route().current('users.edit')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                 fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"/>
                                            </svg>
                                        </template>
                                        <template #name>Users</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Invoice')" :href="route('invoice.index')"
                                              :active="(route().current('invoice.index') || route().current('invoice.view') || route().current('stocktransfer') ||  route().current('invoice.create') || route().current('invoice.edit') || route().current('stocktransfer.edit') || route().current('challaninvoice.edit') || route().current('creditnote.add'))">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                                :class="(route().current('invoice.index') || route().current('invoice.view') || route().current('stocktransfer') ||
                                                        route().current('invoice.create') || route().current('invoice.edit') || route().current('stocktransfer.edit') ||
                                                        route().current('challaninvoice.edit') || route().current('creditnote.add')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                                <rect x="4" y="3" width="16" height="18" rx="2" ry="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                <text x="7" y="9" font-size="2.5" font-weight="normal" fill="currentColor">INVOICE</text>
                                                <path d="M7 12h10" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M7 15h10" stroke-linecap="round" stroke-linejoin="round"/>
                                                <path d="M7 18h10" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </template>
                                        <template #name>Invoice</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Jobcard')" :href="route('jobcard.index')"
                                              :active="(route().current('jobcard.index') || route().current('jobcard.show') || route().current('jobcard.create') || route().current('jobcard.edit'))">
                                        <template #svg>
                                            <svg
                                                class="h-6 w-6 shrink-0 transition-colors duration-200"
                                                :class="{
                                                    'text-white': ['jobcard.index', 'jobcard.show', 'jobcard.create', 'jobcard.edit'].includes(route().current()),
                                                    'text-gray-700 group-hover:text-white': !['jobcard.index', 'jobcard.show', 'jobcard.create', 'jobcard.edit'].includes(route().current())
                                                }"
                                                viewBox="0 0 48 48"
                                                fill="none"
                                                xmlns="http://www.w3.org/2000/svg"
                                                stroke="currentColor"
                                                stroke-width="2"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                            >
                                                <rect x="6" y="6" width="36" height="28" rx="3" ry="3" fill="none" />
                                                <circle cx="16" cy="14" r="4" fill="none" />
                                                <path d="M12 26v-5a6 6 0 0 1 8 0v5" fill="none" />
                                                <path d="M24 14h12" fill="none" />
                                                <path d="M24 20h12" fill="none" />
                                                <path d="M24 26h12" fill="none" />
                                            </svg>
                                        </template>
                                        <template #name>Service Jobcard</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Maintenance')" :href="route('maintenance-contract.index')"
                                              :active="(route().current('maintenance-contract.index') || route().current('maintenance-contract.create') || route().current('maintenance-contract.edit'))">
                                        <template #svg>
                                            <svg class="h-6 w-6"
                                                :class="(route().current('maintenance-contract.index')|| route().current('maintenance-contract.create') || route().current('maintenance-contract.edit')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M6 2h12a2 2 0 0 1 2 2v2H4V4a2 2 0 0 1 2-2zm0 4h12v14H6V6z" />
                                                <path d="M8 12h8v2H8v-2zm0 4h8v2H8v-4z" />
                                            </svg>
                                        </template>
                                        <template #name>Maintenance Contract</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('Sales Stock') || $can('Service Stock')" :href="route('salesstock')"
                                              :active="(route().current('salesstock') || route().current('servicestock') || route().current('products.history'))">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                                :class="(route().current('salesstock') || route().current('servicestock') || route().current('products.history')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
                                                <rect x="6" y="22" width="14" height="14" rx="2" ry="2" stroke="currentColor" fill="none" stroke-width="3"/>
                                                <rect x="26" y="22" width="14" height="14" rx="2" ry="2" stroke="currentColor" fill="none" stroke-width="3"/>
                                                <rect x="16" y="6" width="16" height="14" rx="2" ry="2" stroke="currentColor" fill="none" stroke-width="3"/>
                                                <path d="M16 20 L12 22" stroke="currentColor" stroke-width="3"/>
                                                <path d="M32 20 L36 22" stroke="currentColor" stroke-width="3"/>
                                            </svg>
                                        </template>
                                        <template #name>Stock</template>
                                    </SideMenu>
                                </li>
                                 <li>
                                    <SideMenu v-if="$can('List Bank Transaction')"
                                              :href="route('payment.index')"
                                              :active="(route().current('payment.index') || route().current('payment.create') || route().current('payment.edit'))">
                                        <template #svg>
                                            <svg class="h-6 w-6"
                                                :class="(route().current('payment.index') || route().current('payment.create') || route().current('payment.edit')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <rect x="3" y="5" width="18" height="14" rx="2" ry="2" stroke="currentColor"/>
                                                <path d="M21 10h-6a2 2 0 0 0-2 2v0a2 2 0 0 0 2 2h6" stroke="currentColor"/>
                                                <circle cx="17" cy="12" r="1.5" stroke="currentColor"/>
                                            </svg>
                                        </template>
                                        <template #name>Payment</template>
                                    </SideMenu>
                                </li>
                                 <li>
                                    <SideMenu v-if="$can('List Bank Transaction')"
                                              :href="route('receipt.index')"
                                              :active="(route().current('receipt.index') || route().current('receipt.create') || route().current('receipt.edit'))">
                                        <template #svg>
                                            <svg class="h-6 w-6"
                                                :class="(route().current('receipt.index') || route().current('receipt.create') || route().current('receipt.edit')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M5 2h14a2 2 0 0 1 2 2v16l-2-1-2 1-2-1-2 1-2-1-2 1-2-1-2 1V4a2 2 0 0 1 2-2z" stroke="currentColor"/>
                                                <line x1="8" y1="6" x2="16" y2="6" stroke="currentColor"/>
                                                <line x1="8" y1="10" x2="16" y2="10" stroke="currentColor"/>
                                                <line x1="8" y1="14" x2="12" y2="14" stroke="currentColor"/>
                                            </svg>
                                        </template>
                                        <template #name>Receipt</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Bank Transaction')"
                                              :href="route('banktransaction.index')"
                                              :active="(route().current('banktransaction.index') || route().current('banktransaction.show') || route().current('banktransaction.create') || route().current('banktransaction.edit'))">
                                        <template #svg>
                                            <svg class="h-6 w-6"
                                                :class="(route().current('banktransaction.index') || route().current('banktransaction.show') || route().current('banktransaction.create') || route().current('banktransaction.edit')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M3 10l9-7 9 7" />
                                                <path d="M4 10v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V10" />
                                                <path d="M8 21v-6h8v6" />
                                                <path d="M12 14v7" />
                                            </svg>
                                        </template>
                                        <template #name>Bank Transactions</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Reports')" :href="route('reports')"
                                              :active="(route().current('reports') || route().current('engineer-business.report')  || route().current('invoice.pending-amount') || route().current('sns-stock.report') || route().current('sns-sales.report')  || route().current('sns-sales.report') || route().current('sns-cusomersales-report') || route().current('hsn.sales.summary') ||  route().current('customer-transaction.report') || route().current('creditnote.index') || route().current('creditnote.show'))">
                                        <template #svg>
                                            <svg class="h-6 w-6"
                                                :class="(route().current('reports') || route().current('engineer-business.report') || route().current('invoice.pending-amount') || route().current('sns-stock.report') || route().current('sns-sales.report') || route().current('sns-customersales-report') || route().current('hsn.sales.summary') || route().current('creditnote.index') ||  route().current('customer-transaction.report') || route().current('creditnote.show')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M6 2h10l4 4v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"/>
                                                <path d="M14 2v4h4"/>
                                                <path d="M8 14v4"/>
                                                <path d="M12 10v8"/>
                                                <path d="M16 12v6"/>
                                            </svg>
                                        </template>
                                        <template #name>Reports</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Weekly Planning')" :href="route('weeklyplan.index')"
                                              :active="(route().current('weeklyplan.index') || route().current('weeklyplan.create') || route().current('weeklyplan.edit'))">
                                        <template #svg>
                                            <svg class="h-6 w-6"
                                                :class="(route().current('weeklyplan.index') || route().current('weeklyplan.create') || route().current('weeklyplan.edit')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <rect x="3" y="4" width="18" height="16" rx="2" ry="2"></rect>
                                                <path d="M8 2v4"/>
                                                <path d="M16 2v4"/>
                                                <path d="M3 10h18"/>
                                                <path d="M7 14l2 2 3-3"/>
                                            </svg>
                                        </template>
                                        <template #name>Daily Plan</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('List Funnel')" :href="route('funnel.index')"
                                              :active="(route().current('funnel.index') || route().current('funnel.create') || route().current('funnel.edit'))">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                                :class="(route().current('funnel.index') || route().current('funnel.create') || route().current('funnel.edit')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <path d="M3 4h18l-6 8v5l-6 3v-8z"/>
                                            </svg>
                                        </template>
                                        <template #name>Funnel</template>
                                    </SideMenu>
                                </li>
                                <li>
                                    <SideMenu v-if="$can('Activity Log')" :href="route('logs')"
                                                :active="(route().current('logs'))">
                                        <template #svg>
                                            <svg class="h-6 w-6 shrink-0"
                                                :class="(route().current('logs')) ? 'text-white' : 'text-gray-700 group-hover:text-white'"
                                                fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                <circle cx="8" cy="8" r="4"/>
                                                <path d="M3 20v-2a6 6 0 0 1 12 0v2"/>
                                                <rect x="14" y="6" width="6" height="10" rx="1"/>
                                                <path d="M16 8h2"/>
                                                <path d="M16 10h2"/>
                                                <path d="M16 12h2"/>
                                                <path d="M14 17l2 2 4-4"/>
                                            </svg>
                                        </template>
                                        <template #name>User Activity</template>
                                    </SideMenu>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>
            <div class="mt-auto px-4 mt-1">
                <SideMenu v-if="$can('List Setting')" :href="route('setting')" :active="(route().current('setting') || route().current('organization.index') || route().current('organization.create') || route().current('organization.edit') || route().current('manage-prefix') || route().current('roles.permission') ||
                        route().current('bankinfo.index') || route().current('bankinfo.create') || route().current('bankinfo.edit') ||
                        route().current('account-type.index') || route().current('account-type.create') || route().current('account-type.edit') ||
                        route().current('roles.index') || route().current('roles.create') || route().current('roles.edit') ||
                        route().current('jobcard-checklist.index') || route().current('jobcard-checklist.create') || route().current('jobcard-checklist.edit') ||
                        route().current('mail-configs.index') || route().current('mail-configs.create') || route().current('mail-configs.edit') ||
                        route().current('emailtemplates.index') || route().current('emailtemplates.create') || route().current('emailtemplates.edit'))">
                    <template #svg>
                        <svg class="h-6 w-6 shrink-0 text-gray-700" :class="(route().current('setting') || route().current('organization.index') || route().current('organization.create') ||
                                    route().current('organization.edit') || route().current('manage-prefix') || route().current('roles.permission') ||
                                    route().current('bankinfo.index') || route().current('bankinfo.create') || route().current('bankinfo.edit') ||
                                    route().current('account-type.index') || route().current('account-type.create') || route().current('account-type.edit')  ||
                                    route().current('jobcard-checklist.index') || route().current('jobcard-checklist.create') || route().current('jobcard-checklist.edit')  ||
                                    route().current('mail-configs.index') || route().current('mail-configs.create') || route().current('mail-configs.edit')  ||
                                    route().current('emailtemplates.index') || route().current('emailtemplates.create') || route().current('emailtemplates.edit')  ||
                                    route().current('roles.index') || route().current('roles.create') || route().current('roles.edit')) ? 'text-white' : 'text-gray-500 group-hover:text-white'"
                                fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                                aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                    </template>
                    <template #name>Settings</template>
                </SideMenu>
            </div>
        </div>

        <div class="lg:pl-72 border-t">
            <div class="sticky top-0 z-20 flex h-16 shrink-0 items-center gap-x-4 bg-white px-4 shadow sm:gap-x-6 sm:px-6 lg:px-8">
                <button type="button" class="-m-2.5 p-2.5 text-gray-700 lg:hidden">
                    <span class="sr-only">Open sidebar</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"/>
                    </svg>
                </button>

                <!-- Separator -->
                <div class="h-6 w-px bg-gray-200 lg:hidden" aria-hidden="true"></div>

                <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6 justify-between">
                    <div class="flex items-center">
                        <Dropdown align="left" width="48">
                            <template #trigger>
                                <div class="flex w-32">
                                    <a class="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" href="#"> Add New +</a>
                                </div>
                            </template>
                            <template #content>
                                <ActionLink :href="route('users.create')">
                                    <template #svg></template>
                                    <template #text>
                                        <span class="text-sm text-gray-700 leading-6">Add New User</span>
                                    </template>
                                </ActionLink>
                                <ActionLink :href="route('customers.create')">
                                    <template #svg></template>
                                    <template #text>
                                        <span class="text-sm text-gray-700 leading-6">Add New Customer</span>
                                    </template>
                                </ActionLink>
                                <ActionLink :href="route('companies.create')">
                                    <template #svg></template>
                                    <template #text>
                                        <span class="text-sm text-gray-700 leading-6">Add New Company</span>
                                    </template>
                                </ActionLink>
                                <ActionLink :href="route('challan.create')">
                                    <template #svg></template>
                                    <template #text>
                                        <span class="text-sm text-gray-700 leading-6">Create Challan</span>
                                    </template>
                                </ActionLink>
                                <ActionLink :href="route('orders.create')">
                                    <template #svg></template>
                                    <template #text>
                                        <span class="text-sm text-gray-700 leading-6">Create Order</span>
                                    </template>
                                </ActionLink>
                                <ActionLink :href="route('companypo.create')">
                                    <template #svg></template>
                                    <template #text>
                                        <span class="text-sm text-gray-700 leading-6">Purchase Order</span>
                                    </template>
                                </ActionLink>
                            </template>
                        </Dropdown>
                    </div>
                    <div class="flex items-center gap-x-4 lg:gap-x-6">
                        <NotificationDropdown :notifications="notifications" />

                        <!-- Separator -->
                        <div class="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200" aria-hidden="true"></div>

                        <!-- Profile dropdown -->
                        <div class="relative">
                            <Dropdown align="right" width="48">
                                <template #trigger>
                                    <button type="button" class="-m-1.5 flex items-center p-1.5" id="user-menu-button"
                                            aria-expanded="false" aria-haspopup="true">
                                        <span class="sr-only">Open User Menu</span>
                                        <img class="h-8 w-8 rounded-full bg-gray-50"
                                             src="https://img.freepik.com/premium-photo/avatar-resourcing-company_1254967-6696.jpg?size=626&ext=jpg&ga=GA1.1.*********.1729255085&semt=ais_hybrid"
                                             alt="">
                                        <span class="hidden lg:flex lg:items-center">
                                <span class="ml-4 text-sm font-semibold leading-6 text-gray-900" aria-hidden="true">{{ $page.props.auth.user.name }}</span>
                                <svg class="ml-2 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd"/>
                                </svg>
                            </span>
                                    </button>
                                </template>
                                <template #content>
                                    <ActionLink :href="route('profile.edit')" as="button">
                                        <template #svg></template>
                                        <template #text>
                                            <span class="text-sm text-gray-700 leading-5">Your Profile</span>
                                        </template>
                                    </ActionLink>
                                    <ActionLink :href="route('logout')" method="post" as="button">
                                        <template #svg></template>
                                        <template #text>
                                            <span class="text-sm text-gray-700 leading-5">Sign Out</span>
                                        </template>
                                    </ActionLink>
                                </template>
                            </Dropdown>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Info flash message -->
            <div v-if="$page.props.flash.message">
                <ToastNotificationVue :message="$page.props.flash.message"/>
            </div>

            <!-- Success flash message -->
            <div v-if="$page.props.flash.success">
                <ToastNotificationSuccessVue :message="$page.props.flash.success"/>
            </div>

            <!-- Error flash message -->
            {{ $page.props.flash.error }}
            <div v-if="$page.props.flash.error">
                <ToastNotificationErrorVue :message="$page.props.flash.error"/>
            </div>

            <!-- Warning flash message -->
            <div v-if="$page.props.flash.warning">
                <ToastNotificationWarningVue :message="$page.props.flash.warning"/>
            </div>

            <main class="py-10 bg-slate-100">
                <div class="px-4 sm:px-6 lg:px-8 min-h-screen">
                    <slot/>
                </div>
            </main>
        </div>
    </div>
</template>

<style>

::-webkit-scrollbar {
    display: none;
}

html {
    scrollbar-width: none;
}

.bg-gray-900 {
    background: #ffffff !important;

}

.hover\:bg-indigo-500:hover {
    background: rgb(62 81 228) !important;
}

.bg-indigo-600 {
    background: rgb(50 68 210) !important;
}

.bg-gray-100 {
    background: #e3e9f1bd !important;
}

.animate-top{
    position:relative;
    animation:animatetop 0.6s
}
@keyframes animatetop
    {from{top:-100px;opacity:0} to{top:0;opacity:1}}

</style>
