import{r as g,o as a,c as r,a as u,u as h,w as i,F as v,Z as R,b as t,k as V,v as F,N as G,g as p,f as x,i as A,e as C,t as o,n as l,s as H,x as K}from"./app-4ea19997.js";import{_ as X,b as Y,a as Z}from"./AdminLayout-5eccc000.js";import{_ as q}from"./CreateButton-19955a3e.js";import{_ as J}from"./SecondaryButton-79105c78.js";import{_ as Q}from"./_plugin-vue_export-helper-c27b6911.js";import{D as W}from"./DangerButton-5e5c1802.js";import{M as D}from"./Modal-65b3f5d9.js";import{_ as ee}from"./Pagination-e0ea25b7.js";import"./html2canvas.esm-1d0ee1b1.js";import{_ as te}from"./ArrowIcon-151eb820.js";import{s as se}from"./sortAndSearch-931279ce.js";/* empty css                                                              */const c=n=>(H("data-v-45bbd180"),n=n(),K(),n),le={class:"animate-top"},ne={class:"sm:flex sm:items-center"},oe=c(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Maintenance Contract")],-1)),ae={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},re={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},ie={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},ce=c(()=>t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),de={key:0,class:"flex justify-end"},me=["src"],ue={class:"mt-8 overflow-x-auto sm:rounded-lg"},he={class:"shadow sm:rounded-lg"},_e={class:"w-full text-sm text-left rtl:text-right text-gray-500"},xe={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},fe={class:"border-b-2"},ge=["onClick"],pe={key:0},ye={class:"flex items-center justify-start gap-4"},be={key:0,type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},we=c(()=>t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})],-1)),ve=[we],Ce=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),ke=c(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),Me=["onClick"],Ae=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.42.271L12 0l-.057.072A2.25 2.25 0 0010.5 1.085c.002-.001.003-.001.004-.001z"})],-1)),Te=c(()=>t("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1)),Ee=[Ae,Te],Se={key:1},Le=c(()=>t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),Ne=[Le],Be={class:"p-6"},Ie=c(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),Oe={class:"mt-6 flex justify-end"},$e={__name:"List",props:["data","permissions"],setup(n){const{form:T,search:y,sort:E,fetchData:k,sortKey:S,sortDirection:L}=se("maintenance-contract.index"),M=g(null),b=g(!1);g("");const N=[{field:"hospital_name",label:"HOSPITAL NAME",sortable:!0},{field:"product_name",label:"PRODUCT",sortable:!0},{field:"price",label:"PRICE (₹)",sortable:!0},{field:"contract_start_date",label:"CONTRACT DATE",sortable:!0,multiFieldSort:["contract_start_date","contract_end_date"]},{field:"pm_date_1",label:"PM DATE 1",sortable:!0},{field:"pm_date_2",label:"PM DATE 2",sortable:!0},{field:"pm_date_3",label:"PM DATE 3",sortable:!0},{field:"pm_date_4",label:"PM DATE 4",sortable:!0},{field:"maintenance_type",label:"TYPE",sortable:!0},{field:"status",label:"STATUS",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],B=s=>{M.value=s,b.value=!0},w=()=>{b.value=!1},I=()=>{T.delete(route("maintenance-contract.destroy",{id:M.value}),{onSuccess:()=>w()})},O=s=>{switch(s){case"Open":return"bg-blue-100";case"Close":return"bg-red-100"}},$=s=>{switch(s){case"Open":return"text-blue-600";case"Close":return"text-red-600"}},z=s=>{switch(s){case"AMC":return"bg-cyan-100";case"CMC":return"bg-green-100"}},P=s=>{switch(s){case"AMC":return"text-cyan-600";case"CMC":return"text-green-600"}},U=g("MAINTENANCE DATA"),j=()=>{const s=U.value.replace(/\s+/g,"_");fetch("/export-maintenance-contract?",{method:"GET"}).then(e=>{if(!e.ok)throw new Error("Network response was not ok");return e.blob()}).then(e=>{const f=window.URL.createObjectURL(new Blob([e])),m=document.createElement("a");m.href=f,m.setAttribute("download",`${s}.xlsx`),document.body.appendChild(m),m.click(),document.body.removeChild(m)}).catch(e=>{console.error("Error exporting data:",e)})},_=s=>{const d=new Date(s),e={year:"numeric",month:"short",day:"numeric"};return d.toLocaleDateString("en-US",e)};return(s,d)=>(a(),r(v,null,[u(h(R),{title:"Maintenance Contract"}),u(X,null,{default:i(()=>[t("div",le,[t("div",ne,[oe,t("div",ae,[t("div",re,[t("div",ie,[ce,V(t("input",{id:"search-field","onUpdate:modelValue":d[0]||(d[0]=e=>G(y)?y.value=e:null),onInput:d[1]||(d[1]=(...e)=>h(k)&&h(k)(...e)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,544),[[F,h(y)]])])]),n.permissions.canCreateMaintenance?(a(),r("div",de,[u(q,{href:s.route("maintenance-contract.create")},{default:i(()=>[p(" Create Maintenance Contract ")]),_:1},8,["href"])])):x("",!0),t("button",{onClick:j},[t("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,me)])])]),t("div",ue,[t("div",he,[t("table",_e,[t("thead",xe,[t("tr",fe,[(a(),r(v,null,A(N,(e,f)=>t("th",{key:f,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:m=>h(E)(e.field,e.sortable)},[p(o(e.label)+" ",1),e.sortable?(a(),C(te,{key:0,isSorted:h(S)===e.field,direction:h(L)},null,8,["isSorted","direction"])):x("",!0)],8,ge)),64))])]),n.data.data&&n.data.data.length>0?(a(),r("tbody",pe,[(a(!0),r(v,null,A(n.data.data,(e,f)=>(a(),r("tr",{class:l(["odd:bg-white even:bg-gray-50 border-b",{"bg-yellow-100":e.highlight}]),key:e.id},[t("td",{class:l(["whitespace-normal px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap truncate min-w-48",{"text-yellow-600":e.highlight}])},o(e.hospital_name??"-")+" - "+o(e.city??"-"),3),t("td",{class:l(["whitespace-normal px-4 py-2.5 min-w-60 truncate",{"text-yellow-600":e.highlight}])},o(e.product_name??""),3),t("td",{scope:"row",class:l(["px-4 py-2.5 min-w-36 truncate",{"text-yellow-600":e.highlight}])},o(e.price??"-"),3),t("td",{class:l(["px-4 py-2.5 min-w-60",{"text-yellow-600":e.highlight}])},o(_(e.contract_start_date))+" - "+o(_(e.contract_end_date)),3),t("td",{class:l(["px-4 py-2.5 min-w-36",{"text-yellow-600":e.highlight}])},o(e.pm_date_1?_(e.pm_date_1):"-"),3),t("td",{class:l(["px-4 py-2.5 min-w-36",{"text-yellow-600":e.highlight}])},o(e.pm_date_2?_(e.pm_date_2):"-"),3),t("td",{class:l(["px-4 py-2.5 min-w-36",{"text-yellow-600":e.highlight}])},o(e.pm_date_3?_(e.pm_date_3):"-"),3),t("td",{class:l(["px-4 py-2.5 min-w-36",{"text-yellow-600":e.highlight}])},o(e.pm_date_4?_(e.pm_date_4):"-"),3),t("td",{class:l(["flex-1 items-center px-4 py-2.5",{"text-yellow-600":e.highlight}])},[t("div",{class:l(["flex rounded-full px-4 py-1",z(e.maintenance_type)])},[t("span",{class:l(["text-sm font-semibold",P(e.maintenance_type)])},o(e.maintenance_type),3)],2)],2),t("td",{class:l(["flex flex-1 items-center px-4 py-2.5",{"text-yellow-600":e.highlight}])},[t("div",{class:l(["flex rounded-full px-4 py-1",O(e.status)])},[t("span",{class:l(["text-sm font-semibold",$(e.status)])},o(e.status),3)],2)],2),t("td",{class:l(["items-center px-4 py-2.5",{"text-yellow-600":e.highlight}])},[t("div",ye,[u(Y,{align:"right",width:"48"},{trigger:i(()=>[e.status!="Close"?(a(),r("button",be,ve)):x("",!0)]),content:i(()=>[n.permissions.canEditMaintenance?(a(),C(Z,{key:0,href:s.route("maintenance-contract.edit",{id:e.id})},{svg:i(()=>[Ce]),text:i(()=>[ke]),_:2},1032,["href"])):x("",!0),n.permissions.canDeleteMaintenance?(a(),r("button",{key:1,type:"button",onClick:m=>B(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Ee,8,Me)):x("",!0)]),_:2},1024)])],2)],2))),128))])):(a(),r("tbody",Se,Ne))])])]),n.data.data&&n.data.data.length>0?(a(),C(ee,{key:0,class:"mt-6",links:n.data.links},null,8,["links"])):x("",!0)]),u(D,{show:b.value,onClose:w},{default:i(()=>[t("div",Be,[Ie,t("div",Oe,[u(J,{onClick:w},{default:i(()=>[p(" Cancel ")]),_:1}),u(W,{class:"ml-3",onClick:I},{default:i(()=>[p(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}},Ze=Q($e,[["__scopeId","data-v-45bbd180"]]);export{Ze as default};
