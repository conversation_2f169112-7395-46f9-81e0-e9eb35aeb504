import{r as v,o as s,c as l,a as i,u as r,w as a,F as p,Z as A,b as e,k as N,v as j,N as z,g as u,f as c,i as k,e as x,t as m}from"./app-ce7743ab.js";import{_ as E,b as I,a as T}from"./AdminLayout-6af2fc6a.js";import{_ as F}from"./CreateButton-ae748c59.js";import{_ as O}from"./SecondaryButton-aec1a882.js";import{D}from"./DangerButton-ca58e5a5.js";import{M as R}from"./Modal-599968f2.js";import{_ as H}from"./SwitchButton-03529106.js";import{_ as K}from"./Pagination-ae99ac61.js";import{_ as Z}from"./ArrowIcon-73c874d9.js";import{s as q}from"./sortAndSearch-31104ada.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const G={class:"animate-top"},J={class:"flex justify-between items-center"},P=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Users")],-1),Q={class:"flex justify-end"},W={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},X={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},Y=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),ee={key:0,class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},te={class:"flex justify-end"},se={class:"mt-8 overflow-x-auto sm:rounded-lg"},oe={class:"shadow sm:rounded-lg"},ae={class:"w-full text-sm text-left rtl:text-right text-gray-500"},le={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},ie={class:"border-b-2"},re=["onClick"],ne={key:0},de={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},ce={class:"px-4 py-2.5 min-w-36"},me={class:"px-4 py-2.5 min-w-36"},ue={class:"items-center px-4 py-2.5"},fe={class:"items-center px-4 py-2.5"},_e={class:"flex items-center justify-start gap-4"},he=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),pe=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),xe=e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),ge=["onClick"],ye=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),we=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),be=[ye,we],ve={key:1},ke=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Ce=[ke],Me={class:"p-6"},Se=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),Ue={class:"mt-6 flex justify-end"},Oe={__name:"List",props:["data","search","permissions"],setup(o){const{form:g,search:f,sort:C,fetchData:y,sortKey:M,sortDirection:S}=q("users.index"),_=v(!1),w=v(null),U=[{field:"first_name",label:"NAME",sortable:!0,multiFieldSort:["first_name","last_name"]},{field:"email",label:"EMAIL",sortable:!0},{field:"role_id",label:"ROLE",sortable:!0},{field:"status",label:"STATUS",sortable:!1},{field:"action",label:"ACTION",sortable:!1}],L=n=>{w.value=n,_.value=!0},h=()=>{_.value=!1},V=()=>{g.delete(route("users.destroy",{id:w.value}),{onSuccess:()=>h()})},$=(n,d)=>{g.post(route("users.activation",{id:d,status:n}),{})};return(n,d)=>(s(),l(p,null,[i(r(A),{title:"Users"}),i(E,null,{default:a(()=>[e("div",G,[e("div",J,[P,e("div",Q,[e("div",W,[e("div",X,[Y,N(e("input",{id:"search-field","onUpdate:modelValue":d[0]||(d[0]=t=>z(f)?f.value=t:null),onInput:d[1]||(d[1]=(...t)=>r(y)&&r(y)(...t)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,544),[[j,r(f)]])])]),o.permissions.canCreateUser?(s(),l("div",ee,[e("div",te,[i(F,{href:n.route("users.create")},{default:a(()=>[u(" Add User ")]),_:1},8,["href"])])])):c("",!0)])]),e("div",se,[e("div",oe,[e("table",ae,[e("thead",le,[e("tr",ie,[(s(),l(p,null,k(U,(t,b)=>e("th",{key:b,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:B=>r(C)(t.field,t.sortable)},[u(m(t.label)+" ",1),t.sortable?(s(),x(Z,{key:0,isSorted:r(M)===t.field,direction:r(S)},null,8,["isSorted","direction"])):c("",!0)],8,re)),64))])]),o.data.data&&o.data.data.length>0?(s(),l("tbody",ne,[(s(!0),l(p,null,k(o.data.data,(t,b)=>(s(),l("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",de,m(t.first_name)+" "+m(t.last_name),1),e("td",ce,m(t.email),1),e("td",me,m(t.roles[0].name),1),e("td",ue,[i(H,{switchValue:t.status,userId:t.id,onUpdateSwitchValue:$},null,8,["switchValue","userId"])]),e("td",fe,[e("div",_e,[i(I,{align:"right",width:"48"},{trigger:a(()=>[he]),content:a(()=>[o.permissions.canEditUser?(s(),x(T,{key:0,href:n.route("users.edit",{id:t.id})},{svg:a(()=>[pe]),text:a(()=>[xe]),_:2},1032,["href"])):c("",!0),o.permissions.canDeleteUser?(s(),l("button",{key:1,type:"button",onClick:B=>L(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},be,8,ge)):c("",!0)]),_:2},1024)])])]))),128))])):(s(),l("tbody",ve,Ce))])])]),o.data.data&&o.data.data.length>0?(s(),x(K,{key:0,class:"mt-6",links:o.data.links},null,8,["links"])):c("",!0)]),i(R,{show:_.value,onClose:h},{default:a(()=>[e("div",Me,[Se,e("div",Ue,[i(O,{onClick:h},{default:a(()=>[u(" Cancel ")]),_:1}),i(D,{class:"ml-3",onClick:V},{default:a(()=>[u(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{Oe as default};
