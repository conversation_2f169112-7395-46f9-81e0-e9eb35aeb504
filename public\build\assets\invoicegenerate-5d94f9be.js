import{K as it,r as h,C as Tt,j as G,l as Dt,o as _,c as g,a as i,u as n,w as f,F as M,Z as $t,b as t,t as p,f as y,k as Ut,v as Gt,d as Mt,n as w,i as A,g as I,e as Nt,s as qt,x as Ot}from"./app-16701445.js";import{_ as At,a as jt}from"./AdminLayout-e15be38d.js";import{_ as zt}from"./InputError-11376965.js";import{_ as b}from"./InputLabel-d69efee6.js";import{P as Y}from"./PrimaryButton-eddb8b77.js";import{_ as v}from"./TextInput-764e3400.js";import{_ as Bt}from"./TextArea-b68da786.js";import{_ as $}from"./SearchableDropdown-c456ce8e.js";import{D as rt}from"./DangerButton-9b74ae84.js";import{_ as H}from"./SecondaryButton-1012464f.js";import{M as j}from"./Modal-754de2c3.js";import{_ as Et}from"./FileViewer-7c38f75a.js";import{_ as Rt}from"./MultipleFileUpload-09565aaf.js";import{u as Lt}from"./index-10107770.js";import{_ as Wt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const u=P=>(qt("data-v-989ac0bd"),P=P(),Ot(),P),Qt={class:"animate-top"},Yt={class:"sm:flex sm:items-center"},Ht=u(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Generate Invoice")],-1)),Kt={class:"w-auto"},Xt={class:"flex space-x-2 items-center"},Zt=u(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6"},"Invoice Number:",-1)),Jt={key:0,class:"text-sm font-semibold text-gray-900 leading-6"},te={key:1,class:"text-sm font-semibold text-gray-900 leading-6"},ee={class:"flex space-x-2 items-center"},se=u(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Date :",-1)),oe=["onSubmit"],le={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},ae={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},ne={class:"sm:col-span-4"},de={class:"relative mt-2"},ie={class:"sm:col-span-4"},re={class:"relative mt-2"},ce={class:"sm:col-span-4"},ue={class:"relative mt-2"},me=u(()=>t("div",{class:"mt-4 sm:flex-auto"},[t("div",{class:"text-xl font-semibold leading-7 text-gray-900"},"Order Details")],-1)),_e={class:"mt-4 bg-white p-4 shadow sm:p-8 sm:rounded-lg border divide-y divide-gray-300"},pe=u(()=>t("div",{class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-9 pb-2"},[t("div",{class:"sm:col-span-4"},[t("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Product Name")]),t("div",{class:"sm:col-span-1"},[t("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"QTY")]),t("div",{class:"sm:col-span-2"},[t("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Price(₹)")]),t("div",{class:"sm:col-span-2"},[t("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Delivered Product")])],-1)),ge={class:"sm:col-span-4 py-2"},ve={class:"text-sm leading-5 text-gray-700"},ye={class:"sm:col-span-1 py-2"},xe={class:"text-sm leading-5 text-gray-700"},he={class:"sm:col-span-2 py-2"},fe={class:"text-sm leading-5 text-gray-700"},we={class:"sm:col-span-2 py-2"},be={class:"text-sm leading-5 text-gray-700"},Ve={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Fe={class:"overflow-x-auto w-full"},Pe={class:"overflow-x-auto divide-y divide-gray-300 w-full",style:{"margin-bottom":"160px"}},Ce=u(()=>t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Product",-1)),Se=u(()=>t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Batch",-1)),Ie=u(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"HSN",-1)),ke=u(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"EXP",-1)),Te=u(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),De=u(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),$e=u(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),Ue=u(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),Ge=u(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),Me={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Ne={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},qe={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Oe={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Ae={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},je=u(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),ze=u(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),Be=u(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),Ee={class:"divide-y divide-gray-300 bg-white"},Re={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-80"},Le={class:"relative mt-2"},We={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-60"},Qe={class:"relative mt-2"},Ye={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},He={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ke={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},Xe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ze={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Je={key:0,class:"text-red-500 text-xs absolute"},ts={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},es={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},ss={class:"text-sm text-gray-900 leading-6 mt-2 py-1.5"},os={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ls={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},as={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ns={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ds={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},is={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},rs={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-36"},cs={class:"flex space-x-2"},us={class:"text-sm text-gray-900 leading-6 py-1.5"},ms=["onClick"],_s=u(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),ps=[_s],gs={class:"flex items-center justify-between"},vs={class:"ml-auto flex items-center justify-end gap-x-6"},ys={key:0,class:"mt-6 bg-white p-1 shadow sm:rounded-lg border"},xs={class:"min-w-full divide-y divide-gray-300"},hs=u(()=>t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"UPLOADED ORDER DOCUMENT "),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1)),fs={class:"divide-y divide-gray-300 bg-white"},ws={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},bs={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},Vs=["onClick"],Fs=u(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),Ps=[Fs],Cs=["onClick"],Ss=u(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),Is=[Ss],ks={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Ts={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Ds={class:"sm:col-span-3 space-y-4"},$s={class:"flex space-x-4"},Us={class:"w-full"},Gs={class:"w-full"},Ms={class:"relative mt-2"},Ns={class:"flex space-x-4"},qs={class:"w-full"},Os={class:"w-full"},As={class:"w-full"},js={class:"flex space-x-4"},zs={class:"w-full"},Bs={class:"w-full"},Es={class:"w-full"},Rs={class:"flex space-x-4"},Ls={class:"w-full"},Ws={class:"w-full"},Qs={class:"sm:col-span-3"},Ys={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Hs={class:"inline-flex items-center justify-end w-full space-x-3"},Ks=u(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),Xs={class:"text-base font-semibold text-gray-900 w-20"},Zs={class:"inline-flex items-center justify-end w-full space-x-3"},Js=u(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Discount On Sub Total(₹):",-1)),to={class:"w-40"},eo={class:"inline-flex items-center justify-end w-full space-x-3"},so=u(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),oo={class:"w-40"},lo={class:"inline-flex items-center justify-end w-full space-x-3"},ao=u(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),no={class:"text-base font-semibold text-gray-900 w-w-32"},io={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},ro=u(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),co={class:"text-base font-semibold text-gray-900 w-w-32"},uo={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},mo=u(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),_o={class:"text-base font-semibold text-gray-900 w-w-32"},po={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},go=u(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),vo={class:"text-base font-semibold text-gray-900 w-w-32"},yo={class:"inline-flex items-center justify-end w-full space-x-3"},xo=u(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),ho={class:"text-base font-semibold text-gray-900 w-w-32"},fo={class:"flex mt-6 items-center justify-between"},wo={class:"ml-auto flex items-center justify-end gap-x-6"},bo=u(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),Vo={class:"p-6"},Fo=u(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this product? ",-1)),Po={class:"mt-6 flex justify-end"},Co={class:"p-6"},So=u(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this document? ",-1)),Io={class:"mt-6 flex justify-end"},ko={class:"p-6"},To={class:"mt-6 px-4 flex justify-end"},Do={class:"p-6"},$o=u(()=>t("div",{class:"flex items-center justify-between"},[t("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Delivered Order")],-1)),Uo={class:"border-b border-gray-900/10 pb-12"},Go={class:"mt-6 bg-white p-4 shadow sm:p-8 sm:rounded-lg border divide-y divide-gray-300"},Mo=u(()=>t("div",{class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10 pb-2"},[t("div",{class:"sm:col-span-3"},[t("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Product")]),t("div",{class:"sm:col-span-2"},[t("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"QTY")]),t("div",{class:"sm:col-span-2"},[t("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Delivered Product")]),t("div",{class:"sm:col-span-3"},[t("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Deliver QTY")])],-1)),No={class:"sm:col-span-3"},qo={class:"text-sm leading-5 text-gray-700"},Oo={class:"sm:col-span-2"},Ao={class:"text-sm leading-5 text-gray-700"},jo={class:"sm:col-span-2"},zo={class:"text-sm leading-5 text-gray-700"},Bo={class:"sm:col-span-3 mb-2"},Eo={key:0,class:"text-red-500 text-xs"},Ro={class:"mt-6 px-4 flex justify-end"},Lo={class:"w-36"},Wo={__name:"invoicegenerate",props:["customers","order_deliver_number","invoice_no","retail_invoice_no","salesuser","products","filepath","category","serialno","organization"],setup(P){const D=P,K=it().props.filepath.view,m=it().props.data[0],z=h(),B=h(),s=Lt("post","/generate-invoice-order",{note:m.note,date:m.date,selectedProductItem:[],customer_id:m.customer_id,sales_user_id:m.sales_user_id,organization_id:m.organization_id,category:m.category,order_id:m.id,invoice_no:"",invoice_type:"",total_amount:"",order_number:m.order_number,order_id:m.id,document:"",cgst:m.cgst,sgst:m.sgst,igst:m.igst,total_gst:m.total_gst,sub_total:m.sub_total,total_discount:m.total_discount,overall_discount:m.overall_discount,discount_before_tax:"",dispatch:"",transport:"",patient_name:"",customer_po_date:"",customer_po_number:"",eway_bill:"",due_days:"",cr_dr_note:""}),X=h([]),ct=D.serialno;X.value=ct;const ut=(o,l,e)=>{const d=D.serialno.filter(a=>a.product_id===o&&a.organization_id===m.organization_id);X.value=d,c.value[e].product_id=o},mt=(o,l,e)=>D.serialno.filter(a=>a.product_id===o&&a.organization_id===m.organization_id),_t=()=>{s.sub_total=tt.value,s.total_discount=et.value,s.cgst=x.value=="CGST/SGST"?k.value/2:"0",s.sgst=x.value=="CGST/SGST"?k.value/2:"0",s.igst=x.value=="IGST"?k.value:"0",s.total_gst=k.value,s.total_amount=J.value,s.invoice_no=N.value=="Tax"?z.value:B.value,s.invoice_type=N.value,s.selectedProductItem=c.value,s.submit({preserveScroll:!0,onSuccess:()=>{s.reset()}})},c=h([{product_id:"",description:"",is_order:!1,serial_number_id:"",expiry_date:"",hsn_code:"",mrp:"",qty:"",price:"",sell_price:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",discount:"",discount_amount:"",discount_before_tax_product:"",total_amount:"",order_details_id:""}]);Tt(()=>{c.value=m.pending_order_details.map(o=>{var l,e;return{is_order:!0,product_id:o.product_id,order_details_id:o.id,description:"",qty:o.qty,price:parseFloat(o.price).toFixed(2),sell_price:parseFloat(o.price).toFixed(2),total_price:parseFloat(o.total_price).toFixed(2),mrp:(e=(l=o.product)==null?void 0:l.serial_numbers[0])!=null&&e.mrp?parseFloat(o.product.serial_numbers[0].mrp).toFixed(2):"-",gst:parseFloat(o.gst).toFixed(2),sgst:parseFloat(o.gst/2).toFixed(2),gst_amount:parseFloat(o.gst_amount).toFixed(2),total_gst_amount:parseFloat(o.total_gst_amount).toFixed(2),total_amount:parseFloat(o.total_amount).toFixed(2),discount:parseFloat(o.discount).toFixed(2),discount_amount:parseFloat(o.discount_amount).toFixed(2)??"0"}})});const x=h(m.customers.gst_type),N=h(m.customers.customer_type);z.value=D.invoice_no[m.organization_id],B.value=D.retail_invoice_no[m.organization_id];const pt=(o,l,e,d,a)=>{const r=D.serialno.find(V=>V.id===o);r&&(a?(c.value[e].serial_number_id=r.id,c.value[e].product_name=r.product.name,c.value[e].item_code=r.product.item_code,c.value[e].expiry_date=r.expiry_date,c.value[e].mrp=r.mrp?parseFloat(r.mrp).toFixed(2):"-",c.value[e].hsn_code=r.product.hsn_code,s.errors[`selectedProductItem.${e}.serial_number_id`]=null):(c.value[e].qty="",c.value[e].serial_number_id=r.id,c.value[e].product_name=r.product.name,c.value[e].item_code=r.product.item_code,c.value[e].expiry_date=r.expiry_date,c.value[e].mrp=r.mrp?parseFloat(r.mrp).toFixed(2):"-",c.value[e].price=parseFloat(r.purchase_price).toFixed(2),c.value[e].sell_price=parseFloat(r.purchase_price).toFixed(2),c.value[e].hsn_code=r.product.hsn_code,c.value[e].discount="0.00",c.value[e].total_price=parseFloat(r.purchase_price).toFixed(2),c.value[e].gst=parseFloat(r.product.gst).toFixed(2),c.value[e].sgst=parseFloat(r.product.gst/2).toFixed(2),c.value[e].gst_amount="",c.value[e].total_gst_amount="",c.value[e].total_amount="",c.value[e].description="",s.errors[`selectedProductItem.${e}.serial_number_id`]=null))},gt=()=>{c.value.push({product_id:"",description:"",is_order:!1,serial_number_id:"",expiry_date:"",hsn_code:"",qty:"",price:"",gst:"",total_price:"",gst_amount:"",total_gst_amount:"",discount:"",discount_amount:"",total_amount:"",order_details_id:""})},E=h(!1),Z=h(null),vt=h(null),R=()=>{E.value=!1},yt=()=>{s.get(route("removeproduct",{id:Z.value,model:"OrderDetails"}),{onSuccess:()=>{R(),c.value.splice(index,1)}})},xt=(o,l)=>{l!==void 0&&l!=""?(Z.value=l,vt.value=o,E.value=!0):c.value.splice(o,1)},ht=(o,l)=>{const e=parseFloat(o.sell_price),d=parseFloat(o.discount_before_tax_product)||0,a=parseFloat(o.discount)||0,r=x.value=="IGST"?o.gst:parseFloat(o.sgst*2),V=parseFloat(o.qty);let F=0,O=0;a>0||d>0?F=e*V:F=e*V*(1+r/100);const U=F*(a/100)||0,nt=e*1*(r/100),Q=(e*V-U-d)*(r/100);a>0||d>0?O=F-U-d+Q:O=F-U;const dt=e*V;return o.total_price=isNaN(dt)?"":parseFloat(dt).toFixed(2),o.gst_amount=isNaN(nt)?"":parseFloat(nt).toFixed(2),o.total_gst_amount=isNaN(Q)?"":parseFloat(Q).toFixed(2),o.discount_amount=isNaN(U)?"":parseFloat(U).toFixed(2),o.gst=r,isNaN(O)?"":parseFloat(O).toFixed(2)},C=(o,l)=>{at(),o.total_amount=ht(o)},J=G(()=>{const o=Math.round(c.value.reduce((e,d)=>e+(d.total_amount?parseFloat(d.total_amount):0),0)),l=s.overall_discount?parseFloat(s.overall_discount):0;return o-l}),k=G(()=>c.value.reduce((o,l)=>o+(l.total_gst_amount?parseFloat(l.total_gst_amount):0),0)),tt=G(()=>c.value.reduce((o,l)=>o+(l.total_price?parseFloat(l.total_price):0),0)),et=G(()=>{const o=c.value.reduce((d,a)=>d+(a.discount_amount?parseFloat(a.discount_amount):0),0),l=s.overall_discount?parseFloat(s.overall_discount):0,e=s.discount_before_tax?parseFloat(s.discount_before_tax):0;return o+l+e}),S=o=>{s.errors[o]=null};G(()=>{const o=new Date(m.date),l={year:"numeric",month:"long",day:"numeric"};return o.toLocaleDateString("en-US",l)});const ft=o=>{s.document=o},st=h(!1),wt=h(null),bt=()=>{s.get(route("removedocument",{id:wt.value,name:"orderDocument"}),{onSuccess:()=>{L()}})},L=()=>{st.value=!1},Vt=(o,l)=>{s.sales_user_id=o},W=h(!1),ot=h(null),Ft=h("custom"),Pt=o=>{ot.value=o,W.value=!0},lt=()=>{W.value=!1},Ct=o=>{const l=window.location.origin+K+o,e=document.createElement("a");e.href=l,e.setAttribute("download",o),document.body.appendChild(e),e.click(),document.body.removeChild(e)},T=o=>{let l=o.toFixed(2).toString(),[e,d]=l.split("."),a=e.substring(e.length-3),r=e.substring(0,e.length-3);return r!==""&&(a=","+a),`${r.replace(/\B(?=(\d{2})+(?!\d))/g,",")+a}.${d}`},St=(o,l)=>{s.customer_id=o,s.errors.customer_id=null},It=(o,l)=>{s.category=o,s.errors.category=null},kt=(o,l)=>{s.organization_id=o,s.errors.organization_id=null},q=(o,l)=>{const e=l.length,d=l.reduce((V,F)=>V+(F.total_price?parseFloat(F.total_price):0),0),a=c.value.reduce((V,F)=>V+(F.total_price?parseFloat(F.total_price):0),0),r=o*d/a/e;l.forEach(V=>{V.discount_before_tax_product=r})},at=()=>{const o=parseFloat(s.discount_before_tax)||0,l=c.value.filter(r=>r.gst==5&&r.total_price>0),e=c.value.filter(r=>r.gst==12&&r.total_price>0),d=c.value.filter(r=>r.gst==18&&r.total_price>0),a=c.value.filter(r=>r.gst==28&&r.total_price>0);q(o,l),q(o,e),q(o,d),q(o,a)};return Dt(()=>s.discount_before_tax,o=>{at(),c.value.forEach(l=>{C(l)})}),(o,l)=>(_(),g(M,null,[i(n($t),{title:"Orders"}),i(At,null,{default:f(()=>[t("div",Qt,[t("div",Yt,[Ht,t("div",Kt,[t("div",Xt,[Zt,N.value=="Retail"?(_(),g("span",Jt,p(B.value),1)):y("",!0),N.value=="Tax"?(_(),g("span",te,p(z.value),1)):y("",!0)]),t("div",ee,[se,Ut(t("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":l[0]||(l[0]=e=>n(s).date=e),onChange:l[1]||(l[1]=e=>n(s).validate("date"))},null,544),[[Gt,n(s).date]])])])]),t("form",{onSubmit:Mt(_t,["prevent"]),class:""},[t("div",le,[t("div",ae,[t("div",ne,[i(b,{for:"company_name",value:"Organization"}),t("div",de,[i($,{options:P.organization,modelValue:n(s).organization_id,"onUpdate:modelValue":l[2]||(l[2]=e=>n(s).organization_id=e),onOnchange:kt,editMode:"editMode",class:w({"error rounded-md":n(s).errors.organization_id})},null,8,["options","modelValue","class"])])]),t("div",ie,[i(b,{for:"customer_id",value:"Customer Name"}),t("div",re,[i($,{options:P.customers,modelValue:n(s).customer_id,"onUpdate:modelValue":l[3]||(l[3]=e=>n(s).customer_id=e),onOnchange:St,editMode:"editMode",class:w({"error rounded-md":n(s).errors.customer_id})},null,8,["options","modelValue","class"])])]),t("div",ce,[i(b,{for:"company_name",value:"Category"}),t("div",ue,[i($,{options:P.category,modelValue:n(s).category,"onUpdate:modelValue":l[4]||(l[4]=e=>n(s).category=e),onOnchange:It,class:w({"error rounded-md":n(s).errors.category})},null,8,["options","modelValue","class"])])])])]),me,t("div",_e,[pe,(_(!0),g(M,null,A(n(m).order_details,(e,d)=>(_(),g("div",{class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-9 items-center",key:d},[t("div",ge,[t("p",ve,p(e.product.item_code)+" "+p(e.product.name),1)]),t("div",ye,[t("p",xe,p(e.qty),1)]),t("div",he,[t("p",fe,p(T(e.price)),1)]),t("div",we,[t("p",be,p(e.delivered_qty),1)])]))),128))]),t("div",Ve,[t("div",Fe,[t("table",Pe,[t("thead",null,[t("tr",null,[Ce,Se,Ie,ke,Te,De,$e,Ue,Ge,x.value=="IGST"?(_(),g("th",Me,"IGST (%)")):y("",!0),x.value=="IGST"?(_(),g("th",Ne,"IGST (₹)")):y("",!0),x.value=="CGST/SGST"?(_(),g("th",qe,"CGST (%)")):y("",!0),x.value=="CGST/SGST"?(_(),g("th",Oe,"SGST (%)")):y("",!0),x.value=="CGST/SGST"?(_(),g("th",Ae,"Total GST (₹)")):y("",!0),je,ze,Be])]),t("tbody",Ee,[(_(!0),g(M,null,A(c.value,(e,d)=>(_(),g("tr",{key:d},[t("td",Re,[t("div",Le,[i($,{options:P.products,modelValue:e.product_id,"onUpdate:modelValue":a=>e.product_id=a,onOnchange:(a,r)=>ut(a,r,d),onChange:l[5]||(l[5]=a=>n(s).validate("product_id")),class:w({"error rounded-md":n(s).errors[`selectedProductItem.${d}.product_id`]})},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class"])])]),t("td",We,[t("div",Qe,[i($,{options:mt(e.product_id),modelValue:e.serial_number_id,"onUpdate:modelValue":a=>e.serial_number_id=a,onOnchange:(a,r)=>pt(a,r,d,e,e.is_order),onChange:l[6]||(l[6]=a=>n(s).validate("serial_number_id")),class:w({"error rounded-md":n(s).errors[`selectedProductItem.${d}.serial_number_id`]}),editMode:e.editmode},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class","editMode"])])]),t("td",Ye,p(e.hsn_code),1),t("td",He,p(e.expiry_date??"-"),1),t("td",Ke,[i(v,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":a=>e.description=a,onInput:a=>C(e,d),onChange:a=>S("selectedProductItem."+d+".description"),class:w({error:n(s).errors[`selectedProductItem.${d}.description`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Xe,p(e.mrp??"-"),1),t("td",Ze,[i(v,{id:"qty",type:"text",numeric:!0,modelValue:e.qty,"onUpdate:modelValue":a=>e.qty=a,autocomplete:"qty",onInput:a=>C(e,d),onChange:a=>S("selectedProductItem."+d+".qty"),class:w({error:n(s).errors[`selectedProductItem.${d}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"]),n(s).errors[`selectedProductItem.${d}.qty1`]?(_(),g("p",Je,p(n(s).errors[`selectedProductItem.${d}.qty1`]),1)):y("",!0)]),t("td",ts,[i(v,{id:"sell_price",type:"text",modelValue:e.sell_price,"onUpdate:modelValue":a=>e.sell_price=a,autocomplete:"sell_price",onInput:a=>C(e,d),onChange:a=>S("selectedProductItem."+d+".sell_price"),class:w({error:n(s).errors[`selectedProductItem.${d}.sell_price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",es,[t("div",ss,p(e.total_price),1)]),x.value=="IGST"?(_(),g("td",os,[i(v,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":a=>e.gst=a,onInput:a=>C(e,d),onChange:a=>S("selectedProductItem."+d+".gst"),class:w({error:n(s).errors[`selectedProductItem.${d}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):y("",!0),x.value=="CGST/SGST"?(_(),g("td",ls,[i(v,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":a=>e.sgst=a,onInput:a=>C(e,d),onChange:a=>S("selectedProductItem."+d+".gst"),class:w({error:n(s).errors[`selectedProductItem.${d}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):y("",!0),x.value=="CGST/SGST"?(_(),g("td",as,[i(v,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":a=>e.sgst=a,onInput:a=>C(e,d),onChange:a=>S("selectedProductItem."+d+".gst"),class:w({error:n(s).errors[`selectedProductItem.${d}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):y("",!0),t("td",ns,p(e.total_gst_amount),1),t("td",ds,[i(v,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":a=>e.discount=a,onInput:a=>C(e,d),onChange:a=>S("selectedProductItem."+d+".discount"),class:w({error:n(s).errors[`selectedProductItem.${d}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",is,p(e.discount_amount),1),t("td",rs,[t("div",cs,[t("div",us,p(e.total_amount),1),t("button",{type:"button",class:"mt-1 flex",onClick:a=>xt(d)},ps,8,ms)])])]))),128))])])]),t("div",gs,[t("div",vs,[i(Y,{onClick:gt,type:"button"},{default:f(()=>[I("Add Product")]),_:1})])])]),n(m).documents&&n(m).documents.length>0?(_(),g("div",ys,[t("table",xs,[hs,t("tbody",fs,[(_(!0),g(M,null,A(n(m).documents,(e,d)=>(_(),g("tr",{key:n(m).id,class:""},[t("td",ws,p(e.orignal_name),1),t("td",bs,[t("button",{type:"button",onClick:a=>Pt(e.name)},Ps,8,Vs),t("button",{type:"button",onClick:a=>Ct(e.name)},Is,8,Cs)])]))),128))])])])):y("",!0),t("div",ks,[t("div",Ts,[t("div",Ds,[t("div",$s,[t("div",Us,[i(b,{for:"note",value:"Upload Documents"}),i(Rt,{inputId:"document",inputName:"document",onFiles:ft})]),t("div",Gs,[i(b,{for:"sales_user_id",value:"Sales Person"}),t("div",Ms,[i($,{options:P.salesuser,modelValue:n(s).sales_user_id,"onUpdate:modelValue":l[7]||(l[7]=e=>n(s).sales_user_id=e),onOnchange:Vt,class:w({"error rounded-md":n(s).errors.sales_user_id})},null,8,["options","modelValue","class"])])])]),t("div",Ns,[t("div",qs,[i(b,{for:"company_name",value:"Transport"}),i(v,{id:"gst",type:"text",modelValue:n(s).dispatch,"onUpdate:modelValue":l[8]||(l[8]=e=>n(s).dispatch=e)},null,8,["modelValue"])]),t("div",Os,[i(b,{for:"company_name",value:"Dispatch"}),i(v,{id:"transport",type:"text",modelValue:n(s).transport,"onUpdate:modelValue":l[9]||(l[9]=e=>n(s).transport=e)},null,8,["modelValue"])]),t("div",As,[i(b,{for:"eway_bill",value:"Eway Bill"}),i(v,{id:"eway_bill",type:"text",modelValue:n(s).eway_bill,"onUpdate:modelValue":l[10]||(l[10]=e=>n(s).eway_bill=e)},null,8,["modelValue"])])]),t("div",js,[t("div",zs,[i(b,{for:"company_name",value:"PO Number"}),i(v,{id:"gst",type:"text",modelValue:n(s).customer_po_number,"onUpdate:modelValue":l[11]||(l[11]=e=>n(s).customer_po_number=e)},null,8,["modelValue"])]),t("div",Bs,[i(b,{for:"company_name",value:"PO Date"}),i(v,{id:"customer_po_date",type:"date",modelValue:n(s).customer_po_date,"onUpdate:modelValue":l[12]||(l[12]=e=>n(s).customer_po_date=e)},null,8,["modelValue"])]),t("div",Es,[i(b,{for:"due_days",value:"Due Days"}),i(v,{id:"due_days",type:"text",modelValue:n(s).due_days,"onUpdate:modelValue":l[13]||(l[13]=e=>n(s).due_days=e)},null,8,["modelValue"])])]),t("div",Rs,[t("div",Ls,[i(b,{for:"patient_name",value:"Patient Name"}),i(v,{id:"patient_name",type:"text",modelValue:n(s).patient_name,"onUpdate:modelValue":l[14]||(l[14]=e=>n(s).patient_name=e)},null,8,["modelValue"])]),t("div",Ws,[i(b,{for:"cr_dr_note",value:"CR DR Note"}),i(v,{id:"cr_dr_note",type:"text",modelValue:n(s).cr_dr_note,"onUpdate:modelValue":l[15]||(l[15]=e=>n(s).cr_dr_note=e)},null,8,["modelValue"])])]),t("div",null,[i(b,{for:"note",value:"Note"}),i(Bt,{id:"note",type:"text",modelValue:n(s).note,"onUpdate:modelValue":l[16]||(l[16]=e=>n(s).note=e),onChange:l[17]||(l[17]=e=>n(s).validate("note"))},null,8,["modelValue"]),n(s).invalid("note")?(_(),Nt(zt,{key:0,class:"",message:n(s).errors.note},null,8,["message"])):y("",!0)])]),t("div",Qs,[t("div",Ys,[t("div",Hs,[Ks,t("p",Xs,p(T(tt.value)),1)]),t("div",Zs,[Js,t("div",to,[i(v,{id:"discount_before_tax",type:"text",modelValue:n(s).discount_before_tax,"onUpdate:modelValue":l[18]||(l[18]=e=>n(s).discount_before_tax=e)},null,8,["modelValue"])])]),t("div",eo,[so,t("div",oo,[i(v,{id:"overall_discount",type:"text",modelValue:n(s).overall_discount,"onUpdate:modelValue":l[19]||(l[19]=e=>n(s).overall_discount=e)},null,8,["modelValue"])])]),t("div",lo,[ao,t("p",no,p(T(et.value)),1)]),x.value=="IGST"?(_(),g("div",io,[ro,t("p",co,p(T(k.value)),1)])):y("",!0),x.value=="CGST/SGST"?(_(),g("div",uo,[mo,t("p",_o,p(T(k.value/2)),1)])):y("",!0),x.value=="CGST/SGST"?(_(),g("div",po,[go,t("p",vo,p(T(k.value/2)),1)])):y("",!0),t("div",yo,[xo,t("p",ho,p(T(J.value)),1)])])])])]),t("div",fo,[t("div",wo,[i(jt,{href:o.route("orders.index")},{svg:f(()=>[bo]),_:1},8,["href"]),i(Y,{disabled:n(s).processing},{default:f(()=>[I("Submit")]),_:1},8,["disabled"])])])],40,oe)]),i(j,{show:E.value,onClose:R},{default:f(()=>[t("div",Vo,[Fo,t("div",Po,[i(H,{onClick:R},{default:f(()=>[I(" Cancel")]),_:1}),i(rt,{class:"ml-3",onClick:yt},{default:f(()=>[I(" Delete ")]),_:1})])])]),_:1},8,["show"]),i(j,{show:st.value,onClose:L},{default:f(()=>[t("div",Co,[So,t("div",Io,[i(H,{onClick:L},{default:f(()=>[I(" Cancel")]),_:1}),i(rt,{class:"ml-3",onClick:bt},{default:f(()=>[I(" Delete ")]),_:1})])])]),_:1},8,["show"]),i(j,{show:W.value,onClose:lt,maxWidth:Ft.value},{default:f(()=>[t("div",ko,[i(Et,{fileUrl:n(K)+ot.value},null,8,["fileUrl"]),t("div",To,[i(H,{onClick:lt},{default:f(()=>[I(" Cancel")]),_:1})])])]),_:1},8,["show","maxWidth"]),i(j,{show:o.orderDeliverModal,onClose:o.closeOrderDeliverModal,maxWidth:o.paymentReceiveWidth},{default:f(()=>[t("div",Do,[$o,t("div",Uo,[t("div",Go,[Mo,(_(!0),g(M,null,A(n(m).pending_order_details,(e,d)=>(_(),g("div",{class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10 items-center",key:d},[t("div",No,[t("p",qo,p(e.product.name),1)]),t("div",Oo,[t("p",Ao,p(e.qty),1)]),t("div",jo,[t("p",zo,p(e.delivered_qty),1)]),t("div",Bo,[i(v,{id:"gst",type:"text",numeric:!0,modelValue:o.deliveredProduct[d].delivered_qty,"onUpdate:modelValue":a=>o.deliveredProduct[d].delivered_qty=a,autocomplete:"delivered_qty",onChange:a=>S("deliveredProduct."+d+".delivered_qty"),class:w({error:n(s).errors[`deliveredProduct.${d}.delivered_qty`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),n(s).errors[`deliveredProduct.${d}.delivered_qty`]?(_(),g("p",Eo,p(n(s).errors[`deliveredProduct.${d}.delivered_qty`]),1)):y("",!0)])]))),128))])]),t("div",Ro,[t("div",Lo,[i(Y,{class:"ml-3 w-20",onClick:o.acceptPayment},{default:f(()=>[I(" Save ")]),_:1},8,["onClick"])])])])]),_:1},8,["show","onClose","maxWidth"])]),_:1})],64))}},rl=Wt(Wo,[["__scopeId","data-v-989ac0bd"]]);export{rl as default};
