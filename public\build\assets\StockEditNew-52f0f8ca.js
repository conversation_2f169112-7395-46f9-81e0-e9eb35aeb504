import{K as $,r as v,C as M,o as p,c as _,a,u as c,w as n,F as V,Z as P,b as e,d as B,i as N,g as w,T as D,f as S,n as d,k as E,v as j,s as L,x as O}from"./app-97275a91.js";import{_ as T,a as F}from"./AdminLayout-595ad5a7.js";import{P as A}from"./PrimaryButton-46ac4375.js";import{_ as y}from"./TextInput-11c46564.js";import{_ as H}from"./SearchableDropdown-9d1b12d3.js";import{M as K}from"./Modal-48c075e7.js";import{D as R}from"./DangerButton-36669f8b.js";import{_ as Z}from"./SecondaryButton-d0c53c3f.js";import{u as G}from"./index-05d29b1c.js";import{_ as J}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const u=i=>(L("data-v-e095ed0f"),i=i(),O(),i),Q={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},W=u(()=>e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Stock",-1)),X=["onSubmit"],Y={class:"overflow-x-auto divide-y divide-gray-300"},ee=u(()=>e("thead",null,[e("tr",null,[e("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Organization"),e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Batch"),e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Expiry Date"),e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)"),e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Purchase Price (₹)"),e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Stock")])],-1)),te={class:"divide-y divide-gray-300 bg-white"},se={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-60"},oe={class:"relative mt-2"},ae={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-60"},re={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-20"},le=["onUpdate:modelValue"],ce={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-20"},ie={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-20"},ne={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-16"},de=["onClick"],me=u(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),pe=[me],_e={class:"flex mt-6 items-center justify-between"},ue={class:"ml-auto flex items-center justify-end gap-x-6"},ye=u(()=>e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),he={key:0,class:"text-sm text-gray-600"},fe={class:"p-6"},xe=u(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this stock? ",-1)),ge={class:"mt-6 flex justify-end"},ve={__name:"StockEditNew",props:["product","organization","page"],setup(i){const h=i,b=$().props.data[0],r=G("post","/savestock",{stockItems:[],company_id:h.product.company_id,product_id:b.id,page:h.page}),I=(s,g,t)=>{m.value[t].organization_id=s},m=v([{product_id:h.product.id,organization_id:"",batch:"",expiry_date:"",mrp:"",purchase_price:"",receive_qty:"",sell_qty:"",serial_number_id:""}]);M(()=>{m.value=b.serial_numbers.map(s=>({serial_number_id:s.id,product_id:s.product_id,organization_id:s.organization_id,batch:s.batch,expiry_date:s.expiry_date,mrp:s.mrp,purchase_price:s.purchase_price,receive_qty:s.receive_qty,sell_qty:s.sell_qty}))});const k=v(null),f=v(!1),x=()=>{f.value=!1},U=()=>{r.get(route("removeproduct",{id:k.value,model:"SerialNumbers"}),{onSuccess:()=>{x(),m.value.splice(index,1)}})},q=(s,g)=>{k.value=g,f.value=!0},z=()=>{r.stockItems=m.value,r.submit({preserveScroll:!0,onSuccess:()=>r.reset()})};return(s,g)=>(p(),_(V,null,[a(c(P),{title:"Stock Update"}),a(T,null,{default:n(()=>[e("div",Q,[W,e("form",{onSubmit:B(z,["prevent"]),class:""},[e("table",Y,[ee,e("tbody",te,[(p(!0),_(V,null,N(m.value,(t,l)=>(p(),_("tr",{key:l},[e("td",se,[e("div",oe,[a(H,{options:i.organization,modelValue:t.organization_id,"onUpdate:modelValue":o=>t.organization_id=o,onOnchange:(o,C)=>I(o,C,l),class:d({error:c(r).errors[`stockItems.${l}.organization_id`]})},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class"])])]),e("td",ae,[a(y,{id:"batch",type:"text",modelValue:t.batch,"onUpdate:modelValue":o=>t.batch=o,class:d({error:c(r).errors[`stockItems.${l}.batch`]})},null,8,["modelValue","onUpdate:modelValue","class"])]),e("td",re,[E(e("input",{class:d(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{error:c(r).errors[`stockItems.${l}.expiry_date`]}]),type:"date","onUpdate:modelValue":o=>t.expiry_date=o},null,10,le),[[j,t.expiry_date]])]),e("td",ce,[a(y,{id:"mrp",type:"text",modelValue:t.mrp,"onUpdate:modelValue":o=>t.mrp=o,class:d({error:c(r).errors[`stockItems.${l}.mrp`]})},null,8,["modelValue","onUpdate:modelValue","class"])]),e("td",ie,[a(y,{id:"purchase_price",type:"text",modelValue:t.purchase_price,"onUpdate:modelValue":o=>t.purchase_price=o,class:d({error:c(r).errors[`stockItems.${l}.purchase_price`]})},null,8,["modelValue","onUpdate:modelValue","class"])]),e("td",ne,[a(y,{id:"receive_qty",type:"text",numeric:!0,modelValue:t.receive_qty,"onUpdate:modelValue":o=>t.receive_qty=o,class:d({error:c(r).errors[`stockItems.${l}.receive_qty`]}),disabled:t.sell_qty>0},null,8,["modelValue","onUpdate:modelValue","class","disabled"])]),e("td",null,[t.sell_qty==0?(p(),_("button",{key:0,type:"button",class:"mt-1",onClick:o=>q(l,t.serial_number_id)},pe,8,de)):S("",!0)])]))),128))])]),e("div",_e,[e("div",ue,[a(F,{href:i.product.category==="Sales"?s.route("salesstock"):s.route("servicestock")},{svg:n(()=>[ye]),_:1},8,["href"]),a(A,{disabled:c(r).processing},{default:n(()=>[w("Update")]),_:1},8,["disabled"]),a(D,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:n(()=>[c(r).recentlySuccessful?(p(),_("p",he,"Saved.")):S("",!0)]),_:1})])])],40,X)]),a(K,{show:f.value,onClose:x},{default:n(()=>[e("div",fe,[xe,e("div",ge,[a(Z,{onClick:x},{default:n(()=>[w(" Cancel ")]),_:1}),a(R,{class:"ml-3",onClick:U},{default:n(()=>[w(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}},Me=J(ve,[["__scopeId","data-v-e095ed0f"]]);export{Me as default};
