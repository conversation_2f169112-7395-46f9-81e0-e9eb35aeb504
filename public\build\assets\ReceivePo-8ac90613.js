import{r as C,C as Q,j as V,A as G,o as p,c as y,a as _,u as l,w as I,F,Z as L,b as e,d as Y,t as u,n as g,e as N,f as w,k as Z,v as H,i as T,g as J,s as K,x as W}from"./app-6cdaf2bc.js";import{_ as X}from"./AdminLayout-b73e8538.js";import{P as ee}from"./PrimaryButton-b7e37df1.js";import{_ as b}from"./TextInput-61ab2d6e.js";import{_ as te}from"./DateInput-3652839d.js";import{_ as P}from"./InputLabel-38b98ddd.js";import{_ as se}from"./SearchableDropdown-8ff24da4.js";import{_ as oe}from"./MultipleFileUpload-cb399142.js";import{_ as ae}from"./TextArea-8bab3e6c.js";import{u as le}from"./index-839fed2d.js";import{_ as ce}from"./_plugin-vue_export-helper-c27b6911.js";const i=m=>(K("data-v-e5694f0c"),m=m(),W(),m),ne={class:"animate-top"},re=["onSubmit"],ie={class:"sm:flex sm:items-center"},de=i(()=>e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Receive Product")],-1)),_e={class:"text-sm font-semibold text-gray-900"},ue={class:"mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},me={class:"inline-flex items-start space-x-6 justify-start w-full"},pe={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},ve={class:"inline-flex items-center justify-start w-full space-x-2"},ge=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Company Name:",-1)),fe={class:"text-sm leading-6 text-gray-700"},ye={class:"inline-flex items-center justify-start w-full space-x-2"},he=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"GST No:",-1)),xe={class:"text-sm leading-6 text-gray-700"},be={class:"inline-flex items-center justify-start w-full space-x-2"},we=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Email:",-1)),Ve={class:"text-sm leading-6 text-gray-700"},Pe={class:"inline-flex items-center justify-start w-full space-x-2"},$e=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1)),qe={class:"text-sm leading-6 text-gray-700"},Ce={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},Fe={class:"inline-flex items-center justify-start w-full space-x-2"},Ne=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Receive No:",-1)),Ue={class:"text-sm leading-6 text-gray-700"},De={class:"inline-flex items-center justify-start w-full space-x-2"},Se=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Number:",-1)),je={class:"text-sm leading-6 text-gray-700"},ke={class:"inline-flex items-center justify-start w-full space-x-2"},Ie=i(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Date:",-1)),Te={class:"text-sm leading-6 text-gray-700"},Ae={class:"mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},Oe={class:"grid grid-cols-1 gap-x-6 sm:grid-cols-12 pb-2"},Re={class:"sm:col-span-4"},Be={class:"sm:col-span-4"},ze={class:"sm:col-span-4"},Ee={class:"relative mt-2"},Me={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border divide-y divide-gray-300 overflow-x-auto"},Qe={class:"grid grid-cols-1 gap-x-4 sm:grid-cols-12 pb-2",style:{width:"140%"}},Ge=i(()=>e("div",{class:"sm:col-span-2"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Product")],-1)),Le={class:"sm:col-span-1"},Ye={key:0,class:"text-sm font-semibold text-gray-900 leading-6"},Ze={key:1,class:"text-sm font-semibold text-gray-900 leading-6"},He=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Price (₹)")],-1)),Je=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"GST %")],-1)),Ke=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"QTY")],-1)),We=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Received QTY")],-1)),Xe=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"MRP (₹)")],-1)),et=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Purchase Price (₹)")],-1)),tt=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"QTY")],-1)),st=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Batch")],-1)),ot=i(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Total Amount")],-1)),at={class:"sm:col-span-2"},lt={class:"text-sm leading-5 text-gray-700"},ct={class:"sm:col-span-1"},nt={class:"text-sm leading-5 text-gray-700"},rt={class:"sm:col-span-1"},it={class:"text-sm leading-5 text-gray-700"},dt={class:"sm:col-span-1"},_t={class:"text-sm leading-5 text-gray-700"},ut={class:"sm:col-span-1"},mt={class:"text-sm leading-5 text-gray-700"},pt={class:"sm:col-span-1"},vt={class:"text-sm leading-5 text-gray-700"},gt={class:"sm:col-span-1 mb-2"},ft={class:"sm:col-span-1 mb-2"},yt={class:"sm:col-span-1 mb-2"},ht={key:0,class:"text-red-500 text-xs absolute"},xt={class:"flex sm:col-span-1 mb-2"},bt={class:"sm:col-span-1"},wt={class:"text-sm leading-5 text-gray-700"},Vt={key:0,class:"sm:col-span-9 mb-2"},Pt={class:"sm:col-span-3"},$t={class:"sm:col-span-3"},qt={class:"sm:col-span-3"},Ct={key:0,class:"text-red-500 text-xs absolute"},Ft={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Nt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Ut={class:"sm:col-span-3 space-y-2"},Dt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},St={class:"sm:col-span-3 space-y-4"},jt={class:"flex space-x-4"},kt={class:"w-full"},It={class:"sm:col-span-3"},Tt={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},At={class:"inline-flex items-center justify-end w-full space-x-3"},Ot=i(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),Rt={class:"text-base font-semibold text-gray-900 w-32"},Bt={class:"inline-flex items-center justify-end w-full space-x-3"},zt=i(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Total GST (₹):",-1)),Et={class:"text-base font-semibold text-gray-900 w-32"},Mt={class:"inline-flex items-center justify-end w-full space-x-3"},Qt=i(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Round Off (₹):",-1)),Gt={class:"w-32"},Lt={class:"inline-flex items-center justify-end w-full space-x-3"},Yt={key:0,class:"text-sm text-red-600 mt-2"},Zt={class:"inline-flex items-center justify-end w-full space-x-3"},Ht=i(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),Jt={class:"text-base font-semibold text-gray-900 w-32"},Kt={class:"flex mt-6 items-center justify-between"},Wt={class:"ml-auto flex items-center justify-end gap-x-6"},Xt={__name:"ReceivePo",props:["data","po_receive_number","salesuser"],setup(m){const h=m,r=C([{organization_id:"",company_id:"",product_id:"",purchase_order_detail_id:"",purchase_order_id:"",qty:"",po_receive_number:"",total_qty:"",received_qty:"",mrp:"",purchase_price:"",total_amount:"",total_price:"",total_gst_amount:""}]),U=C();U.value=h.po_receive_number[h.data[0].organization.id],Q(()=>{r.value=h.data[0].purchase_order_detail_for_receive.map(a=>({organization_id:h.data[0].organization_id,company_id:h.data[0].company_id,product_id:a.product_id,purchase_order_detail_id:a.id,purchase_order_id:h.data[0].id,po_receive_number:h.po_receive_number[h.data[0].organization_id],total_qty:a.qty,received_qty:a.receive_qty,receive_qty:"",total_batch:"",mrp:"",purchase_price:"",total_amount:"",total_price:"",total_gst_amount:""}))});const t=le("post","/savereceivepo",{receivedProduct:[],created_by:"",total_price:"",total_gst_amount:"",total_amount:"",customer_invoice_no:"",customer_invoice_date:"",category:h.data[0].category,type:h.data[0].type,round_off:"0.00",document:null,note:""}),A=a=>{t.document=a},O=()=>{parseFloat(t.round_off)>.99||(t.total_amount=S.value,t.total_gst_amount=j.value,t.total_price=k.value,t.receivedProduct=r.value.map((s,d)=>({...s,productDetails:$.value[d]||[]})),t.submit({preserveScroll:!0,onSuccess:()=>t.reset()}))},x=a=>{t.errors[a]=null},$=C([]),R=a=>{t.errors["receivedProduct."+a+".receive_qty"]=null;const s=r.value[a].total_batch,d=r.value[a].total_qty,c=r.value[a].received_qty,o=[];let n;if(s&&!isNaN(s)){s>d-c?n=d-c:n=s;for(let v=0;v<n;v++)o.push({batch:"",expiry_date:"",qty:""})}r.value[a].total_batch=n,$.value[a]=o};V(()=>{const a=new Date,s={year:"numeric",month:"long",day:"numeric"};return a.toLocaleDateString("en-US",s)});const B=(a,s)=>{t.created_by=a,t.errors.created_by=null},z=(a,s)=>{const d=parseFloat(r.value[s].purchase_price),c=parseFloat(a.gst)||0,o=parseFloat(r.value[s].receive_qty),n=d*o*(1+c/100),v=d*o,f=d*o*(c/100);return r.value[s].total_price=isNaN(v)?"":parseFloat(v).toFixed(2),r.value[s].total_gst_amount=isNaN(f)?"":parseFloat(f).toFixed(2),isNaN(n)?"":parseFloat(n).toFixed(2)},D=(a,s)=>{r.value[s].total_amount=z(a,s)},S=V(()=>{const a=r.value.reduce((c,o)=>c+(o.total_amount?parseFloat(o.total_amount):0),0),s=parseFloat(t.round_off)||0,d=a+s;return parseFloat(d.toFixed(2))}),E=V(()=>{const a=parseFloat(t.round_off);return a>=0&&a<=.99}),j=V(()=>r.value.reduce((a,s)=>a+(s.total_gst_amount?parseFloat(s.total_gst_amount):0),0)),k=V(()=>r.value.reduce((a,s)=>a+(s.total_price?parseFloat(s.total_price):0),0)),M=a=>{const s=new Date(a),d={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",d)},q=a=>{let d=Math.round(a).toString(),c=d.substring(d.length-3),o=d.substring(0,d.length-3);return o!==""&&(c=","+c),`${o.replace(/\B(?=(\d{2})+(?!\d))/g,",")+c}.00`};return(a,s)=>{const d=G("InputError");return p(),y(F,null,[_(l(L),{title:"Company PO"}),_(X,null,{default:I(()=>[e("div",ne,[e("form",{onSubmit:Y(O,["prevent"]),class:""},[e("div",ie,[de,e("p",_e,u(m.data[0].organization.name),1)]),e("div",ue,[e("div",me,[e("div",pe,[e("div",ve,[ge,e("p",fe,u(m.data[0].company.name??"-"),1)]),e("div",ye,[he,e("p",xe,u(m.data[0].company.gst_no??"-"),1)]),e("div",be,[we,e("p",Ve,u(m.data[0].company.email??"-"),1)]),e("div",Pe,[$e,e("p",qe,u(m.data[0].company.contact_no??"-"),1)])]),e("div",Ce,[e("div",Fe,[Ne,e("p",Ue,u(U.value??"-"),1)]),e("div",De,[Se,e("p",je,u(m.data[0].po_number??"-"),1)]),e("div",ke,[Ie,e("p",Te,u(M(m.data[0].date)??"-"),1)])])])]),e("div",Ae,[e("div",Oe,[e("div",Re,[_(P,{for:"customer_invoice_no",value:"Company Invoice No"}),_(b,{id:"customer_invoice_no",type:"text",modelValue:l(t).customer_invoice_no,"onUpdate:modelValue":s[0]||(s[0]=c=>l(t).customer_invoice_no=c),onChange:s[1]||(s[1]=c=>x("customer_invoice_no")),class:g({"error rounded-md":l(t).errors.customer_invoice_no})},null,8,["modelValue","class"]),l(t).invalid("customer_invoice_no")?(p(),N(d,{key:0,class:"",message:l(t).errors.customer_invoice_no},null,8,["message"])):w("",!0)]),e("div",Be,[_(P,{for:"customer_invoice_date",value:"Company Invoice Date"}),Z(e("input",{class:g(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":l(t).errors.customer_invoice_date}]),type:"date","onUpdate:modelValue":s[2]||(s[2]=c=>l(t).customer_invoice_date=c),onChange:s[3]||(s[3]=c=>x("customer_invoice_date"))},null,34),[[H,l(t).customer_invoice_date]]),l(t).invalid("customer_invoice_date")?(p(),N(d,{key:0,class:"",message:l(t).errors.customer_invoice_date},null,8,["message"])):w("",!0)]),e("div",ze,[_(P,{for:"company_name",value:"Received By:"}),e("div",Ee,[_(se,{options:m.salesuser,modelValue:l(t).created_by,"onUpdate:modelValue":s[4]||(s[4]=c=>l(t).created_by=c),onOnchange:B,class:g({"error rounded-md":l(t).errors.created_by})},null,8,["options","modelValue","class"])])])])]),e("div",Me,[e("div",Qe,[Ge,e("div",Le,[m.data[0].category=="Service"?(p(),y("p",Ye,"Part No")):(p(),y("p",Ze,"Product Code"))]),He,Je,Ke,We,Xe,et,tt,st,ot]),(p(!0),y(F,null,T(m.data[0].purchase_order_detail_for_receive,(c,o)=>(p(),y("div",{class:"grid grid-cols-1 gap-x-4 sm:grid-cols-12 items-center",style:{width:"140%"},key:o},[e("div",at,[e("p",lt,u(c.product.name??"-"),1)]),e("div",ct,[e("p",nt,u(c.product.item_code??"-"),1)]),e("div",rt,[e("p",it,u(q(c.price)??"-"),1)]),e("div",dt,[e("p",_t,u(q(c.gst)??"-"),1)]),e("div",ut,[e("p",mt,u(c.qty??"-"),1)]),e("div",pt,[e("p",vt,u(c.receive_qty??"-"),1)]),e("div",gt,[_(b,{id:"gst",type:"text",modelValue:r.value[o].mrp,"onUpdate:modelValue":n=>r.value[o].mrp=n,class:g({error:l(t).errors[`receivedProduct.${o}.mrp`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","class"])]),e("div",ft,[_(b,{id:"gst",type:"text",modelValue:r.value[o].purchase_price,"onUpdate:modelValue":n=>r.value[o].purchase_price=n,onInput:n=>D(c,o),onChange:n=>x("receivedProduct."+o+".purchase_price"),class:g({error:l(t).errors[`receivedProduct.${o}.purchase_price`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),e("div",yt,[_(b,{id:"gst",type:"text",modelValue:r.value[o].receive_qty,"onUpdate:modelValue":n=>r.value[o].receive_qty=n,numeric:!0,onChange:n=>x(`receivedProduct.${o}.receive_qty`),onInput:n=>D(c,o),class:g({error:l(t).errors[`receivedProduct.${o}.receive_qty`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","onChange","onInput","class"]),l(t).errors[`receivedProduct.${o}.receive_qty`]?(p(),y("p",ht,u(l(t).errors[`receivedProduct.${o}.receive_qty`]),1)):w("",!0)]),e("div",xt,[_(b,{id:"gst",type:"text",numeric:!0,modelValue:r.value[o].total_batch,"onUpdate:modelValue":n=>r.value[o].total_batch=n,onChange:n=>R(o),class:g({error:l(t).errors[`receivedProduct.${o}.total_batch`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),e("div",bt,[e("p",wt,u(r.value[o].total_amount),1)]),$.value[o]?(p(),y("div",Vt,[(p(!0),y(F,null,T($.value[o],(n,v)=>(p(),y("div",{key:v,class:"grid grid-cols-1 gap-x-6 sm:grid-cols-12 items-center"},[e("div",Pt,[_(b,{type:"text",modelValue:n.batch,"onUpdate:modelValue":f=>n.batch=f,placeholder:"Batch",onChange:f=>x("receivedProduct."+o+".productDetails."+v+".batch"),class:g({error:l(t).errors[`receivedProduct.${o}.productDetails.${v}.batch`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),e("div",$t,[_(te,{modelValue:n.expiry_date,"onUpdate:modelValue":f=>n.expiry_date=f,onChange:f=>x("receivedProduct."+o+".productDetails."+v+".expiry_date"),class:g({error:l(t).errors[`receivedProduct.${o}.productDetails.${v}.expiry_date`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),e("div",qt,[_(b,{type:"text",modelValue:n.qty,"onUpdate:modelValue":f=>n.qty=f,placeholder:"Qty",onChange:f=>x("receivedProduct."+o+".productDetails."+v+".qty"),class:g({error:l(t).errors[`receivedProduct.${o}.productDetails.${v}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])])]))),128)),l(t).errors[`receivedProduct.${o}.productDetails`]?(p(),y("p",Ct,u(l(t).errors[`receivedProduct.${o}.productDetails`]),1)):w("",!0)])):w("",!0)]))),128))]),e("div",Ft,[e("div",Nt,[e("div",Ut,[e("div",Dt,[e("div",St,[e("div",jt,[e("div",kt,[_(P,{for:"note",value:"Upload Documents"}),_(oe,{inputId:"document",inputName:"document",onFiles:A})])])])]),e("div",null,[_(P,{for:"note",value:"Note"}),_(ae,{id:"note",type:"text",modelValue:l(t).note,"onUpdate:modelValue":s[5]||(s[5]=c=>l(t).note=c),onChange:s[6]||(s[6]=c=>l(t).validate("note"))},null,8,["modelValue"]),l(t).invalid("note")?(p(),N(d,{key:0,class:"",message:l(t).errors.note},null,8,["message"])):w("",!0)])]),e("div",It,[e("div",Tt,[e("div",At,[Ot,e("p",Rt,u(q(k.value)),1)]),e("div",Bt,[zt,e("p",Et,u(q(j.value)),1)]),e("div",Mt,[Qt,e("div",Gt,[_(b,{id:"round_off",type:"text",modelValue:l(t).round_off,"onUpdate:modelValue":s[7]||(s[7]=c=>l(t).round_off=c),onChange:s[8]||(s[8]=c=>x("round_off")),class:g({"error rounded-md":l(t).errors.round_off}),min:"0"},null,8,["modelValue","class"])])]),e("div",Lt,[E.value?w("",!0):(p(),y("p",Yt,"Round Off should be between 0.01 and 0.99."))]),e("div",Zt,[Ht,e("p",Jt,u(S.value),1)])])])])]),e("div",Kt,[e("div",Wt,[_(ee,{class:g(["",{"opacity-25":l(t).processing}]),disabled:l(t).processing},{default:I(()=>[J(" Submit ")]),_:1},8,["class","disabled"])])])],40,re)])]),_:1})],64)}}},_s=ce(Xt,[["__scopeId","data-v-e5694f0c"]]);export{_s as default};
