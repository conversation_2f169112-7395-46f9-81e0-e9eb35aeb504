import{r as w,j as H,l as I,m as te,o as d,c as u,a as l,u as a,w as M,F as D,Z as oe,b as s,t as c,f,d as se,n as g,k as ne,v as ae,i as R,g as le,T as ie}from"./app-ce7743ab.js";import{_ as re,a as de}from"./AdminLayout-6af2fc6a.js";import{_ as h}from"./InputLabel-3aa35471.js";import{P as ue}from"./PrimaryButton-6ff8a943.js";import{_ as F}from"./TextInput-65921831.js";import{_ as me}from"./TextArea-5fab1749.js";import{_ as ce}from"./RadioButton-2a9849cb.js";import{_ as O}from"./SearchableDropdown-6fd7fbbe.js";import{u as _e}from"./index-588ba5dc.js";/* empty css                                                                          */import{_ as pe}from"./Checkbox-540f8602.js";import"./_plugin-vue_export-helper-c27b6911.js";const ve={class:"h-screen animate-top"},ye={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},fe={class:"sm:flex sm:items-center"},ge=s("div",{class:"sm:flex-auto"},[s("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Payment")],-1),he={class:"flex items-center justify-between"},xe={key:0,class:"text-base font-semibold leading-6 text-gray-900"},be=["onSubmit"],ke={class:"border-b border-gray-900/10 pb-12"},we={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},Ve={class:"sm:col-span-3"},Fe={class:"relative mt-2"},Ne={class:"sm:col-span-3"},Ae={class:"relative mt-2"},Ce={class:"sm:col-span-2 hidden"},ze={class:"relative mt-2"},Se={key:0,class:"sm:col-span-3"},$e={class:"relative mt-2"},Ue={key:1,class:"sm:col-span-3"},Pe={key:2,class:"sm:col-span-2"},Te={key:3,class:"sm:col-span-1"},Ee={key:4,class:"sm:col-span-1"},Me={key:5,class:"sm:col-span-1"},De={key:6,class:"sm:col-span-3"},Oe={key:7,class:"sm:col-span-2"},Be={class:"mt-4 flex justify-start"},je={class:"text-base font-semibold"},Ie={key:8,class:"sm:col-span-2"},Re={key:9,class:"sm:col-span-2"},Le={key:10,class:"sm:col-span-2"},Ye={class:"relative mt-2"},qe={key:11,class:"sm:col-span-3"},He={class:"sm:col-span-6"},Ze={class:"overflow-x-auto divide-y divide-gray-300 w-full"},Ge=s("div",{class:"w-full"},[s("thead",{class:"w-full"},[s("tr",{class:""},[s("th",{scope:"col",class:""}),s("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),s("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),s("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1),Je={style:{"overflow-y":"auto","max-height":"318px"}},Ke={class:"divide-y divide-gray-300 bg-white"},Qe={class:"whitespace-nowrap px-2 text-sm text-gray-900"},We={class:"text-sm text-gray-900 leading-6 py-1.5"},Xe={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},et={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},tt={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},ot={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},st={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},nt={key:0,class:"text-red-500 text-xs absolute"},at={class:"whitespace-nowrap px-2 text-sm text-gray-900"},lt={class:"sm:col-span-2"},it={class:"mt-2 p-3 bg-gray-50 rounded-md"},rt={class:"space-y-2 text-sm"},dt={class:"flex items-center gap-2"},ut=s("hr",{class:"my-2"},null,-1),mt={class:"flex justify-between items-center font-semibold"},ct=s("span",null,"Settlement:",-1),_t={class:"flex justify-between items-center font-semibold"},pt=s("span",null,"Advance Amount:",-1),vt={key:0,class:"text-red-500 text-xs absolute"},yt={key:12,class:"sm:col-span-6"},ft={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},gt=s("thead",null,[s("tr",null,[s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1),ht={class:"divide-y divide-gray-300 bg-white"},xt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},bt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},kt={class:"flex flex-col"},wt={class:"text-sm text-gray-900"},Vt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},Ft={class:"whitespace-nowrap py-3 text-sm text-gray-900"},Nt={class:"flex mt-6 items-center justify-between"},At={class:"ml-auto flex items-center justify-end gap-x-6"},Ct=s("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),zt={key:0,class:"text-sm text-gray-600"},Rt={__name:"Edit",props:["payment","paymentType","bankinfo","organization","customers","invoices","credit"],setup(P){const r=P;w([]);const B=w([]),Z=r.bankinfo.filter(n=>n.organization_id===r.payment.organization_id);B.value=Z;const N=w(r.payment.payment_type),t=_e("post","/receipt",{id:r.payment.id,organization_id:r.payment.organization_id,customer_id:r.payment.customer_id,payment_type:r.payment.payment_type,date:r.payment.date,note:r.payment.note,amount:r.payment.amount,tds_amount:r.payment.tds_amount||0,discount_amount:r.payment.discount_amount||0,round_off:r.payment.round_off||0,check_number:r.payment.check_number,bank_name:r.payment.bank_name,org_bank_id:r.payment.org_bank_id,invoice:[],settled_amount:"",advance_amount:"",is_credit:"No",credit_data:[],total_unused_amount:""}),G=()=>{t.settled_amount=S.value,t.advance_amount=$.value,t.total_unused_amount=A.value,t.is_credit=_.value,t.invoice=x.value,t.credit_data=k.value,t.submit({preserveScroll:!0,onSuccess:()=>{}})},J=(n,o)=>{N.value=n,t.payment_type=n,t.errors.payment_type=null,o==="Cash"?t.note="Cash":t.note==="Cash"&&(t.note="")},z=w([]),k=w([]),A=w(""),L=(n,o)=>{const e=r.bankinfo.filter(m=>m.organization_id===n);B.value=e;const p=r.invoices.filter(m=>m.organization_id===n&&m.customer_id===t.customer_id);z.value=p;const i=r.credit.filter(m=>m.organization_id===n&&m.customer_id===t.customer_id);k.value=i,A.value=k.value.reduce((m,b)=>m+b.unused_amount,0),t.organization_id=n,t.errors.organization_id=null},K=(n,o)=>{const e=r.invoices.filter(i=>i.customer_id===n&&i.organization_id===t.organization_id);z.value=e;const p=r.credit.filter(i=>i.customer_id===n&&i.organization_id===t.organization_id);k.value=p,A.value=k.value.reduce((i,m)=>i+m.unused_amount,0),t.customer_id=n,t.errors.customer_id=null},Q=(n,o)=>{t.org_bank_id=n,t.errors.org_bank_id=null},S=H(()=>x.value.reduce((n,o)=>{if(o.check&&o.amount){const e=parseFloat(o.amount);return o.invoice_type==="sales"?n+e:n-e}return n},0)),$=H(()=>{const n=parseFloat(t.amount||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0),o=parseFloat(t.round_off||0),e=S.value;return n-e-o}),j=()=>{},V=n=>{let o=n.toFixed(2).toString(),[e,p]=o.split("."),i=e.substring(e.length-3),m=e.substring(0,e.length-3);return m!==""&&(i=","+i),`${m.replace(/\B(?=(\d{2})+(?!\d))/g,",")+i}.${p}`},Y=n=>{const o=new Date(n),e={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",e)},_=w("No"),W=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],X=n=>{const o=_.value==="Yes"?parseFloat(A.value||0):parseFloat(t.amount||0)+parseFloat(t.round_off||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0);if(!x.value[n].check){x.value[n].amount=0;return}const e=x.value.filter(y=>y.check),p=e.some(y=>y.invoice_type==="sales"),i=e.some(y=>y.invoice_type==="purchase");if(p&&i&&e.length>1&&ee(o,e))return;let m=o;x.value.forEach((y,E)=>{if(y.check&&E!==n&&parseFloat(y.amount||0)>0){const U=parseFloat(y.amount||0);y.invoice_type==="sales"?m-=U:y.invoice_type==="purchase"&&(m+=U)}});const b=parseFloat(x.value[n].pending_amount||0),v=Math.min(b,Math.max(0,m));x.value[n].amount=v.toFixed(2)},ee=(n,o)=>{const e=o.filter(v=>v.invoice_type==="sales"),p=o.filter(v=>v.invoice_type==="purchase"),i=e.reduce((v,y)=>v+parseFloat(y.pending_amount||0),0),m=p.reduce((v,y)=>v+parseFloat(y.pending_amount||0),0),b=i-m;return Math.abs(n-Math.abs(b))<=1?(o.forEach(v=>{v.amount=parseFloat(v.pending_amount||0).toFixed(2)}),!0):b>0&&n>=b?(o.forEach(v=>{v.amount=parseFloat(v.pending_amount||0).toFixed(2)}),!0):!1},x=w([]),T=()=>{x.value=z.value.map(n=>{const o=n.invoice_type||"sales",e=r.payment.invoice_data.some(i=>i.id===n.id&&(i.invoice_type||"sales")===o),p=e?r.payment.invoice_data.find(i=>i.id===n.id&&(i.invoice_type||"sales")===o).amount:0;return{id:n.id,date:n.date,invoice_no:n.invoice_no,total_amount:parseFloat(n.total_amount||0).toFixed(2),pending_amount:parseFloat(n.original_pending_amount||n.pending_amount||0).toFixed(2),invoice_type:o,check:e,amount:e?p.toString():"0.00"}})};I(z,()=>{T()}),I(_,()=>{T()}),I(()=>t.amount,()=>{_.value==="No"&&T()});const C=n=>{t.errors[n]=null,t.errors.settled_amount=null};return te(()=>{L(r.payment.organization_id),z.value=r.invoices,T();const n=r.credit.filter(o=>o.organization_id===r.payment.organization_id&&o.customer_id===r.payment.customer_id);k.value=n,A.value=k.value.reduce((o,e)=>o+e.unused_amount,0)}),(n,o)=>(d(),u(D,null,[l(a(oe),{title:"Edit Receipt"}),l(re,null,{default:M(()=>[s("div",ve,[s("div",ye,[s("div",fe,[ge,s("div",he,[k.value.length>0?(d(),u("div",xe," Credits Available: ₹"+c(V(A.value)),1)):f("",!0)])]),s("form",{onSubmit:se(G,["prevent"]),class:""},[s("div",ke,[s("div",we,[s("div",Ve,[l(h,{for:"payment_type",value:"Organization"}),s("div",Fe,[l(O,{options:P.organization,modelValue:a(t).organization_id,"onUpdate:modelValue":o[0]||(o[0]=e=>a(t).organization_id=e),onOnchange:L,class:g({"error rounded-md":a(t).errors.organization_id}),editMode:"editMode"},null,8,["options","modelValue","class"])])]),s("div",Ne,[l(h,{for:"payment_type",value:"Customer"}),s("div",Ae,[l(O,{options:P.customers,modelValue:a(t).customer_id,"onUpdate:modelValue":o[1]||(o[1]=e=>a(t).customer_id=e),onOnchange:K,class:g({"error rounded-md":a(t).errors.customer_id}),editMode:"editMode"},null,8,["options","modelValue","class"])])]),s("div",Ce,[l(h,{for:"role_id",value:"Payment Through Credit ?"}),s("div",ze,[l(ce,{modelValue:_.value,"onUpdate:modelValue":o[2]||(o[2]=e=>_.value=e),options:W},null,8,["modelValue"])])]),_.value=="No"?(d(),u("div",Se,[l(h,{for:"payment_type",value:"Payment Type"}),s("div",$e,[l(O,{options:P.paymentType,modelValue:a(t).payment_type,"onUpdate:modelValue":o[3]||(o[3]=e=>a(t).payment_type=e),onOnchange:J,class:g({"error rounded-md":a(t).errors.payment_type})},null,8,["options","modelValue","class"])])])):f("",!0),_.value=="No"?(d(),u("div",Ue,[l(h,{for:"date",value:"Payment Date"}),ne(s("input",{"onUpdate:modelValue":o[4]||(o[4]=e=>a(t).date=e),class:g(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":a(t).errors.date}]),type:"date",onChange:o[5]||(o[5]=e=>C("date"))},null,34),[[ae,a(t).date]])])):f("",!0),_.value=="No"?(d(),u("div",Pe)):f("",!0),_.value=="No"?(d(),u("div",Te,[l(h,{for:"tds_amount",value:"TDS Amount"}),l(F,{type:"text",onChange:o[6]||(o[6]=e=>C("tds_amount")),onInput:o[7]||(o[7]=e=>j()),modelValue:a(t).tds_amount,"onUpdate:modelValue":o[8]||(o[8]=e=>a(t).tds_amount=e),class:g({"error rounded-md":a(t).errors.tds_amount})},null,8,["modelValue","class"])])):f("",!0),_.value=="No"?(d(),u("div",Ee,[l(h,{for:"discount_amount",value:"Discount Amount"}),l(F,{type:"text",onChange:o[9]||(o[9]=e=>C("discount_amount")),onInput:o[10]||(o[10]=e=>j()),modelValue:a(t).discount_amount,"onUpdate:modelValue":o[11]||(o[11]=e=>a(t).discount_amount=e),class:g({"error rounded-md":a(t).errors.discount_amount})},null,8,["modelValue","class"])])):f("",!0),_.value=="No"?(d(),u("div",Me,[l(h,{for:"round_off",value:"Round Off"}),l(F,{type:"text",onChange:o[12]||(o[12]=e=>C("round_off")),modelValue:a(t).round_off,"onUpdate:modelValue":o[13]||(o[13]=e=>a(t).round_off=e),class:g({"error rounded-md":a(t).errors.round_off})},null,8,["modelValue","class"])])):f("",!0),_.value=="No"?(d(),u("div",De,[l(h,{for:"amount",value:"Amount"}),l(F,{id:"amount",type:"text",onChange:o[14]||(o[14]=e=>C("amount")),onInput:o[15]||(o[15]=e=>j()),modelValue:a(t).amount,"onUpdate:modelValue":o[16]||(o[16]=e=>a(t).amount=e),class:g({"error rounded-md":a(t).errors.amount})},null,8,["modelValue","class"])])):f("",!0),_.value=="No"?(d(),u("div",Oe,[l(h,{for:"advance",value:"Advance(Ref) Amount"}),s("div",Be,[s("p",je,c(V($.value)),1)])])):f("",!0),N.value=="check"||N.value=="Cheque"&&_.value=="No"?(d(),u("div",Ie,[l(h,{for:"check_number",value:"Cheque Number"}),l(F,{id:"check_number",type:"text",modelValue:a(t).check_number,"onUpdate:modelValue":o[17]||(o[17]=e=>a(t).check_number=e),class:g({"error rounded-md":a(t).errors["data.check_number"]})},null,8,["modelValue","class"])])):f("",!0),N.value=="check"&&_.value=="No"?(d(),u("div",Re,[l(h,{for:"bank_name",value:"Bank Name"}),l(F,{id:"bank_name",type:"text",modelValue:a(t).bank_name,"onUpdate:modelValue":o[18]||(o[18]=e=>a(t).bank_name=e),class:g({"error rounded-md":a(t).errors["data.bank_name"]})},null,8,["modelValue","class"])])):f("",!0),N.value!="cash"&&_.value=="No"?(d(),u("div",Le,[l(h,{for:"org_bank_id",value:"Our Bank"}),s("div",Ye,[l(O,{options:B.value,modelValue:a(t).org_bank_id,"onUpdate:modelValue":o[19]||(o[19]=e=>a(t).org_bank_id=e),onOnchange:Q,class:g({"error rounded-md":a(t).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):f("",!0),N.value!="cash"&&_.value=="No"?(d(),u("div",qe)):f("",!0),s("div",He,[s("table",Ze,[Ge,s("div",Je,[s("tbody",Ke,[(d(!0),u(D,null,R(x.value,(e,p)=>(d(),u("tr",{key:p},[s("td",Qe,[s("div",We,[l(pe,{name:"check",checked:e.check,"onUpdate:checked":i=>e.check=i,onChange:i=>X(p)},null,8,["checked","onUpdate:checked","onChange"])])]),s("td",Xe,[s("span",{class:g([e.invoice_type==="sales"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},c(e.invoice_type==="sales"?"SALES":"PURCHASE"),3)]),s("td",et,c(e.invoice_no),1),s("td",tt,c(e.total_amount),1),s("td",ot,c(e.pending_amount),1),s("td",st,[l(F,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":i=>e.amount=i,onChange:i=>C("invoice."+p+".amount"),class:g({error:a(t).errors[`invoice.${p}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),a(t).errors[`invoice.${p}.amount`]?(d(),u("p",nt,c(a(t).errors[`invoice.${p}.amount`]),1)):f("",!0)]),s("td",at,c(Y(e.date)),1)]))),128))])])])]),s("div",lt,[l(h,{for:"note",value:"Net Settlement Summary"}),s("div",it,[s("div",rt,[(d(!0),u(D,null,R(x.value.filter(e=>e.check),e=>(d(),u("div",{key:e.id,class:"flex justify-between items-center"},[s("div",dt,[s("span",{class:g([e.invoice_type==="sales"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800","inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium"])},c(e.invoice_type==="sales"?"S":"P"),3),s("span",null,c(e.invoice_no),1)]),s("span",{class:g([e.invoice_type==="sales"?"text-green-600":"text-blue-600","font-medium"])},c(e.invoice_type==="sales"?"+":"-")+"₹"+c(V(parseFloat(e.amount||0))),3)]))),128)),ut,s("div",mt,[ct,s("span",{class:g(S.value>=0?"text-green-600":"text-red-600")}," ₹"+c(V(Math.abs(S.value)))+" "+c(S.value>=0?"(Receive)":"(Pay)"),3)]),s("div",_t,[pt,s("span",{class:g($.value>=0?"text-green-600":"text-red-600")},c($.value>0?"+":"")+"₹"+c(V(parseFloat($.value||0))),3)])])]),a(t).errors.settled_amount?(d(),u("p",vt,c(a(t).errors.settled_amount),1)):f("",!0)]),_.value=="No"?(d(),u("div",yt,[l(h,{for:"note",value:"Note"}),l(me,{id:"note",type:"text",rows:2,modelValue:a(t).note,"onUpdate:modelValue":o[20]||(o[20]=e=>a(t).note=e)},null,8,["modelValue"])])):f("",!0)]),k.value.length>0&&_.value=="Yes"?(d(),u("table",ft,[gt,s("tbody",ht,[(d(!0),u(D,null,R(k.value,(e,p)=>{var i,m,b,v,y,E,U,q;return d(),u("tr",{key:p},[s("td",xt,c(Y(e.date)),1),s("td",bt,[s("div",kt,[s("div",wt,c((m=(i=e.paymentreceive)==null?void 0:i.bank_info)!=null&&m.bank_name?(v=(b=e.paymentreceive)==null?void 0:b.bank_info)==null?void 0:v.bank_name:"Cash")+" - "+c((E=(y=e.paymentreceive)==null?void 0:y.bank_info)!=null&&E.account_number?(q=(U=e.paymentreceive)==null?void 0:U.bank_info)==null?void 0:q.account_number:""),1)])]),s("td",Vt,c(V(e.amount)),1),s("td",Ft,c(V(e.unused_amount)),1)])}),128))])])):f("",!0)]),s("div",Nt,[s("div",At,[l(de,{href:n.route("receipt.index")},{svg:M(()=>[Ct]),_:1},8,["href"]),l(ue,{disabled:a(t).processing},{default:M(()=>[le("Update")]),_:1},8,["disabled"]),l(ie,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:M(()=>[a(t).recentlySuccessful?(d(),u("p",zt,"Saved.")):f("",!0)]),_:1})])])],40,be)])])]),_:1})],64))}};export{Rt as default};
