<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\BankTransaction;
use App\Models\Organization;
use App\Models\Company;
use Illuminate\Support\Facades\Redirect;
use App\Http\Requests\PaymentPayRequest;
use App\Models\AccountType;
use App\Models\BankInfo;
use App\Models\PurchaseOrderReceives;
use App\Models\PurchaseTransaction;
use App\Models\PaymentPaid;
use App\Models\CompanyCredit;
use App\Models\CompanyCreditDetails;
use App\Models\Invoice;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;
use App\Traits\QueryTrait;
use Illuminate\Support\Facades\Config;

class PaymentController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:List Bank Transaction')->only(['index']);
        $this->middleware('permission:Create Bank Transaction')->only(['create', 'store']);
        $this->middleware('permission:Edit Bank Transaction')->only(['edit', 'update']);
        $this->middleware('permission:Delete Bank Transaction')->only('destroy');
    }

    use QueryTrait;

    public function index(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $companyId = $request->input('company_id');
        $query = PaymentPaid::with('company', 'bankInfo');

        $organization  = Organization::select('id', 'name')->get();
        $companies = Company::select('name', 'id')->orderByRaw('name')->get();
        $allOption = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOption);
        $allOption2 = ['id' => null, 'name' => 'ALL COMPANY'];
        $companies->prepend($allOption2);

        if($companyId) {
            $query->where('company_id', $companyId);
        }
        if($organizationId) {
            $query->where('organization_id', $organizationId);
        }

        $searchableFields = ['company.name', 'invoice_no', 'payment_type', 'bank_info.bank_name', 'date', 'amount'];
        $this->searchAndSort($query, $request, $searchableFields);

        $data = $query->orderBy('id', 'desc')->paginate(20);
        $data->withQueryString()->links();
        return Inertia::render('Payment/List', compact('data', 'organization', 'companies', 'companyId', 'organizationId'));
    }

    public function create(Request $request)
    {
        $organization  = Organization::select('id', 'name')->get();
        $companies = Company::select('name', 'id', 'party_id')->orderByRaw('name')->get();
        $paymentType = Config::get('constants.paymentTypes');
        $bankinfo  = BankInfo::select('id', DB::raw("CONCAT(bank_name, ' - ', account_number) AS name"), 'organization_id')->get();

        // Get purchase invoices (from PurchaseOrderReceives)
        $purchaseInvoices = PurchaseOrderReceives::with('purchaseOrder')
            ->where('status', '!=', 'Paid')
            ->where('total_amount', '>', 0)
            ->whereHas('purchaseOrder', function ($query) {
                $query->where('type', 'invoice');
            })
            ->join('purchase_orders', 'purchase_order_receives.purchase_order_id', '=', 'purchase_orders.id')
            ->select(DB::raw("CONCAT('PURCHASE - ', purchase_order_receives.customer_invoice_no, '  ', 'Date', ' : ', DATE_FORMAT(purchase_order_receives.customer_invoice_date, '%d %b %Y'), '  ',  'Total Amount : ', purchase_order_receives.total_amount) AS name"),
                'purchase_order_receives.id', 'purchase_orders.organization_id', 'purchase_orders.company_id', 'purchase_order_receives.total_amount',
                'purchase_order_receives.customer_invoice_no as invoice_no', 'purchase_order_receives.customer_invoice_date as date',
                'purchase_order_receives.pending_amount', DB::raw("'purchase' as invoice_type"))
            ->orderBy('purchase_order_receives.customer_invoice_date', 'asc');

        // Get sales invoices (from Invoice table) with company info
        $salesInvoices = Invoice::with('customer')
            ->where('status', '!=', 'Paid')
            ->join('customers', 'invoices.customer_id', '=', 'customers.id')
            ->select(DB::raw("CONCAT('SALES - ', invoices.invoice_no, '  ', 'Date', ' : ', DATE_FORMAT(invoices.date, '%d %b %Y'), '  ',  'Total Amount : ', invoices.total_amount) AS name"),
                'invoices.id', 'invoices.organization_id', 'customers.party_id', 'invoices.total_amount',
                'invoices.invoice_no', 'invoices.date', 'invoices.pending_amount',
                DB::raw("'sales' as invoice_type"))
            ->orderBy('invoices.date', 'asc');

        // Combine both invoice types
        $invoices = $purchaseInvoices->union($salesInvoices)->get();

        $credit = CompanyCredit::with('paymentpaid.bankInfo')->where('unused_amount', '>', 0)->get();
        return Inertia::render('Payment/Add', compact('paymentType', 'organization', 'bankinfo', 'companies', 'invoices', 'credit'));
    }

    public function store(PaymentPayRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            //payment edit
            if(isset($data['id'])){
                $paymentId = $data['id'];
                // Find the original payment
                $originalPayment = PaymentPaid::with('credit', 'credit.creditDetail')->find($paymentId);

                // Revert the effects of the original payment
                // 1. Revert invoice payments
                foreach ($originalPayment->invoice_data as $invoice) {
                    $invoiceDetail = PurchaseOrderReceives::find($invoice['id']);
                    if ($invoiceDetail) {
                        $updateData['paid_amount'] = $invoiceDetail->paid_amount - $invoice['amount'];
                        $updateData['pending_amount'] = $invoiceDetail->pending_amount + $invoice['amount'];
                        if ($updateData['pending_amount'] <= 0) {
                            $updateData['status'] = 'Paid';
                        } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                            $updateData['status'] = 'Partially Paid';
                        } else {
                            $updateData['status'] = 'Unpaid';
                        }
                        $invoiceDetail->update($updateData);
                    }
                }

                // 2. Revert credit usage
                // if ($originalPayment->credit) {
                //     $credit = $originalPayment->credit;
                //     foreach ($credit->creditDetail as $detail) {
                //         $invoice = PurchaseOrderReceives::find($detail->purchase_order_receive_id);
                //         if ($invoice) {
                //             $invoice->paid_amount -= $detail->amount;
                //             $invoice->pending_amount += $detail->amount;
                //             if ($invoice->paid_amount <= 0) {
                //                 $invoice->status = 'Unpaid';
                //             } elseif ($invoice->paid_amount > 0 && $invoice->pending_amount > 0) {
                //                 $invoice->status = 'Partially Paid';
                //             } else {
                //                 $invoice->status = 'Paid';
                //             }
                //             $invoice->save();
                //         }
                //         $detail->delete();
                //     }

                //     // Don't delete the credit record, just update it
                //     if ($originalPayment->advance_amount > 0) {
                //         $credit->unused_amount = $originalPayment->advance_amount;
                //         $credit->save();
                //     } else {
                //         $credit->delete();
                //     }
                // }

                // 3. Delete related bank transaction and purchase transaction
                if ($originalPayment->payment_type == 'check' || $originalPayment->payment_type == 'NEFT') {
                    BankTransaction::where(['entity_id' => $paymentId, 'entity_type' => 'payment_paid'])->delete();
                }
                PurchaseTransaction::where(['entity_id' => $paymentId, 'entity_type' => 'payment_paid'])->delete();

                // Process the new payment data
                $filteredInvoiceData = array_filter($data['invoice'], function ($invoice) {
                    return $invoice['check'] === true;  // Only include invoices where `check` is true
                });
                $data['invoice_data'] = array_values(array_map(function ($invoice) {
                    return ([
                        'id' => $invoice['id'],
                        'invoice_no' => $invoice['invoice_no'],
                        'amount' => $invoice['amount'],
                        'invoice_type' => $invoice['invoice_type'] ?? 'purchase',
                    ]);
                }, $filteredInvoiceData));

                $invoice_nos = implode(',', array_map(function($item) {
                    return $item['invoice_no'];  // Return the 'invoice_no' field
                }, $data['invoice_data']));

                $data['created_by'] = $data['updated_by'] = auth()->id();
                $originalPayment->update($data);

                if ($originalPayment) {
                    foreach ($data['invoice_data'] as $invoice) {
                        $invoiceType = $invoice['invoice_type'] ?? 'purchase';

                        if ($invoiceType === 'purchase') {
                            $InvoiceDetail = PurchaseOrderReceives::find($invoice['id']);
                            $updateData['paid_amount'] = $InvoiceDetail->paid_amount + $invoice['amount'];
                            $updateData['pending_amount'] = $InvoiceDetail->pending_amount - $invoice['amount'];
                            if ($updateData['pending_amount'] <= 0) {
                                $updateData['status'] = 'Paid';
                            } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                                $updateData['status'] = 'Partially Paid';
                            } else {
                                $updateData['status'] = 'Unpaid';
                            }
                            $InvoiceDetail->update($updateData);
                        } else if ($invoiceType === 'sales') {
                            $SalesInvoiceDetail = Invoice::find($invoice['id']);
                            $updateData['paid_amount'] = $SalesInvoiceDetail->paid_amount + $invoice['amount'];
                            $updateData['pending_amount'] = $SalesInvoiceDetail->pending_amount - $invoice['amount'];
                            if ($updateData['pending_amount'] <= 0) {
                                $updateData['status'] = 'Paid';
                            } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                                $updateData['status'] = 'Partially Paid';
                            } else {
                                $updateData['status'] = 'Unpaid';
                            }
                            $SalesInvoiceDetail->update($updateData);
                        }
                    }
                }

                // Create purchase transaction and bank transaction
                $data['entity_id'] = $originalPayment->id;
                $data['entity_type'] = 'payment_paid';
                if ($data['payment_type'] == 'check') {
                    $data['payment_type'] = 'dr';
                    $data['note'] = 'Cheque No:' . $data['check_number'] . ' ' . 'Invoice No:' . $invoice_nos;
                    BankTransaction::create($data);
                } else if ($data['payment_type'] == 'NEFT') {
                    $data['payment_type'] = 'dr';
                    $data['note'] = 'NEFT' . ' ' . 'Invoice No:' . $invoice_nos;
                    BankTransaction::create($data);
                } else if ($data['payment_type'] == 'cash') {
                    $data['payment_type'] = 'dr';
                    $data['note'] = $data['note'] . ' ' . 'Invoice No:' . $invoice_nos;
                }

                if ($data['advance_amount'] > 0) {
                    // Check if there's an existing credit record
                    $existingCredit = CompanyCredit::where('payment_paid_id', $originalPayment->id)->first();
                    if ($existingCredit) {
                        $existingCredit->update([
                            'unused_amount' => $data['advance_amount'],
                            'amount' => $data['advance_amount']
                        ]);
                    } else {
                        $data['payment_paid_id'] = $originalPayment->id;
                        $data['unused_amount'] = $data['amount'] = $data['advance_amount'];
                        CompanyCredit::create($data);
                    }
                }
                $data['amount'] = $data['settled_amount'] + $data['advance_amount'];
                PurchaseTransaction::create($data);
                DB::commit();
                return Redirect::to('/payment')->with('success', 'Payment Updated Successfully');
            } else {
                $filteredInvoiceData = array_filter($data['invoice'], function ($invoice) {
                    return $invoice['check'] === true;  // Only include invoices where `check` is true
                });
                $data['invoice_data'] = array_values(array_map(function ($invoice) {
                    return ([
                        'id' => $invoice['id'],
                        'invoice_no' => $invoice['invoice_no'],
                        'amount' => $invoice['amount'],
                        'invoice_type' => $invoice['invoice_type'] ?? 'purchase',
                    ]);
                }, $filteredInvoiceData));

                $invoice_nos = implode(',', array_map(function($item) {
                    return $item['invoice_no'];  // Return the 'invoice_no' field
                }, $data['invoice_data']));

                if($data['is_credit'] == 'Yes'){
                    $credits = $data['credit_data'];
                    $invoiceData = $data['invoice_data'];
                    foreach ($invoiceData as $invoice) {
                        $remainingAmount = $invoice['amount'];
                        foreach ($credits as &$creditEntry) {
                            if ($remainingAmount <= 0) break;

                            $usableAmount = min($remainingAmount, $creditEntry['unused_amount']);
                            if ($usableAmount > 0) {
                                // 1. Update invoice based on type
                                $invoiceType = $invoice['invoice_type'] ?? 'purchase';

                                if ($invoiceType === 'purchase') {
                                    $InvoiceDetail = PurchaseOrderReceives::find($invoice['id']);
                                    $updateData['paid_amount'] = $InvoiceDetail->paid_amount + $usableAmount;
                                    $updateData['pending_amount'] = $InvoiceDetail->pending_amount - $usableAmount;
                                    if ($updateData['pending_amount'] <= 0) {
                                        $updateData['status'] = 'Paid';
                                    } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                                        $updateData['status'] = 'Partially Paid';
                                    } else {
                                        $updateData['status'] = 'Unpaid';
                                    }
                                    $InvoiceDetail->update($updateData);
                                } else if ($invoiceType === 'sales') {
                                    $SalesInvoiceDetail = Invoice::find($invoice['id']);
                                    $updateData['paid_amount'] = $SalesInvoiceDetail->paid_amount + $usableAmount;
                                    $updateData['pending_amount'] = $SalesInvoiceDetail->pending_amount - $usableAmount;
                                    if ($updateData['pending_amount'] <= 0) {
                                        $updateData['status'] = 'Paid';
                                    } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                                        $updateData['status'] = 'Partially Paid';
                                    } else {
                                        $updateData['status'] = 'Unpaid';
                                    }
                                    $SalesInvoiceDetail->update($updateData);
                                }
                                // 2. Create credit details record
                                $creditDetail = [
                                    'purchase_order_receive_id' => $invoice['id'],
                                    'company_credit_id' => $creditEntry['id'],
                                    'amount' => $usableAmount,
                                    'date' => date('Y-m-d'),
                                    'created_by' => auth()->id(),
                                    'updated_by' => auth()->id(),
                                ];
                                CompanyCreditDetails::create($creditDetail);
                                // 3. Deduct from credit's unused amount
                                $creditEntry['unused_amount'] -= $usableAmount;
                                CompanyCredit::where('id', $creditEntry['id'])->update([
                                    'unused_amount' => $creditEntry['unused_amount']
                                ]);
                                $remainingAmount -= $usableAmount;
                            }
                            $customerTransaction = PurchaseTransaction::where(['entity_id' => $creditEntry['payment_paid_id'], 'entity_type' => 'payment_paid'])->first();
                            if ($customerTransaction) {
                                $existingNote = trim($customerTransaction->note ?? '');
                                $updatedNote = $existingNote . ' Invoice No: ' . $invoice_nos;
                                $customerTransaction->update(['note' => $updatedNote]);
                            }
                        }
                    }

                } else {

                    $data['created_by'] = $data['updated_by'] = auth()->id();
                    $paidPayment = PaymentPaid::create($data);
                    if($paidPayment){
                        foreach($paidPayment->invoice_data as $invoice){
                            $invoiceType = $invoice['invoice_type'] ?? 'purchase';

                            if ($invoiceType === 'purchase') {
                                $InvoiceDetail = PurchaseOrderReceives::find($invoice['id']);
                                $updateData['paid_amount'] = $InvoiceDetail->paid_amount + $invoice['amount'];
                                $updateData['pending_amount'] = $InvoiceDetail->pending_amount - $invoice['amount'];
                                if ($updateData['pending_amount'] <= 0) {
                                    $updateData['status'] = 'Paid';
                                } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                                    $updateData['status'] = 'Partially Paid';
                                } else {
                                    $updateData['status'] = 'Unpaid';
                                }
                                $InvoiceDetail->update($updateData);
                            } else if ($invoiceType === 'sales') {
                                $SalesInvoiceDetail = Invoice::find($invoice['id']);
                                $updateData['paid_amount'] = $SalesInvoiceDetail->paid_amount + $invoice['amount'];
                                $updateData['pending_amount'] = $SalesInvoiceDetail->pending_amount - $invoice['amount'];
                                if ($updateData['pending_amount'] <= 0) {
                                    $updateData['status'] = 'Paid';
                                } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                                    $updateData['status'] = 'Partially Paid';
                                } else {
                                    $updateData['status'] = 'Unpaid';
                                }
                                $SalesInvoiceDetail->update($updateData);
                            }
                        }
                    }

                    $data['entity_id'] = $paidPayment->id;
                    $data['entity_type'] = 'payment_paid';

                    if($data['payment_type'] == 'check'){
                        $data['payment_type'] = 'dr';
                        $data['note'] = 'Cheque No:' .$data['check_number'] .' '.'Invoice No:' .$invoice_nos;
                        $bankTransaction = BankTransaction::create($data);
                    } else if($data['payment_type'] == 'NEFT'){
                        $data['payment_type'] = 'dr';
                        $data['note'] = 'NEFT'. ' ' .'Invoice No:' .$invoice_nos;
                        $bankTransaction = BankTransaction::create($data);
                    } else if($data['payment_type'] == 'cash'){
                        $data['payment_type'] = 'dr';
                        $data['note'] = $data['note'].' '.'Invoice No:' .$invoice_nos;
                    }

                    if($data['advance_amount'] > 0){
                        $data['payment_paid_id'] = $paidPayment->id;
                        $data['unused_amount'] =  $data['amount'] = $data['advance_amount'];
                        CompanyCredit::create($data);
                    }

                    $data['amount'] = $data['settled_amount'] + $data['advance_amount'];
                    PurchaseTransaction::create($data);
                }

                DB::commit();
                return Redirect::to('/payment')->with('success', 'Payment Paid Successfully');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->withInput()->with('error', $e->getMessage());
        }
    }

    public function edit($id)
    {
        $payment = PaymentPaid::with('bankInfo')->find($id);
        $organization = Organization::select('id', 'name')->get();
        $companies = Company::select('name', 'id')->orderByRaw('name')->get();
        $paymentType = Config::get('constants.paymentTypes');
        $bankinfo = BankInfo::select('id', DB::raw("CONCAT(bank_name, ' - ', account_number) AS name"), 'organization_id')->get();

        // Get invoices that are either paid in this payment or still have pending amounts
        $invoices = PurchaseOrderReceives::with('purchaseOrder')
            ->where(function($query) use ($payment) {
                // Include invoices that are not fully paid
                $query->where('status', '!=', 'Paid')
                    ->where('total_amount', '>', 0)
                    ->whereHas('purchaseOrder', function ($subQuery) use ($payment) {
                        $subQuery->where('type', 'invoice')
                            ->where('organization_id', $payment->organization_id)
                            ->where('company_id', $payment->company_id);
                    });
            })
            ->orWhereIn('id', collect($payment->invoice_data)->pluck('id')) // Include invoices paid in this payment
            ->orderBy('customer_invoice_date', 'asc')
            ->get();

        // Get the original pending amount for each invoice by adding back the amount paid in this payment
        foreach ($invoices as $invoice) {
            foreach ($payment->invoice_data as $paidInvoice) {
                if ($invoice->id == $paidInvoice['id']) {
                    $invoice->original_pending_amount = $invoice->pending_amount + $paidInvoice['amount'];
                    $invoice->paid_in_this_payment = $paidInvoice['amount'];
                    break;
                }
            }
            if (!isset($invoice->original_pending_amount)) {
                $invoice->original_pending_amount = $invoice->pending_amount;
                $invoice->paid_in_this_payment = 0;
            }
        }

        // Get all credits for this company
        $credit = CompanyCredit::with('paymentpaid.bankInfo')
            ->where('company_id', $payment->company_id)
            ->where('organization_id', $payment->organization_id)
            ->where('unused_amount', '>', 0)
            ->get();

        // Add the payment's own credit if it exists
        if ($payment->advance_amount > 0) {
            $ownCredit = CompanyCredit::with('paymentpaid.bankInfo')
                ->where('payment_paid_id', $payment->id)
                ->first();
            if ($ownCredit) {
                $credit->push($ownCredit);
            }
        }
        return Inertia::render('Payment/Edit', compact('payment', 'paymentType', 'organization', 'bankinfo', 'companies', 'invoices', 'credit'));
    }

    public function update(PaymentPayRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = (array) $request->DTO();
            if ($data['is_credit'] == 'Yes') {
                $credits = $data['credit_data'];
                $invoiceData = $data['invoice_data'];
                foreach ($invoiceData as $invoice) {
                    $remainingAmount = $invoice['amount'];
                    foreach ($credits as &$creditEntry) {
                        if ($remainingAmount <= 0) break;

                        $usableAmount = min($remainingAmount, $creditEntry['unused_amount']);

                        if ($usableAmount > 0) {
                            // 1. Update invoice
                            $InvoiceDetail = PurchaseOrderReceives::find($invoice['id']);
                            $updateData['paid_amount'] = $InvoiceDetail->paid_amount + $usableAmount;
                            $updateData['pending_amount'] = $InvoiceDetail->pending_amount - $usableAmount;
                            if ($updateData['pending_amount'] <= 0) {
                                $updateData['status'] = 'Paid';
                            } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                                $updateData['status'] = 'Partially Paid';
                            } else {
                                $updateData['status'] = 'Unpaid';
                            }
                            $InvoiceDetail->update($updateData);

                            // 2. Create credit details record
                            $creditDetail = [
                                'purchase_order_receive_id' => $invoice['id'],
                                'company_credit_id' => $creditEntry['id'],
                                'amount' => $usableAmount,
                                'date' => date('Y-m-d'),
                                'created_by' => auth()->id(),
                                'updated_by' => auth()->id(),
                            ];
                            CompanyCreditDetails::create($creditDetail);

                            // 3. Deduct from credit's unused amount
                            $creditEntry['unused_amount'] -= $usableAmount;
                            CompanyCredit::where('id', $creditEntry['id'])->update([
                                'unused_amount' => $creditEntry['unused_amount']
                            ]);
                            $remainingAmount -= $usableAmount;
                        }
                        $customerTransaction = PurchaseTransaction::where(['entity_id' => $creditEntry['payment_paid_id'], 'entity_type' => 'payment_paid'])->first();
                        if ($customerTransaction) {
                            $existingNote = trim($customerTransaction->note ?? '');
                            $updatedNote = $existingNote . ' Invoice No: ' . $invoice_nos;
                            $customerTransaction->update(['note' => $updatedNote]);
                        }
                    }
                }
            } else {

            }

            DB::commit();
            return Redirect::to('/payment')->with('success', 'Payment Updated Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $paidPayment = PaymentPaid::with('credit', 'credit.creditDetail')->find($id);

            foreach($paidPayment->invoice_data as $invoice){
                $InvoiceDetail = PurchaseOrderReceives::find($invoice['id']);
                $updateData['paid_amount'] = $InvoiceDetail->paid_amount - $invoice['amount'];
                $updateData['pending_amount'] = $InvoiceDetail->pending_amount + $invoice['amount'];
                if ($updateData['pending_amount'] <= 0) {
                    $updateData['status'] = 'Paid';
                } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                    $updateData['status'] = 'Partially Paid';
                } else {
                    $updateData['status'] = 'Unpaid';
                }
                $InvoiceDetail->update($updateData);
            }

            if($paidPayment->credit) {
                $credit = $paidPayment->credit;
                foreach ($credit->creditDetail as $detail) {
                    $invoice = PurchaseOrderReceives::find($detail->purchase_order_receive_id);
                    if ($invoice) {
                        $invoice->paid_amount -= $detail->amount;
                        $invoice->pending_amount += $detail->amount;
                        if ($invoice->paid_amount <= 0) {
                            $invoice->status = 'Unpaid';
                        } elseif ($invoice->paid_amount > 0 && $invoice->pending_amount > 0) {
                            $invoice->status = 'Partially Paid';
                        } else {
                            $invoice->status = 'Paid';
                        }
                        $invoice->save();
                    }
                    $detail->delete();
                }
                $credit->delete();
            }
            if($paidPayment->payment_type == 'check' || $paidPayment->payment_type== 'NEFT'){
                $bankTransaction = BankTransaction::where(['entity_id' => $paidPayment->id , 'entity_type' => 'payment_paid'])->delete();
            }
            $PurchaseTransaction = PurchaseTransaction::where(['entity_id' => $paidPayment->id , 'entity_type' => 'payment_paid'])->delete();
            $paidPayment->delete();
            DB::commit();
            return Redirect::back()->with('success','Transaction Deleted Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());
            return Redirect::back()->with('error', $e->getMessage());
        }
    }
}
