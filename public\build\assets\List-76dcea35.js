import{_ as v,b,a as k}from"./AdminLayout-aac65a75.js";import{_ as u}from"./CreateButton-7506df4f.js";import{_ as z}from"./SecondaryButton-12775633.js";import{D as C}from"./DangerButton-dc982a69.js";import{M}from"./Modal-b2e3ff36.js";import{_ as B}from"./Pagination-02bcc8c2.js";import{h as L,r as _,o,c as n,a,u as O,w as e,F as f,Z as N,b as t,g as r,f as c,i as $,e as g,t as x}from"./app-b320a640.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const j={class:"animate-top"},A={class:"sm:flex sm:items-center"},E=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Organization")],-1),V={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},D={class:"flex justify-end w-20"},I={key:0,class:"flex justify-end"},T={class:"mt-8 overflow-x-auto sm:rounded-lg"},F={class:"shadow sm:rounded-lg"},S={class:"w-full text-sm text-left rtl:text-right text-gray-500"},U=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"COMPANY NAME"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"EMAIL"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"ACTION")])],-1),H={key:0},P={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},Y={class:"px-4 py-2.5"},Z={class:"items-center px-4 py-2.5"},q={class:"flex items-center justify-start gap-4"},G=t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),J=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),K=t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),Q=["onClick"],R=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),W=t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),X=[R,W],tt={key:1},et=t("tr",{class:"bg-white"},[t("td",{colspan:"6",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),st=[et],ot={class:"p-6"},at=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),nt={class:"mt-6 flex justify-end"},pt={__name:"List",props:["data","search","permissions"],setup(s){const p=L({}),d=_(!1),h=_(null),y=i=>{h.value=i,d.value=!0},m=()=>{d.value=!1},w=()=>{p.delete(route("organization.destroy",{id:h.value}),{onSuccess:()=>m()})};return(i,it)=>(o(),n(f,null,[a(O(N),{title:"Organization"}),a(v,null,{default:e(()=>[t("div",j,[t("div",A,[E,t("div",V,[t("div",D,[a(u,{href:i.route("setting")},{default:e(()=>[r(" Back ")]),_:1},8,["href"])]),s.permissions.canCreateOrganization?(o(),n("div",I,[a(u,{href:i.route("organization.create")},{default:e(()=>[r(" Add Organization ")]),_:1},8,["href"])])):c("",!0)])]),t("div",T,[t("div",F,[t("table",S,[U,s.data.data&&s.data.data.length>0?(o(),n("tbody",H,[(o(!0),n(f,null,$(s.data.data,(l,lt)=>(o(),n("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:l.id},[t("td",P,x(l.name),1),t("td",Y,x(l.email),1),t("td",Z,[t("div",q,[a(b,{align:"right",width:"48"},{trigger:e(()=>[G]),content:e(()=>[s.permissions.canEditOrganization?(o(),g(k,{key:0,href:i.route("organization.edit",{id:l.id})},{svg:e(()=>[J]),text:e(()=>[K]),_:2},1032,["href"])):c("",!0),s.permissions.canDeleteOrganization?(o(),n("button",{key:1,type:"button",onClick:rt=>y(l.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},X,8,Q)):c("",!0)]),_:2},1024)])])]))),128))])):(o(),n("tbody",tt,st))])])])]),s.data.data&&s.data.data.length>0?(o(),g(B,{key:0,class:"mt-6",links:s.data.links},null,8,["links"])):c("",!0),a(M,{show:d.value,onClose:m},{default:e(()=>[t("div",ot,[at,t("div",nt,[a(z,{onClick:m},{default:e(()=>[r(" Cancel ")]),_:1}),a(C,{class:"ml-3",onClick:w},{default:e(()=>[r(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{pt as default};
