import{o as l,c as b,a as o,u as s,w as _,F as x,Z as V,b as a,d as B,n as m,e as d,f as i,k as T,v as w,g as S,T as $}from"./app-03250c83.js";import{_ as C,a as F}from"./AdminLayout-a6b1643c.js";import{_ as c}from"./InputError-564dc17f.js";import{_ as u}from"./InputLabel-28ecec2a.js";import{P as U}from"./PrimaryButton-e6f8c536.js";import{_ as N}from"./TextInput-374b3fdd.js";import{_ as p}from"./SearchableDropdownNew-aff78425.js";import{u as A}from"./index-1ecceea2.js";import"./_plugin-vue_export-helper-c27b6911.js";const I={class:"h-screen animate-top"},O={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},P=a("div",{class:"sm:flex sm:items-center"},[a("div",{class:"sm:flex-auto"},[a("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Internal Bank Transaction")])],-1),j=["onSubmit"],D={class:"border-b border-gray-900/10 pb-12"},E={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},M={class:"sm:col-span-3"},z={class:"relative mt-2"},Z={class:"sm:col-span-3"},q={class:"relative mt-2"},G={class:"sm:col-span-3"},H={class:"sm:col-span-3"},J={class:"sm:col-span-3"},K={class:"relative mt-2"},L={class:"flex mt-6 items-center justify-between"},Q={class:"ml-auto flex items-center justify-end gap-x-6"},R=a("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),W={key:0,class:"text-sm text-gray-600"},le={__name:"InterTransfer",props:["bankinfo","accounttype"],setup(f){const e=A("post","/saveinternalbanktransfer",{from_bank:"",from_bank_name:"",to_bank:"",to_bank_name:"",account_type:"",date:"",amount:""}),k=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()}),g=(r,t)=>{e.from_bank=r,e.from_bank_name=t,e.errors.from_bank=null},v=(r,t)=>{e.to_bank=r,e.to_bank_name=t,e.errors.to_bank=null},y=(r,t)=>{e.account_type=r,e.errors.to_bank=null},h=r=>{e.errors[r]=null};return(r,t)=>(l(),b(x,null,[o(s(V),{title:"Internal Bank Transaction"}),o(C,null,{default:_(()=>[a("div",I,[a("div",O,[P,a("form",{onSubmit:B(k,["prevent"]),class:""},[a("div",D,[a("div",E,[a("div",M,[o(u,{for:"from_bank",value:"From Bank"}),a("div",z,[o(p,{options:f.bankinfo,modelValue:s(e).from_bank,"onUpdate:modelValue":t[0]||(t[0]=n=>s(e).from_bank=n),onOnchange:g,class:m({"error rounded-md":s(e).errors.from_bank})},null,8,["options","modelValue","class"]),s(e).invalid("from_bank")?(l(),d(c,{key:0,class:"",message:s(e).errors.from_bank},null,8,["message"])):i("",!0)])]),a("div",Z,[o(u,{for:"to_bank",value:"To Bank"}),a("div",q,[o(p,{options:f.bankinfo,modelValue:s(e).to_bank,"onUpdate:modelValue":t[1]||(t[1]=n=>s(e).to_bank=n),onOnchange:v,class:m({"error rounded-md":s(e).errors.to_bank})},null,8,["options","modelValue","class"]),s(e).invalid("to_bank")?(l(),d(c,{key:0,class:"",message:s(e).errors.to_bank},null,8,["message"])):i("",!0)])]),a("div",G,[o(u,{for:"date",value:"Payment Date"}),T(a("input",{"onUpdate:modelValue":t[2]||(t[2]=n=>s(e).date=n),class:m(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":s(e).errors["data.date"]}]),type:"date"},null,2),[[w,s(e).date]]),s(e).invalid("date")?(l(),d(c,{key:0,class:"",message:s(e).errors.date},null,8,["message"])):i("",!0)]),a("div",H,[o(u,{for:"amount",value:"Amount"}),o(N,{id:"amount",type:"text",onChange:t[3]||(t[3]=n=>h("data.amount")),modelValue:s(e).amount,"onUpdate:modelValue":t[4]||(t[4]=n=>s(e).amount=n),class:m({"error rounded-md":s(e).errors["data.amount"]})},null,8,["modelValue","class"]),s(e).invalid("amount")?(l(),d(c,{key:0,class:"",message:s(e).errors.amount},null,8,["message"])):i("",!0)]),a("div",J,[o(u,{for:"account_type",value:"Account Type"}),a("div",K,[o(p,{options:f.accounttype,modelValue:s(e).account_type,"onUpdate:modelValue":t[5]||(t[5]=n=>s(e).account_type=n),onOnchange:y,class:m({"error rounded-md":s(e).errors.account_type})},null,8,["options","modelValue","class"]),s(e).invalid("account_type")?(l(),d(c,{key:0,class:"",message:s(e).errors.account_type},null,8,["message"])):i("",!0)])])])]),a("div",L,[a("div",Q,[o(F,{href:r.route("banktransaction.index")},{svg:_(()=>[R]),_:1},8,["href"]),o(U,{disabled:s(e).processing},{default:_(()=>[S("Save")]),_:1},8,["disabled"]),o($,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:_(()=>[s(e).recentlySuccessful?(l(),b("p",W,"Saved.")):i("",!0)]),_:1})])])],40,j)])])]),_:1})],64))}};export{le as default};
