import{o as n,c as v,a,u as s,w as c,F as V,Z as x,b as l,d as h,e as d,f as r,g as $,T as k}from"./app-b7a94f67.js";import{_ as C,a as q}from"./AdminLayout-0f1fdf67.js";import{_ as i}from"./InputError-86b88c86.js";import{_ as m}from"./InputLabel-11b5d690.js";import{P as U}from"./PrimaryButton-4ffecd1c.js";import{_ as u}from"./TextInput-fea73171.js";import{_ as S}from"./TextArea-500c5ac8.js";import{u as w}from"./index-5a4eda7d.js";import{_ as y}from"./SearchableDropdown-711fb977.js";import"./_plugin-vue_export-helper-c27b6911.js";const N={class:"animate-top h-screen"},O={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},T=l("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add Funnel",-1),B=["onSubmit"],F={class:"border-b border-gray-900/10 pb-12"},j={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},P={class:"sm:col-span-3"},A={class:"sm:col-span-3"},M={class:"sm:col-span-3"},E={class:"sm:col-span-3"},I={class:"sm:col-span-3"},L={class:"sm:col-span-3"},D={class:"sm:col-span-3"},Z={class:"sm:col-span-3"},z={class:"relative mt-2"},G={class:"sm:col-span-3"},H={class:"relative mt-2"},J={class:"sm:col-span-6"},K={class:"flex mt-6 items-center justify-between"},Q={class:"ml-auto flex items-center justify-end gap-x-6"},R=l("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),W={key:0,class:"text-sm text-gray-600"},me={__name:"Add",props:{inquiryType:{type:Object},months:{type:Object}},setup(_){const e=w("post","/funnel",{customer_name:"",dr_name:"",place:"",mobile_number:"",company:"",product:"",order_value:"",order_month:"",inquiry_type:"",close_date:"",status:""}),f=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()}),g=(p,t)=>{e.inquiry_type=p,e.errors.inquiry_type=null},b=(p,t)=>{e.order_month=p};return(p,t)=>(n(),v(V,null,[a(s(x),{title:"Add Funnel"}),a(C,null,{default:c(()=>[l("div",N,[l("div",O,[T,l("form",{onSubmit:h(f,["prevent"]),class:""},[l("div",F,[l("div",j,[l("div",P,[a(m,{for:"customer_name",value:"Customer Name"}),a(u,{id:"customer_name",type:"text",modelValue:s(e).customer_name,"onUpdate:modelValue":t[0]||(t[0]=o=>s(e).customer_name=o),onChange:t[1]||(t[1]=o=>s(e).validate("customer_name"))},null,8,["modelValue"]),s(e).invalid("customer_name")?(n(),d(i,{key:0,class:"",message:s(e).errors.customer_name},null,8,["message"])):r("",!0)]),l("div",A,[a(m,{for:"dr_name",value:"Doctor Name"}),a(u,{id:"dr_name",type:"text",modelValue:s(e).dr_name,"onUpdate:modelValue":t[2]||(t[2]=o=>s(e).dr_name=o)},null,8,["modelValue"]),s(e).invalid("dr_name")?(n(),d(i,{key:0,class:"",message:s(e).errors.dr_name},null,8,["message"])):r("",!0)]),l("div",M,[a(m,{for:"place",value:"Place/City"}),a(u,{id:"place",type:"text",modelValue:s(e).place,"onUpdate:modelValue":t[3]||(t[3]=o=>s(e).place=o),onChange:t[4]||(t[4]=o=>s(e).validate("place"))},null,8,["modelValue"]),s(e).invalid("place")?(n(),d(i,{key:0,class:"",message:s(e).errors.place},null,8,["message"])):r("",!0)]),l("div",E,[a(m,{for:"mobile_number",value:" Number"}),a(u,{id:"mobile_number",type:"text",numeric:!0,maxLength:"10",modelValue:s(e).mobile_number,"onUpdate:modelValue":t[5]||(t[5]=o=>s(e).mobile_number=o),onChange:t[6]||(t[6]=o=>s(e).validate("mobile_number"))},null,8,["modelValue"]),s(e).invalid("mobile_number")?(n(),d(i,{key:0,class:"",message:s(e).errors.mobile_number},null,8,["message"])):r("",!0)]),l("div",I,[a(m,{for:"company",value:"Company"}),a(u,{id:"company",type:"text",modelValue:s(e).company,"onUpdate:modelValue":t[7]||(t[7]=o=>s(e).company=o),onChange:t[8]||(t[8]=o=>s(e).validate("company"))},null,8,["modelValue"]),s(e).invalid("company")?(n(),d(i,{key:0,class:"",message:s(e).errors.company},null,8,["message"])):r("",!0)]),l("div",L,[a(m,{for:"product",value:"Products"}),a(u,{id:"product",type:"text",modelValue:s(e).product,"onUpdate:modelValue":t[9]||(t[9]=o=>s(e).product=o),onChange:t[10]||(t[10]=o=>s(e).validate("product"))},null,8,["modelValue"]),s(e).invalid("product")?(n(),d(i,{key:0,class:"",message:s(e).errors.product},null,8,["message"])):r("",!0)]),l("div",D,[a(m,{for:"order_value",value:"Order Value"}),a(u,{id:"order_value",type:"text",numeric:!0,modelValue:s(e).order_value,"onUpdate:modelValue":t[11]||(t[11]=o=>s(e).order_value=o),onChange:t[12]||(t[12]=o=>s(e).validate("order_value"))},null,8,["modelValue"]),s(e).invalid("order_value")?(n(),d(i,{key:0,class:"",message:s(e).errors.order_value},null,8,["message"])):r("",!0)]),l("div",Z,[a(m,{for:"customer_id",value:"Estimated Month Of Order"}),l("div",z,[a(y,{options:_.months,modelValue:s(e).order_month,"onUpdate:modelValue":t[13]||(t[13]=o=>s(e).order_month=o),onOnchange:b},null,8,["options","modelValue"])])]),l("div",G,[a(m,{for:"customer_id",value:"Inquiry Type"}),l("div",H,[a(y,{options:_.inquiryType,modelValue:s(e).inquiry_type,"onUpdate:modelValue":t[14]||(t[14]=o=>s(e).inquiry_type=o),onOnchange:g},null,8,["options","modelValue"]),s(e).invalid("inquiry_type")?(n(),d(i,{key:0,class:"",message:s(e).errors.inquiry_type},null,8,["message"])):r("",!0)])]),l("div",J,[a(m,{for:"status",value:"Latest Status"}),a(S,{id:"status",type:"text",rows:3,modelValue:s(e).status,"onUpdate:modelValue":t[15]||(t[15]=o=>s(e).status=o),onChange:t[16]||(t[16]=o=>s(e).validate("status"))},null,8,["modelValue"]),s(e).invalid("status")?(n(),d(i,{key:0,class:"",message:s(e).errors.status},null,8,["message"])):r("",!0)])])]),l("div",K,[l("div",Q,[a(q,{href:p.route("funnel.index")},{svg:c(()=>[R]),_:1},8,["href"]),a(U,{disabled:s(e).processing},{default:c(()=>[$("Save")]),_:1},8,["disabled"]),a(k,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:c(()=>[s(e).recentlySuccessful?(n(),v("p",W,"Saved.")):r("",!0)]),_:1})])])],40,B)])])]),_:1})],64))}};export{me as default};
