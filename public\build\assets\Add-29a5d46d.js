import{r as U,K as lt,j as F,o as d,c,a as r,u as l,w as k,F as M,Z as at,b as s,t as v,k as nt,v as rt,d as it,n as m,f as p,i as dt,g as L,e as ct,s as ut,x as mt}from"./app-97275a91.js";import{_ as pt,a as _t}from"./AdminLayout-595ad5a7.js";import{_ as yt}from"./InputError-b3250228.js";import{_ as g}from"./InputLabel-eb73087c.js";import{P as Q}from"./PrimaryButton-46ac4375.js";import{_ as f}from"./TextInput-11c46564.js";import{_ as N}from"./TextArea-5e21e606.js";import{_ as T}from"./SearchableDropdown-9d1b12d3.js";import{_ as gt}from"./MultipleFileUpload-368d3540.js";import{_ as vt}from"./FileViewer-01b17a23.js";import{u as xt}from"./index-05d29b1c.js";import{_ as ft}from"./_plugin-vue_export-helper-c27b6911.js";const u=x=>(ut("data-v-d5347c74"),x=x(),mt(),x),ht={class:"animate-top"},wt={class:"sm:flex sm:items-center"},bt=u(()=>s("div",{class:"sm:flex-auto"},[s("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create New Quotation")],-1)),Vt={class:"w-auto"},St={class:"flex space-x-2 items-center"},It=u(()=>s("span",{class:"text-sm font-semibold text-gray-900 leading-6"},"Quotation Number:",-1)),Tt={class:"text-sm font-semibold text-gray-900 leading-6"},Ct={class:"flex space-x-2 items-center"},$t=u(()=>s("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Date :",-1)),Gt=["onSubmit"],Pt={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Ut={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Ft={class:"sm:col-span-4"},kt={class:"relative mt-2"},Nt={class:"sm:col-span-4"},At={class:"relative mt-2"},qt={class:"sm:col-span-4"},Dt={class:"relative mt-2"},jt={key:0,class:"sm:col-span-3"},Ot={key:1,class:"sm:col-span-2"},zt={class:"relative mt-2"},Bt={key:2,class:"sm:col-span-2"},Mt={class:"relative mt-2"},Lt={key:3,class:"sm:col-span-2"},Qt={key:4,class:"sm:col-span-3"},Et={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Yt={class:"overflow-x-auto w-full"},Ht={class:"overflow-x-auto divide-y divide-gray-300 w-full",style:{"margin-bottom":"160px"}},Kt=u(()=>s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Model",-1)),Rt=u(()=>s("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Image",-1)),Wt=u(()=>s("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"}," Description",-1)),Zt=u(()=>s("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),Jt=u(()=>s("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"}," Qty",-1)),Xt=u(()=>s("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹) ",-1)),te=u(()=>s("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),ee={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},se={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},oe={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},le={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ae={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ne=u(()=>s("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),re=u(()=>s("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),ie=u(()=>s("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),de={class:"divide-y divide-gray-300 bg-white"},ce={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-80"},ue={class:"relative mt-2"},me={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900"},pe={class:"w-48 h-24"},_e={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-96"},ye={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ge={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ve={key:0,class:"text-red-500 text-xs absolute"},xe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},fe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},he={class:"text-sm text-gray-900 leading-6 mt-2 py-1.5"},we={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},be={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ve={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Se={key:3,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ie={key:4,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Te={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ce={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},$e={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-36"},Ge={class:"flex space-x-2"},Pe={class:"text-sm text-gray-900 leading-6 py-1.5"},Ue=["onClick"],Fe=u(()=>s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),ke=[Fe],Ne={class:"flex items-center justify-between"},Ae={class:"ml-auto flex items-center justify-end gap-x-6"},qe={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},De={class:"items-center justify-between"},je={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Oe={class:"inline-flex items-center justify-end w-full space-x-3"},ze=u(()=>s("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),Be={class:"text-base font-semibold text-gray-900"},Me={class:"inline-flex items-center justify-end w-full space-x-3"},Le=u(()=>s("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),Qe={class:"w-40"},Ee={class:"inline-flex items-center justify-end w-full space-x-3"},Ye=u(()=>s("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),He={class:"text-base font-semibold text-gray-900 w-32"},Ke={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},Re=u(()=>s("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),We={class:"text-base font-semibold text-gray-900"},Ze={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},Je=u(()=>s("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),Xe={class:"text-base font-semibold text-gray-900"},ts={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},es=u(()=>s("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),ss={class:"text-base font-semibold text-gray-900"},os={class:"inline-flex items-center justify-end w-full space-x-3"},ls=u(()=>s("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),as={class:"text-base font-semibold text-gray-900"},ns={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},rs={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},is={class:"sm:col-span-10"},ds={class:"flex space-x-4"},cs={class:"w-full"},us=u(()=>s("div",{class:"w-full"},null,-1)),ms={class:"w-full"},ps={class:"relative mt-2"},_s={class:"sm:col-span-10"},ys={class:"flex space-x-4"},gs={class:"w-full"},vs={class:"w-full"},xs={class:"w-full"},fs={class:"sm:col-span-10"},hs={class:"flex space-x-4"},ws={class:"w-full"},bs={class:"w-full"},Vs={class:"flex mt-6 items-center justify-between"},Ss={class:"ml-auto flex items-center justify-end gap-x-6"},Is=u(()=>s("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),Ts={__name:"Add",props:["customers","gst_types","types","salesuser","products","quotation_number","organization","category","terms","filepath"],setup(x){const V=x,_=U(null),b=U("Yes"),A=U(),E=lt().props.filepath.view,y=U([{product_id:"",description:"",qty:"",price:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",discount:"",discount_amount:"",total_amount:"",mrp:"",image:""}]);U([{product_id:"",description:"",qty:"",price:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",mrp:""}]);const Y=(n,o)=>{if(e.customer_id=n,e.errors.customer_id=null,n=="new_customer")b.value="No";else{b.value="Yes";const t=V.customers.find(i=>i.id===n);t&&(_.value=t.gst_type)}},H=(n,o)=>{e.sales_user_id=n,e.errors.sales_user_id=null},K=(n,o,t,i)=>{const a=V.products.find(h=>h.id===n);a&&(y.value[t].product_id=a.id,y.value[t].price=parseFloat(a.price).toFixed(2),y.value[t].description=a.description,y.value[t].mrp=a!=null&&a.serial_numbers[0]?parseFloat(a.serial_numbers[0].mrp).toFixed(2):"-",y.value[t].gst=parseFloat(a.gst).toFixed(2),y.value[t].sgst=parseFloat(a.gst/2).toFixed(2),y.value[t].image=a.image,y.value[t].discount="0.00",e.errors[`selectedProductItem.${t}.product_id`]=null,e.errors[`selectedProductItem.${t}.price`]=null,S(i))},e=xt("post","/quotation",{note:"",date:new Date().toISOString().slice(0,10),selectedProductItem:[],is_customer:b.value,customer_name:"",address:"",city:"",gst_type:"",type:"",customer_id:"",sales_user_id:"",total_amount:"",quotation_number:"",document:"",organization_id:"",category:"",cgst:"",sgst:"",igst:"",total_gst:"",sub_total:"",validity:V.terms.validity,delivery:V.terms.delivery,payment_terms:V.terms.payment_terms,warranty:V.terms.warranty,overall_discount:"",total_discount:""}),R=()=>{e.sub_total=j.value,e.total_discount=O.value,e.total_amount=D.value,e.is_customer=b.value,e.cgst=_.value=="CGST/SGST"?I.value/2:"0",e.sgst=_.value=="CGST/SGST"?I.value/2:"0",e.igst=_.value=="IGST"?I.value:"0",e.total_gst=I.value,e.quotation_number=A.value,e.selectedProductItem=y.value,e.submit({preserveScroll:!0,onSuccess:()=>e.reset()})},W=()=>{y.value.push({product_id:"",description:"",qty:"",price:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",total_gst_amount:"",total_amount:"",image:""})},Z=n=>{y.value.splice(n,1)},J=(n,o)=>{const t=parseFloat(n.price),i=parseFloat(n.discount)||0,a=_.value=="IGST"?parseFloat(n.gst):parseFloat(n.sgst*2),h=parseFloat(n.qty);let $=0,G=0;i>0?$=t*h:$=t*h*(1+a/100);const P=$*(i/100)||0,z=t*1*(a/100),q=(t*h-P)*(a/100);i>0?G=$-P+q:G=$-P;const B=t*h;return n.total_price=isNaN(B)?"":parseFloat(B).toFixed(2),n.gst_amount=isNaN(z)?"":parseFloat(z).toFixed(2),n.total_gst_amount=isNaN(q)?"":parseFloat(q).toFixed(2),n.discount_amount=isNaN(P)?"":parseFloat(P).toFixed(2),isNaN(G)?"":parseFloat(G).toFixed(2)},S=(n,o)=>{n.total_amount=J(n)},D=F(()=>{const n=Math.round(y.value.reduce((t,i)=>t+(i.total_amount?parseFloat(i.total_amount):0),0)),o=e.overall_discount?parseFloat(e.overall_discount):0;return n-o}),I=F(()=>y.value.reduce((n,o)=>n+(o.total_gst_amount?parseFloat(o.total_gst_amount):0),0)),j=F(()=>y.value.reduce((n,o)=>n+(o.total_price?parseFloat(o.total_price):0),0)),O=F(()=>{const n=y.value.reduce((t,i)=>t+(i.discount_amount?parseFloat(i.discount_amount):0),0),o=e.overall_discount?parseFloat(e.overall_discount):0;return n+o}),w=n=>{e.errors[n]=null},X=n=>{e.document=n},C=n=>{let o=n.toFixed(2).toString(),[t,i]=o.split("."),a=t.substring(t.length-3),h=t.substring(0,t.length-3);return h!==""&&(a=","+a),`${h.replace(/\B(?=(\d{2})+(?!\d))/g,",")+a}.${i}`},tt=(n,o)=>{e.category=n,e.errors.category=null},et=(n,o)=>{e.gst_type=n,_.value=n,e.errors.category=null},st=(n,o)=>{e.type=n,e.errors.category=null},ot=(n,o)=>{e.organization_id=n,e.errors.organization_id=null,A.value=V.quotation_number[n]};return(n,o)=>(d(),c(M,null,[r(l(at),{title:"Quotation"}),r(pt,null,{default:k(()=>[s("div",ht,[s("div",wt,[bt,s("div",Vt,[s("div",St,[It,s("span",Tt,v(A.value),1)]),s("div",Ct,[$t,nt(s("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":o[0]||(o[0]=t=>l(e).date=t),onChange:o[1]||(o[1]=t=>l(e).validate("date"))},null,544),[[rt,l(e).date]])])])]),s("form",{onSubmit:it(R,["prevent"]),class:""},[s("div",Pt,[s("div",Ut,[s("div",Ft,[r(g,{for:"company_name",value:"Organization"}),s("div",kt,[r(T,{options:x.organization,modelValue:l(e).organization_id,"onUpdate:modelValue":o[2]||(o[2]=t=>l(e).organization_id=t),onOnchange:ot,class:m({"error rounded-md":l(e).errors.organization_id})},null,8,["options","modelValue","class"])])]),s("div",Nt,[r(g,{for:"customer_id",value:"Customer Name"}),s("div",At,[r(T,{options:x.customers,modelValue:l(e).customer_id,"onUpdate:modelValue":o[3]||(o[3]=t=>l(e).customer_id=t),onOnchange:Y,class:m({"error rounded-md":l(e).errors.customer_id})},null,8,["options","modelValue","class"])])]),s("div",qt,[r(g,{for:"company_name",value:"Category"}),s("div",Dt,[r(T,{options:x.category,modelValue:l(e).category,"onUpdate:modelValue":o[4]||(o[4]=t=>l(e).category=t),onOnchange:tt,class:m({"error rounded-md":l(e).errors.category})},null,8,["options","modelValue","class"])])]),b.value=="No"?(d(),c("div",jt,[r(g,{for:"customer_name",value:"Customer Name"}),r(f,{id:"customer_name",type:"text",modelValue:l(e).customer_name,"onUpdate:modelValue":o[5]||(o[5]=t=>l(e).customer_name=t),class:m({"error rounded-md":l(e).errors.customer_name}),onChange:o[6]||(o[6]=t=>l(e).validate("customer_name"))},null,8,["modelValue","class"])])):p("",!0),b.value=="No"?(d(),c("div",Ot,[r(g,{for:"type",value:"GST Type"}),s("div",zt,[r(T,{options:x.gst_types,modelValue:l(e).gst_type,"onUpdate:modelValue":o[7]||(o[7]=t=>l(e).gst_type=t),onOnchange:et,class:m({"error rounded-md":l(e).errors.gst_type})},null,8,["options","modelValue","class"])])])):p("",!0),b.value=="No"?(d(),c("div",Bt,[r(g,{for:"type",value:"Occupation Type"}),s("div",Mt,[r(T,{options:x.types,modelValue:l(e).type,"onUpdate:modelValue":o[8]||(o[8]=t=>l(e).type=t),onOnchange:st,class:m({"error rounded-md":l(e).errors.type})},null,8,["options","modelValue","class"])])])):p("",!0),b.value=="No"?(d(),c("div",Lt,[r(g,{for:"city",value:"City"}),r(f,{id:"city",type:"text",modelValue:l(e).city,"onUpdate:modelValue":o[9]||(o[9]=t=>l(e).city=t),class:m({"error rounded-md":l(e).errors.city}),onChange:o[10]||(o[10]=t=>l(e).validate("city"))},null,8,["modelValue","class"])])):p("",!0),b.value=="No"?(d(),c("div",Qt,[r(g,{for:"address",value:"Address"}),r(N,{id:"address",type:"text",modelValue:l(e).address,"onUpdate:modelValue":o[11]||(o[11]=t=>l(e).address=t),rows:2,class:m({"error rounded-md":l(e).errors.address}),onChange:o[12]||(o[12]=t=>l(e).validate("address"))},null,8,["modelValue","class"])])):p("",!0)])]),s("div",Et,[s("div",Yt,[s("table",Ht,[s("thead",null,[s("tr",null,[Kt,Rt,Wt,Zt,Jt,Xt,te,_.value=="IGST"?(d(),c("th",ee,"IGST (%)")):p("",!0),_.value=="IGST"?(d(),c("th",se,"IGST (₹)")):p("",!0),_.value=="CGST/SGST"?(d(),c("th",oe,"CGST (%)")):p("",!0),_.value=="CGST/SGST"?(d(),c("th",le,"SGST (%)")):p("",!0),_.value=="CGST/SGST"?(d(),c("th",ae,"Total GST (₹)")):p("",!0),ne,re,ie])]),s("tbody",de,[(d(!0),c(M,null,dt(y.value,(t,i)=>(d(),c("tr",{key:i},[s("td",ce,[s("div",ue,[r(T,{options:x.products,modelValue:t.product_id,"onUpdate:modelValue":a=>t.product_id=a,onOnchange:(a,h)=>K(a,h,i,t),onChange:o[13]||(o[13]=a=>l(e).validate("product_id")),class:m({"error rounded-md":l(e).errors[`selectedProductItem.${i}.product_id`]})},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class"])])]),s("td",me,[s("div",pe,[r(vt,{fileUrl:l(E)+t.image},null,8,["fileUrl"])])]),s("td",_e,[r(N,{id:"description",type:"text",modelValue:t.description,"onUpdate:modelValue":a=>t.description=a,autocomplete:"description",rows:2,onChange:a=>t.validate("description"),class:m({"error rounded-md":l(e).errors[`selectedProductItem.${i}.description`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),s("td",ye,v(t.mrp??"-"),1),s("td",ge,[r(f,{id:"qty",type:"text",numeric:!0,modelValue:t.qty,"onUpdate:modelValue":a=>t.qty=a,autocomplete:"qty",onInput:a=>S(t,i),onChange:a=>w("selectedProductItem."+i+".qty"),class:m({error:l(e).errors[`selectedProductItem.${i}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"]),l(e).errors[`selectedProductItem.${i}.qty1`]?(d(),c("p",ve,v(l(e).errors[`selectedProductItem.${i}.qty1`]),1)):p("",!0)]),s("td",xe,[r(f,{id:"price",type:"text",modelValue:t.price,"onUpdate:modelValue":a=>t.price=a,autocomplete:"price",onInput:a=>S(t,i),onChange:a=>w("selectedProductItem."+i+".price"),class:m({error:l(e).errors[`selectedProductItem.${i}.price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),s("td",fe,[s("div",he,v(t.total_price),1)]),_.value=="IGST"?(d(),c("td",we,[r(f,{id:"gst",type:"text",modelValue:t.gst,"onUpdate:modelValue":a=>t.gst=a,onInput:a=>S(t,i),onChange:a=>w("selectedProductItem."+i+".gst"),class:m({error:l(e).errors[`selectedProductItem.${i}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):p("",!0),_.value=="CGST/SGST"?(d(),c("td",be,[r(f,{id:"gst",type:"text",modelValue:t.sgst,"onUpdate:modelValue":a=>t.sgst=a,onInput:a=>S(t,i),onChange:a=>w("selectedProductItem."+i+".gst"),class:m({error:l(e).errors[`selectedProductItem.${i}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):p("",!0),_.value=="CGST/SGST"?(d(),c("td",Ve,[r(f,{id:"gst",type:"text",modelValue:t.sgst,"onUpdate:modelValue":a=>t.sgst=a,onInput:a=>S(t,i),onChange:a=>w("selectedProductItem."+i+".gst"),class:m({error:l(e).errors[`selectedProductItem.${i}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):p("",!0),_.value=="IGST"?(d(),c("td",Se,v(t.total_gst_amount),1)):p("",!0),_.value=="CGST/SGST"?(d(),c("td",Ie,v(t.total_gst_amount),1)):p("",!0),s("td",Te,[r(f,{id:"discount",type:"text",modelValue:t.discount,"onUpdate:modelValue":a=>t.discount=a,onInput:a=>S(t,i),onChange:a=>w("selectedProductItem."+i+".discount"),class:m({error:l(e).errors[`selectedProductItem.${i}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),s("td",Ce,v(t.discount_amount),1),s("td",$e,[s("div",Ge,[s("div",Pe,v(t.total_amount),1),i!=0?(d(),c("button",{key:0,type:"button",class:"mt-1",onClick:a=>Z(i)},ke,8,Ue)):p("",!0)])])]))),128))])])]),s("div",Ne,[s("div",Ae,[r(Q,{onClick:W,type:"button"},{default:k(()=>[L("Add Product")]),_:1})])])]),s("div",qe,[s("div",De,[s("div",je,[s("div",Oe,[ze,s("p",Be,v(C(j.value)),1)]),s("div",Me,[Le,s("div",Qe,[r(f,{id:"overall_discount",type:"text",modelValue:l(e).overall_discount,"onUpdate:modelValue":o[14]||(o[14]=t=>l(e).overall_discount=t)},null,8,["modelValue"])])]),s("div",Ee,[Ye,s("p",He,v(C(O.value)),1)]),_.value=="IGST"?(d(),c("div",Ke,[Re,s("p",We,v(C(I.value)),1)])):p("",!0),_.value=="CGST/SGST"?(d(),c("div",Ze,[Je,s("p",Xe,v(C(I.value/2)),1)])):p("",!0),_.value=="CGST/SGST"?(d(),c("div",ts,[es,s("p",ss,v(C(I.value/2)),1)])):p("",!0),s("div",os,[ls,s("p",as,v(C(D.value)),1)])])])]),s("div",ns,[s("div",rs,[s("div",is,[s("div",ds,[s("div",cs,[r(g,{for:"note",value:"Upload Documents"}),r(gt,{inputId:"document",inputName:"document",onFiles:X})]),us,s("div",ms,[r(g,{for:"company_name",value:"Sales Person"}),s("div",ps,[r(T,{options:x.salesuser,modelValue:l(e).sales_user_id,"onUpdate:modelValue":o[15]||(o[15]=t=>l(e).sales_user_id=t),onOnchange:H,class:m({"error rounded-md":l(e).errors.sales_user_id})},null,8,["options","modelValue","class"])])])])]),s("div",_s,[s("div",ys,[s("div",gs,[r(g,{for:"Validity",value:"Validity"}),r(f,{id:"price",type:"text",modelValue:l(e).validity,"onUpdate:modelValue":o[16]||(o[16]=t=>l(e).validity=t),onChange:o[17]||(o[17]=t=>w(l(e).errors.validity)),class:m({"error rounded-md":l(e).errors.validity})},null,8,["modelValue","class"])]),s("div",vs,[r(g,{for:"delivery",value:"Delivery"}),r(f,{id:"price",type:"text",modelValue:l(e).delivery,"onUpdate:modelValue":o[18]||(o[18]=t=>l(e).delivery=t),onChange:o[19]||(o[19]=t=>w(l(e).errors.delivery)),class:m({"error rounded-md":l(e).errors.delivery})},null,8,["modelValue","class"])]),s("div",xs,[r(g,{for:"warranty",value:"Warranty"}),r(f,{id:"price",type:"text",modelValue:l(e).warranty,"onUpdate:modelValue":o[20]||(o[20]=t=>l(e).warranty=t),onChange:o[21]||(o[21]=t=>w(l(e).errors.warranty)),class:m({"error rounded-md":l(e).errors.warranty})},null,8,["modelValue","class"])])])]),s("div",fs,[s("div",hs,[s("div",ws,[r(g,{for:"payment_terms",value:"Payment Terms"}),r(N,{id:"price",type:"text",rows:4,modelValue:l(e).payment_terms,"onUpdate:modelValue":o[22]||(o[22]=t=>l(e).payment_terms=t),onChange:o[23]||(o[23]=t=>w(l(e).errors.payment_terms)),class:m({"error rounded-md":l(e).errors.payment_terms})},null,8,["modelValue","class"])]),s("div",bs,[r(g,{for:"note",value:"Note"}),r(N,{id:"note",type:"text",rows:4,modelValue:l(e).note,"onUpdate:modelValue":o[24]||(o[24]=t=>l(e).note=t),autocomplete:"note",onChange:o[25]||(o[25]=t=>l(e).validate("note"))},null,8,["modelValue"]),l(e).invalid("note")?(d(),ct(yt,{key:0,class:"",message:l(e).errors.note},null,8,["message"])):p("",!0)])])])])]),s("div",Vs,[s("div",Ss,[r(_t,{href:n.route("quotation.index")},{svg:k(()=>[Is]),_:1},8,["href"]),r(Q,{disabled:l(e).processing},{default:k(()=>[L("Submit")]),_:1},8,["disabled"])])])],40,Gt)])]),_:1})],64))}},Os=ft(Ts,[["__scopeId","data-v-d5347c74"]]);export{Os as default};
