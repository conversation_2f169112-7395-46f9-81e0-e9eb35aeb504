import{K as v,r as k,o as i,c,a as n,u as d,w as a,F as x,Z as O,b as t,k as U,v as R,N as F,g as m,f,t as l,i as C,e as p,n as j}from"./app-4ea19997.js";import{s as G}from"./sortAndSearch-931279ce.js";import{_ as H,b as K,a as S}from"./AdminLayout-5eccc000.js";import{_ as I}from"./CreateButton-19955a3e.js";import{_ as D}from"./SecondaryButton-79105c78.js";import{D as Y}from"./DangerButton-5e5c1802.js";import{M as Z}from"./Modal-65b3f5d9.js";import{_ as q}from"./SwitchButton-9a16768c.js";import{_ as J}from"./Pagination-e0ea25b7.js";import{_ as Q}from"./ArrowIcon-151eb820.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const W={class:"animate-top"},X={class:"flex justify-between items-center"},tt=t("div",{class:"items-start"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Products")],-1),et={class:"flex justify-end"},st={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},ot={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},lt=t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),at={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},it={key:0,class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},nt={class:"flex justify-end"},ct={class:"mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},dt={class:"inline-flex items-start space-x-6 justify-start w-full"},rt={class:"inline-flex flex-col space-y-1 items-start justify-start w-1/2"},mt={class:"inline-flex items-center justify-start w-full space-x-2"},ft=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Company Name:",-1),ut={class:"text-sm leading-1 text-gray-700"},_t={class:"inline-flex items-center justify-start w-full space-x-2"},ht=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Email:",-1),xt={class:"text-sm leading-6 text-gray-700"},pt={class:"inline-flex items-center justify-start w-full space-x-2"},gt=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1),yt={class:"text-sm leading-6 text-gray-700"},wt={class:"inline-flex flex-col space-y-1 items-start justify-start w-1/2"},bt={class:"inline-flex items-center justify-start w-full space-x-2"},vt=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"GST No:",-1),kt={class:"text-sm leading-6 text-gray-700"},Ct={class:"inline-flex items-center justify-start w-full space-x-2"},jt=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Drug Licence No:",-1),St={class:"text-sm leading-6 text-gray-700"},It={class:"inline-flex justify-start w-full space-x-2"},Nt=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Address:",-1),Bt={class:"text-sm leading-6 text-gray-700 truncate"},Lt={class:"mt-8 overflow-x-auto sm:rounded-lg"},Mt={class:"shadow sm:rounded-lg"},Pt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Tt={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Vt={class:"border-b-2"},$t=["onClick"],At={key:0},Et={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},zt={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},Ot={class:"px-4 py-2.5 min-w-32"},Ut={class:"px-4 py-2.5"},Rt={class:"px-4 py-2.5 min-w-24"},Ft={class:"flex flex-1 px-4 py-2.5"},Gt={class:"items-center px-4 py-2.5"},Ht={class:"items-center px-4 py-2.5"},Kt={class:"flex items-center justify-start gap-4"},Dt=t("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Yt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),Zt=t("span",{class:"text-sm text-gray-700 leading-5"},"Edit",-1),qt=["onClick"],Jt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),Qt=t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),Wt=[Jt,Qt],Xt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7-7m0 0L5 14m7-7v12"})],-1),te=t("span",{class:"text-sm text-gray-700 leading-5"},"Product Log History",-1),ee={key:1},se=t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),oe=[se],le={class:"p-6"},ae=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),ie={class:"mt-6 flex justify-end"},ye={__name:"List",props:["data","companyInfo","permissions"],setup(s){const N=v().props.companyInfo.id,u=k(!1),g=k(null);v().props.data.links.find(o=>o.active===!0);const{form:y,search:_,sort:B,fetchData:w,sortKey:L,sortDirection:M}=G("products.show",{id:N}),P=o=>{g.value=o,u.value=!0},h=()=>{u.value=!1},T=()=>{y.delete(route("products.destroy",{id:g.value}),{onSuccess:()=>h()})},V=(o,r)=>{y.post(route("products.activation",{id:r,status:o}),{})},$=[{field:"item_code",label:"PRODUCT CODE",sortable:!0},{field:"name",label:"PRODUCT NAME",sortable:!0},{field:"hsn_code",label:"HSN CODE",sortable:!0},{field:"price",label:"PRICE (₹)",sortable:!0},{field:"gst",label:"GST (%)",sortable:!0},{field:"category",label:"CATEGORY",sortable:!0},{field:"status",label:"STATUS",sortable:!1},{field:"action",label:"ACTION",sortable:!1}],A=o=>{switch(o){case"Service":return"bg-blue-100";case"Sales":return"bg-yellow-100";default:return"bg-gray-100"}},E=o=>{switch(o){case"Service":return"text-blue-600";case"Sales":return"text-yellow-600";default:return"text-gray-600"}};return(o,r)=>(i(),c(x,null,[n(d(O),{title:"Product List"}),n(H,null,{default:a(()=>[t("div",W,[t("div",X,[tt,t("div",et,[t("div",st,[t("div",ot,[lt,U(t("input",{id:"search-field","onUpdate:modelValue":r[0]||(r[0]=e=>F(_)?_.value=e:null),onInput:r[1]||(r[1]=(...e)=>d(w)&&d(w)(...e)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,544),[[R,d(_)]])])]),t("div",at,[n(I,{href:o.route("companies.index")},{default:a(()=>[m(" Back ")]),_:1},8,["href"])]),s.permissions.canCreateProducts?(i(),c("div",it,[t("div",nt,[n(I,{href:o.route("products.create",{id:s.companyInfo.id})},{default:a(()=>[m(" Add Product ")]),_:1},8,["href"])])])):f("",!0)])]),t("div",ct,[t("div",dt,[t("div",rt,[t("div",mt,[ft,t("p",ut,l(s.companyInfo.name??"-"),1)]),t("div",_t,[ht,t("p",xt,l(s.companyInfo.email??"-"),1)]),t("div",pt,[gt,t("p",yt,l(s.companyInfo.contact_no??"-"),1)])]),t("div",wt,[t("div",bt,[vt,t("p",kt,l(s.companyInfo.gst_no??"-"),1)]),t("div",Ct,[jt,t("p",St,l(s.companyInfo.drug_licence_no??"-"),1)]),t("div",It,[Nt,t("p",Bt,l(s.companyInfo.address??"-"),1)])])])]),t("div",Lt,[t("div",Mt,[t("table",Pt,[t("thead",Tt,[t("tr",Vt,[(i(),c(x,null,C($,(e,b)=>t("th",{key:b,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:z=>d(B)(e.field,e.sortable)},[m(l(e.label)+" ",1),e.sortable?(i(),p(Q,{key:0,isSorted:d(L)===e.field,direction:d(M)},null,8,["isSorted","direction"])):f("",!0)],8,$t)),64))])]),s.data.data&&s.data.data.length>0?(i(),c("tbody",At,[(i(!0),c(x,null,C(s.data.data,(e,b)=>(i(),c("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",Et,l(e.item_code??"-"),1),t("td",zt,l(e.name??"-"),1),t("td",Ot,l(e.hsn_code??"-"),1),t("td",Ut,l(e.price?Number(e.price).toFixed(2):"-"),1),t("td",Rt,l(e.gst?Number(e.gst).toFixed(2):"-"),1),t("td",Ft,[t("div",{class:j(["flex rounded-full px-4 py-1",A(e.category)])},[t("span",{class:j(["text-sm font-semibold",E(e.category)])},l(e.category??"-"),3)],2)]),t("td",Gt,[n(q,{switchValue:e.status,userId:e.id,onUpdateSwitchValue:V},null,8,["switchValue","userId"])]),t("td",Ht,[t("div",Kt,[n(K,{align:"right",width:"48"},{trigger:a(()=>[Dt]),content:a(()=>[s.permissions.canEditProducts?(i(),p(S,{key:0,href:o.route("products.edit",{id:e.id})},{svg:a(()=>[Yt]),text:a(()=>[Zt]),_:2},1032,["href"])):f("",!0),s.permissions.canDeleteProducts?(i(),c("button",{key:1,type:"button",onClick:z=>P(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Wt,8,qt)):f("",!0),n(S,{href:o.route("product.logs",{id:e.id})},{svg:a(()=>[Xt]),text:a(()=>[te]),_:2},1032,["href"])]),_:2},1024)])])]))),128))])):(i(),c("tbody",ee,oe))])])]),s.data.data&&s.data.data.length>0?(i(),p(J,{key:0,class:"mt-6",links:s.data.links},null,8,["links"])):f("",!0)]),n(Z,{show:u.value,onClose:h},{default:a(()=>[t("div",le,[ae,t("div",ie,[n(D,{onClick:h},{default:a(()=>[m(" Cancel ")]),_:1}),n(Y,{class:"ml-3",onClick:T},{default:a(()=>[m("Delete")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{ye as default};
