import{h as N,r as d,o as O,c as G,a as n,u as h,w as x,F as $,Z as B,b as e,g as E,k as w,v as b,n as y}from"./app-21e66fd5.js";import{_ as M}from"./AdminLayout-db62264f.js";import{_ as U}from"./CreateButton-ac594764.js";/* empty css                                                              */import{_ as j}from"./SimpleDropdown-8f3922e2.js";import{_ as m}from"./InputLabel-4a50badc.js";const A={class:"animate-top h-screen"},I={class:"flex justify-between items-center"},L=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"GST Sales Data")],-1),F={class:"flex mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},R={class:"flex ml-6"},q={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},X={class:"flex justify-between mb-2"},H={class:"flex"},P=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),Z={class:"inline-flex items-center space-x-4 justify-end w-full"},J=["src"],K={class:"sm:col-span-3"},Q={class:"grid grid-cols-1 sm:grid-cols-12 gap-x-6 gap-y-5 items-center"},W={class:"sm:col-span-4"},Y={class:"relative mt-2"},ee={class:"sm:col-span-4"},te={class:"sm:col-span-4"},ie={__name:"GstSalesData",props:["organization","organizationId","from_date","to_date"],setup(f){const v=f,u=N({}),o=d(v.organizationId),k=d(v.companyId);d("GST SALES DATA");const r=d(""),l=d(""),g=d(""),_=(t,a,s,p)=>{u.get(route("gst-sales-data",{search:t,organization_id:a,from_date:s,to_date:p}),{preserveState:!0})},S=(t,a)=>{o.value=t,_(g.value,o.value,k.value,r.value,l.value)},z=()=>{let t="";switch(o.value){case 1:t="MC";break;case 2:t="HC";break;case 3:t="NOX";break;default:t="All_Organizations";break}const a=`GST_Sales_Data_${t}`,s={organization_id:o.value||"",from_date:r.value||"",to_date:l.value||""},D=`/export-gst-sales-data?${new URLSearchParams(s).toString()}`;fetch(D,{method:"GET"}).then(i=>{if(!i.ok)throw new Error("Network response was not ok");return i.blob()}).then(i=>{const V=window.URL.createObjectURL(new Blob([i])),c=document.createElement("a");c.href=V,c.setAttribute("download",`${a}.xlsx`),document.body.appendChild(c),c.click(),document.body.removeChild(c)}).catch(i=>{console.error("Error exporting data:",i)})},C=()=>{_(g.value,o.value,r.value,l.value)},T=()=>{_(g.value,o.value,r.value,l.value)};return(t,a)=>(O(),G($,null,[n(h(B),{title:"GST Sales Data"}),n(M,null,{default:x(()=>[e("div",A,[e("div",I,[L,e("div",F,[e("div",R,[n(U,{href:t.route("reports")},{default:x(()=>[E(" Back ")]),_:1},8,["href"])])])]),e("div",q,[e("div",X,[e("div",H,[P,n(m,{for:"customer_id",value:"Filters"})]),e("div",Z,[e("button",{onClick:z},[e("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,J)])])]),e("div",K,[e("div",Q,[e("div",W,[n(m,{for:"customer_id",value:"Organization Name"}),e("div",Y,[n(j,{options:f.organization,modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=s=>o.value=s),onOnchange:S},null,8,["options","modelValue"])])]),e("div",ee,[n(m,{for:"date",value:"From Date"}),w(e("input",{"onUpdate:modelValue":a[1]||(a[1]=s=>r.value=s),class:y(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":h(u).errors.from_date}]),type:"date",onChange:C},null,34),[[b,r.value]])]),e("div",te,[n(m,{for:"date",value:"To Date"}),w(e("input",{"onUpdate:modelValue":a[2]||(a[2]=s=>l.value=s),class:y(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":h(u).errors.to_date}]),type:"date",onChange:T},null,34),[[b,l.value]])])])])])])]),_:1})],64))}};export{ie as default};
