import{o as l,c as _,a as o,u as t,w as d,F as b,Z as R,b as s,t as S,d as w,n as m,k as V,v as T,e as U,f as u,g as k,T as $,s as B,x as I}from"./app-16701445.js";import{_ as N,a as C}from"./AdminLayout-e15be38d.js";import{_ as D}from"./InputError-11376965.js";import{_ as c}from"./InputLabel-d69efee6.js";import{P as F}from"./PrimaryButton-eddb8b77.js";import{_ as v}from"./SearchableDropdown-c456ce8e.js";import{_ as P}from"./MultipleFileUpload-09565aaf.js";import{u as j}from"./index-10107770.js";import{_ as E}from"./_plugin-vue_export-helper-c27b6911.js";const f=r=>(B("data-v-7af5ce9d"),r=r(),I(),r),M={class:"h-screen animate-top"},O={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},z={class:"sm:flex sm:items-center"},Z=f(()=>s("div",{class:"sm:flex-auto"},[s("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Upload Report")],-1)),q={class:"text-sm font-semibold text-gray-900"},A=["onSubmit"],G={class:"border-b border-gray-900/10 pb-12"},H={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10"},J={class:"sm:col-span-3"},K={class:"relative mt-2"},L={class:"sm:col-span-3"},Q={class:"relative mt-2"},W={class:"sm:col-span-3"},X={class:"sm:col-span-4"},Y={class:"w-full"},ee={class:"flex mt-6 items-center justify-between"},se={class:"ml-auto flex items-center justify-end gap-x-6"},te=f(()=>s("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),oe={key:0,class:"text-sm text-gray-600"},re={__name:"TempUploadReport",props:["reporttype","serviceperson","serviceReport"],setup(r){const p=r,e=j("post","/save-upload-service-report",{service_report_id:p.serviceReport.id,customer_id:p.serviceReport.customers.id,date:"",document:"",type:"",service_engineer_id:""}),g=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()}),y=(i,a)=>{e.type=i,e.errors.type=null},h=(i,a)=>{e.service_engineer_id=i,e.errors.service_engineer_id=null},x=i=>{e.document=i};return(i,a)=>(l(),_(b,null,[o(t(R),{title:"Upload Report"}),o(N,null,{default:d(()=>[s("div",M,[s("div",O,[s("div",z,[Z,s("p",q,S(r.serviceReport.customers.customer_name),1)]),s("form",{onSubmit:w(g,["prevent"]),class:""},[s("div",G,[s("div",H,[s("div",J,[o(c,{for:"type",value:"Report Type"}),s("div",K,[o(v,{options:r.reporttype,modelValue:t(e).type,"onUpdate:modelValue":a[0]||(a[0]=n=>t(e).type=n),onOnchange:y,class:m({"error rounded-md":t(e).errors.type})},null,8,["options","modelValue","class"])])]),s("div",L,[o(c,{for:"service_engineer_id",value:"Service Engineer"}),s("div",Q,[o(v,{options:r.serviceperson,modelValue:t(e).service_engineer_id,"onUpdate:modelValue":a[1]||(a[1]=n=>t(e).service_engineer_id=n),onOnchange:h,class:m({"error rounded-md":t(e).errors.service_engineer_id})},null,8,["options","modelValue","class"])])]),s("div",W,[o(c,{for:"date",value:"Date"}),V(s("input",{"onUpdate:modelValue":a[2]||(a[2]=n=>t(e).date=n),class:m(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":t(e).errors.date}]),type:"date"},null,2),[[T,t(e).date]])]),s("div",X,[s("div",Y,[o(c,{for:"note",value:"Upload Report"}),o(P,{inputId:"document",inputName:"document",onFiles:x}),t(e).invalid("document")?(l(),U(D,{key:0,class:"",message:t(e).errors.document},null,8,["message"])):u("",!0)])])])]),s("div",ee,[s("div",se,[o(C,{href:i.route("service-reports.show",{id:r.serviceReport.customers.id})},{svg:d(()=>[te]),_:1},8,["href"]),o(F,{disabled:t(e).processing},{default:d(()=>[k("Save")]),_:1},8,["disabled"]),o($,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:d(()=>[t(e).recentlySuccessful?(l(),_("p",oe,"Saved.")):u("",!0)]),_:1})])])],40,A)])])]),_:1})],64))}},ue=E(re,[["__scopeId","data-v-7af5ce9d"]]);export{ue as default};
