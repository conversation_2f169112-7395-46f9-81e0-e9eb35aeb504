import{r as b,j as q,l as O,o as d,c,a as l,u as n,w as S,F as I,Z as ee,b as o,t as p,f as _,d as te,n as h,k as oe,v as ae,i as H,g as se,T as ne,s as le,x as ie}from"./app-b320a640.js";import{_ as re,a as de}from"./AdminLayout-aac65a75.js";import{_ as v}from"./InputLabel-946d937b.js";import{P as ce}from"./PrimaryButton-cb5bb104.js";import{_ as U}from"./TextInput-cb7ba6f7.js";import{_ as me}from"./TextArea-5264b61a.js";import{_ as ue}from"./RadioButton-2f4bb735.js";import{_ as $}from"./SearchableDropdown-4997ffb6.js";import{u as pe}from"./index-c4301439.js";/* empty css                                                                          */import{_ as _e}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as ve}from"./Checkbox-124714ed.js";const z=g=>(le("data-v-728a7de0"),g=g(),ie(),g),fe={class:"h-screen animate-top"},ye={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},he={class:"sm:flex sm:items-center"},ge=z(()=>o("div",{class:"sm:flex-auto"},[o("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Make Payment")],-1)),xe={class:"flex items-center justify-between"},be={key:0,class:"text-base font-semibold leading-6 text-gray-900"},we=["onSubmit"],ke={class:"border-b border-gray-900/10 pb-12"},Ve={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},Ce={class:"sm:col-span-3"},Ae={class:"relative mt-2"},Ne={class:"sm:col-span-3"},Fe={class:"relative mt-2"},Se={class:"sm:col-span-2"},Ue={class:"relative mt-2"},$e={key:0,class:"sm:col-span-3"},ze={class:"relative mt-2"},Pe={key:1,class:"sm:col-span-1"},Te={key:2,class:"sm:col-span-2"},Oe={key:3,class:"sm:col-span-2"},Ie={class:"mt-4 flex justify-start"},De={class:"text-base font-semibold"},Be={key:4,class:"sm:col-span-3"},Ee={key:5,class:"sm:col-span-3"},je={key:6,class:"sm:col-span-3"},Me={key:7,class:"sm:col-span-3"},Le={class:"relative mt-2"},Ye={class:"sm:col-span-6"},Re={class:"overflow-x-auto divide-y divide-gray-300 w-full"},qe=z(()=>o("div",{class:"w-full"},[o("thead",{class:"w-full"},[o("tr",{class:""},[o("th",{scope:"col",class:""}),o("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),o("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),o("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1)),He={style:{"overflow-y":"auto","max-height":"318px"}},Ze={class:"divide-y divide-gray-300 bg-white"},Ge={class:"whitespace-nowrap px-2 text-sm text-gray-900"},Je={class:"text-sm text-gray-900 leading-6 py-1.5"},Ke={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},Qe={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},We={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Xe={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},et={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},tt={key:0,class:"text-red-500 text-xs absolute"},ot={class:"whitespace-nowrap px-2 text-sm text-gray-900"},at={class:"sm:col-span-2"},st={class:"mt-4 flex justify-start"},nt={class:"text-base font-semibold"},lt={key:0,class:"text-red-500 text-xs absolute"},it={key:8,class:"sm:col-span-6"},rt={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},dt=z(()=>o("thead",null,[o("tr",null,[o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1)),ct={class:"divide-y divide-gray-300 bg-white"},mt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ut={class:"whitespace-nowrap py-3 text-sm text-gray-900"},pt={class:"flex flex-col"},_t={class:"text-sm text-gray-900"},vt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ft={class:"whitespace-nowrap py-3 text-sm text-gray-900"},yt={class:"flex mt-6 items-center justify-between"},ht={class:"ml-auto flex items-center justify-end gap-x-6"},gt=z(()=>o("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),xt={key:0,class:"text-sm text-gray-600"},bt={__name:"Add",props:["paymentType","bankinfo","organization","companies","invoices","credit"],setup(g){const w=g;b([]);const t=pe("post","/payment",{organization_id:"",company_id:"",payment_type:"",date:"",note:"",amount:"",discount_amount:0,round_off:0,check_number:"",org_bank_id:"",invoice:[],settled_amount:"",advance_amount:"",is_credit:"",credit_data:[],total_unused_amount:""}),A=b(""),Z=()=>{t.settled_amount=P.value,t.advance_amount=E.value,t.total_unused_amount=k.value,t.is_credit=u.value,t.invoice=f.value,t.credit_data=x.value,t.submit({preserveScroll:!0,onSuccess:()=>t.reset()})},G=(s,a)=>{A.value=a,t.payment_type=s,t.errors.payment_type=null,a==="Cash"?t.note="Cash":t.note==="Cash"&&(t.note="")},N=b([]),D=b([]),x=b([]),k=b(""),J=(s,a)=>{t.organization_id=s,t.errors.organization_id=null;const e=w.bankinfo.filter(i=>i.organization_id===s);D.value=e,t.company_id&&B(t.company_id,s);const r=w.credit.filter(i=>i.organization_id===s&&i.company_id===t.company_id);x.value=r,k.value=x.value.reduce((i,m)=>i+m.unused_amount,0)},K=(s,a)=>{t.company_id=s,B(s,t.organization_id);const e=w.credit.filter(r=>r.company_id===s&&r.organization_id===t.organization_id);x.value=e,k.value=x.value.reduce((r,i)=>r+i.unused_amount,0),t.errors.company_id=null},B=(s,a)=>{if(!s||!a){N.value=[];return}const e=w.companies.find(m=>m.id===s),r=e==null?void 0:e.party_id,i=w.invoices.filter(m=>{const y=m.organization_id===a;return m.invoice_type==="purchase"?y&&m.company_id===s:m.invoice_type==="sales"&&r?y&&m.party_id===r:!1});N.value=i},Q=(s,a)=>{t.org_bank_id=s,t.errors.org_bank_id=null},P=q(()=>{const s=f.value.reduce((a,e)=>a+(e.check&&e.amount?parseFloat(e.amount):0),0);return parseFloat(s.toFixed(2))}),E=q(()=>{const s=parseFloat(t.amount||0),a=parseFloat(t.round_off||0),e=P.value;return s>e?s-e-a:0}),V=s=>{let a=s.toFixed(2).toString(),[e,r]=a.split("."),i=e.substring(e.length-3),m=e.substring(0,e.length-3);return m!==""&&(i=","+i),`${m.replace(/\B(?=(\d{2})+(?!\d))/g,",")+i}.${r}`},j=s=>{const a=new Date(s),e={year:"numeric",month:"short",day:"numeric"};return a.toLocaleDateString("en-US",e)},W=(s,a)=>{const e=u.value==="Yes"?parseFloat(k.value||0):parseFloat(t.amount||0)+parseFloat(t.round_off||0);if(a===void 0&&(a=f.value.findIndex(y=>y.check===s.target.checked)),a===-1||a>=f.value.length)return;if(!f.value[a].check){f.value[a].amount=0;return}let r=e;f.value.forEach((y,C)=>{y.check&&C!==a&&(r-=parseFloat(y.amount||0))});const i=parseFloat(f.value[a].pending_amount||0),m=Math.min(i,r);f.value[a].amount=m.toFixed(2)},f=b([{id:"",date:"",invoice_no:"",total_amount:"",pending_amount:"",check:!1,amount:0}]),u=b("No"),X=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],T=()=>{f.value=N.value.map(s=>({id:s.id,date:s.date,invoice_no:s.invoice_no,total_amount:parseFloat(s.total_amount||0).toFixed(2),pending_amount:parseFloat(s.pending_amount||0).toFixed(2),invoice_type:s.invoice_type||"purchase",check:!1,amount:"0.00"}))};O(N,()=>{T()}),O(u,()=>{T()}),O(()=>t.amount,()=>{u.value==="No"&&T()});const F=s=>{t.errors[s]=null,t.errors.settled_amount=null};return(s,a)=>(d(),c(I,null,[l(n(ee),{title:"Payment"}),l(re,null,{default:S(()=>[o("div",fe,[o("div",ye,[o("div",he,[ge,o("div",xe,[x.value.length>0?(d(),c("div",be," Credits Available: ₹"+p(V(k.value)),1)):_("",!0)])]),o("form",{onSubmit:te(Z,["prevent"]),class:""},[o("div",ke,[o("div",Ve,[o("div",Ce,[l(v,{for:"payment_type",value:"Organization"}),o("div",Ae,[l($,{options:g.organization,modelValue:n(t).organization_id,"onUpdate:modelValue":a[0]||(a[0]=e=>n(t).organization_id=e),onOnchange:J,class:h({"error rounded-md":n(t).errors.organization_id})},null,8,["options","modelValue","class"])])]),o("div",Ne,[l(v,{for:"payment_type",value:"Company"}),o("div",Fe,[l($,{options:g.companies,modelValue:n(t).company_id,"onUpdate:modelValue":a[1]||(a[1]=e=>n(t).company_id=e),onOnchange:K,class:h({"error rounded-md":n(t).errors.company_id})},null,8,["options","modelValue","class"])])]),o("div",Se,[l(v,{for:"role_id",value:"Payment Through Credit ?"}),o("div",Ue,[l(ue,{modelValue:u.value,"onUpdate:modelValue":a[2]||(a[2]=e=>u.value=e),options:X},null,8,["modelValue"])])]),u.value=="No"?(d(),c("div",$e,[l(v,{for:"payment_type",value:"Payment Type"}),o("div",ze,[l($,{options:g.paymentType,modelValue:n(t).payment_type,"onUpdate:modelValue":a[3]||(a[3]=e=>n(t).payment_type=e),onOnchange:G,class:h({"error rounded-md":n(t).errors.payment_type})},null,8,["options","modelValue","class"])])])):_("",!0),u.value=="No"?(d(),c("div",Pe,[l(v,{for:"round_off",value:"Round Off"}),l(U,{type:"text",onChange:a[4]||(a[4]=e=>F("round_off")),modelValue:n(t).round_off,"onUpdate:modelValue":a[5]||(a[5]=e=>n(t).round_off=e),class:h({"error rounded-md":n(t).errors.round_off})},null,8,["modelValue","class"])])):_("",!0),u.value=="No"?(d(),c("div",Te,[l(v,{for:"amount",value:"Amount"}),l(U,{id:"amount",type:"text",onChange:a[6]||(a[6]=e=>F("amount")),modelValue:n(t).amount,"onUpdate:modelValue":a[7]||(a[7]=e=>n(t).amount=e),class:h({"error rounded-md":n(t).errors.amount})},null,8,["modelValue","class"])])):_("",!0),u.value=="No"?(d(),c("div",Oe,[l(v,{for:"advance",value:"Advance(Ref) Amount"}),o("div",Ie,[o("p",De,p(V(E.value)),1)])])):_("",!0),A.value=="Cheque"&&u.value=="No"?(d(),c("div",Be,[l(v,{for:"check_number",value:"Cheque Number"}),l(U,{id:"check_number",type:"text",modelValue:n(t).check_number,"onUpdate:modelValue":a[8]||(a[8]=e=>n(t).check_number=e),class:h({"error rounded-md":n(t).errors["data.check_number"]})},null,8,["modelValue","class"])])):_("",!0),u.value=="No"?(d(),c("div",Ee,[l(v,{for:"date",value:"Payment Date"}),oe(o("input",{"onUpdate:modelValue":a[9]||(a[9]=e=>n(t).date=e),class:h(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":n(t).errors.date}]),type:"date",onChange:a[10]||(a[10]=e=>F("date"))},null,34),[[ae,n(t).date]])])):_("",!0),A.value=="Cash"&&u.value=="No"?(d(),c("div",je)):_("",!0),A.value!="Cash"&&u.value=="No"?(d(),c("div",Me,[l(v,{for:"org_bank_id",value:"Our Bank"}),o("div",Le,[l($,{options:D.value,modelValue:n(t).org_bank_id,"onUpdate:modelValue":a[11]||(a[11]=e=>n(t).org_bank_id=e),onOnchange:Q,class:h({"error rounded-md":n(t).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):_("",!0),o("div",Ye,[o("table",Re,[qe,o("div",He,[o("tbody",Ze,[(d(!0),c(I,null,H(f.value,(e,r)=>(d(),c("tr",{key:r},[o("td",Ge,[o("div",Je,[l(ve,{name:"check",checked:e.check,"onUpdate:checked":i=>e.check=i,onChange:i=>W(i,r)},null,8,["checked","onUpdate:checked","onChange"])])]),o("td",Ke,[o("span",{class:h([e.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},p(e.invoice_type==="purchase"?"PURCHASE":"SALES"),3)]),o("td",Qe,p(e.invoice_no),1),o("td",We,p(e.total_amount),1),o("td",Xe,p(e.pending_amount),1),o("td",et,[l(U,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":i=>e.amount=i,onChange:i=>F("invoice."+r+".amount"),class:h({error:n(t).errors[`invoice.${r}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),n(t).errors[`invoice.${r}.amount`]?(d(),c("p",tt,p(n(t).errors[`invoice.${r}.amount`]),1)):_("",!0)]),o("td",ot,p(j(e.date)),1)]))),128))])])])]),o("div",at,[l(v,{for:"note",value:"Total Settled Amount"}),o("div",st,[o("p",nt,p(V(P.value)),1)]),n(t).errors.settled_amount?(d(),c("p",lt,p(n(t).errors.settled_amount),1)):_("",!0)]),u.value=="No"?(d(),c("div",it,[l(v,{for:"note",value:"Note"}),l(me,{id:"note",type:"text",rows:2,modelValue:n(t).note,"onUpdate:modelValue":a[12]||(a[12]=e=>n(t).note=e)},null,8,["modelValue"])])):_("",!0)]),x.value.length>0&&u.value=="Yes"?(d(),c("table",rt,[dt,o("tbody",ct,[(d(!0),c(I,null,H(x.value,(e,r)=>{var i,m,y,C,M,L,Y,R;return d(),c("tr",{key:r},[o("td",mt,p(j(e.date)),1),o("td",ut,[o("div",pt,[o("div",_t,p((m=(i=e.paymentpaid)==null?void 0:i.bank_info)!=null&&m.bank_name?(C=(y=e.paymentpaid)==null?void 0:y.bank_info)==null?void 0:C.bank_name:"Cash")+" - "+p((L=(M=e.paymentpaid)==null?void 0:M.bank_info)!=null&&L.account_number?(R=(Y=e.paymentpaid)==null?void 0:Y.bank_info)==null?void 0:R.account_number:""),1)])]),o("td",vt,p(V(e.amount)),1),o("td",ft,p(V(e.unused_amount)),1)])}),128))])])):_("",!0)]),o("div",yt,[o("div",ht,[l(de,{href:s.route("receipt.index")},{svg:S(()=>[gt]),_:1},8,["href"]),l(ce,{disabled:n(t).processing},{default:S(()=>[se("Save")]),_:1},8,["disabled"]),l(ne,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:S(()=>[n(t).recentlySuccessful?(d(),c("p",xt,"Saved.")):_("",!0)]),_:1})])])],40,we)])])]),_:1})],64))}},Tt=_e(bt,[["__scopeId","data-v-728a7de0"]]);export{Tt as default};
