import{_ as G,b as K,a as M}from"./AdminLayout-aac65a75.js";import{K as Q,h as X,r as v,j as Y,o as n,c as u,a as m,u as y,w as c,F as A,Z,b as t,y as O,n as P,t as r,f as _,i as L,e as w}from"./app-b320a640.js";/* empty css                                                              */import{_ as J}from"./Pagination-02bcc8c2.js";import{_ as W}from"./SimpleDropdown-736e6482.js";import{_ as k}from"./InputLabel-946d937b.js";import{_ as D}from"./SearchableDropdownNew-eda97ecc.js";const tt={class:"animate-top"},et={class:"flex justify-between items-center"},st={class:"flex items-center space-x-6"},ot=t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Sales Stock",-1),at=t("h1",{class:"text-lg font-semibold leading-7 text-gray-700"},"Service Stock",-1),lt=t("div",{class:"items-start"},null,-1),nt={class:"justify-end"},rt={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},ct={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},it=t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),dt={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},mt={class:"flex justify-between mb-2"},pt={class:"flex"},ht=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),ut=["src"],gt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center"},_t={class:"sm:col-span-4"},yt={class:"relative mt-2"},xt={class:"sm:col-span-4"},ft={class:"relative mt-2"},vt={key:0,class:"sm:col-span-2"},wt={class:"text-base font-semibold text-gray-900"},bt={class:"mt-8 overflow-x-auto sm:rounded-lg"},kt={class:"shadow sm:rounded-lg"},St={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Ct=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2 grid grid-cols-1 gap-x-2 sm:grid-cols-12"},[t("th",{scope:"col",class:"sm:col-span-5 px-4 py-4 text-sm font-semi bold text-gray-900"},"PRODUCT"),t("th",{scope:"col",class:"sm:col-span-1 px-4 py-4 text-sm font-semi bold text-gray-900"},"HSN"),t("th",{scope:"col",class:"sm:col-span-1 px-4 py-4 text-sm font-semi bold text-gray-900"},"GST (%)"),t("th",{scope:"col",class:"sm:col-span-3 px-4 py-4 text-sm font-semi bold text-gray-900"},"COMPANY"),t("th",{scope:"col",class:"sm:col-span-1 px-4 py-4 text-sm font-semi bold text-gray-900"},"STOCK"),t("th",{scope:"col",class:"sm:col-span-1 px-4 py-4 text-sm font-semi bold text-gray-900"},"ACTION")])],-1),Nt={key:0},zt={class:"sm:col-span-5 px-4 py-2.5 font-medium text-gray-900"},Mt={class:"sm:col-span-1 px-4 py-2.5"},At={class:"sm:col-span-1 px-4 py-2.5"},Bt={class:"sm:col-span-3 px-4 py-2.5 truncate"},It={class:"sm:col-span-1 px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},Ot={class:"sm:col-span-1 px-4 py-2.5"},Pt={class:"flex items-center justify-start gap-2"},Lt=["onClick"],$t=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),jt=[$t],Vt=t("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Tt=t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1",class:"h-5 w-5","stroke-linecap":"round","stroke-linejoin":"round"},[t("line",{x1:"12",y1:"5",x2:"12",y2:"19"}),t("line",{x1:"5",y1:"12",x2:"19",y2:"12"})],-1),qt=t("span",{class:"text-sm text-gray-700 leading-5"},"Add Stock",-1),Et=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),Ft=t("span",{class:"text-sm text-gray-700 leading-5"},"Edit Stock",-1),Ut=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7-7m0 0L5 14m7-7v12"})],-1),Rt=t("span",{class:"text-sm text-gray-700 leading-5"},"Product History",-1),Ht={key:0,class:"divide-y divide-gray-300 sm:col-span-12 product-details border mx-6 mb-4"},Gt=t("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-5 bg-gray-50"},[t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Batch"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Expiry Date"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"MRP (₹)"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Purchase Price (₹)"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"Stock")],-1),Kt={class:"divide-y divide-gray-300 bg-white grid grid-cols-1 overflow-y-auto",style:{"max-height":"184px"}},Qt={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},Xt={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},Yt={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},Zt={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},Jt={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},Wt={key:1},Dt=t("tr",{class:"bg-white"},[t("td",{colspan:"8",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),te=[Dt],ce={__name:"SalesStock",props:["data","user","permissions","stockdata","organization","search","company","organizationId","companyId"],setup(l){const S=l,C=Q().props.data.links.find(s=>s.active===!0),$=X({}),B=v("ALL COMPANY"),p=v(S.organizationId),g=v(S.companyId),b=v(""),N=s=>{b.value=s,j()},j=()=>{$.get(route("salesstock",{search:b.value,organization_id:p.value,company_id:g.value}),{preserveState:!0})},V=(s,o)=>{p.value=s,N(b.value,p.value,g.value)},T=(s,o)=>{g.value=s,B.value=o,N(b.value,p.value,g.value)},z=v(null),q=s=>{z.value=z.value===s?null:s},E=s=>{const o=new Date(s),e={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",e)},I=s=>{const[o,e]=s.toFixed(2).toString().split(".");return o.replace(/\B(?=(\d{3})+(?!\d))/g,",")+(e?"."+e:"")},F=s=>{let o=s.toFixed(2).toString(),[e,i]=o.split("."),a=e.substring(e.length-3),h=e.substring(0,e.length-3);return h!==""&&(a=","+a),`${h.replace(/\B(?=(\d{2})+(?!\d))/g,",")+a}.${i}`},U=s=>s&&s.length>0?s.reduce((o,e)=>o+(e.receive_qty-e.sell_qty),0):"-",R=()=>{let s="";switch(p.value){case 1:s="MC";break;case 2:s="HC";break;case 3:s="NOX";break;default:s="All_Organizations";break}const o=B.value.replace(/\s+/g,"_"),e=`SNS_Sales_Report_${s}_${o}.xlsx`,i={organizationId:p.value,companyId:g.value,category:"Sales"},h=`/export-stock?${new URLSearchParams(i).toString()}`;fetch(h,{method:"GET"}).then(d=>{if(!d.ok)throw new Error("Network response was not ok");return d.blob()}).then(d=>{const x=window.URL.createObjectURL(new Blob([d])),f=document.createElement("a");f.href=x,f.setAttribute("download",e),document.body.appendChild(f),f.click(),document.body.removeChild(f)}).catch(d=>{console.error("Error exporting data:",d)})},H=Y(()=>Math.round(S.stockdata.reduce((s,o)=>{let e=0;return o.sales_product&&o.sales_product.length>0&&(e=o.sales_product.reduce((i,a)=>{if(a.purchase_price!==null){const h=parseFloat(a.purchase_price),d=parseFloat(a.receive_qty),x=parseFloat(a.sell_qty);if(!isNaN(h)&&!isNaN(d)&&!isNaN(x))return i+h*(d-x)}return i},0)),s+e},0)));return(s,o)=>(n(),u(A,null,[m(y(Z),{title:"Sales Stock"}),m(G,null,{default:c(()=>[t("div",tt,[t("div",et,[t("div",st,[m(y(O),{href:s.route("salesstock"),class:P("border-b-2 pb-2 border-gray-900")},{default:c(()=>[ot]),_:1},8,["href"]),m(y(O),{href:s.route("servicestock"),class:P("pb-2")},{default:c(()=>[at]),_:1},8,["href"])]),lt,t("div",nt,[t("div",rt,[t("div",ct,[it,t("input",{id:"search-field",onInput:o[0]||(o[0]=e=>N(e.target.value,p.value,g.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])])])]),t("div",dt,[t("div",mt,[t("div",pt,[ht,m(k,{for:"customer_id",value:"Filters"})]),t("button",{onClick:R},[t("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,ut)])]),t("div",gt,[t("div",_t,[m(k,{for:"customer_id",value:"Organization Name"}),t("div",yt,[m(W,{options:l.organization,modelValue:p.value,"onUpdate:modelValue":o[1]||(o[1]=e=>p.value=e),onOnchange:V},null,8,["options","modelValue"])])]),t("div",xt,[m(k,{for:"customer_id",value:"Company Name"}),t("div",ft,[m(D,{options:l.company,modelValue:g.value,"onUpdate:modelValue":o[2]||(o[2]=e=>g.value=e),onOnchange:T},null,8,["options","modelValue"])])]),l.user.role_id==1?(n(),u("div",vt,[m(k,{for:"customer_id",value:"Total Amount (₹):"}),t("div",null,[t("p",wt,r(F(H.value)),1)])])):_("",!0)])]),t("div",bt,[t("div",kt,[t("table",St,[Ct,l.data.data&&l.data.data.length>0?(n(),u("tbody",Nt,[(n(!0),u(A,null,L(l.data.data,(e,i)=>(n(),u("tr",{class:"odd:bg-white even:bg-gray-50 border-b grid grid-cols-1 gap-x-2 sm:grid-cols-12",key:e.id},[t("td",zt,r(e.item_code?e.item_code+"  : ":"")+" "+r(e.name??""),1),t("td",Mt,r(e.hsn_code??"-"),1),t("td",At,r(e.gst??"-"),1),t("td",Bt,r(e.company.name??"-"),1),t("td",It,r(U(e.sales_product)??"-"??"-"),1),t("td",Ot,[t("div",Pt,[t("button",{onClick:a=>q(i)},jt,8,Lt),l.permissions.canProductHistory?(n(),w(K,{key:0,align:"right",width:"48"},{trigger:c(()=>[Vt]),content:c(()=>[l.permissions.canStockAdd?(n(),w(M,{key:0,href:s.route("stock.add",{id:e.id,page:y(C).url})},{svg:c(()=>[Tt]),text:c(()=>[qt]),_:2},1032,["href"])):_("",!0),l.permissions.canStockEdit?(n(),w(M,{key:1,href:s.route("stock.edit",{id:e.id,page:y(C).url})},{svg:c(()=>[Et]),text:c(()=>[Ft]),_:2},1032,["href"])):_("",!0),l.permissions.canProductHistory?(n(),w(M,{key:2,href:s.route("products.history",{id:e.id,page:y(C).url})},{svg:c(()=>[Ut]),text:c(()=>[Rt]),_:2},1032,["href"])):_("",!0)]),_:2},1024)):_("",!0)])]),z.value===i&&e.sales_product.length>0?(n(),u("div",Ht,[Gt,t("tbody",Kt,[(n(!0),u(A,null,L(e.sales_product,(a,h)=>(n(),u("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-5",key:h},[t("td",Qt,r(a.batch??"-"),1),t("td",Xt,r(a.expiry_date!=null?E(a.expiry_date):"-"),1),t("td",Yt,r(a.mrp?I(a.mrp):"-"),1),t("td",Zt,r(a.purchase_price?I(a.purchase_price):"-"),1),t("td",Jt,r(a.receive_qty-a.sell_qty),1)]))),128))])])):_("",!0)]))),128))])):(n(),u("tbody",Wt,te))])]),l.data.data&&l.data.data.length>0?(n(),w(J,{key:0,class:"mt-6",links:l.data.links},null,8,["links"])):_("",!0)])])]),_:1})],64))}};export{ce as default};
