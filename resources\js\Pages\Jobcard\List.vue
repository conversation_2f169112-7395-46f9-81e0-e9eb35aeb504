<script setup>
import { ref, onMounted, watch } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import InputLabel from '@/Components/InputLabel.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import Dropdown from '@/Components/Dropdown.vue';
import TextArea from '@/Components/TextArea.vue';
import CheckboxWithLabel from '@/Components/CheckboxWithLabel.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Head , useForm , usePage } from '@inertiajs/vue3';
import ArrowIcon from '@/Components/ArrowIcon.vue';
import { sortAndSearch } from '@/Composables/sortAndSearch.js';

const props = defineProps(['data', 'permissions', 'checklist','jobStatus', 'statusId', 'jobFilterStatus', 'pagetypes', 'notifications', 'serviceEngineer','engineerId']);
const { form, search, sort, fetchData, sortKey, sortDirection, updateParams } = sortAndSearch('jobcard.index', {
    status: props.statusId,
});

// const companyID = usePage().props.organization.id;
const modalVisible = ref(false);
const selectedUserId = ref(null);
const generateJobCardModal = ref(false);
const modalMaxWidth = ref('custom');
const jobCardData = ref([]);
const checkedValues = ref([]);
const jobCardCloseModal = ref(false);
const jobCardWidth = ref('custom2');
const statusId = ref('Open');
const engineerId = ref(props.engineerId);
const searchValue = ref('');

watch([statusId ], () => {
    updateParams({
        status: statusId.value,
    });
});

const columns = [
    { field: 'job_card_number',      label: 'JOB NUMBER',        sortable: true },
    { field: 'hospital_name',        label: 'HOSPITAL NAME',     sortable: true },
    { field: 'product_name',         label: 'PRODUCT',           sortable: true, multiFieldSort: ['product_name', 'product_code']},
    { field: 'serial_no',            label: 'SERIAL NUMBER',     sortable: true },
    { field: 'users.first_name',     label: 'ENGINEER',          sortable: true },
    { field: 'date',                 label: 'DATE',              sortable: true },
    { field: 'time_ago',             label: 'TIME',              sortable: true },
    { field: 'warranty_status',      label: 'WARRANTY STATUS',   sortable: true },
    { field: 'status',               label: 'STATUS',            sortable: true },
    { field: 'action',               label: 'ACTION',            sortable: false}
];

const openDeleteModal = (userId) => {
  selectedUserId.value = userId;
  modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const deleteUser = () => {
    form.delete(route('jobcard.destroy',{id:selectedUserId.value}), {
        onSuccess: () => closeModal()
    });
};

const openPreviewModal = (id) => {
    const jobcard = props.data.data.find(jobcard  => jobcard.id === id);
    jobCardData.value = jobcard
    checkedValues.value = jobcard.job_card_checks.map(check => check.job_card_checklist_id);
    generateJobCardModal.value = true;
};

const closeJobCardModal = () => {
    generateJobCardModal.value = false;
};

const jobform = {
    job_status: '',
    close_note: '',
    id: ''
};

const openCloseJobCardModal = (id) => {
    jobform.job_status = '';
    jobform.close_note = '';
    jobCardCloseModal.value = true;
    jobform.id = id;
};

const closeJobCardCloseModal = () => {
    jobCardCloseModal.value = false;
};

const closeJobCard = () => {
    form.post(route('jobcard.close',{ data : jobform}), {
        onSuccess: () => {
            form.reset();
            jobCardCloseModal.value = false;
        },
        onError: (errors) => {
            // console.log(errors);
        }
    });
};

const FilterStatus = (id, name) => {
    statusId.value = id;
    handleSearchChange(searchValue.value, statusId.value , engineerId.value);
};

const setEnigneer = (id, name) => {
    engineerId.value = id;
    handleSearchChange(searchValue.value, statusId.value , engineerId.value );
};

const exportJobcardXls = () => {
    const xlsName = 'Jobcard_Report_' + new Date().toISOString().split('T')[0];
    const params = {
        status: statusId.value
    };

    const queryString = new URLSearchParams(params).toString();
    const url = `/export-jobcard-report?${queryString}`;

    fetch(url, {
        method: 'GET',
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.blob();
    })
    .then(blob => {
        const url = window.URL.createObjectURL(new Blob([blob]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', xlsName + '.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    })
    .catch(error => {
        // Handle error
        console.error('Error exporting data:', error);
    });
};

const setJobStatus = (id, name) => {
    jobform.job_status = id;
    form.errors[`data.job_status`] = null;
};

const getStatusBgClass = (status) => {
    switch (status) {
        case 'Open':
            return 'bg-blue-100';
        case 'Close':
            return 'bg-green-100';
    }
};

const getStatusClass = (status) => {
    switch (status) {
        case 'Open':
            return 'text-blue-600';
        case 'Close':
            return 'text-green-600';
    }
};

const getWarrantyBgClass = (warranty_status) => {
    switch (warranty_status) {
        case 'Out Of Warranty':
            return 'bg-red-100';
        case 'amc':
            return 'bg-cyan-100';
        case 'cmc':
            return 'bg-green-100';
        case 'warranty':
            return 'bg-blue-100';
        default:
            return 'bg-gray-100';
    }
};

const getWarrantyTextClass = (warranty_status) => {
    switch (warranty_status) {
        case 'Out Of Warranty':
            return 'text-red-600';
        case 'amc':
            return 'text-cyan-600';
        case 'cmc':
            return 'text-green-600';
        case 'warranty':
            return 'text-blue-600';
        default:
            return 'text-gray-600';
    }
};


const handleSearchChange = (value,statusId, engineerId) => {
    searchValue.value = value;
    form.get(route('jobcard.index',{search:value, status: statusId, engineer_id: engineerId}),  {
        preserveState: true,
        // replace: true,
    });
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const generatePDF = (filename) => {
    const sanitizedFilename = filename.replace(/\s+/g, '_').replace(/[<>:"./\\|?*]+/g, '');
    const doc = new jsPDF();
    const pdfContent = document.getElementById('pdf-content');
    html2canvas(pdfContent, { scale: 2 }).then((canvas) => {
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 196;
        const pageHeight = 297;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        let position = 0;
        if (imgHeight <= pageHeight) {
            doc.addImage(imgData, 'PNG', 7, 10, imgWidth, imgHeight);
        } else {
            while (position < imgHeight) {
                doc.addImage(imgData, 'PNG', 7, -position +10 , imgWidth, imgHeight);
                position += pageHeight - 20;
                if (position < imgHeight) {
                    doc.addPage();
                }
            }
        }
        doc.save(sanitizedFilename);
    });
};

const page = ref('portrait');

const setPageType = (id, name) => {
    page.value = id;
};


const downloadPDF = (id, page) => {
    window.open(`/jobcard/download/${id}/${page}`, '_blank');
};

</script>

<template>
    <Head title="Jobcard"/>

    <AdminLayout :notifications="notifications">
        <div class="animate-top">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Jobcard</h1>
            </div>
            <div class="flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none">
                <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                     <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                            <input id="search-field"  @input="handleSearchChange($event.target.value, statusId, engineerId)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                            <!-- <input id="search-field" v-model="search" @input="fetchData" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search"> -->
                    </div>
                </div>
                <div class="flex justify-end" v-if="permissions.canCreateJobcard">
                    <CreateButton :href="route('jobcard.create')">
                            Create Jobcard
                    </CreateButton>
                </div>
            </div>
        </div>
        <div class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
            <div class="flex justify-between mb-2">
                <div class="flex">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                    </svg>
                    <InputLabel for="customer_id" value="Filters" />
                </div>
                <button @click="exportJobcardXls" v-if="!permissions.isServiceEngineer">
                    <img class="w-8 h-8" :src="'/uploads/img/export-excel.png'" alt="LOGO">
                </button>
            </div>
            <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                <div class="sm:col-span-4">
                    <InputLabel for="customer_id" value="Status" />
                    <div class="relative mt-2">
                        <SimpleDropdown :options="jobFilterStatus"
                        v-model="statusId"
                        @onchange="FilterStatus"
                        />
                    </div>
                </div>
                <div class="sm:col-span-4" v-if="!permissions.isServiceEngineer">
                    <InputLabel for="customer_id" value="Service Engineer" />
                    <div class="relative mt-2">
                        <SearchableDropdownNew :options="serviceEngineer"
                        v-model="engineerId"
                        @onchange="setEnigneer"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-8 overflow-x-auto sm:rounded-lg">
            <div class="shadow sm:rounded-lg">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                        <tr class="border-b-2">
                            <th v-for="(column, index) in columns" :key="index" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer" @click="sort(column.field, column.sortable)">
                                {{ column.label }}
                                <ArrowIcon :isSorted="sortKey === column.field" :direction="sortDirection" v-if="column.sortable" />
                            </th>
                        </tr>
                    </thead>
                    <tbody v-if="data.data && (data.data.length > 0)">
                        <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(poData, index) in data.data" :key="poData.id">
                            <td class="px-4 py-2.5 min-w-40">
                                {{ poData.job_card_number }}
                            </td>
                            <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap truncate">
                                {{ poData.hospital_name ?? '-' }}
                            </td>
                            <td scope="row" class="px-4 py-2.5 whitespace-normal  min-w-52 truncate">
                               {{ poData.product_name ?? '' }} {{ poData.product_code ?? '' }}
                            </td>
                            <td class="px-4 py-2.5 min-w-48">
                                {{ poData.serial_no ?? '-' }}
                            </td>
                            <td class="px-4 py-2.5 min-w-52">
                                {{ poData.users.first_name }} {{ poData.users.last_name }}
                            </td>
                            <td class="px-4 py-2.5 min-w-32">
                                {{ formatDate(poData.date) }}
                            </td>
                            <td class="px-4 py-2.5 min-w-32">
                                {{ poData.time_ago }}
                            </td>

                            <td class="px-4 py-2.5">
                                <div class="flex justify-center item-center w-full">
                                <div class="flex rounded-full px-4 py-1" :class="getWarrantyBgClass(poData.warranty_status)">
                                    <span class="text-sm font-semibold" :class="getWarrantyTextClass(poData.warranty_status)">{{ poData.warranty_status ?? '-' }} </span>
                                </div>
                            </div>
                            </td>

                            <td class="flex flex-1 items-center px-4 py-2.5">
                                <div class="flex rounded-full px-4 py-1" :class="getStatusBgClass(poData.status)">
                                    <span class="text-sm font-semibold" :class="getStatusClass(poData.status)">{{ poData.status }}</span>
                                </div>
                            </td>
                            <td class="items-center px-4 py-2.5">
                                <div class="flex items-center justify-start gap-4">
                                     <Dropdown align="right" width="48">
                                        <template #trigger>
                                            <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                    <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                </svg>
                                            </button>
                                        </template>
                                        <template #content>
                                            <ActionLink v-if="poData.status == 'Open' &&  permissions.canEditJobcard"  :href="route('jobcard.edit',{id:poData.id})">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                            />
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Edit
                                                    </span>
                                                </template>
                                            </ActionLink>
                                            <button  v-if="poData.status == 'Open' && permissions.canDeleteJobcard" type="button" @click="openDeleteModal(poData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                    d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                    />
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">
                                                    Delete
                                                </span>
                                            </button>
                                            <ActionLink :href="route('jobcard.show',{id:poData.id})" v-if="permissions.canViewJobcard">
                                                <template #svg>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                                    </svg>
                                                </template>
                                                <template #text>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        View Jobcard
                                                    </span>
                                                </template>
                                            </ActionLink>
                                            <button type="button"  @click="openPreviewModal(poData.id)"  class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M6 2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm2 6h8m-4 4h4"></path>
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">
                                                        Generate PDF
                                                </span>
                                            </button>
                                            <button
                                                v-if="poData.status == 'Open' && permissions.canCreateJobcard"
                                                type="button"
                                                @click="openCloseJobCardModal(poData.id)"
                                                class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"
                                                >
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M6 18L18 6M6 6l12 12"/>
                                                </svg>
                                                <span class="text-sm text-gray-700 leading-5">
                                                    Close Jobcard
                                                </span>
                                            </button>
                                        </template>
                                    </Dropdown>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                    <tbody v-else>
                        <tr class="bg-white">
                        <td colspan="12" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                            No data found.
                        </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>
        <Modal :show="modalVisible" @close="closeModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteUser"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <Modal :show="generateJobCardModal" @close="closeJobCardModal" :maxWidth="modalMaxWidth">
              <div class="p-6">
                <div class="container1 p-2" id="pdf-content">
                    <div class="header" style="align-items: start; justify-content: center; text-align: center;">
                        <!-- <img class="w-full h-10" :src="filePath + jobcard.organization.logo" alt="logo"> -->
                        <div style="align-items: center; justify-content: space-between; margin-bottom: 10px;">
                            <p style="font-size: 20px;"><strong>Maintenance Workshop</strong></p>
                        </div>
                    </div>
                    <div style="display:flex; justify-content: space-between;">
                        <div class="" style="margin-bottom: 20px; justify-items: start; width: 300px;">
                            <p style="margin-bottom: 4px;"><strong></strong></p>
                            <div style="display:flex;"><p style="width: 100px;"><strong>Hospital Name</strong></p><p>: {{ jobCardData.hospital_name }}</p></div>
                            <div style="display:flex;"><p style="width: 100px;"><strong>Address</strong></p><p>: {{ jobCardData.address }}</p></div>
                            <div style="display:flex;"><p style="width: 100px;"><strong>Contact Number</strong></p><p>: {{ jobCardData.contact_no }}</p></div>
                        </div>
                        <div class="invoice-details" style="margin-bottom: 20px; justify-items: start; width: 320px;">
                            <div style="display:flex;"><p style="width: 100px;"><strong>Job Number</strong></p><p>: {{ jobCardData.job_card_number }}</p></div>
                            <div style="display:flex;"><p style="width: 100px;"><strong>Job Date</strong></p><p>: {{ formatDate(jobCardData.date) }}</p></div>
                            <div style="display:flex;"><p style="width: 100px;"><strong>Engineer</strong></p><p>: {{ jobCardData.users.first_name }} {{ jobCardData.users.last_name }}</p></div>
                        </div>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>Equipment Name</th>
                                <th>Model</th>
                                <th>Serial No</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="">
                                <td>{{ jobCardData.product_name ?? '-' }}</td>
                                <td>{{ jobCardData.product_code ?? '-' }}</td>
                                <td>{{ jobCardData.serial_no ?? '-' }}</td>
                            </tr>
                        </tbody>
                    </table>
                    <div>
                        <div class="">
                            <div style="display:flex;"><p><strong>Problem Description</strong></p><p>: {{ jobCardData.problem_description ?? 'NA' }}</p></div>
                        </div>
                        <div class="mt-2">
                            <div style="display:flex;"><p><strong>Accessories</strong></p><p>: {{ jobCardData.accessories ?? 'NA' }}</p></div>
                        </div>
                    </div>
                    <div class="sm:col-span-6 mt-2">
                         <p style="width: 200px;"><strong>Checklist</strong></p>
                        <div class="grid sm:grid-cols-6 relative" style="margin-bottom: 20px;  width: 500px;">
                            <CheckboxWithLabel
                            v-for="item in checklist"
                            :key="item.id"
                            :checked="checkedValues"
                            :value="item.id"
                            :label="item.type"
                            @update:checked="updateChecked"
                            />
                        </div>
                    </div>
                    <div style="display:flex; justify-content: space-between; margin-top: 20px;">
                         <div class="" style="margin-bottom: 20px; justify-items: start; width: 400;">
                            <div style="display:flex;"><p><strong>Close Note</strong></p><p>: {{ jobCardData.problem_description }}</p></div>
                         </div>
                         <div class="" style="margin-bottom: 20px; justify-items: end; width: 260px;">
                            <div style="display:flex;"><p><strong>Close Date</strong></p><p>: {{ jobCardData.close_date }}</p></div>
                         </div>
                     </div>
                </div>
                <div class="mt-6 px-4 flex justify-end">
                    <div class="flex flex-col justify-end space-y-6">
                        <div class="flex items-center space-x-2">
                            <InputLabel for="customer_id" value="Page Type :" />
                            <SearchableDropdown :options="pagetypes"
                            v-model="page"
                            @onchange="setPageType"
                        />
                        </div>
                        <div class="flex justify-end">
                            <SecondaryButton @click="closeJobCardModal"> Cancel </SecondaryButton>
                                <div class="w-36">
                                    <PrimaryButton
                                        class="ml-3 w-20"
                                        @click="downloadPDF(jobCardData.id, page)"
                                    >
                                        Generate Pdf
                                    </PrimaryButton>
                                </div>
                        </div>
                    </div>
                </div>
            </div>
        </Modal>

        <Modal :show="jobCardCloseModal" @close="closeJobCardCloseModal" :maxWidth="jobCardWidth">
             <div class="p-6">
                <h2 class="text-2xl font-semibold leading-7 text-gray-900">Close Jobcard</h2>
                    <div class="border-b border-gray-900/10 pb-12">
                        <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                            <div class="sm:col-span-6">
                                <InputLabel for="role_id" value="Job Status" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="jobStatus"
                                    v-model="jobform.job_status"
                                    @onchange="setJobStatus"
                                    :class="{ 'error rounded-md': form.errors[`data.job_status`] }"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-6">
                                <InputLabel for="close_note" value="Close Note" />
                                <TextArea
                                    id="close_note"
                                    type="text"
                                    :rows="3"
                                    v-model="jobform.close_note"
                                    :class="{ 'error rounded-md': form.errors[`data.close_note`] }"
                                />
                            </div>
                        </div>
                    </div>
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="closeJobCardCloseModal"> Cancel </SecondaryButton>
                    <div class="w-36">
                    <PrimaryButton
                        class="ml-3 w-20"
                        @click="closeJobCard"
                    >
                    Save
                    </PrimaryButton>
                    </div>
                </div>
             </div>
        </Modal>
    </AdminLayout>

</template>



<style scoped>
    .error {
        border: 1px solid red;
    }
    .container1 {
        font-size: 12px;
        width: 100%;
        margin: 0 auto;
        box-sizing: border-box;
        page-break-inside: avoid;
    }
    .container1 input[type="checkbox"] {
        margin-top: 10px !important;
    }
    .container1 p {
        font-size: 12px;
    }
    #pdf-content table {
        page-break-inside: auto;
        page-break-after: auto;
        border-left: 0.1px solid rgb(55 65 81)  !important;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
    #pdf-content td {
        border-bottom : 0.1px solid rgb(55 65 81)  !important;
        border-right: 0.1px solid rgb(55 65 81)  !important;
        padding: 4px 4px !important;
        text-align: left;
        font-size: 12px;
    }
    #pdf-content th {
        background-color: #bfe2f291;
        border-bottom: 0.1px solid rgb(55 65 81)  !important;
        border-right: 0.1px solid rgb(55 65 81)  !important;
        border-top: 0.1px solid rgb(55 65 81)  !important;
        padding: 6px 4px !important;
        text-align: left;
        font-size: 12px;
    }

    #pdf-content table {
        page-break-inside: auto;
        page-break-after: auto;
        border-left: 0.1px solid rgb(55 65 81)  !important;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
</style>
