<?php

namespace App\Models;

use App\Traits\ActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DebitNoteDetail extends Model
{
    use HasFactory;
    use SoftDeletes;
    use ActivityTrait;

    protected $table = 'debit_note_details';

    protected static $logName = 'DebitNote-Detail';

    public function getLogDescription(string $event): string
    {
        $debitNoteNumber = $this->debitNote->debit_note_no;
        $productName = $this->product?->name ?? 'Unknown Product';

        return "Debit note details has been {$event} for <strong>{$productName}</strong> : {$debitNoteNumber} by";
    }

    protected static $logAttributes = [
        'debit_note_id',
        'product_id',
        'serial_number_id',
        'purchase_order_receive_detail_id',
        'hsn_code',
        'qty',
        'price',
        'total_price',
        'gst_amount',
        'gst',
        'total_gst_amount',
        'discount',
        'discount_amount',
        'total_amount',
        'batch',
        'expiry_date',
        'description',
        'created_by',
        'updated_by'
    ];

    protected $fillable = [
        'debit_note_id',
        'product_id',
        'serial_number_id',
        'purchase_order_receive_detail_id',
        'hsn_code',
        'qty',
        'price',
        'total_price',
        'gst_amount',
        'gst',
        'total_gst_amount',
        'discount',
        'discount_amount',
        'total_amount',
        'batch',
        'expiry_date',
        'description',
        'created_by',
        'updated_by'
    ];

    public function debitNote()
    {
        return $this->belongsTo(DebitNote::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function serialNumbers()
    {
        return $this->belongsTo(SerialNumbers::class, 'serial_number_id', 'id');
    }

    public function purchaseOrderReceiveDetail()
    {
        return $this->belongsTo(PurchaseOrderReceiveDetails::class, 'purchase_order_receive_detail_id', 'id');
    }

    public static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            self::handleLogEntry($model, 'created');
        });

        static::updated(function ($model) {
            if ($model->isDirty()) {
                self::handleLogEntry($model, 'updated');
            }
        });

        static::deleted(function ($model) {
            self::handleLogEntry($model, 'deleted');
        });
    }

    protected static function handleLogEntry($model, $event)
    {
        $logName = "Debit note detail has been $event for this product";
        self::addCustomLogEntry($model, $event, $logName);
    }
}
