import{K as st,r as g,C as It,j as $,o as _,c as p,a as c,u as i,w as f,F as A,Z as Ft,b as t,t as m,k as kt,v as Pt,d as Tt,n as y,f as x,i as ot,g as F,e as $t,s as Gt,x as Ut}from"./app-21e66fd5.js";import{_ as Dt,a as Mt}from"./AdminLayout-db62264f.js";import{_ as jt}from"./InputError-01d93b90.js";import{_ as P}from"./InputLabel-4a50badc.js";import{P as lt}from"./PrimaryButton-ed35dcb4.js";import{_ as v}from"./TextInput-625f6add.js";import{_ as Nt}from"./TextArea-2c14c909.js";import{_ as T}from"./SearchableDropdown-06b090a4.js";import{D as at}from"./DangerButton-b1b34a7e.js";import{_ as B}from"./SecondaryButton-0406f438.js";import{M as q}from"./Modal-7ff7f630.js";import{_ as At}from"./FileViewer-8efb6642.js";import{_ as Bt}from"./MultipleFileUpload-cc973978.js";import{u as qt}from"./index-c670349c.js";import{_ as zt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const r=w=>(Gt("data-v-74ec5c03"),w=w(),Ut(),w),Et={class:"animate-top"},Ot={class:"sm:flex sm:items-center"},Lt=r(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Challan")],-1)),Ht={class:"w-auto"},Wt={class:"flex space-x-2 items-center"},Rt={class:"text-sm font-semibold text-gray-900"},Kt={class:"flex space-x-2 items-center"},Qt=["onSubmit"],Xt={class:"shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},Zt={class:"inline-flex items-start space-x-6 justify-start w-full"},Jt={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},Yt={class:"inline-flex items-center justify-start w-full space-x-6"},te=r(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Customer:",-1)),ee={class:"relative w-full"},se={class:"inline-flex items-center justify-start w-full space-x-2"},oe=r(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"GST No:",-1)),le={class:"text-sm leading-6 text-gray-700"},ae={class:"inline-flex items-center justify-start w-full space-x-2"},ne=r(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Email:",-1)),ie={class:"text-sm leading-6 text-gray-700"},ce={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},re={class:"inline-flex items-center justify-start w-full space-x-2"},de=r(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Challan Number:",-1)),ue={class:"text-sm leading-6 text-gray-700"},me={class:"inline-flex items-center justify-start w-full space-x-2"},_e=r(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1)),pe={class:"text-sm leading-6 text-gray-700"},he={class:"inline-flex items-center justify-start w-full space-x-2"},xe=r(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Category:",-1)),ge={class:"relative mt-2"},fe={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border space-y-2"},ye={class:"overflow-x-auto w-full"},ve={class:"overflow-x-auto divide-y divide-gray-300 w-full",style:{"margin-bottom":"160px"}},we=r(()=>t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Product Name",-1)),be=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Serial No",-1)),Ce=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"HSN",-1)),Se=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"EXP",-1)),Ve=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),Ie=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),Fe=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),ke=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),Pe=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Sell Price (₹)",-1)),Te=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),$e={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Ge={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Ue={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},De={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Me={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},je=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),Ne=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),Ae=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),Be={class:"divide-y divide-gray-300 bg-white"},qe={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-80"},ze={class:"relative mt-2"},Ee={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-60"},Oe={class:"relative mt-2"},Le={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},He={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},We={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},Re={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ke={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Qe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Xe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Ze={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Je={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ye={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ts={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},es={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ss={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},os={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ls={class:"whitespace-nowrap px-3 py-3 flex space-x-2 min-w-48"},as={class:"px-3 py-3 text-sm text-gray-900"},ns=["onClick"],is=r(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),cs=[is],rs={class:"mt-12 flex items-center justify-between"},ds={class:"ml-auto flex items-center justify-end gap-x-6"},us={key:0,class:"bg-white p-1 shadow sm:rounded-lg border"},ms={class:"min-w-full divide-y divide-gray-300"},_s=r(()=>t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"UPLOADED DOCUMENT"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1)),ps={class:"divide-y divide-gray-300 bg-white"},hs={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},xs={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},gs=["onClick"],fs=r(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),ys=[fs],vs=["onClick"],ws=r(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),bs=[ws],Cs=["onClick"],Ss=r(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),Vs=[Ss],Is={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Fs={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},ks={class:"sm:col-span-3 space-y-4"},Ps={class:"flex space-x-4"},Ts={class:"w-full"},$s={class:"w-full"},Gs={class:"relative mt-2"},Us={class:"flex space-x-4"},Ds={class:"w-full"},Ms={class:"w-full"},js={class:"sm:col-span-3"},Ns={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},As={class:"inline-flex items-center justify-end w-full space-x-3"},Bs=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),qs={class:"text-base font-semibold text-gray-900 w-32"},zs={class:"inline-flex items-center justify-end w-full space-x-3"},Es=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),Os={class:"text-base font-semibold text-gray-900 w-32"},Ls={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},Hs=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),Ws={class:"text-base font-semibold text-gray-900 w-32"},Rs={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},Ks=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),Qs={class:"text-base font-semibold text-gray-900 w-32"},Xs={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},Zs=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),Js={class:"text-base font-semibold text-gray-900 w-32"},Ys={class:"inline-flex items-center justify-end w-full space-x-3"},to=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),eo={class:"text-base font-semibold text-gray-900 w-32"},so={class:"flex items-center justify-between"},oo={class:"ml-auto flex items-center justify-end gap-x-6"},lo=r(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),ao={class:"p-6"},no=r(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this product? ",-1)),io={class:"mt-6 flex justify-end"},co={class:"p-6"},ro=r(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this document? ",-1)),uo={class:"mt-6 flex justify-end"},mo={class:"p-6"},_o={class:"mt-6 px-4 flex justify-end"},po={__name:"Edit",props:["salesuser","customers","category","serialno","filepath","products"],setup(w){const b=w,z=st().props.filepath.view,d=st().props.data[0],E=g([]),nt=b.products.filter(s=>s.serial_numbers.some(e=>e.organization_id===d.organization_id));E.value=nt;const O=g([]),it=b.serialno;O.value=it;const o=qt("post","/challan",{note:d.note,date:d.date,category:d.category,organization_id:d.organization_id,selectedProductItem:[],customer_id:d.customer_id,sales_user_id:d.sales_user_id,challan_number:d.challan_number,challan_id:d.id,document:d.documents,cgst:d.cgst,sgst:d.sgst,igst:d.igst,total_gst:d.total_gst,sub_total:d.sub_total,total_amount:d.total_amount,total_discount:d.total_discount,dispatch:d.dispatch,transport:d.transport}),ct=()=>{o.sub_total=H.value,o.challan_number=b.challan_number,o.cgst=h.value=="CGST/SGST"?S.value/2:"0",o.sgst=h.value=="CGST/SGST"?S.value/2:"0",o.igst=h.value=="IGST"?S.value:"0",o.total_gst=S.value,o.total_amount=L.value,o.total_discount=W.value,o.selectedProductItem=u.value,o.submit({preserveScroll:!0,onSuccess:()=>o.reset()})},u=g([{serial_number_id:"",editmode:"",product_id:"",product_name:"",item_code:"",expiry_date:"",mrp:"",price:"",hsn_code:"",sell_price:"",discount:"",discount_amount:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",qty:"",challan_detail_id:"",description:""}]);It(()=>{u.value=d.challan_detail.map(s=>({challan_detail_id:s.id,editmode:"editMode",product_id:s.viewserialnumbers.product.id,serial_number_id:s.serial_number_id,product_name:s.viewserialnumbers.product.name,qty:s.qty,expiry_date:s.viewserialnumbers.expiry_date,mrp:s.viewserialnumbers.mrp?parseFloat(s.viewserialnumbers.mrp).toFixed(2):"-",price:parseFloat(s.viewserialnumbers.purchase_price).toFixed(2),hsn_code:s.viewserialnumbers.product.hsn_code,item_code:s.viewserialnumbers.product.item_code,description:s.description,discount:parseFloat(s.discount).toFixed(2),sell_price:parseFloat(s.price).toFixed(2),gst:parseFloat(s.gst).toFixed(2),sgst:parseFloat(s.gst/2).toFixed(2),discount_amount:parseFloat(s.discount_amount).toFixed(2)??"0",total_price:parseFloat(s.total_price).toFixed(2),gst_amount:parseFloat(s.gst_amount).toFixed(2),total_gst_amount:parseFloat(s.total_gst_amount).toFixed(2),total_amount:parseFloat(s.total_amount).toFixed(2)}))});const h=g(d.customers.gst_type),rt=(s,a)=>{o.customer_id=s,o.errors.customer_id=null;const e=b.customers.find(n=>n.id===s);e&&(h.value=e.gst_type)},dt=(s,a)=>{o.category=s,o.errors.category=null},ut=()=>{u.value.push({challan_detail_id:"",product_name:"",product_id:"",serial_number_id:"",expiry_date:"",mrp:"",price:"",hsn_code:""})},mt=(s,a,e)=>b.serialno.filter(l=>l.product_id===s&&l.organization_id===d.organization_id),_t=(s,a,e)=>{const n=b.serialno.filter(l=>l.product_id===s&&l.organization_id===d.organization_id);O.value=n,u.value[e].product_id=s},pt=(s,a,e)=>{const n=b.serialno.find(l=>l.id===s);n&&(u.value[e].qty="",u.value[e].serial_number_id=n.id,u.value[e].product_name=n.product.name,u.value[e].expiry_date=n.expiry_date,u.value[e].mrp=n.mrp?parseFloat(n.mrp).toFixed(2):"-",u.value[e].price=parseFloat(n.purchase_price).toFixed(2),u.value[e].hsn_code=n.product.hsn_code,u.value[e].discount=0,u.value[e].sell_price=0,u.value[e].total_price=parseFloat(n.purchase_price).toFixed(2),u.value[e].gst=parseFloat(n.product.gst).toFixed(2),u.value[e].sgst=parseFloat(n.product.gst/2).toFixed(2),u.value[e].gst_amount=0,u.value[e].total_gst_amount=0,u.value[e].total_amount=0,u.value[e].description="",o.errors[`selectedProductItem.${e}.serial_number_id`]=null)},ht=(s,a)=>{const e=parseFloat(s.sell_price),n=parseFloat(s.discount)||0,l=h.value=="IGST"?parseFloat(s.gst):parseFloat(s.sgst*2),I=parseFloat(s.qty),Z=e*I*(1+l/100),J=e*1*(l/100),Y=e*I*(l/100),N=Z*(n/100)||0,tt=Z-N,et=e*I;return s.total_price=isNaN(et)?"":parseFloat(et).toFixed(2),s.gst_amount=isNaN(J)?"":parseFloat(J).toFixed(2),s.total_gst_amount=isNaN(Y)?"":parseFloat(Y).toFixed(2),s.discount_amount=isNaN(N)?"":parseFloat(N).toFixed(2),isNaN(tt)?"":parseFloat(tt).toFixed(2)},C=(s,a)=>{s.total_amount=ht(s)},L=$(()=>Math.round(u.value.reduce((s,a)=>s+(a.total_amount?parseFloat(a.total_amount):0),0))),S=$(()=>u.value.reduce((s,a)=>s+(a.total_gst_amount?parseFloat(a.total_gst_amount):0),0)),H=$(()=>u.value.reduce((s,a)=>s+(a.total_price?parseFloat(a.total_price):0),0)),W=$(()=>u.value.reduce((s,a)=>s+(a.discount_amount?parseFloat(a.discount_amount):0),0)),G=g(!1),R=g(null),xt=g(null),U=()=>{G.value=!1},gt=()=>{o.get(route("removeproduct",{id:R.value,model:"ChallanDetail"}),{onSuccess:()=>{U(),u.value.splice(index,1)}})},ft=(s,a)=>{a!==void 0&&a!=""?(R.value=a,xt.value=s,G.value=!0):u.value.splice(s,1)},V=s=>{o.errors[s]=null},yt=s=>{o.document=s},vt=(s,a)=>{o.sales_user_id=s},D=g(!1),K=g(null),wt=s=>{K.value=s,D.value=!0},bt=()=>{o.get(route("removedocument",{id:K.value,name:"challanDocument"}),{onSuccess:()=>{M()}})},M=()=>{D.value=!1},j=g(!1),Q=g(null),Ct=g("custom"),St=s=>{Q.value=s,j.value=!0},X=()=>{j.value=!1},Vt=s=>{const a=window.location.origin+z+s,e=document.createElement("a");e.href=a,e.setAttribute("download",s),document.body.appendChild(e),e.click(),document.body.removeChild(e)},k=s=>{const[a,e]=s.toFixed(2).toString().split(".");return a.replace(/\B(?=(\d{3})+(?!\d))/g,",")+(e?"."+e:"")};return(s,a)=>(_(),p(A,null,[c(i(Ft),{title:"Challan"}),c(Dt,null,{default:f(()=>[t("div",Et,[t("div",Ot,[Lt,t("div",Ht,[t("div",Wt,[t("p",Rt,m(i(d).organization.name),1),t("div",Kt,[kt(t("input",{class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":a[0]||(a[0]=e=>i(o).date=e),onChange:a[1]||(a[1]=e=>i(o).validate("date"))},null,544),[[Pt,i(o).date]])])])])]),t("form",{onSubmit:Tt(ct,["prevent"]),class:"mt-6 space-y-6"},[t("div",Xt,[t("div",Zt,[t("div",Jt,[t("div",Yt,[te,t("div",ee,[c(T,{options:w.customers,modelValue:i(o).customer_id,"onUpdate:modelValue":a[2]||(a[2]=e=>i(o).customer_id=e),onOnchange:rt,class:y({"error rounded-md":i(o).errors.customer_id})},null,8,["options","modelValue","class"])])]),t("div",se,[oe,t("p",le,m(i(d).customers.gst_no??"-"),1)]),t("div",ae,[ne,t("p",ie,m(i(d).customers.email??"-"),1)])]),t("div",ce,[t("div",re,[de,t("span",ue,m(i(d).challan_number),1)]),t("div",me,[_e,t("p",pe,m(i(d).customers.contact_no??"-"),1)]),t("div",he,[xe,t("div",ge,[c(T,{options:w.category,modelValue:i(o).category,"onUpdate:modelValue":a[3]||(a[3]=e=>i(o).category=e),onOnchange:dt,class:y({"error rounded-md":i(o).errors.category})},null,8,["options","modelValue","class"])])])])])]),t("div",fe,[t("div",ye,[t("table",ve,[t("thead",null,[t("tr",null,[we,be,Ce,Se,Ve,Ie,Fe,ke,Pe,Te,h.value=="IGST"?(_(),p("th",$e,"IGST (%)")):x("",!0),h.value=="IGST"?(_(),p("th",Ge,"IGST (₹)")):x("",!0),h.value=="CGST/SGST"?(_(),p("th",Ue,"CGST (%)")):x("",!0),h.value=="CGST/SGST"?(_(),p("th",De,"SGST (%)")):x("",!0),h.value=="CGST/SGST"?(_(),p("th",Me,"Total GST (₹)")):x("",!0),je,Ne,Ae])]),t("tbody",Be,[(_(!0),p(A,null,ot(u.value,(e,n)=>(_(),p("tr",{key:n},[t("td",qe,[t("div",ze,[c(T,{options:E.value,modelValue:e.product_id,"onUpdate:modelValue":l=>e.product_id=l,onOnchange:(l,I)=>_t(l,I,n),onChange:a[4]||(a[4]=l=>i(o).validate("product_id")),class:y({"error rounded-md":i(o).errors[`selectedProductItem.${n}.product_id`]}),editMode:e.editmode},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class","editMode"])])]),t("td",Ee,[t("div",Oe,[c(T,{options:mt(e.product_id),modelValue:e.serial_number_id,"onUpdate:modelValue":l=>e.serial_number_id=l,onOnchange:(l,I)=>pt(l,I,n),onChange:a[5]||(a[5]=l=>i(o).validate("serial_number_id")),class:y({"error rounded-md":i(o).errors[`selectedProductItem.${n}.serial_number_id`]}),editMode:e.editmode},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class","editMode"])])]),t("td",Le,m(e.hsn_code??"-"),1),t("td",He,m(e.expiry_date??"-"),1),t("td",We,[c(v,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":l=>e.description=l,onInput:l=>C(e,n),onChange:l=>V("selectedProductItem."+n+".description"),class:y({error:i(o).errors[`selectedProductItem.${n}.description`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Re,m(e.mrp??"-"),1),t("td",Ke,m(e.price),1),t("td",Qe,[c(v,{id:"qty",type:"text",modelValue:e.qty,"onUpdate:modelValue":l=>e.qty=l,onInput:l=>C(e,n),onChange:l=>V("selectedProductItem."+n+".qty"),class:y({error:i(o).errors[`selectedProductItem.${n}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Xe,[c(v,{id:"sell_price",type:"text",modelValue:e.sell_price,"onUpdate:modelValue":l=>e.sell_price=l,onInput:l=>C(e,n),onChange:l=>V("selectedProductItem."+n+".sell_price"),class:y({error:i(o).errors[`selectedProductItem.${n}.sell_price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Ze,m(e.total_price),1),h.value=="IGST"?(_(),p("td",Je,[c(v,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":l=>e.gst=l,onInput:l=>C(e,n),onChange:l=>V("selectedProductItem."+n+".gst"),class:y({error:i(o).errors[`selectedProductItem.${n}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):x("",!0),h.value=="CGST/SGST"?(_(),p("td",Ye,[c(v,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":l=>e.sgst=l,onInput:l=>C(e,n),onChange:l=>V("selectedProductItem."+n+".gst"),class:y({error:i(o).errors[`selectedProductItem.${n}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):x("",!0),h.value=="CGST/SGST"?(_(),p("td",ts,[c(v,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":l=>e.sgst=l,onInput:l=>C(e,n),onChange:l=>V("selectedProductItem."+n+".gst"),class:y({error:i(o).errors[`selectedProductItem.${n}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):x("",!0),t("td",es,m(e.total_gst_amount),1),t("td",ss,[c(v,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":l=>e.discount=l,onInput:l=>C(e,n),onChange:l=>V("selectedProductItem."+n+".discount"),class:y({error:i(o).errors[`selectedProductItem.${n}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",os,m(e.discount_amount),1),t("td",ls,[t("div",as,m(e.total_amount),1),t("button",{type:"button",class:"mt-1",onClick:l=>ft(n,e.challan_detail_id)},cs,8,ns)])]))),128))])])]),t("div",rs,[t("div",ds,[c(lt,{onClick:ut,type:"button"},{default:f(()=>[F("Add Product")]),_:1})])])]),i(d).documents&&i(d).documents.length>0?(_(),p("div",us,[t("table",ms,[_s,t("tbody",ps,[(_(!0),p(A,null,ot(i(d).documents,(e,n)=>(_(),p("tr",{key:i(d).id,class:""},[t("td",hs,m(e.orignal_name),1),t("td",xs,[t("button",{type:"button",onClick:l=>wt(e.id)},ys,8,gs),t("button",{type:"button",onClick:l=>St(e.name)},bs,8,vs),t("button",{type:"button",onClick:l=>Vt(e.name)},Vs,8,Cs)])]))),128))])])])):x("",!0),t("div",Is,[t("div",Fs,[t("div",ks,[t("div",Ps,[t("div",Ts,[c(P,{for:"note",value:"Upload Documents"}),c(Bt,{inputId:"document",inputName:"document",uploadedFiles:i(o).document,onFiles:yt},null,8,["uploadedFiles"])]),t("div",$s,[c(P,{for:"company_name",value:"Sales Person"}),t("div",Gs,[c(T,{options:w.salesuser,modelValue:i(o).sales_user_id,"onUpdate:modelValue":a[6]||(a[6]=e=>i(o).sales_user_id=e),onOnchange:vt,class:y({"error rounded-md":i(o).errors.sales_user_id})},null,8,["options","modelValue","class"])])])]),t("div",Us,[t("div",Ds,[c(P,{for:"company_name",value:"Transport"}),c(v,{id:"gst",type:"text",modelValue:i(o).dispatch,"onUpdate:modelValue":a[7]||(a[7]=e=>i(o).dispatch=e)},null,8,["modelValue"])]),t("div",Ms,[c(P,{for:"company_name",value:"Dispatch"}),c(v,{id:"transport",type:"text",modelValue:i(o).transport,"onUpdate:modelValue":a[8]||(a[8]=e=>i(o).transport=e)},null,8,["modelValue"])])]),t("div",null,[c(P,{for:"note",value:"Note"}),c(Nt,{id:"note",type:"text",modelValue:i(o).note,"onUpdate:modelValue":a[9]||(a[9]=e=>i(o).note=e),onChange:a[10]||(a[10]=e=>i(o).validate("note"))},null,8,["modelValue"]),i(o).invalid("note")?(_(),$t(jt,{key:0,class:"",message:i(o).errors.note},null,8,["message"])):x("",!0)])]),t("div",js,[t("div",Ns,[t("div",As,[Bs,t("p",qs,m(k(H.value)),1)]),t("div",zs,[Es,t("p",Os,m(k(W.value)),1)]),h.value=="IGST"?(_(),p("div",Ls,[Hs,t("p",Ws,m(k(S.value)),1)])):x("",!0),h.value=="CGST/SGST"?(_(),p("div",Rs,[Ks,t("p",Qs,m(k(S.value/2)),1)])):x("",!0),h.value=="CGST/SGST"?(_(),p("div",Xs,[Zs,t("p",Js,m(k(S.value/2)),1)])):x("",!0),t("div",Ys,[to,t("p",eo,m(k(L.value)),1)])])])])]),t("div",so,[t("div",oo,[c(Mt,{href:s.route("challan.index")},{svg:f(()=>[lo]),_:1},8,["href"]),c(lt,{disabled:i(o).processing},{default:f(()=>[F("Submit")]),_:1},8,["disabled"])])])],40,Qt)]),c(q,{show:G.value,onClose:U},{default:f(()=>[t("div",ao,[no,t("div",io,[c(B,{onClick:U},{default:f(()=>[F(" Cancel ")]),_:1}),c(at,{class:"ml-3",onClick:gt},{default:f(()=>[F(" Delete ")]),_:1})])])]),_:1},8,["show"]),c(q,{show:D.value,onClose:M},{default:f(()=>[t("div",co,[ro,t("div",uo,[c(B,{onClick:M},{default:f(()=>[F(" Cancel ")]),_:1}),c(at,{class:"ml-3",onClick:bt},{default:f(()=>[F(" Delete ")]),_:1})])])]),_:1},8,["show"]),c(q,{show:j.value,onClose:X,maxWidth:Ct.value},{default:f(()=>[t("div",mo,[c(At,{fileUrl:i(z)+Q.value},null,8,["fileUrl"]),t("div",_o,[c(B,{onClick:X},{default:f(()=>[F(" Cancel ")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},$o=zt(po,[["__scopeId","data-v-74ec5c03"]]);export{$o as default};
