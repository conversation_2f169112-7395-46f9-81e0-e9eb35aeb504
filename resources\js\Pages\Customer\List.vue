<script setup>
import { ref, onMounted, watch } from 'vue';
import { sortAndSearch } from '@/Composables/sortAndSearch.js';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import ArrowIcon from '@/Components/ArrowIcon.vue';
import Dropdown from '@/Components/Dropdown.vue';
import { Head , useForm} from '@inertiajs/vue3';
import InputLabel from '@/Components/InputLabel.vue';

const props = defineProps(['data', 'bankinfo', 'organization', 'permissions', 'paymentType']);
const { form, search, sort, fetchData, sortKey, sortDirection } = sortAndSearch('customers.index');

const modalVisible = ref(false);
const selectedUserId = ref(null);

const openDeleteModal = (userId) => {
  selectedUserId.value = userId;
  modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const deleteUser = () => {
    form.delete(route('customers.destroy',{id:selectedUserId.value}), {
        onSuccess: () => closeModal()
    });
};

const updateSwitchValue = (status,id) => {
    form.post(route('customers.activation',{id:id, status:status}), {
    });
};

const bankinfo = ref([]);
const paymentReceiveModal = ref(false);
const paymentReceiveWidth = ref('custom2');
const payment_type = ref('');

const columns = [
    { field: 'customer_name',   label: 'CUSTOMER NAME',     sortable: true },
    { field: 'contact_no',      label: 'PHONE',             sortable: false },
    { field: 'email',           label: 'EMAIL',             sortable: true },
    { field: 'type',            label: 'TYPE',              sortable: true },
    { field: 'city',            label: 'LOCATION',          sortable: true },
    { field: 'balance',         label: 'BALANCE',           sortable: true },
    { field: 'status',          label: 'STATUS',            sortable: false },
    { field: 'action',          label: 'ACTION',            sortable: false}
];

const paymentform = {
    organization_id: '',
    customer_id: '',
    org_bank_id: '',
    invoice_id: '',
    invoice_no: '',
    payment_type:'',
    amount: '',
    check_number: '',
    bank_name: '',
    date: '',
    note: ''
};

const setOrganization = (id, name) => {
    paymentform.organization_id = id;
    const bank = props.bankinfo.filter(bank  => bank.organization_id === id);
    bankinfo.value = bank;
    form.errors[`data.organization_id`] = null;
};

const setPaymentType = (id, name) => {
    paymentform.payment_type = id;
    payment_type.value = name;
    form.errors[`data.payment_type`] = null;
};

const setBankInfo = (id, name) => {
    paymentform.org_bank_id = id;
    form.errors[`data.org_bank_id`] = null;
};

const openPaymentReceiveModal = (id) => {
    paymentform.organization_id = '';
    paymentform.customer_id = '';
    paymentform.org_bank_id = '';
    paymentform.invoice_id = '';
    paymentform.payment_type = '';
    paymentform.amount = '';
    paymentform.check_number = '';
    paymentform.bank_name = '';
    paymentform.date = '';
    paymentform.note = '';
    paymentReceiveModal.value = true;
    paymentform.customer_id = id;
};

const paymentReceiveCloseModal = () => {
    paymentReceiveModal.value = false;
};

const acceptPayment = () => {
    form.post(route('customers.advancepayment',{ data : paymentform}), {
        onSuccess: () => {
            form.reset();
            paymentReceiveModal.value = false;
        },
        onError: (errors) => {
            // console.log(errors);
        }
    });
};

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
};

</script>

<template>
    <Head title="Customers"/>
    <AdminLayout>
        <div class="animate-top">
            <!-- Header -->
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
                <div class="items-start">
                    <h1 class="text-xl sm:text-2xl font-semibold leading-7 text-gray-900">Customers</h1>
                </div>
                <div class="flex flex-col sm:flex-row gap-3 sm:gap-4">
                    <!-- Search -->
                    <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64">
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path>
                        </svg>
                        <input id="search-field" v-model="search" @input="fetchData" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                    </div>
                    <!-- Add Button -->
                    <div v-if="permissions.canCreateCustomer">
                        <CreateButton :href="route('customers.create')" class="w-full sm:w-auto">
                            Add Customer
                        </CreateButton>
                    </div>
                </div>
            </div>

            <div class="mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                            <tr class="border-b-2">
                                <th v-for="(column, index) in columns" :key="index" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer" @click="sort(column.field, column.sortable)">
                                    {{ column.label }}
                                    <ArrowIcon :isSorted="sortKey === column.field" :direction="sortDirection" v-if="column.sortable" />
                                </th>
                            </tr>
                        </thead>
                        <tbody v-if="data.data && (data.data.length > 0)">
                            <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(customerData, index) in data.data" :key="customerData.id">
                                <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36">
                                    {{ customerData.customer_name ?? '-' }}
                                </td>
                                <td class="px-4 py-2.5 min-w-36">
                                {{ customerData.contact_no ?? '-' }}
                                </td>
                                <td class="px-4 py-2.5 min-w-36">
                                    {{ customerData.email ?? '-' }}
                                </td>
                                <td class="px-4 py-2.5 min-w-32">
                                    {{ customerData.type ?? '-' }}
                                </td>
                                <td class="px-4 py-2.5 min-w-32">
                                    {{ customerData.city ?? '-' }}
                                </td>
                                <td class="px-4 py-2.5 min-w-36">
                                    {{ customerData.balance ?? '-' }}
                                </td>
                                <td class="items-center px-4 py-2.5">
                                    <SwitchButton :switchValue="customerData.status" :userId="customerData.id" @updateSwitchValue="updateSwitchValue">
                                    </SwitchButton>
                                </td>
                                <td class="items-center px-4 py-2.5">
                                    <div class="flex items-center justify-start gap-4">
                                        <Dropdown :align="'right'" width="48">
                                            <template #trigger>
                                                <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                    <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                        <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                    </svg>
                                                </button>
                                            </template>
                                            <template #content>
                                                <ActionLink :href="route('customers.edit',{id:customerData.id})" v-if="permissions.canEditCustomer">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                                />
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Edit
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <button type="button" @click="openDeleteModal(customerData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full" v-if="permissions.canDeleteCustomer">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                        />
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Delete
                                                    </span>
                                                </button>
                                                <ActionLink :href="route('customers.transaction',{id:customerData.id})" v-if="permissions.canTransactionCustomer">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 6l4 4M4 6l4-4M20 18H4m16 0l-4-4m4 4l-4 4"/>
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            View Transaction
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <ActionLink :href="route('customers.credit',{id:customerData.id})" v-if="permissions.canTransactionCustomer">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"></path>
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            View Credit
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <button type="button"  @click="openPaymentReceiveModal(customerData.id)"  class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"></path>
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Advance Payment
                                                    </span>
                                                </button>
                                                <ActionLink :href="route('service-reports.show',{id:customerData.id})" v-if="permissions.canTransactionCustomer">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3h14a2 2 0 012 2v14a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2zm2 5h10M7 11h10M7 15h10M7 19h10"/>
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Service Reports
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                            </template>
                                        </Dropdown>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr class="bg-white">
                            <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                No data found.
                            </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>
        <Modal :show="modalVisible" @close="closeModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteUser"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <Modal :show="paymentReceiveModal" @close="paymentReceiveCloseModal" :maxWidth="paymentReceiveWidth">
             <div class="p-6">
                <h2 class="text-2xl font-semibold leading-7 text-gray-900">Advance Payment</h2>
                    <div class="border-b border-gray-900/10 pb-12">
                        <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                            <div class="sm:col-span-3">
                                <InputLabel for="role_id" value="Organization" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="organization"
                                    v-model="paymentform.organization_id"
                                    @onchange="setOrganization"
                                    :class="{ 'error rounded-md': form.errors[`data.organization_id`] }"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="role_id" value="Payment Type" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="paymentType"
                                    v-model="paymentform.payment_type"
                                    @onchange="setPaymentType"
                                    :class="{ 'error rounded-md': form.errors[`data.payment_type`] }"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="amount" value="Amount" />
                                <TextInput
                                    id="amount"
                                    type="text"
                                    @change="clearError('data.amount')"
                                    v-model="paymentform.amount"
                                    :class="{ 'error rounded-md': form.errors[`data.amount`] }"
                                />
                            </div>
                            <div v-if="payment_type == 'Cheque'" class="sm:col-span-3">
                                <InputLabel for="check_number" value="Cheque Number" />
                                <TextInput
                                    id="check_number"
                                    type="text"
                                    v-model="paymentform.check_number"
                                    :class="{ 'error rounded-md': form.errors[`data.check_number`] }"
                                />
                            </div>
                            <div v-if="payment_type == 'Cheque'" class="sm:col-span-3">
                                <InputLabel for="bank_name" value="Our Bank" />
                                <TextInput
                                    id="bank_name"
                                    type="text"
                                    v-model="paymentform.bank_name"
                                    :class="{ 'error rounded-md': form.errors[`data.bank_name`] }"
                                />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="date" value="Payment Date" />
                                <input
                                    v-model="paymentform.date"
                                    class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    type="date"
                                     @change="clearError('data.date')"
                                    :class="{ 'error rounded-md': form.errors[`data.date`] }"
                                />
                            </div>
                            <div v-if="payment_type != 'Cash'" class="sm:col-span-3">
                                <InputLabel for="org_bank_id" value="Our Bank" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="bankinfo"
                                    v-model="paymentform.org_bank_id"
                                    @onchange="setBankInfo"
                                    :class="{ 'error rounded-md': form.errors[`data.org_bank_id`] }"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-6">
                                <InputLabel for="note" value="Note" />
                                <TextArea
                                    id="note"
                                    type="text"
                                    :rows="2"
                                    v-model="paymentform.note"
                                />
                            </div>
                        </div>
                    </div>
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="paymentReceiveCloseModal"> Cancel </SecondaryButton>
                    <div class="w-36">
                    <PrimaryButton
                        class="ml-3 w-20"
                        @click="acceptPayment"
                    >
                    Save
                    </PrimaryButton>
                    </div>
                </div>
             </div>
        </Modal>
    </AdminLayout>

</template>

<style scoped>
    .error {
        border: 1px solid red;
    }
</style>
