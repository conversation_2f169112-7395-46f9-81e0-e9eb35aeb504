import{h as u,j as f,o as a,e as g,w as i,a as o,u as e,Z as h,c as p,f as _,b as t,g as n,n as y,y as x,d as v}from"./app-b7a94f67.js";import{G as b}from"./GuestLayout-459b332f.js";import{P as k}from"./PrimaryButton-4ffecd1c.js";import"./_plugin-vue_export-helper-c27b6911.js";const V=t("h2",{class:"text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"},"Email Verification",-1),w=t("div",{class:"mb-4 mt-2 text-sm text-gray-500"}," Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another. ",-1),B={key:0,class:"mb-4 font-medium text-sm text-green-500"},E=["onSubmit"],N={class:"mt-4"},S={class:"text-center items-center mt-4"},P={__name:"VerifyEmail",props:{status:{type:String}},setup(r){const c=r,s=u({}),d=()=>{s.post(route("verification.send"))},l=f(()=>c.status==="verification-link-sent");return(m,C)=>(a(),g(b,null,{default:i(()=>[o(e(h),{title:"Email Verification"}),V,w,l.value?(a(),p("div",B," A new verification link has been sent to the email address you provided during registration. ")):_("",!0),t("form",{onSubmit:v(d,["prevent"])},[t("div",N,[o(k,{class:y({"opacity-25":e(s).processing}),disabled:e(s).processing},{default:i(()=>[n(" Resend Verification Email ")]),_:1},8,["class","disabled"])]),t("div",S,[o(e(x),{href:m.route("logout"),method:"post",as:"button",class:"font-semibold text-indigo-600 text-sm hover:text-indigo-500"},{default:i(()=>[n("Log Out")]),_:1},8,["href"])])],40,E)]),_:1}))}};export{P as default};
