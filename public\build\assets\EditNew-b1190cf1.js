import{r as V,C as Z,j as $,A as J,o as h,c as f,a as m,u as r,w as q,F as N,Z as K,b as e,d as W,t as _,g as D,n as y,e as A,f as C,k as X,v as ee,i as M,s as te,x as se}from"./app-4f4c883b.js";import{_ as oe}from"./AdminLayout-d9d2bc31.js";import{P as ae}from"./PrimaryButton-3e579b0b.js";import{_ as ce}from"./SecondaryButton-69637431.js";import{D as le}from"./DangerButton-b7cb11b9.js";import{_ as b}from"./TextInput-21f4f57b.js";import{_ as re}from"./DateInput-d3fd891a.js";import{_ as S}from"./InputLabel-468796e0.js";import{_ as ne}from"./SearchableDropdown-ace42120.js";import{M as ie}from"./Modal-85d770f4.js";import{u as de}from"./index-20fd5540.js";import{_ as _e}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const n=u=>(te("data-v-447272f8"),u=u(),se(),u),me={class:"animate-top"},ue=["onSubmit"],pe={class:"sm:flex sm:items-center"},ve=n(()=>e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Receive Product")],-1)),ge={class:"text-sm font-semibold text-gray-900"},he={class:"mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},ye={class:"inline-flex items-start space-x-6 justify-start w-full"},fe={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},xe={class:"inline-flex items-center justify-start w-full space-x-2"},be=n(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Company Name:",-1)),we={class:"text-sm leading-6 text-gray-700"},Pe={class:"inline-flex items-center justify-start w-full space-x-2"},Ve=n(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"GST No:",-1)),qe={class:"text-sm leading-6 text-gray-700"},Ce={class:"inline-flex items-center justify-start w-full space-x-2"},$e=n(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Email:",-1)),De={class:"text-sm leading-6 text-gray-700"},ke={class:"inline-flex items-center justify-start w-full space-x-2"},Fe=n(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1)),Ne={class:"text-sm leading-6 text-gray-700"},Se={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},je={class:"inline-flex items-center justify-start w-full space-x-2"},Ue=n(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Receive No:",-1)),Ie={class:"text-sm leading-6 text-gray-700"},Be={class:"inline-flex items-center justify-start w-full space-x-2"},Te=n(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Number:",-1)),Ae={class:"text-sm leading-6 text-gray-700"},Me={class:"inline-flex items-center justify-start w-full space-x-2"},Ee=n(()=>e("p",{class:"text-sm font-semibold text-gray-900 w-28"},"PO Date:",-1)),ze={class:"text-sm leading-6 text-gray-700"},Re={class:"mt-6 shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},Le={class:"grid grid-cols-1 gap-x-6 sm:grid-cols-12 pb-2"},Oe={class:"sm:col-span-4"},Qe={class:"sm:col-span-4"},Ge={class:"sm:col-span-4"},Ye={class:"relative mt-2"},He={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border divide-y divide-gray-300 overflow-x-auto"},Ze={class:"grid grid-cols-1 gap-x-4 sm:grid-cols-12 pb-2",style:{width:"140%"}},Je=n(()=>e("div",{class:"sm:col-span-2"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Product")],-1)),Ke={class:"sm:col-span-1"},We={key:0,class:"text-sm font-semibold text-gray-900 leading-6"},Xe={key:1,class:"text-sm font-semibold text-gray-900 leading-6"},et=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Price (₹)")],-1)),tt=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"GST %")],-1)),st=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"QTY")],-1)),ot=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Received QTY")],-1)),at=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"MRP (₹)")],-1)),ct=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Purchase Price (₹)")],-1)),lt=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"QTY")],-1)),rt=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Batch")],-1)),nt=n(()=>e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Total Amount")],-1)),it={class:"sm:col-span-2"},dt={class:"text-sm leading-5 text-gray-700"},_t={class:"sm:col-span-1"},mt={class:"text-sm leading-5 text-gray-700"},ut={class:"sm:col-span-1"},pt={class:"text-sm leading-5 text-gray-700"},vt={class:"sm:col-span-1"},gt={class:"text-sm leading-5 text-gray-700"},ht={class:"sm:col-span-1"},yt={class:"text-sm leading-5 text-gray-700"},ft={class:"sm:col-span-1"},xt={class:"text-sm leading-5 text-gray-700"},bt={class:"sm:col-span-1 mb-2"},wt={class:"sm:col-span-1 mb-2"},Pt={class:"sm:col-span-1 mb-2"},Vt={key:0,class:"text-red-500 text-xs absolute"},qt={class:"flex sm:col-span-1 mb-2"},Ct={class:"sm:col-span-1"},$t={class:"flex space-x-2"},Dt={class:"text-sm leading-5 text-gray-700"},kt=["onClick"],Ft=n(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Nt=[Ft],St={key:0,class:"sm:col-span-9 mb-2"},jt={class:"sm:col-span-3"},Ut={class:"sm:col-span-3"},It={class:"sm:col-span-3"},Bt={key:0,class:"text-red-500 text-xs absolute"},Tt={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},At={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Mt=n(()=>e("div",{class:"sm:col-span-3 space-y-2"},null,-1)),Et={class:"sm:col-span-3"},zt={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Rt={class:"inline-flex items-center justify-end w-full space-x-3"},Lt=n(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),Ot={class:"text-base font-semibold text-gray-900 w-32"},Qt={class:"inline-flex items-center justify-end w-full space-x-3"},Gt=n(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Total GST (₹):",-1)),Yt={class:"text-base font-semibold text-gray-900 w-32"},Ht={class:"inline-flex items-center justify-end w-full space-x-3"},Zt=n(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),Jt={class:"text-base font-semibold text-gray-900 w-32"},Kt={class:"flex mt-6 items-center justify-between"},Wt={class:"ml-auto flex items-center justify-end gap-x-6"},Xt={class:"p-6"},es=n(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to Remove this Purchase? ",-1)),ts={class:"mt-6 flex justify-end"},ss={__name:"EditNew",props:["data","po_receive_number","salesuser"],setup(u){const p=u,i=V([{organization_id:"",company_id:"",product_id:"",purchase_order_detail_id:"",purchase_order_id:"",qty:"",po_receive_number:"",total_qty:"",received_qty:"",mrp:"",purchase_price:"",total_amount:"",total_price:"",total_gst_amount:""}]),E=V();E.value=p.po_receive_number[p.data[0].purchase_order.organization.id];const z=(t,s)=>{const d=parseFloat(t==null?void 0:t.serial_numbers[0].purchase_price)||0,c=parseFloat(t==null?void 0:t.purchase_order_detail.gst)||0,o=parseFloat(t==null?void 0:t.receive_qty)||0;console.log("price",d),console.log("gst",c),console.log("qty",o),console.log("Product:",t);const l=d*o,v=l*(c/100);return{total_amount:(l+v).toFixed(2),total_price:l.toFixed(2),total_gst_amount:v.toFixed(2)}};Z(()=>{i.value=p.data[0].purchase_order_receive_details.map((t,s)=>{const d=z(t),c=t.serial_numbers.length>0?t.serial_numbers[0]:{};return{organization_id:p.data[0].organization_id,company_id:p.data[0].company_id,product_id:t.product_id,purchase_order_detail_id:t.purchase_order_detail_id,purchase_order_id:p.data[0].purchase_order_id,po_receive_number:p.po_receive_number[p.data[0].organization_id],total_qty:t.qty,received_qty:t.receive_qty,receive_qty:t.receive_qty,total_batch:t.serial_numbers.length,mrp:c.mrp??"",purchase_price:t.serial_numbers[0].purchase_price,total_amount:d.total_amount,total_price:d.total_price,total_gst_amount:d.total_gst_amount}}),w.value=p.data[0].purchase_order_receive_details.map(t=>t.serial_numbers.map(s=>({batch:s.batch||"",expiry_date:s.expiry_date||"",qty:s.receive_qty||""})))});const a=de("post","/purchaseinvoice",{purchase_order_receive_id:p.data[0].id,created_by:p.data[0].purchase_order.purchase_order_receives[0].created_by,total_price:"",total_gst_amount:"",total_amount:"",receivedProduct:[],customer_invoice_no:p.data[0].purchase_order.purchase_order_receives[0].customer_invoice_no,customer_invoice_date:p.data[0].purchase_order.purchase_order_receives[0].customer_invoice_date,category:p.data[0].purchase_order.category}),R=()=>{a.total_amount=U.value,a.total_gst_amount=I.value,a.total_price=B.value,a.receivedProduct=i.value.map((t,s)=>({...t,productDetails:w.value[s]||[]})),a.submit({preserveScroll:!0,onSuccess:()=>a.reset()})},x=t=>{a.errors[t]=null},w=V([]),L=t=>{a.errors["receivedProduct."+t+".receive_qty"]=null;const s=i.value[t].total_batch;i.value[t].total_qty,i.value[t].received_qty;const d=[];let c;if(s&&!isNaN(s)){c=s;for(let o=0;o<c;o++)d.push({batch:"",expiry_date:"",qty:""})}i.value[t].total_batch=c,w.value[t]=d};$(()=>{const t=new Date,s={year:"numeric",month:"long",day:"numeric"};return t.toLocaleDateString("en-US",s)});const O=(t,s)=>{a.created_by=t,a.errors.created_by=null},Q=(t,s)=>{const d=parseFloat(i.value[s].purchase_price),c=parseFloat(t.gst)||0,o=parseFloat(i.value[s].receive_qty),l=d*o*(1+c/100),v=d*o,g=d*o*(c/100);return i.value[s].total_price=isNaN(v)?"":parseFloat(v).toFixed(2),i.value[s].total_gst_amount=isNaN(g)?"":parseFloat(g).toFixed(2),isNaN(l)?"":parseFloat(l).toFixed(2)},j=(t,s)=>{i.value[s].total_amount=Q(t,s)},U=$(()=>i.value.reduce((t,s)=>t+(s.total_amount?parseFloat(s.total_amount):0),0)),I=$(()=>i.value.reduce((t,s)=>t+(s.total_gst_amount?parseFloat(s.total_gst_amount):0),0)),B=$(()=>i.value.reduce((t,s)=>t+(s.total_price?parseFloat(s.total_price):0),0)),G=t=>{const s=new Date(t),d={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",d)},P=t=>{const[s,d]=t.toFixed(2).toString().split(".");return s.replace(/\B(?=(\d{3})+(?!\d))/g,",")+(d?"."+d:"")},k=V(!1),T=V(null),F=()=>{k.value=!1},Y=()=>{a.get(route("removeproduct",{id:T.value,model:"InvoiceDetail"}),{onSuccess:()=>{F()}})},H=(t,s)=>{s!==void 0&&s!=""&&(T.value=s,k.value=!0)};return(t,s)=>{const d=J("InputError");return h(),f(N,null,[m(r(K),{title:"Company PO"}),m(oe,null,{default:q(()=>[e("div",me,[e("form",{onSubmit:W(R,["prevent"]),class:""},[e("div",pe,[ve,e("p",ge,_(u.data[0].purchase_order.organization.name),1)]),e("div",he,[e("div",ye,[e("div",fe,[e("div",xe,[be,e("p",we,_(u.data[0].purchase_order.company.name??"-"),1)]),e("div",Pe,[Ve,e("p",qe,_(u.data[0].purchase_order.company.gst_no??"-"),1)]),e("div",Ce,[$e,e("p",De,_(u.data[0].purchase_order.company.email??"-"),1)]),e("div",ke,[Fe,e("p",Ne,_(u.data[0].purchase_order.company.contact_no??"-"),1)])]),e("div",Se,[e("div",je,[Ue,e("p",Ie,_(u.data[0].po_receive_number??"-"),1)]),e("div",Be,[Te,e("p",Ae,_(u.data[0].purchase_order.po_number??"-"),1)]),e("div",Me,[Ee,e("p",ze,_(G(u.data[0].purchase_order.date)??"-"),1)])])])]),e("div",Re,[e("div",Le,[e("div",Oe,[m(S,{for:"customer_invoice_no",value:"Company Invoice No"}),D(" "+_(t.customer_invoice_no)+" ",1),m(b,{id:"customer_invoice_no",type:"text",modelValue:r(a).customer_invoice_no,"onUpdate:modelValue":s[0]||(s[0]=c=>r(a).customer_invoice_no=c),onChange:s[1]||(s[1]=c=>x("customer_invoice_no")),class:y({"error rounded-md":r(a).errors.customer_invoice_no})},null,8,["modelValue","class"]),r(a).invalid("customer_invoice_no")?(h(),A(d,{key:0,class:"",message:r(a).errors.customer_invoice_no},null,8,["message"])):C("",!0)]),e("div",Qe,[m(S,{for:"customer_invoice_date",value:"Company Invoice Date"}),X(e("input",{class:y(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":r(a).errors.customer_invoice_date}]),type:"date","onUpdate:modelValue":s[2]||(s[2]=c=>r(a).customer_invoice_date=c),onChange:s[3]||(s[3]=c=>x("customer_invoice_date"))},null,34),[[ee,r(a).customer_invoice_date]]),r(a).invalid("customer_invoice_date")?(h(),A(d,{key:0,class:"",message:r(a).errors.customer_invoice_date},null,8,["message"])):C("",!0)]),e("div",Ge,[m(S,{for:"company_name",value:"Received By:"}),e("div",Ye,[m(ne,{options:u.salesuser,modelValue:r(a).created_by,"onUpdate:modelValue":s[4]||(s[4]=c=>r(a).created_by=c),onOnchange:O,class:y({"error rounded-md":r(a).errors.created_by})},null,8,["options","modelValue","class"])])])])]),e("div",He,[e("div",Ze,[Je,e("div",Ke,[u.data[0].category=="Service"?(h(),f("p",We,"Part No")):(h(),f("p",Xe,"Product Code"))]),et,tt,st,ot,at,ct,lt,rt,nt]),(h(!0),f(N,null,M(u.data[0].purchase_order.purchase_order_detail,(c,o)=>(h(),f("div",{class:"grid grid-cols-1 gap-x-4 sm:grid-cols-12 items-center",style:{width:"140%"},key:o},[e("div",it,[e("p",dt,_(c.product.name??"-"),1)]),e("div",_t,[e("p",mt,_(c.product.item_code??"-"),1)]),e("div",ut,[e("p",pt,_(P(c.price)??"-"),1)]),e("div",vt,[e("p",gt,_(P(c.gst)??"-"),1)]),e("div",ht,[e("p",yt,_(c.qty??"-"),1)]),e("div",ft,[e("p",xt,_(c.receive_qty??"-"),1)]),e("div",bt,[m(b,{id:"gst",type:"text",modelValue:i.value[o].mrp,"onUpdate:modelValue":l=>i.value[o].mrp=l,class:y({error:r(a).errors[`receivedProduct.${o}.mrp`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","class"])]),e("div",wt,[m(b,{id:"gst",type:"text",modelValue:i.value[o].purchase_price,"onUpdate:modelValue":l=>i.value[o].purchase_price=l,onInput:l=>j(c,o),onChange:l=>x("receivedProduct."+o+".purchase_price"),class:y({error:r(a).errors[`receivedProduct.${o}.purchase_price`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),e("div",Pt,[m(b,{id:"gst",type:"text",modelValue:i.value[o].receive_qty,"onUpdate:modelValue":l=>i.value[o].receive_qty=l,numeric:!0,onChange:l=>x(`receivedProduct.${o}.receive_qty`),onInput:l=>j(c,o),class:y({error:r(a).errors[`receivedProduct.${o}.receive_qty`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","onChange","onInput","class"]),r(a).errors[`receivedProduct.${o}.receive_qty`]?(h(),f("p",Vt,_(r(a).errors[`receivedProduct.${o}.receive_qty`]),1)):C("",!0)]),e("div",qt,[m(b,{id:"gst",type:"text",numeric:!0,modelValue:i.value[o].total_batch,"onUpdate:modelValue":l=>i.value[o].total_batch=l,onChange:l=>L(o),class:y({error:r(a).errors[`receivedProduct.${o}.total_batch`]}),min:"1"},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),e("div",Ct,[e("div",$t,[e("p",Dt,_(i.value[o].total_amount),1),e("button",{type:"button",onClick:l=>H(o,c.id)},Nt,8,kt)])]),w.value[o]?(h(),f("div",St,[(h(!0),f(N,null,M(w.value[o],(l,v)=>(h(),f("div",{key:v,class:"grid grid-cols-1 gap-x-6 sm:grid-cols-12 items-center"},[e("div",jt,[m(b,{type:"text",modelValue:l.batch,"onUpdate:modelValue":g=>l.batch=g,placeholder:"Batch",onChange:g=>x("receivedProduct."+o+".productDetails."+v+".batch"),class:y({error:r(a).errors[`receivedProduct.${o}.productDetails.${v}.batch`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),e("div",Ut,[m(re,{modelValue:l.expiry_date,"onUpdate:modelValue":g=>l.expiry_date=g,onChange:g=>x("receivedProduct."+o+".productDetails."+v+".expiry_date"),class:y({error:r(a).errors[`receivedProduct.${o}.productDetails.${v}.expiry_date`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),e("div",It,[m(b,{type:"text",modelValue:l.qty,"onUpdate:modelValue":g=>l.qty=g,placeholder:"Qty",onChange:g=>x("receivedProduct."+o+".productDetails."+v+".qty"),class:y({error:r(a).errors[`receivedProduct.${o}.productDetails.${v}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])])]))),128)),r(a).errors[`receivedProduct.${o}.productDetails`]?(h(),f("p",Bt,_(r(a).errors[`receivedProduct.${o}.productDetails`]),1)):C("",!0)])):C("",!0)]))),128))]),e("div",Tt,[e("div",At,[Mt,e("div",Et,[e("div",zt,[e("div",Rt,[Lt,e("p",Ot,_(P(B.value)),1)]),e("div",Qt,[Gt,e("p",Yt,_(P(I.value)),1)]),e("div",Ht,[Zt,e("p",Jt,_(P(U.value)),1)])])])])]),e("div",Kt,[e("div",Wt,[m(ae,{class:y(["",{"opacity-25":r(a).processing}]),disabled:r(a).processing},{default:q(()=>[D(" Submit ")]),_:1},8,["class","disabled"])])])],40,ue)]),m(ie,{show:k.value,onClose:F},{default:q(()=>[e("div",Xt,[es,e("div",ts,[m(ce,{onClick:F},{default:q(()=>[D(" Cancel ")]),_:1}),m(le,{class:"ml-3",onClick:Y},{default:q(()=>[D(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64)}}},gs=_e(ss,[["__scopeId","data-v-447272f8"]]);export{gs as default};
