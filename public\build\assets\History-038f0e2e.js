import{r as y,h as F,o as p,c as m,a as l,u as A,w as x,F as f,Z as V,b as t,t as i,g as N,i as E}from"./app-b320a640.js";import{_ as C}from"./AdminLayout-aac65a75.js";import{_ as Y}from"./CreateButton-7506df4f.js";import{_ as z}from"./SearchableDropdownNew-eda97ecc.js";import{_ as I}from"./SimpleDropdown-736e6482.js";import{_ as g}from"./InputLabel-946d937b.js";const O={class:"animate-top"},D={class:"sm:flex sm:items-center"},T=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Product History")],-1),U={class:"w-auto"},$={class:"flex space-x-6 items-center"},H={class:"text-sm leading-6 text-gray-900 font-semibold"},L={class:"flex justify-end w-20"},P={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},j={class:"inline-flex flex-col space-y-1 items-start justify-end w-full"},R={class:"inline-flex items-center justify-end w-full space-x-2"},Z=t("p",{class:"text-base font-semibold text-gray-900 w-32"},"Product Name:",-1),G={class:"text-base leading-6 text-gray-900 font-semibold"},J={class:"text-base leading-6 text-gray-900 font-semibold"},K={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center"},M={class:"sm:col-span-4"},Q={class:"relative mt-2"},W={class:"sm:col-span-4"},X={class:"relative mt-2"},k={class:"mt-8 overflow-x-auto sm:rounded-lg"},ee={class:"shadow sm:rounded-lg"},te={class:"w-full text-sm text-left rtl:text-right text-gray-500"},ae=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"INVOICE NO"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"DATE"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"PARTICULAR"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"BATCH"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"RECEIVE"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"ISSUE"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"BALANCE")])],-1),se={key:0},ie={class:"whitespace-nowrap px-4 py-2.5"},oe={class:"whitespace-nowrap px-4 py-2.5"},ne={class:"whitespace-nowrap px-4 py-2.5 font-medium text-gray-900"},ce={class:"whitespace-nowrap px-4 py-2.5"},le={class:"whitespace-nowrap px-4 py-2.5"},re={class:"whitespace-nowrap px-4 py-2.5"},de={class:"whitespace-nowrap px-4 py-2.5"},_e={key:1},pe=t("tr",{class:"bg-white"},[t("td",{colspan:"8",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),me=[pe],ge={__name:"History",props:["data","balanceStock","page","product","organization","financialYears","selectedFinancialYear","openingBalance"],setup(a){const d=a,u=y(d.selectedFinancialYear),v=()=>{S.get(route("products.history",{id:d.product[0].id,organization_id:h.value,financial_year:u.value}),{preserveState:!0})},b=(n,s)=>{u.value=n,v()},h=y(),w=(n,s)=>{h.value=n,v()},r=n=>{const s=new Date(n),c={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",c)},S=F({}),q=y(d.page);console.log(d.openingBalance,"balance");const B=(n,s,c)=>{let e=parseFloat(d.openingBalance||0);for(let _=c.length-1;_>=s;_--){let o=c[_];o.receive_qty?e+=parseFloat(o.receive_qty):o.is_receive==="yes"?e+=parseFloat(o.qty):(o.invoice_id||o.is_receive==null)&&(e-=parseFloat(o.qty))}return e};return(n,s)=>(p(),m(f,null,[l(A(V),{title:"Product History"}),l(C,null,{default:x(()=>{var c;return[t("div",O,[t("div",D,[T,t("div",U,[t("div",$,[t("p",H,i(a.product[0].company.name),1),t("div",L,[l(Y,{href:q.value},{default:x(()=>[N(" Back ")]),_:1},8,["href"])])])])]),t("div",P,[t("div",j,[t("div",R,[Z,t("p",G,i(((c=a.product[0])==null?void 0:c.item_code)??""),1),t("p",J,i(a.product[0].name),1)])]),t("div",K,[t("div",M,[l(g,{for:"customer_id",value:"Organization Name"}),t("div",Q,[l(I,{options:a.organization,modelValue:h.value,"onUpdate:modelValue":s[0]||(s[0]=e=>h.value=e),onOnchange:w},null,8,["options","modelValue"])])]),t("div",W,[l(g,{for:"financial_year",value:"Financial Year"}),t("div",X,[l(z,{options:a.financialYears,modelValue:u.value,"onUpdate:modelValue":s[1]||(s[1]=e=>u.value=e),onOnchange:b},null,8,["options","modelValue"])])])])]),t("div",k,[t("div",ee,[t("table",te,[ae,a.balanceStock&&a.balanceStock.length>0?(p(),m("tbody",se,[(p(!0),m(f,null,E(a.balanceStock,(e,_)=>(p(),m("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",ie,i(e.purchase_order_receive_details?e.purchase_order_receive_details.purchase_order_receives.customer_invoice_no:e.invoice?e.invoice.invoice_no:e.challan?e.challan.challan_number:"-"),1),t("td",oe,i(e.purchase_order_receive_details?r(e.purchase_order_receive_details.purchase_order_receives.customer_invoice_date):e.invoice?e.is_receive=="yes"?r(e.creditnote.date):r(e.invoice.date):e.challan?e.is_receive=="yes"?r(e.created_at):r(e.challan.date):r(e.created_at)),1),t("td",ne,i(e.purchase_order_receive_details?e.purchase_order_receive_details.purchase_order_receives.purchase_order.company.name:e.invoice?e.invoice.customers.customer_name:e.challan?e.challan.customers.customer_name:"Stock Added"),1),t("td",ce,i(e.invoice?e.serialnumbers.unique_id:e.challan?e.viewserialnumbers.unique_id:e.unique_id),1),t("td",le,i(e.receive_qty?e.receive_qty:e.is_receive=="yes"?e.qty:"-"),1),t("td",re,i(e.invoice&&e.is_receive==null||e.challan&&e.is_receive==null?e.qty:"-"),1),t("td",de,i(B(e,_,a.balanceStock)),1)]))),128))])):(p(),m("tbody",_e,me))])])])])]}),_:1})],64))}};export{ge as default};
