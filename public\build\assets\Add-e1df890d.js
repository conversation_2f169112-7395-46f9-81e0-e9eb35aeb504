import{r as V,j as H,l as B,o as d,c as u,a as r,u as n,w as I,F as D,Z as te,b as t,t as c,f as y,d as se,n as h,k as oe,v as ae,i as L,g as ne,T as le,s as ie,x as re}from"./app-ce7743ab.js";import{_ as ce,a as de}from"./AdminLayout-6af2fc6a.js";import{_ as x}from"./InputLabel-3aa35471.js";import{P as ue}from"./PrimaryButton-6ff8a943.js";import{_ as E}from"./TextInput-65921831.js";import{_ as me}from"./TextArea-5fab1749.js";import{_ as pe}from"./RadioButton-2a9849cb.js";import{_ as M}from"./SearchableDropdown-6fd7fbbe.js";import{u as _e}from"./index-588ba5dc.js";/* empty css                                                                          */import{_ as ve}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as fe}from"./Checkbox-540f8602.js";const A=b=>(ie("data-v-ff0e97ce"),b=b(),re(),b),ye={class:"h-screen animate-top"},he={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},ge={class:"sm:flex sm:items-center"},xe=A(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Make Payment")],-1)),be={class:"flex items-center justify-between"},we={key:0,class:"text-base font-semibold leading-6 text-gray-900"},ke=["onSubmit"],Ve={class:"border-b border-gray-900/10 pb-12"},Fe={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},Ae={class:"sm:col-span-3"},Ce={class:"relative mt-2"},Se={class:"sm:col-span-3"},Ne={class:"relative mt-2"},Pe={class:"sm:col-span-2"},Ue={class:"relative mt-2"},$e={key:0,class:"sm:col-span-3"},ze={class:"relative mt-2"},Te={key:1,class:"sm:col-span-1"},Oe={key:2,class:"sm:col-span-2"},Ie={key:3,class:"sm:col-span-2"},De={class:"mt-4 flex justify-start"},Ee={class:"text-base font-semibold"},Me={key:4,class:"sm:col-span-3"},je={key:5,class:"sm:col-span-3"},Be={key:6,class:"sm:col-span-3"},Le={key:7,class:"sm:col-span-3"},Re={class:"relative mt-2"},Ye={class:"sm:col-span-6"},qe={class:"overflow-x-auto divide-y divide-gray-300 w-full"},He=A(()=>t("div",{class:"w-full"},[t("thead",{class:"w-full"},[t("tr",{class:""},[t("th",{scope:"col",class:""}),t("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),t("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),t("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1)),Ze={style:{"overflow-y":"auto","max-height":"318px"}},Ge={class:"divide-y divide-gray-300 bg-white"},Je={class:"whitespace-nowrap px-2 text-sm text-gray-900"},Ke={class:"text-sm text-gray-900 leading-6 py-1.5"},Qe={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},We={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Xe={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},et={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},tt={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},st={key:0,class:"text-red-500 text-xs absolute"},ot={class:"whitespace-nowrap px-2 text-sm text-gray-900"},at={class:"sm:col-span-2"},nt={class:"mt-2 p-3 bg-gray-50 rounded-md"},lt={class:"space-y-2 text-sm"},it={class:"flex items-center gap-2"},rt=A(()=>t("hr",{class:"my-2"},null,-1)),ct={class:"flex justify-between items-center font-semibold"},dt=A(()=>t("span",null,"Settlement:",-1)),ut={class:"flex justify-between items-center font-semibold"},mt=A(()=>t("span",null,"Advance Amount:",-1)),pt={key:0,class:"text-red-500 text-xs mt-1"},_t={key:8,class:"sm:col-span-6"},vt={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},ft=A(()=>t("thead",null,[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1)),yt={class:"divide-y divide-gray-300 bg-white"},ht={class:"whitespace-nowrap py-3 text-sm text-gray-900"},gt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},xt={class:"flex flex-col"},bt={class:"text-sm text-gray-900"},wt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},kt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},Vt={class:"flex mt-6 items-center justify-between"},Ft={class:"ml-auto flex items-center justify-end gap-x-6"},At=A(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),Ct={key:0,class:"text-sm text-gray-600"},St={__name:"Add",props:["paymentType","bankinfo","organization","companies","invoices","credit"],setup(b){const C=b;V([]);const s=_e("post","/payment",{organization_id:"",company_id:"",payment_type:"",date:"",note:"",amount:"",discount_amount:0,round_off:0,check_number:"",org_bank_id:"",invoice:[],settled_amount:"",advance_amount:"",is_credit:"",credit_data:[],total_unused_amount:""}),$=V(""),Z=()=>{s.settled_amount=N.value,s.advance_amount=P.value,s.total_unused_amount=S.value,s.is_credit=v.value,s.invoice=g.value,s.credit_data=w.value,s.submit({preserveScroll:!0,onSuccess:()=>s.reset()})},G=(a,o)=>{$.value=o,s.payment_type=a,s.errors.payment_type=null,o==="Cash"?s.note="Cash":s.note==="Cash"&&(s.note="")},z=V([]),R=V([]),w=V([]),S=V(""),J=(a,o)=>{s.organization_id=a,s.errors.organization_id=null;const e=C.bankinfo.filter(i=>i.organization_id===a);R.value=e,s.company_id&&Y(s.company_id,a);const l=C.credit.filter(i=>i.organization_id===a&&i.company_id===s.company_id);w.value=l,S.value=w.value.reduce((i,m)=>i+m.unused_amount,0)},K=(a,o)=>{s.company_id=a,Y(a,s.organization_id);const e=C.credit.filter(l=>l.company_id===a&&l.organization_id===s.organization_id);w.value=e,S.value=w.value.reduce((l,i)=>l+i.unused_amount,0),s.errors.company_id=null},Y=(a,o)=>{if(!a||!o){z.value=[];return}const e=C.companies.find(m=>m.id===a),l=e==null?void 0:e.party_id,i=C.invoices.filter(m=>{const f=m.organization_id===o;return m.invoice_type==="purchase"?f&&m.company_id===a:m.invoice_type==="sales"&&l?f&&m.party_id===l:!1});z.value=i},Q=(a,o)=>{s.org_bank_id=a,s.errors.org_bank_id=null},N=H(()=>{const a=g.value.reduce((o,e)=>{if(e.check&&e.amount){const l=parseFloat(e.amount);return e.invoice_type==="purchase"?o+l:o-l}return o},0);return parseFloat(a.toFixed(2))}),P=H(()=>{const a=parseFloat(s.amount||0),o=parseFloat(s.round_off||0),e=N.value;return a>e?a-e-o:0}),F=a=>{let o=a.toFixed(2).toString(),[e,l]=o.split("."),i=e.substring(e.length-3),m=e.substring(0,e.length-3);return m!==""&&(i=","+i),`${m.replace(/\B(?=(\d{2})+(?!\d))/g,",")+i}.${l}`},q=a=>{const o=new Date(a),e={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",e)},W=(a,o)=>{const e=v.value==="Yes"?parseFloat(S.value||0):parseFloat(s.amount||0)+parseFloat(s.round_off||0);if(o===void 0&&(o=g.value.findIndex(_=>_.check===a.target.checked)),o===-1||o>=g.value.length)return;if(!g.value[o].check){g.value[o].amount=0;return}const l=g.value.filter(_=>_.check),i=l.some(_=>_.invoice_type==="purchase"),m=l.some(_=>_.invoice_type==="sales");if(i&&m&&l.length>1&&X(e,l))return;let f=e;g.value.forEach((_,O)=>{if(_.check&&O!==o&&parseFloat(_.amount||0)>0){const U=parseFloat(_.amount||0);_.invoice_type==="purchase"?f-=U:_.invoice_type==="sales"&&(f+=U)}});const p=parseFloat(g.value[o].pending_amount||0),k=Math.min(p,Math.max(0,f));g.value[o].amount=k.toFixed(2)},X=(a,o)=>{const e=o.filter(p=>p.invoice_type==="purchase"),l=o.filter(p=>p.invoice_type==="sales"),i=e.reduce((p,k)=>p+parseFloat(k.pending_amount||0),0),m=l.reduce((p,k)=>p+parseFloat(k.pending_amount||0),0),f=i-m;return Math.abs(a-Math.abs(f))<=1?(o.forEach(p=>{p.amount=parseFloat(p.pending_amount||0).toFixed(2)}),!0):f>0&&a>=f?(o.forEach(p=>{p.amount=parseFloat(p.pending_amount||0).toFixed(2)}),!0):!1},g=V([{id:"",date:"",invoice_no:"",total_amount:"",pending_amount:"",check:!1,amount:0}]),v=V("No"),ee=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],j=()=>{g.value=z.value.map(a=>({id:a.id,date:a.date,invoice_no:a.invoice_no,total_amount:parseFloat(a.total_amount||0).toFixed(2),pending_amount:parseFloat(a.pending_amount||0).toFixed(2),invoice_type:a.invoice_type||"purchase",check:!1,amount:"0.00"}))};B(z,()=>{j()}),B(v,()=>{j()}),B(()=>s.amount,()=>{v.value==="No"&&j()});const T=a=>{s.errors[a]=null,s.errors.settled_amount=null};return(a,o)=>(d(),u(D,null,[r(n(te),{title:"Payment"}),r(ce,null,{default:I(()=>[t("div",ye,[t("div",he,[t("div",ge,[xe,t("div",be,[w.value.length>0?(d(),u("div",we," Credits Available: ₹"+c(F(S.value)),1)):y("",!0)])]),t("form",{onSubmit:se(Z,["prevent"]),class:""},[t("div",Ve,[t("div",Fe,[t("div",Ae,[r(x,{for:"payment_type",value:"Organization"}),t("div",Ce,[r(M,{options:b.organization,modelValue:n(s).organization_id,"onUpdate:modelValue":o[0]||(o[0]=e=>n(s).organization_id=e),onOnchange:J,class:h({"error rounded-md":n(s).errors.organization_id})},null,8,["options","modelValue","class"])])]),t("div",Se,[r(x,{for:"payment_type",value:"Company"}),t("div",Ne,[r(M,{options:b.companies,modelValue:n(s).company_id,"onUpdate:modelValue":o[1]||(o[1]=e=>n(s).company_id=e),onOnchange:K,class:h({"error rounded-md":n(s).errors.company_id})},null,8,["options","modelValue","class"])])]),t("div",Pe,[r(x,{for:"role_id",value:"Payment Through Credit ?"}),t("div",Ue,[r(pe,{modelValue:v.value,"onUpdate:modelValue":o[2]||(o[2]=e=>v.value=e),options:ee},null,8,["modelValue"])])]),v.value=="No"?(d(),u("div",$e,[r(x,{for:"payment_type",value:"Payment Type"}),t("div",ze,[r(M,{options:b.paymentType,modelValue:n(s).payment_type,"onUpdate:modelValue":o[3]||(o[3]=e=>n(s).payment_type=e),onOnchange:G,class:h({"error rounded-md":n(s).errors.payment_type})},null,8,["options","modelValue","class"])])])):y("",!0),v.value=="No"?(d(),u("div",Te,[r(x,{for:"round_off",value:"Round Off"}),r(E,{type:"text",onChange:o[4]||(o[4]=e=>T("round_off")),modelValue:n(s).round_off,"onUpdate:modelValue":o[5]||(o[5]=e=>n(s).round_off=e),class:h({"error rounded-md":n(s).errors.round_off})},null,8,["modelValue","class"])])):y("",!0),v.value=="No"?(d(),u("div",Oe,[r(x,{for:"amount",value:"Amount"}),r(E,{id:"amount",type:"text",onChange:o[6]||(o[6]=e=>T("amount")),modelValue:n(s).amount,"onUpdate:modelValue":o[7]||(o[7]=e=>n(s).amount=e),class:h({"error rounded-md":n(s).errors.amount})},null,8,["modelValue","class"])])):y("",!0),v.value=="No"?(d(),u("div",Ie,[r(x,{for:"advance",value:"Advance(Ref) Amount"}),t("div",De,[t("p",Ee,c(F(P.value)),1)])])):y("",!0),$.value=="Cheque"&&v.value=="No"?(d(),u("div",Me,[r(x,{for:"check_number",value:"Cheque Number"}),r(E,{id:"check_number",type:"text",modelValue:n(s).check_number,"onUpdate:modelValue":o[8]||(o[8]=e=>n(s).check_number=e),class:h({"error rounded-md":n(s).errors["data.check_number"]})},null,8,["modelValue","class"])])):y("",!0),v.value=="No"?(d(),u("div",je,[r(x,{for:"date",value:"Payment Date"}),oe(t("input",{"onUpdate:modelValue":o[9]||(o[9]=e=>n(s).date=e),class:h(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":n(s).errors.date}]),type:"date",onChange:o[10]||(o[10]=e=>T("date"))},null,34),[[ae,n(s).date]])])):y("",!0),$.value=="Cash"&&v.value=="No"?(d(),u("div",Be)):y("",!0),$.value!="Cash"&&v.value=="No"?(d(),u("div",Le,[r(x,{for:"org_bank_id",value:"Our Bank"}),t("div",Re,[r(M,{options:R.value,modelValue:n(s).org_bank_id,"onUpdate:modelValue":o[11]||(o[11]=e=>n(s).org_bank_id=e),onOnchange:Q,class:h({"error rounded-md":n(s).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):y("",!0),t("div",Ye,[t("table",qe,[He,t("div",Ze,[t("tbody",Ge,[(d(!0),u(D,null,L(g.value,(e,l)=>(d(),u("tr",{key:l},[t("td",Je,[t("div",Ke,[r(fe,{name:"check",checked:e.check,"onUpdate:checked":i=>e.check=i,onChange:i=>W(i,l)},null,8,["checked","onUpdate:checked","onChange"])])]),t("td",Qe,[t("span",{class:h([e.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},c(e.invoice_type==="purchase"?"PURCHASE":"SALES"),3)]),t("td",We,c(e.invoice_no),1),t("td",Xe,c(e.total_amount),1),t("td",et,c(e.pending_amount),1),t("td",tt,[r(E,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":i=>e.amount=i,onChange:i=>T("invoice."+l+".amount"),class:h({error:n(s).errors[`invoice.${l}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),n(s).errors[`invoice.${l}.amount`]?(d(),u("p",st,c(n(s).errors[`invoice.${l}.amount`]),1)):y("",!0)]),t("td",ot,c(q(e.date)),1)]))),128))])])])]),t("div",at,[r(x,{for:"note",value:"Net Settlement Summary"}),t("div",nt,[t("div",lt,[(d(!0),u(D,null,L(g.value.filter(e=>e.check),e=>(d(),u("div",{key:e.id,class:"flex justify-between items-center"},[t("div",it,[t("span",{class:h([e.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium"])},c(e.invoice_type==="purchase"?"P":"S"),3),t("span",null,c(e.invoice_no),1)]),t("span",{class:h([e.invoice_type==="purchase"?"text-blue-600":"text-green-600","font-medium"])},c(e.invoice_type==="purchase"?"+":"-")+"₹"+c(F(parseFloat(e.amount||0))),3)]))),128)),rt,t("div",ct,[dt,t("span",{class:h(N.value>=0?"text-blue-600":"text-red-600")}," ₹"+c(F(Math.abs(N.value)))+" "+c(N.value>=0?"(Pay)":"(Receive)"),3)]),t("div",ut,[mt,t("span",{class:h(P.value>=0?"text-green-600":"text-red-600")},c(P.value>0?"+":"")+"₹"+c(F(parseFloat(P.value||0))),3)])])]),n(s).errors.settled_amount?(d(),u("p",pt,c(n(s).errors.settled_amount),1)):y("",!0)]),v.value=="No"?(d(),u("div",_t,[r(x,{for:"note",value:"Note"}),r(me,{id:"note",type:"text",rows:2,modelValue:n(s).note,"onUpdate:modelValue":o[12]||(o[12]=e=>n(s).note=e)},null,8,["modelValue"])])):y("",!0)]),w.value.length>0&&v.value=="Yes"?(d(),u("table",vt,[ft,t("tbody",yt,[(d(!0),u(D,null,L(w.value,(e,l)=>{var i,m,f,p,k,_,O,U;return d(),u("tr",{key:l},[t("td",ht,c(q(e.date)),1),t("td",gt,[t("div",xt,[t("div",bt,c((m=(i=e.paymentpaid)==null?void 0:i.bank_info)!=null&&m.bank_name?(p=(f=e.paymentpaid)==null?void 0:f.bank_info)==null?void 0:p.bank_name:"Cash")+" - "+c((_=(k=e.paymentpaid)==null?void 0:k.bank_info)!=null&&_.account_number?(U=(O=e.paymentpaid)==null?void 0:O.bank_info)==null?void 0:U.account_number:""),1)])]),t("td",wt,c(F(e.amount)),1),t("td",kt,c(F(e.unused_amount)),1)])}),128))])])):y("",!0)]),t("div",Vt,[t("div",Ft,[r(de,{href:a.route("receipt.index")},{svg:I(()=>[At]),_:1},8,["href"]),r(ue,{disabled:n(s).processing},{default:I(()=>[ne("Save")]),_:1},8,["disabled"]),r(le,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:I(()=>[n(s).recentlySuccessful?(d(),u("p",Ct,"Saved.")):y("",!0)]),_:1})])])],40,ke)])])]),_:1})],64))}},Bt=ve(St,[["__scopeId","data-v-ff0e97ce"]]);export{Bt as default};
