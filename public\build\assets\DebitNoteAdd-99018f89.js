import{r as q,C as W,j as b,o as g,c as v,a as m,u as d,w as T,F as Q,Z as X,b as e,d as ee,t as n,g as L,k as C,v as O,n as j,e as A,f as x,D as Y,i as te}from"./app-b7a94f67.js";import{_ as se,a as oe}from"./AdminLayout-0f1fdf67.js";import{_ as ae}from"./CreateButton-fedd28a2.js";import{P as ne}from"./PrimaryButton-4ffecd1c.js";import{_ as P}from"./TextInput-fea73171.js";import{_ as k}from"./InputLabel-11b5d690.js";import{_ as V}from"./InputError-86b88c86.js";import{u as ie}from"./index-5a4eda7d.js";import"./_plugin-vue_export-helper-c27b6911.js";const de={class:"animate-top"},le=["onSubmit"],ce={class:"sm:flex sm:items-center"},re=e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create Debit Note")],-1),ue={class:"flex items-center space-x-4"},me={class:"text-sm font-semibold text-gray-900"},_e={class:"flex justify-end w-20"},pe={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},ge={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},he=e("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Debit Note Information",-1),fe={class:"grid grid-cols-1 gap-x-6 sm:grid-cols-6 pb-2"},ve={class:"sm:col-span-2"},xe={class:"sm:col-span-2"},ye={class:"sm:col-span-2"},be={class:"sm:col-span-6 mt-4"},we=e("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Purchase Invoice Information",-1),Ne={class:"space-y-3 bg-gray-50 p-4 rounded-lg"},Se={class:"flex justify-between"},Te=e("span",{class:"text-sm text-gray-700"},"Invoice No:",-1),Ce={class:"font-medium text-sm"},je={class:"flex justify-between"},ke=e("span",{class:"text-sm text-gray-700"},"Date:",-1),Ve={class:"font-medium text-sm"},De={class:"flex justify-between"},Ge=e("p",{class:"text-sm text-gray-700"},"Company Name:",-1),qe={class:"font-medium text-sm"},Ae={class:"flex justify-between"},Pe=e("p",{class:"text-sm text-gray-700"},"GST No:",-1),Ie={class:"font-medium text-sm"},$e={class:"flex justify-between"},Ee=e("p",{class:"text-sm text-gray-700"},"Email:",-1),Be={class:"font-medium text-sm"},Fe={class:"flex justify-between"},Ue=e("p",{class:"text-sm text-gray-700"},"Contact No:",-1),ze={class:"font-medium text-sm"},Me={class:"flex justify-between"},Qe=e("span",{class:"text-sm text-gray-700"},"Invoice Amount:",-1),Le={class:"font-medium text-sm"},Oe={key:0,class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border divide-y divide-gray-300 overflow-x-auto"},Ye={class:"grid grid-cols-1 gap-x-4 sm:grid-cols-12 pb-2",style:{width:"160%"}},He={class:"sm:col-span-1"},Re=e("div",{class:"sm:col-span-2"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Product")],-1),Ze=e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Batch")],-1),Je=e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Expiry Date")],-1),Ke=e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Price (₹)")],-1),We=e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"GST %")],-1),Xe=e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Available QTY")],-1),et=e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Debit QTY")],-1),tt=e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Total Price (₹)")],-1),st=e("div",{class:"sm:col-span-1"},[e("p",{class:"text-sm font-semibold text-gray-900 leading-6"},"Total Amount (₹)")],-1),ot={class:"sm:col-span-1"},at=["onUpdate:modelValue","onChange"],nt={class:"sm:col-span-2"},it={class:"text-sm leading-5 text-gray-700"},dt={class:"sm:col-span-1"},lt={class:"text-sm leading-5 text-gray-700"},ct={class:"sm:col-span-1"},rt={class:"text-sm leading-5 text-gray-700"},ut={class:"sm:col-span-1"},mt={class:"text-sm leading-5 text-gray-700"},_t={class:"sm:col-span-1"},pt={class:"text-sm leading-5 text-gray-700"},gt={class:"sm:col-span-1"},ht={class:"text-sm leading-5 text-gray-700"},ft={class:"sm:col-span-1 mb-2"},vt={class:"sm:col-span-1"},xt={class:"text-sm leading-5 text-gray-700"},yt={class:"sm:col-span-1"},bt={class:"text-sm leading-5 text-gray-700"},wt={key:1,class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Nt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},St={class:"sm:col-span-3 space-y-2"},Tt={class:"bg-gray-50 p-4 rounded-lg"},Ct=e("h3",{class:"text-sm font-semibold text-gray-900 mb-2"},"Product History",-1),jt={class:"text-xs text-gray-600"},kt={class:"sm:col-span-3"},Vt={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Dt={class:"inline-flex items-center justify-end w-full space-x-3"},Gt=e("p",{class:"text-sm text-gray-700"},"Sub Total (₹):",-1),qt={class:"text-base font-semibold text-gray-900 w-32"},At={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},Pt=e("p",{class:"text-sm text-gray-700"},"CGST (₹):",-1),It={class:"text-base font-semibold text-gray-900 w-32"},$t={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},Et=e("p",{class:"text-sm text-gray-700"},"SGST (₹):",-1),Bt={class:"text-base font-semibold text-gray-900 w-32"},Ft={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},Ut=e("p",{class:"text-sm text-gray-700"},"IGST (₹):",-1),zt={class:"text-base font-semibold text-gray-900 w-32"},Mt={class:"inline-flex items-center justify-end w-full space-x-3"},Qt=e("p",{class:"text-sm text-gray-700"},"Total GST (₹):",-1),Lt={class:"text-base font-semibold text-gray-900 w-32"},Ot={class:"inline-flex items-center justify-end w-full space-x-3 border-t pt-2"},Yt=e("p",{class:"font-semibold text-gray-900"},"Total Amount (₹):",-1),Ht={class:"text-base font-semibold text-gray-900 w-32"},Rt={class:"flex mt-6 items-center justify-between"},Zt={class:"ml-auto flex items-center justify-end gap-x-6"},Jt=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1),is={__name:"DebitNoteAdd",props:["data","debit_no","serialNumbers"],setup(l){const i=l,u=q({}),c=q({}),S=q(!1);W(()=>{i.serialNumbers&&i.serialNumbers.forEach(o=>{u.value[o.id]=!1,c.value[o.id]=0})});const a=ie("post","/debitnotesave",{organization_id:i.data[0].purchase_order.organization_id,company_id:i.data[0].purchase_order.company_id,purchase_invoice_id:i.data[0].id,debit_note_no:i.debit_no[i.data[0].purchase_order.organization_id],credit_note_number:"",date:new Date().toISOString().split("T")[0],reason:"",selectedProductItem:[],sub_total:"",cgst:"",sgst:"",igst:"",total_gst:"",total_amount:""}),H=()=>{a.sub_total=$.value,a.total_gst=w.value,a.cgst=E.value,a.sgst=B.value,a.igst=F.value,a.total_amount=I.value,a.selectedProductItem=J(),a.submit({preserveScroll:!0,onSuccess:()=>a.reset()})},D=o=>{a.errors[o]=null},R=()=>{i.serialNumbers&&i.serialNumbers.forEach(o=>{u.value[o.id]=S.value,S.value?c.value[o.id]=o.receive_qty-o.sell_qty:c.value[o.id]=0})},Z=o=>{if(!u.value[o])c.value[o]=0;else{const t=i.serialNumbers.find(r=>r.id===o);t&&(c.value[o]=t.receive_qty-t.sell_qty)}},G=o=>{var h,y;if(!u.value[o.id]||!c.value[o.id])return 0;const t=c.value[o.id],r=parseFloat(o.purchase_price)||0,_=parseFloat((y=(h=o.purchase_order_receive_details)==null?void 0:h.purchase_order_detail)==null?void 0:y.gst)||0,p=r*t,s=p*(_/100);return p+s},J=()=>{const o=[];return i.serialNumbers&&i.serialNumbers.forEach(t=>{u.value[t.id]&&c.value[t.id]>0&&o.push({serial_number_id:t.id,purchase_detail_id:t.purchase_order_receive_detail_id,product_id:t.product_id,qty:c.value[t.id],purchase_price:t.purchase_price,batch:t.batch,expiry_date:t.expiry_date})}),o},I=b(()=>{let o=0;return i.serialNumbers&&i.serialNumbers.forEach(t=>{o+=G(t)}),o}),$=b(()=>{let o=0;return i.serialNumbers&&i.serialNumbers.forEach(t=>{if(u.value[t.id]&&c.value[t.id]>0){const r=c.value[t.id],_=parseFloat(t.purchase_price)||0;o+=_*r}}),o}),w=b(()=>{let o=0;return i.serialNumbers&&i.serialNumbers.forEach(t=>{var r,_;if(u.value[t.id]&&c.value[t.id]>0){const p=c.value[t.id],s=parseFloat(t.purchase_price)||0,h=parseFloat((_=(r=t.purchase_order_receive_details)==null?void 0:r.purchase_order_detail)==null?void 0:_.gst)||0;o+=s*p*(h/100)}}),o}),E=b(()=>i.data[0].purchase_order.company.gst_type==="CGST/SGST"?w.value/2:0),B=b(()=>i.data[0].purchase_order.company.gst_type==="CGST/SGST"?w.value/2:0),F=b(()=>i.data[0].purchase_order.company.gst_type==="IGST"?w.value:0),f=o=>{let t=o.toFixed(2).toString(),[r,_]=t.split("."),p=r.substring(r.length-3),s=r.substring(0,r.length-3);return s!==""&&(p=","+p),`${s.replace(/\B(?=(\d{2})+(?!\d))/g,",")+p}.${_}`},K=o=>{const t=new Date(o),r={year:"numeric",month:"short",day:"numeric"};return t.toLocaleDateString("en-US",r)};return(o,t)=>(g(),v(Q,null,[m(d(X),{title:"Create Debit Note"}),m(se,null,{default:T(()=>{var r,_,p;return[e("div",de,[e("form",{onSubmit:ee(H,["prevent"]),class:""},[e("div",ce,[re,e("div",ue,[e("div",null,[e("p",me,n(l.data[0].purchase_order.organization.name),1)]),e("div",_e,[m(ae,{href:o.route("purchaseinvoice.index")},{default:T(()=>[L(" Back ")]),_:1},8,["href"])])])]),e("div",pe,[e("div",ge,[e("div",null,[he,e("div",fe,[e("div",ve,[m(k,{for:"debit_note_no",value:"Debit Note No"}),m(P,{id:"debit_note_no",type:"text",modelValue:d(a).debit_note_no,"onUpdate:modelValue":t[0]||(t[0]=s=>d(a).debit_note_no=s),readonly:"",class:"bg-gray-100"},null,8,["modelValue"]),m(V,{class:"",message:d(a).errors.debit_note_no},null,8,["message"])]),e("div",xe,[m(k,{for:"date",value:"Date"}),C(e("input",{class:j(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":d(a).errors.date}]),type:"date","onUpdate:modelValue":t[1]||(t[1]=s=>d(a).date=s),onChange:t[2]||(t[2]=s=>D("date"))},null,34),[[O,d(a).date]]),d(a).invalid("date")?(g(),A(V,{key:0,class:"",message:d(a).errors.date},null,8,["message"])):x("",!0)]),e("div",ye,[m(k,{for:"credit_note_number",value:"Credit Note Number"}),m(P,{id:"credit_note_number",type:"text",modelValue:d(a).credit_note_number,"onUpdate:modelValue":t[3]||(t[3]=s=>d(a).credit_note_number=s),onChange:t[4]||(t[4]=s=>D("credit_note_number")),class:j({"error rounded-md":d(a).errors.credit_note_number}),required:""},null,8,["modelValue","class"]),d(a).invalid("credit_note_number")?(g(),A(V,{key:0,class:"",message:d(a).errors.credit_note_number},null,8,["message"])):x("",!0)]),e("div",be,[m(k,{for:"reason",value:"Reason for Debit Note"}),C(e("textarea",{id:"reason",class:j(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":d(a).errors.reason}]),rows:"4","onUpdate:modelValue":t[5]||(t[5]=s=>d(a).reason=s),onChange:t[6]||(t[6]=s=>D("reason")),required:"",placeholder:"Enter the reason for creating this debit note..."},null,34),[[O,d(a).reason]]),d(a).invalid("reason")?(g(),A(V,{key:0,class:"",message:d(a).errors.reason},null,8,["message"])):x("",!0)])])]),e("div",null,[we,e("div",Ne,[e("div",Se,[Te,e("span",Ce,n(((r=l.data[0])==null?void 0:r.customer_invoice_no)||"-"),1)]),e("div",je,[ke,e("span",Ve,n(K((_=l.data[0])==null?void 0:_.customer_invoice_date)||"-"),1)]),e("div",De,[Ge,e("p",qe,n(l.data[0].purchase_order.company.name??"-")+" "+n(l.data[0].purchase_order.company.city??""),1)]),e("div",Ae,[Pe,e("p",Ie,n(l.data[0].purchase_order.company.gst_no??"-"),1)]),e("div",$e,[Ee,e("p",Be,n(l.data[0].purchase_order.company.email??"-"),1)]),e("div",Fe,[Ue,e("p",ze,n(l.data[0].purchase_order.company.contact_no??"-"),1)]),e("div",Me,[Qe,e("span",Le,"₹"+n(((p=l.data[0])==null?void 0:p.total_amount)||0),1)])])])])]),l.serialNumbers&&l.serialNumbers.length>0?(g(),v("div",Oe,[e("div",Ye,[e("div",He,[C(e("input",{type:"checkbox","onUpdate:modelValue":t[7]||(t[7]=s=>S.value=s),onChange:R,class:"rounded"},null,544),[[Y,S.value]])]),Re,Ze,Je,Ke,We,Xe,et,tt,st]),(g(!0),v(Q,null,te(l.serialNumbers,(s,h)=>{var y,U,z,M;return g(),v("div",{class:"grid grid-cols-1 gap-x-4 sm:grid-cols-12 items-center py-2",style:{width:"160%"},key:h},[e("div",ot,[C(e("input",{type:"checkbox","onUpdate:modelValue":N=>u.value[s.id]=N,onChange:N=>Z(s.id),class:"rounded"},null,40,at),[[Y,u.value[s.id]]])]),e("div",nt,[e("p",it,n(((y=s.product)==null?void 0:y.item_code)??"")+" "+n(((U=s.product)==null?void 0:U.name)??"-"),1)]),e("div",dt,[e("p",lt,n(s.batch??"-"),1)]),e("div",ct,[e("p",rt,n(s.expiry_date??"-"),1)]),e("div",ut,[e("p",mt,n(f(s.purchase_price)??"-"),1)]),e("div",_t,[e("p",pt,n(((M=(z=s.purchase_order_receive_details)==null?void 0:z.purchase_order_detail)==null?void 0:M.gst)??"-")+"%",1)]),e("div",gt,[e("p",ht,n(s.receive_qty-s.sell_qty),1)]),e("div",ft,[m(P,{type:"number",modelValue:c.value[s.id],"onUpdate:modelValue":N=>c.value[s.id]=N,onInput:N=>G(s),disabled:!u.value[s.id],min:"0",max:s.receive_qty-s.sell_qty,class:"w-20"},null,8,["modelValue","onUpdate:modelValue","onInput","disabled","max"])]),e("div",vt,[e("p",xt,n(u.value[s.id]&&c.value[s.id]>0?f(parseFloat(s.purchase_price)*c.value[s.id]):"0.00"),1)]),e("div",yt,[e("p",bt,n(u.value[s.id]&&c.value[s.id]>0?f(G(s)):"0.00"),1)])])}),128))])):x("",!0),l.serialNumbers&&l.serialNumbers.length>0?(g(),v("div",wt,[e("div",Nt,[e("div",St,[e("div",Tt,[Ct,e("div",jt,[e("p",null,"Total Products: "+n(l.serialNumbers.length),1),e("p",null,"Available Quantity: "+n(l.serialNumbers.reduce((s,h)=>s+(h.receive_qty-h.sell_qty),0)),1),e("p",null,"Selected Items: "+n(Object.values(u.value).filter(Boolean).length),1)])])]),e("div",kt,[e("div",Vt,[e("div",Dt,[Gt,e("p",qt,n(f($.value)),1)]),l.data[0].purchase_order.company.gst_type==="CGST/SGST"?(g(),v("div",At,[Pt,e("p",It,n(f(E.value)),1)])):x("",!0),l.data[0].purchase_order.company.gst_type==="CGST/SGST"?(g(),v("div",$t,[Et,e("p",Bt,n(f(B.value)),1)])):x("",!0),l.data[0].purchase_order.company.gst_type==="IGST"?(g(),v("div",Ft,[Ut,e("p",zt,n(f(F.value)),1)])):x("",!0),e("div",Mt,[Qt,e("p",Lt,n(f(w.value)),1)]),e("div",Ot,[Yt,e("p",Ht,n(f(I.value)),1)])])])])])):x("",!0),e("div",Rt,[e("div",Zt,[m(oe,{href:o.route("purchaseinvoice.index")},{svg:T(()=>[Jt]),_:1},8,["href"]),m(ne,{class:j({"opacity-25":d(a).processing}),disabled:d(a).processing},{default:T(()=>[L(" Submit ")]),_:1},8,["class","disabled"])])])],40,le)])]}),_:1})],64))}};export{is as default};
