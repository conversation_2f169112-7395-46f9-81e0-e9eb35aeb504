import{_ as o}from"./AdminLayout-b73e8538.js";import i from"./DeleteUserForm-64b397ec.js";import m from"./UpdatePasswordForm-11e967b4.js";import r from"./UpdateProfileInformationForm-cde71e29.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-6cdaf2bc.js";import"./DangerButton-ce6d88c3.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-5c3c98c2.js";import"./InputLabel-38b98ddd.js";import"./Modal-8b2a2aa4.js";/* empty css                                                              */import"./SecondaryButton-ae621792.js";import"./TextInput-61ab2d6e.js";import"./PrimaryButton-b7e37df1.js";import"./TextArea-8bab3e6c.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
