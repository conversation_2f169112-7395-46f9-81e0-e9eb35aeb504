import{o as i,c as _,a as o,u as s,w as p,F as V,Z as x,b as l,d as b,e as r,f as d,n as h,g as C,T as $}from"./app-16701445.js";import{_ as U,a as k}from"./AdminLayout-e15be38d.js";import{_ as m}from"./InputError-11376965.js";import{_ as n}from"./InputLabel-d69efee6.js";import{P as w}from"./PrimaryButton-eddb8b77.js";import{_ as u}from"./TextInput-764e3400.js";import{_ as P}from"./TextArea-b68da786.js";import{_ as S}from"./SearchableDropdown-c456ce8e.js";import{_ as q}from"./FileUpload-6a456abf.js";import{u as N}from"./index-10107770.js";import"./_plugin-vue_export-helper-c27b6911.js";const B={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},F=l("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Product",-1),T=["onSubmit"],A={class:"border-b border-gray-900/10 pb-12"},I={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},j={class:"sm:col-span-2"},M={class:"sm:col-span-2"},z=l("div",{class:"sm:col-span-2"},null,-1),D={class:"sm:col-span-2"},E={class:"relative mt-2"},G={class:"sm:col-span-2"},H=l("div",{class:"sm:col-span-2"},null,-1),O={class:"sm:col-span-1"},Q={class:"sm:col-span-1"},Z={class:"sm:col-span-1"},J={class:"sm:col-span-1"},K={class:"sm:col-span-4"},L={class:"sm:col-span-4"},R={class:"flex mt-6 items-center justify-between"},W={class:"ml-auto flex items-center justify-end gap-x-6"},X=l("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Y={key:0,class:"text-sm text-gray-600"},ce={__name:"Add",props:["company_id","category","filepath"],setup(g){const e=N("post","/products",{company_id:g.company_id,name:"",prefix:"",min_qty:"1",item_code:"",description:"",price:"",rating:"",gst:"",hsn_code:"",image:"/uploads/companyprofile/defaultimg.png",category:""}),v=()=>{e.submit({preserveScroll:!0,onSuccess:()=>e.reset()})},f=(c,t)=>{e.category=t},y=c=>{e.image=c};return(c,t)=>(i(),_(V,null,[o(s(x),{title:"Product Add"}),o(U,null,{default:p(()=>[l("div",B,[F,l("form",{onSubmit:b(v,["prevent"]),class:""},[l("div",A,[l("div",I,[l("div",j,[o(n,{for:"name",value:"Product Name"}),o(u,{id:"name",type:"text",modelValue:s(e).name,"onUpdate:modelValue":t[0]||(t[0]=a=>s(e).name=a),onChange:t[1]||(t[1]=a=>s(e).validate("name"))},null,8,["modelValue"]),s(e).invalid("name")?(i(),r(m,{key:0,class:"",message:s(e).errors.name},null,8,["message"])):d("",!0)]),l("div",M,[o(n,{for:"item_code",value:"Product Code"}),o(u,{id:"item_code",type:"text",modelValue:s(e).item_code,"onUpdate:modelValue":t[2]||(t[2]=a=>s(e).item_code=a),onChange:t[3]||(t[3]=a=>s(e).validate("item_code"))},null,8,["modelValue"]),s(e).invalid("item_code")?(i(),r(m,{key:0,class:"",message:s(e).errors.item_code},null,8,["message"])):d("",!0)]),z,l("div",D,[o(n,{for:"type",value:"Category"}),l("div",E,[o(S,{options:g.category,modelValue:s(e).category,"onUpdate:modelValue":t[4]||(t[4]=a=>s(e).category=a),onOnchange:f,class:h({"error rounded-md":s(e).errors.category})},null,8,["options","modelValue","class"])]),s(e).invalid("category")?(i(),r(m,{key:0,class:"",message:s(e).errors.category},null,8,["message"])):d("",!0)]),l("div",G,[o(n,{for:"hsn_code",value:"HSN Code"}),o(u,{id:"hsn_code",type:"text",modelValue:s(e).hsn_code,"onUpdate:modelValue":t[5]||(t[5]=a=>s(e).hsn_code=a),onChange:t[6]||(t[6]=a=>s(e).validate("hsn_code"))},null,8,["modelValue"]),s(e).invalid("hsn_code")?(i(),r(m,{key:0,class:"",message:s(e).errors.hsn_code},null,8,["message"])):d("",!0)]),H,l("div",O,[o(n,{for:"price",value:"Price (₹)"}),o(u,{id:"price",type:"text",modelValue:s(e).price,"onUpdate:modelValue":t[7]||(t[7]=a=>s(e).price=a),onChange:t[8]||(t[8]=a=>s(e).validate("price")),min:"1"},null,8,["modelValue"]),s(e).invalid("price")?(i(),r(m,{key:0,class:"",message:s(e).errors.price},null,8,["message"])):d("",!0)]),l("div",Q,[o(n,{for:"gst",value:"GST(%)"}),o(u,{id:"gst",type:"text",modelValue:s(e).gst,"onUpdate:modelValue":t[9]||(t[9]=a=>s(e).gst=a),onChange:t[10]||(t[10]=a=>s(e).validate("gst")),min:"0",max:"100"},null,8,["modelValue"]),s(e).invalid("gst")?(i(),r(m,{key:0,class:"",message:s(e).errors.gst},null,8,["message"])):d("",!0)]),l("div",Z,[o(n,{for:"min_qty",value:"Minimum Qty"}),o(u,{id:"min_qty",type:"text",modelValue:s(e).min_qty,"onUpdate:modelValue":t[11]||(t[11]=a=>s(e).min_qty=a),onChange:t[12]||(t[12]=a=>s(e).validate("min_qty"))},null,8,["modelValue"]),s(e).invalid("min_qty")?(i(),r(m,{key:0,class:"",message:s(e).errors.min_qty},null,8,["message"])):d("",!0)]),l("div",J,[o(n,{for:"prefix",value:"Prefix"}),o(u,{id:"prefix",type:"text",modelValue:s(e).prefix,"onUpdate:modelValue":t[13]||(t[13]=a=>s(e).prefix=a)},null,8,["modelValue"])]),l("div",K,[o(n,{for:"image",value:"Upload Image"}),o(q,{label:"Upload Image",inputId:"image",inputName:"image",fileUrl:s(e).image,onFile:y},null,8,["fileUrl"])]),l("div",L,[o(n,{for:"description",value:"Description"}),o(P,{id:"description",type:"text",modelValue:s(e).description,"onUpdate:modelValue":t[14]||(t[14]=a=>s(e).description=a),onChange:t[15]||(t[15]=a=>s(e).validate("description")),rows:5},null,8,["modelValue"]),s(e).invalid("description")?(i(),r(m,{key:0,class:"",message:s(e).errors.description},null,8,["message"])):d("",!0)])])]),l("div",R,[l("div",W,[o(k,{href:c.route("products.show",{id:g.company_id})},{svg:p(()=>[X]),_:1},8,["href"]),o(w,{disabled:s(e).processing},{default:p(()=>[C("Save")]),_:1},8,["disabled"]),o($,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:p(()=>[s(e).recentlySuccessful?(i(),_("p",Y,"Saved.")):d("",!0)]),_:1})])])],40,T)])]),_:1})],64))}};export{ce as default};
