import{_ as o}from"./AdminLayout-dc64724f.js";import i from"./DeleteUserForm-f16726a9.js";import m from"./UpdatePasswordForm-eb09b68c.js";import r from"./UpdateProfileInformationForm-c9476964.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-6a429cee.js";import"./DangerButton-c7881a4e.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-17731bba.js";import"./InputLabel-5e6ac969.js";import"./Modal-b05cc76d.js";/* empty css                                                              */import"./SecondaryButton-9a822eb1.js";import"./TextInput-94a28154.js";import"./PrimaryButton-c589c744.js";import"./TextArea-217f7d79.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
