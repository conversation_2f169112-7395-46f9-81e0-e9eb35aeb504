import{r as A,j as G,o as c,c as u,a as i,u as a,w as F,F as B,Z as tt,b as t,t as y,k as et,v as st,d as ot,n as p,f as _,i as lt,g as M,e as at,s as nt,x as rt}from"./app-4f4c883b.js";import{_ as it,a as dt}from"./AdminLayout-d9d2bc31.js";import{_ as ct}from"./InputError-41a68047.js";import{_ as h}from"./InputLabel-468796e0.js";import{P as L}from"./PrimaryButton-3e579b0b.js";import{_ as w}from"./TextInput-21f4f57b.js";import{_ as N}from"./TextArea-b7098398.js";import{_ as P}from"./SearchableDropdown-ace42120.js";import{_ as mt}from"./MultipleFileUpload-90344961.js";import{u as ut}from"./index-20fd5540.js";import{_ as pt}from"./_plugin-vue_export-helper-c27b6911.js";const d=x=>(nt("data-v-b7037499"),x=x(),rt(),x),_t={class:"animate-top"},gt={class:"sm:flex sm:items-center"},yt=d(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create New Order")],-1)),vt={class:"w-auto"},xt={class:"flex space-x-2 items-center"},ft=d(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6"},"Order Number:",-1)),ht={class:"text-sm font-semibold text-gray-900 leading-6"},wt={class:"flex space-x-2 items-center"},bt=d(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Date :",-1)),Vt=["onSubmit"],St={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},It={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Tt={class:"sm:col-span-4"},Ct={class:"relative mt-2"},$t={class:"sm:col-span-4"},Pt={class:"relative mt-2"},Gt={class:"sm:col-span-4"},Ft={class:"relative mt-2"},kt={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Ut={class:"overflow-x-auto w-full"},At={class:"overflow-x-auto divide-y divide-gray-300 w-full",style:{"margin-bottom":"160px"}},Nt=d(()=>t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Model",-1)),Dt=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"}," Description",-1)),jt=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),Ot=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"}," Qty",-1)),qt=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹) ",-1)),zt=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),Bt={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Mt={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Lt={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Et={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Ht={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Qt=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),Rt=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),Wt=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),Zt={class:"divide-y divide-gray-300 bg-white"},Jt={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-80"},Kt={class:"relative mt-2"},Xt={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-96"},Yt={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},te={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ee={key:0,class:"text-red-500 text-xs absolute"},se={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},oe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},le={class:"text-sm text-gray-900 leading-6 mt-2 py-1.5"},ae={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ne={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},re={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ie={key:3,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},de={key:4,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ce={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},me={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ue={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-36"},pe={class:"flex space-x-2"},_e={class:"text-sm text-gray-900 leading-6 py-1.5"},ge=["onClick"],ye=d(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),ve=[ye],xe={class:"flex items-center justify-between"},fe={class:"ml-auto flex items-center justify-end gap-x-6"},he={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},we={class:"items-center justify-between"},be={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Ve={class:"inline-flex items-center justify-end w-full space-x-3"},Se=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),Ie={class:"text-base font-semibold text-gray-900 w-20"},Te={class:"inline-flex items-center justify-end w-full space-x-3"},Ce=d(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),$e={class:"w-40"},Pe={class:"inline-flex items-center justify-end w-full space-x-3"},Ge=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),Fe={class:"text-base font-semibold text-gray-900 w-32"},ke={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},Ue=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),Ae={class:"text-base font-semibold text-gray-900 w-w-32"},Ne={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},De=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),je={class:"text-base font-semibold text-gray-900 w-w-32"},Oe={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},qe=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),ze={class:"text-base font-semibold text-gray-900 w-w-32"},Be={class:"inline-flex items-center justify-end w-full space-x-3"},Me=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),Le={class:"text-base font-semibold text-gray-900 w-20"},Ee={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},He={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Qe={class:"sm:col-span-10"},Re={class:"flex space-x-4"},We={class:"w-full"},Ze=d(()=>t("div",{class:"w-full"},null,-1)),Je={class:"w-full"},Ke={class:"relative mt-2"},Xe={class:"sm:col-span-10"},Ye={class:"flex space-x-4"},ts={class:"w-full"},es={class:"w-full"},ss={class:"w-full"},os={class:"sm:col-span-10"},ls={class:"flex space-x-4"},as={class:"w-full"},ns={class:"w-full"},rs={class:"flex mt-6 items-center justify-between"},is={class:"ml-auto flex items-center justify-end gap-x-6"},ds=d(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),cs={__name:"Add",props:["customers","salesuser","products","order_number","organization","category","terms"],setup(x){const b=x,m=A(null),k=A(),g=A([{product_id:"",description:"",qty:"",price:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",discount:"",discount_amount:"",total_amount:"",mrp:""}]),E=(n,o)=>{s.customer_id=n,s.errors.customer_id=null;const e=b.customers.find(r=>r.id===n);e&&(m.value=e.gst_type)},H=(n,o)=>{s.sales_user_id=n,s.errors.sales_user_id=null},Q=(n,o,e,r)=>{const l=b.products.find(v=>v.id===n);l&&(g.value[e].product_id=l.id,g.value[e].price=parseFloat(l.price).toFixed(2),g.value[e].description=l.description,g.value[e].mrp=l!=null&&l.serial_numbers[0]?parseFloat(l.serial_numbers[0].mrp).toFixed(2):"-",g.value[e].gst=parseFloat(l.gst).toFixed(2),g.value[e].sgst=parseFloat(l.gst/2).toFixed(2),g.value[e].discount="0.00",s.errors[`selectedProductItem.${e}.product_id`]=null,s.errors[`selectedProductItem.${e}.price`]=null,V(r))},s=ut("post","/orders",{note:"",date:new Date().toISOString().slice(0,10),selectedProductItem:[],customer_id:"",sales_user_id:"",total_amount:"",order_number:"",document:"",organization_id:"",category:"",cgst:"",sgst:"",igst:"",total_gst:"",sub_total:"",validity:b.terms.validity,delivery:b.terms.delivery,payment_terms:b.terms.payment_terms,warranty:b.terms.warranty,overall_discount:"",total_discount:""}),R=()=>{s.sub_total=j.value,s.total_discount=O.value,s.total_amount=D.value,s.cgst=m.value=="CGST/SGST"?S.value/2:"0",s.sgst=m.value=="CGST/SGST"?S.value/2:"0",s.igst=m.value=="IGST"?S.value:"0",s.total_gst=S.value,s.order_number=k.value,s.selectedProductItem=g.value,s.submit({preserveScroll:!0,onSuccess:()=>s.reset()})},W=()=>{g.value.push({product_id:"",description:"",qty:"",price:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:""})},Z=n=>{g.value.splice(n,1)},J=(n,o)=>{const e=parseFloat(n.price),r=parseFloat(n.discount)||0,l=m.value=="IGST"?parseFloat(n.gst):parseFloat(n.sgst*2),v=parseFloat(n.qty);let T=0,C=0;r>0?T=e*v:T=e*v*(1+l/100);const $=T*(r/100)||0,q=e*1*(l/100),U=(e*v-$)*(l/100);r>0?C=T-$+U:C=T-$;const z=e*v;return n.total_price=isNaN(z)?"":parseFloat(z).toFixed(2),n.gst_amount=isNaN(q)?"":parseFloat(q).toFixed(2),n.total_gst_amount=isNaN(U)?"":parseFloat(U).toFixed(2),n.discount_amount=isNaN($)?"":parseFloat($).toFixed(2),isNaN(C)?"":parseFloat(C).toFixed(2)},V=(n,o)=>{n.total_amount=J(n)},D=G(()=>{const n=Math.round(g.value.reduce((e,r)=>e+(r.total_amount?parseFloat(r.total_amount):0),0)),o=s.overall_discount?parseFloat(s.overall_discount):0;return n-o}),S=G(()=>g.value.reduce((n,o)=>n+(o.total_gst_amount?parseFloat(o.total_gst_amount):0),0)),j=G(()=>g.value.reduce((n,o)=>n+(o.total_price?parseFloat(o.total_price):0),0)),O=G(()=>{const n=g.value.reduce((e,r)=>e+(r.discount_amount?parseFloat(r.discount_amount):0),0),o=s.overall_discount?parseFloat(s.overall_discount):0;return n+o}),f=n=>{s.errors[n]=null},K=n=>{s.document=n},I=n=>{let o=n.toFixed(2).toString(),[e,r]=o.split("."),l=e.substring(e.length-3),v=e.substring(0,e.length-3);return v!==""&&(l=","+l),`${v.replace(/\B(?=(\d{2})+(?!\d))/g,",")+l}.${r}`},X=(n,o)=>{s.category=n,s.errors.category=null},Y=(n,o)=>{s.organization_id=n,s.errors.organization_id=null,k.value=b.order_number[n]};return(n,o)=>(c(),u(B,null,[i(a(tt),{title:"Orders"}),i(it,null,{default:F(()=>[t("div",_t,[t("div",gt,[yt,t("div",vt,[t("div",xt,[ft,t("span",ht,y(k.value),1)]),t("div",wt,[bt,et(t("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":o[0]||(o[0]=e=>a(s).date=e),onChange:o[1]||(o[1]=e=>a(s).validate("date"))},null,544),[[st,a(s).date]])])])]),t("form",{onSubmit:ot(R,["prevent"]),class:""},[t("div",St,[t("div",It,[t("div",Tt,[i(h,{for:"company_name",value:"Organization"}),t("div",Ct,[i(P,{options:x.organization,modelValue:a(s).organization_id,"onUpdate:modelValue":o[2]||(o[2]=e=>a(s).organization_id=e),onOnchange:Y,class:p({"error rounded-md":a(s).errors.organization_id})},null,8,["options","modelValue","class"])])]),t("div",$t,[i(h,{for:"customer_id",value:"Customer Name"}),t("div",Pt,[i(P,{options:x.customers,modelValue:a(s).customer_id,"onUpdate:modelValue":o[3]||(o[3]=e=>a(s).customer_id=e),onOnchange:E,class:p({"error rounded-md":a(s).errors.customer_id})},null,8,["options","modelValue","class"])])]),t("div",Gt,[i(h,{for:"company_name",value:"Category"}),t("div",Ft,[i(P,{options:x.category,modelValue:a(s).category,"onUpdate:modelValue":o[4]||(o[4]=e=>a(s).category=e),onOnchange:X,class:p({"error rounded-md":a(s).errors.category})},null,8,["options","modelValue","class"])])])])]),t("div",kt,[t("div",Ut,[t("table",At,[t("thead",null,[t("tr",null,[Nt,Dt,jt,Ot,qt,zt,m.value=="IGST"?(c(),u("th",Bt,"IGST (%)")):_("",!0),m.value=="IGST"?(c(),u("th",Mt,"IGST (₹)")):_("",!0),m.value=="CGST/SGST"?(c(),u("th",Lt,"CGST (%)")):_("",!0),m.value=="CGST/SGST"?(c(),u("th",Et,"SGST (%)")):_("",!0),m.value=="CGST/SGST"?(c(),u("th",Ht,"Total GST (₹)")):_("",!0),Qt,Rt,Wt])]),t("tbody",Zt,[(c(!0),u(B,null,lt(g.value,(e,r)=>(c(),u("tr",{key:r},[t("td",Jt,[t("div",Kt,[i(P,{options:x.products,modelValue:e.product_id,"onUpdate:modelValue":l=>e.product_id=l,onOnchange:(l,v)=>Q(l,v,r,e),onChange:o[5]||(o[5]=l=>a(s).validate("product_id")),class:p({"error rounded-md":a(s).errors[`selectedProductItem.${r}.product_id`]})},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class"])])]),t("td",Xt,[i(N,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":l=>e.description=l,autocomplete:"description",rows:2,onChange:l=>e.validate("description"),class:p({"error rounded-md":a(s).errors[`selectedProductItem.${r}.description`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),t("td",Yt,y(e.mrp??"-"),1),t("td",te,[i(w,{id:"qty",type:"text",numeric:!0,modelValue:e.qty,"onUpdate:modelValue":l=>e.qty=l,autocomplete:"qty",onInput:l=>V(e,r),onChange:l=>f("selectedProductItem."+r+".qty"),class:p({error:a(s).errors[`selectedProductItem.${r}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"]),a(s).errors[`selectedProductItem.${r}.qty1`]?(c(),u("p",ee,y(a(s).errors[`selectedProductItem.${r}.qty1`]),1)):_("",!0)]),t("td",se,[i(w,{id:"price",type:"text",modelValue:e.price,"onUpdate:modelValue":l=>e.price=l,autocomplete:"price",onInput:l=>V(e,r),onChange:l=>f("selectedProductItem."+r+".price"),class:p({error:a(s).errors[`selectedProductItem.${r}.price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",oe,[t("div",le,y(e.total_price),1)]),m.value=="IGST"?(c(),u("td",ae,[i(w,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":l=>e.gst=l,onInput:l=>V(e,r),onChange:l=>f("selectedProductItem."+r+".gst"),class:p({error:a(s).errors[`selectedProductItem.${r}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):_("",!0),m.value=="CGST/SGST"?(c(),u("td",ne,[i(w,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":l=>e.sgst=l,onInput:l=>V(e,r),onChange:l=>f("selectedProductItem."+r+".gst"),class:p({error:a(s).errors[`selectedProductItem.${r}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):_("",!0),m.value=="CGST/SGST"?(c(),u("td",re,[i(w,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":l=>e.sgst=l,onInput:l=>V(e,r),onChange:l=>f("selectedProductItem."+r+".gst"),class:p({error:a(s).errors[`selectedProductItem.${r}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):_("",!0),m.value=="IGST"?(c(),u("td",ie,y(e.total_gst_amount),1)):_("",!0),m.value=="CGST/SGST"?(c(),u("td",de,y(e.total_gst_amount),1)):_("",!0),t("td",ce,[i(w,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":l=>e.discount=l,onInput:l=>V(e,r),onChange:l=>f("selectedProductItem."+r+".discount"),class:p({error:a(s).errors[`selectedProductItem.${r}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",me,y(e.discount_amount),1),t("td",ue,[t("div",pe,[t("div",_e,y(e.total_amount),1),r!=0?(c(),u("button",{key:0,type:"button",class:"mt-1",onClick:l=>Z(r)},ve,8,ge)):_("",!0)])])]))),128))])])]),t("div",xe,[t("div",fe,[i(L,{onClick:W,type:"button"},{default:F(()=>[M("Add Product")]),_:1})])])]),t("div",he,[t("div",we,[t("div",be,[t("div",Ve,[Se,t("p",Ie,y(I(j.value)),1)]),t("div",Te,[Ce,t("div",$e,[i(w,{id:"overall_discount",type:"text",modelValue:a(s).overall_discount,"onUpdate:modelValue":o[6]||(o[6]=e=>a(s).overall_discount=e)},null,8,["modelValue"])])]),t("div",Pe,[Ge,t("p",Fe,y(I(O.value)),1)]),m.value=="IGST"?(c(),u("div",ke,[Ue,t("p",Ae,y(I(S.value)),1)])):_("",!0),m.value=="CGST/SGST"?(c(),u("div",Ne,[De,t("p",je,y(I(S.value/2)),1)])):_("",!0),m.value=="CGST/SGST"?(c(),u("div",Oe,[qe,t("p",ze,y(I(S.value/2)),1)])):_("",!0),t("div",Be,[Me,t("p",Le,y(I(D.value)),1)])])])]),t("div",Ee,[t("div",He,[t("div",Qe,[t("div",Re,[t("div",We,[i(h,{for:"note",value:"Upload Documents"}),i(mt,{inputId:"document",inputName:"document",onFiles:K})]),Ze,t("div",Je,[i(h,{for:"company_name",value:"Sales Person"}),t("div",Ke,[i(P,{options:x.salesuser,modelValue:a(s).sales_user_id,"onUpdate:modelValue":o[7]||(o[7]=e=>a(s).sales_user_id=e),onOnchange:H,class:p({"error rounded-md":a(s).errors.sales_user_id})},null,8,["options","modelValue","class"])])])])]),t("div",Xe,[t("div",Ye,[t("div",ts,[i(h,{for:"Validity",value:"Validity"}),i(w,{id:"price",type:"text",modelValue:a(s).validity,"onUpdate:modelValue":o[8]||(o[8]=e=>a(s).validity=e),onChange:o[9]||(o[9]=e=>f(a(s).errors.validity)),class:p({"error rounded-md":a(s).errors.validity})},null,8,["modelValue","class"])]),t("div",es,[i(h,{for:"delivery",value:"Delivery"}),i(w,{id:"price",type:"text",modelValue:a(s).delivery,"onUpdate:modelValue":o[10]||(o[10]=e=>a(s).delivery=e),onChange:o[11]||(o[11]=e=>f(a(s).errors.delivery)),class:p({"error rounded-md":a(s).errors.delivery})},null,8,["modelValue","class"])]),t("div",ss,[i(h,{for:"warranty",value:"Warranty"}),i(w,{id:"price",type:"text",modelValue:a(s).warranty,"onUpdate:modelValue":o[12]||(o[12]=e=>a(s).warranty=e),onChange:o[13]||(o[13]=e=>f(a(s).errors.warranty)),class:p({"error rounded-md":a(s).errors.warranty})},null,8,["modelValue","class"])])])]),t("div",os,[t("div",ls,[t("div",as,[i(h,{for:"payment_terms",value:"Payment Terms"}),i(N,{id:"price",type:"text",rows:4,modelValue:a(s).payment_terms,"onUpdate:modelValue":o[14]||(o[14]=e=>a(s).payment_terms=e),onChange:o[15]||(o[15]=e=>f(a(s).errors.payment_terms)),class:p({"error rounded-md":a(s).errors.payment_terms})},null,8,["modelValue","class"])]),t("div",ns,[i(h,{for:"note",value:"Note"}),i(N,{id:"note",type:"text",rows:4,modelValue:a(s).note,"onUpdate:modelValue":o[16]||(o[16]=e=>a(s).note=e),autocomplete:"note",onChange:o[17]||(o[17]=e=>a(s).validate("note"))},null,8,["modelValue"]),a(s).invalid("note")?(c(),at(ct,{key:0,class:"",message:a(s).errors.note},null,8,["message"])):_("",!0)])])])])]),t("div",rs,[t("div",is,[i(dt,{href:n.route("orders.index")},{svg:F(()=>[ds]),_:1},8,["href"]),i(L,{disabled:a(s).processing},{default:F(()=>[M("Submit")]),_:1},8,["disabled"])])])],40,Vt)])]),_:1})],64))}},bs=pt(cs,[["__scopeId","data-v-b7037499"]]);export{bs as default};
