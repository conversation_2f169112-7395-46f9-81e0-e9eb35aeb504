import{r as b,j as H,l as I,o as d,c,a as l,u as n,w as $,F as z,Z as ee,b as t,t as m,f as _,d as te,n as v,k as oe,v as se,i as D,g as ae,T as ne,s as le,x as ie}from"./app-16701445.js";import{_ as re,a as de}from"./AdminLayout-e15be38d.js";import{_ as f}from"./InputLabel-d69efee6.js";import{P as ce}from"./PrimaryButton-eddb8b77.js";import{_ as P}from"./TextInput-764e3400.js";import{_ as me}from"./TextArea-b68da786.js";import{_ as ue}from"./RadioButton-b4275d4f.js";import{_ as T}from"./SearchableDropdown-c456ce8e.js";import{u as pe}from"./index-10107770.js";/* empty css                                                                          */import{_ as _e}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as ve}from"./Checkbox-743761b5.js";const k=g=>(le("data-v-080ae9a0"),g=g(),ie(),g),ye={class:"h-screen animate-top"},fe={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},he={class:"sm:flex sm:items-center"},ge=k(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Make Payment")],-1)),xe={class:"flex items-center justify-between"},be={key:0,class:"text-base font-semibold leading-6 text-gray-900"},we=["onSubmit"],ke={class:"border-b border-gray-900/10 pb-12"},Ve={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},Ce={class:"sm:col-span-3"},Ae={class:"relative mt-2"},Ne={class:"sm:col-span-3"},Se={class:"relative mt-2"},Fe={class:"sm:col-span-2"},Ue={class:"relative mt-2"},$e={key:0,class:"sm:col-span-3"},ze={class:"relative mt-2"},Pe={key:1,class:"sm:col-span-1"},Te={key:2,class:"sm:col-span-2"},Oe={key:3,class:"sm:col-span-2"},Ie={class:"mt-4 flex justify-start"},De={class:"text-base font-semibold"},Be={key:4,class:"sm:col-span-3"},je={key:5,class:"sm:col-span-3"},Ee={key:6,class:"sm:col-span-3"},Me={key:7,class:"sm:col-span-3"},Le={class:"relative mt-2"},Re={class:"sm:col-span-6"},Ye={class:"overflow-x-auto divide-y divide-gray-300 w-full"},qe=k(()=>t("div",{class:"w-full"},[t("thead",{class:"w-full"},[t("tr",{class:""},[t("th",{scope:"col",class:""}),t("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),t("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),t("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1)),He={style:{"overflow-y":"auto","max-height":"318px"}},Ze={class:"divide-y divide-gray-300 bg-white"},Ge={class:"whitespace-nowrap px-2 text-sm text-gray-900"},Je={class:"text-sm text-gray-900 leading-6 py-1.5"},Ke={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},Qe={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},We={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Xe={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},et={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},tt={key:0,class:"text-red-500 text-xs absolute"},ot={class:"whitespace-nowrap px-2 text-sm text-gray-900"},st={class:"sm:col-span-4"},at={class:"mt-2 p-3 bg-gray-50 rounded-md"},nt={class:"space-y-2 text-sm"},lt={class:"flex items-center gap-2"},it=k(()=>t("hr",{class:"my-2"},null,-1)),rt={class:"flex justify-between items-center font-semibold"},dt=k(()=>t("span",null,"Net Settlement:",-1)),ct={key:0,class:"text-red-500 text-xs mt-1"},mt={key:8,class:"sm:col-span-6"},ut={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},pt=k(()=>t("thead",null,[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1)),_t={class:"divide-y divide-gray-300 bg-white"},vt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},yt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ft={class:"flex flex-col"},ht={class:"text-sm text-gray-900"},gt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},xt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},bt={class:"flex mt-6 items-center justify-between"},wt={class:"ml-auto flex items-center justify-end gap-x-6"},kt=k(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),Vt={key:0,class:"text-sm text-gray-600"},Ct={__name:"Add",props:["paymentType","bankinfo","organization","companies","invoices","credit"],setup(g){const V=g;b([]);const o=pe("post","/payment",{organization_id:"",company_id:"",payment_type:"",date:"",note:"",amount:"",discount_amount:0,round_off:0,check_number:"",org_bank_id:"",invoice:[],settled_amount:"",advance_amount:"",is_credit:"",credit_data:[],total_unused_amount:""}),S=b(""),Z=()=>{o.settled_amount=A.value,o.advance_amount=E.value,o.total_unused_amount=C.value,o.is_credit=p.value,o.invoice=y.value,o.credit_data=x.value,o.submit({preserveScroll:!0,onSuccess:()=>o.reset()})},G=(a,s)=>{S.value=s,o.payment_type=a,o.errors.payment_type=null,s==="Cash"?o.note="Cash":o.note==="Cash"&&(o.note="")},F=b([]),B=b([]),x=b([]),C=b(""),J=(a,s)=>{o.organization_id=a,o.errors.organization_id=null;const e=V.bankinfo.filter(r=>r.organization_id===a);B.value=e,o.company_id&&j(o.company_id,a);const i=V.credit.filter(r=>r.organization_id===a&&r.company_id===o.company_id);x.value=i,C.value=x.value.reduce((r,u)=>r+u.unused_amount,0)},K=(a,s)=>{o.company_id=a,j(a,o.organization_id);const e=V.credit.filter(i=>i.company_id===a&&i.organization_id===o.organization_id);x.value=e,C.value=x.value.reduce((i,r)=>i+r.unused_amount,0),o.errors.company_id=null},j=(a,s)=>{if(!a||!s){F.value=[];return}const e=V.companies.find(u=>u.id===a),i=e==null?void 0:e.party_id,r=V.invoices.filter(u=>{const h=u.organization_id===s;return u.invoice_type==="purchase"?h&&u.company_id===a:u.invoice_type==="sales"&&i?h&&u.party_id===i:!1});F.value=r},Q=(a,s)=>{o.org_bank_id=a,o.errors.org_bank_id=null},A=H(()=>{const a=y.value.reduce((s,e)=>{if(e.check&&e.amount){const i=parseFloat(e.amount);return e.invoice_type==="purchase"?s+i:s-i}return s},0);return parseFloat(a.toFixed(2))}),E=H(()=>{const a=parseFloat(o.amount||0),s=parseFloat(o.round_off||0),e=A.value;return a>e?a-e-s:0}),w=a=>{let s=a.toFixed(2).toString(),[e,i]=s.split("."),r=e.substring(e.length-3),u=e.substring(0,e.length-3);return u!==""&&(r=","+r),`${u.replace(/\B(?=(\d{2})+(?!\d))/g,",")+r}.${i}`},M=a=>{const s=new Date(a),e={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",e)},W=(a,s)=>{const e=p.value==="Yes"?parseFloat(C.value||0):parseFloat(o.amount||0)+parseFloat(o.round_off||0);if(s===void 0&&(s=y.value.findIndex(h=>h.check===a.target.checked)),s===-1||s>=y.value.length)return;if(!y.value[s].check){y.value[s].amount=0;return}let i=e;y.value.forEach((h,N)=>{h.check&&N!==s&&(i-=parseFloat(h.amount||0))});const r=parseFloat(y.value[s].pending_amount||0),u=Math.min(r,i);y.value[s].amount=u.toFixed(2)},y=b([{id:"",date:"",invoice_no:"",total_amount:"",pending_amount:"",check:!1,amount:0}]),p=b("No"),X=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],O=()=>{y.value=F.value.map(a=>({id:a.id,date:a.date,invoice_no:a.invoice_no,total_amount:parseFloat(a.total_amount||0).toFixed(2),pending_amount:parseFloat(a.pending_amount||0).toFixed(2),invoice_type:a.invoice_type||"purchase",check:!1,amount:"0.00"}))};I(F,()=>{O()}),I(p,()=>{O()}),I(()=>o.amount,()=>{p.value==="No"&&O()});const U=a=>{o.errors[a]=null,o.errors.settled_amount=null};return(a,s)=>(d(),c(z,null,[l(n(ee),{title:"Payment"}),l(re,null,{default:$(()=>[t("div",ye,[t("div",fe,[t("div",he,[ge,t("div",xe,[x.value.length>0?(d(),c("div",be," Credits Available: ₹"+m(w(C.value)),1)):_("",!0)])]),t("form",{onSubmit:te(Z,["prevent"]),class:""},[t("div",ke,[t("div",Ve,[t("div",Ce,[l(f,{for:"payment_type",value:"Organization"}),t("div",Ae,[l(T,{options:g.organization,modelValue:n(o).organization_id,"onUpdate:modelValue":s[0]||(s[0]=e=>n(o).organization_id=e),onOnchange:J,class:v({"error rounded-md":n(o).errors.organization_id})},null,8,["options","modelValue","class"])])]),t("div",Ne,[l(f,{for:"payment_type",value:"Company"}),t("div",Se,[l(T,{options:g.companies,modelValue:n(o).company_id,"onUpdate:modelValue":s[1]||(s[1]=e=>n(o).company_id=e),onOnchange:K,class:v({"error rounded-md":n(o).errors.company_id})},null,8,["options","modelValue","class"])])]),t("div",Fe,[l(f,{for:"role_id",value:"Payment Through Credit ?"}),t("div",Ue,[l(ue,{modelValue:p.value,"onUpdate:modelValue":s[2]||(s[2]=e=>p.value=e),options:X},null,8,["modelValue"])])]),p.value=="No"?(d(),c("div",$e,[l(f,{for:"payment_type",value:"Payment Type"}),t("div",ze,[l(T,{options:g.paymentType,modelValue:n(o).payment_type,"onUpdate:modelValue":s[3]||(s[3]=e=>n(o).payment_type=e),onOnchange:G,class:v({"error rounded-md":n(o).errors.payment_type})},null,8,["options","modelValue","class"])])])):_("",!0),p.value=="No"?(d(),c("div",Pe,[l(f,{for:"round_off",value:"Round Off"}),l(P,{type:"text",onChange:s[4]||(s[4]=e=>U("round_off")),modelValue:n(o).round_off,"onUpdate:modelValue":s[5]||(s[5]=e=>n(o).round_off=e),class:v({"error rounded-md":n(o).errors.round_off})},null,8,["modelValue","class"])])):_("",!0),p.value=="No"?(d(),c("div",Te,[l(f,{for:"amount",value:"Amount"}),l(P,{id:"amount",type:"text",onChange:s[6]||(s[6]=e=>U("amount")),modelValue:n(o).amount,"onUpdate:modelValue":s[7]||(s[7]=e=>n(o).amount=e),class:v({"error rounded-md":n(o).errors.amount})},null,8,["modelValue","class"])])):_("",!0),p.value=="No"?(d(),c("div",Oe,[l(f,{for:"advance",value:"Advance(Ref) Amount"}),t("div",Ie,[t("p",De,m(w(E.value)),1)])])):_("",!0),S.value=="Cheque"&&p.value=="No"?(d(),c("div",Be,[l(f,{for:"check_number",value:"Cheque Number"}),l(P,{id:"check_number",type:"text",modelValue:n(o).check_number,"onUpdate:modelValue":s[8]||(s[8]=e=>n(o).check_number=e),class:v({"error rounded-md":n(o).errors["data.check_number"]})},null,8,["modelValue","class"])])):_("",!0),p.value=="No"?(d(),c("div",je,[l(f,{for:"date",value:"Payment Date"}),oe(t("input",{"onUpdate:modelValue":s[9]||(s[9]=e=>n(o).date=e),class:v(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":n(o).errors.date}]),type:"date",onChange:s[10]||(s[10]=e=>U("date"))},null,34),[[se,n(o).date]])])):_("",!0),S.value=="Cash"&&p.value=="No"?(d(),c("div",Ee)):_("",!0),S.value!="Cash"&&p.value=="No"?(d(),c("div",Me,[l(f,{for:"org_bank_id",value:"Our Bank"}),t("div",Le,[l(T,{options:B.value,modelValue:n(o).org_bank_id,"onUpdate:modelValue":s[11]||(s[11]=e=>n(o).org_bank_id=e),onOnchange:Q,class:v({"error rounded-md":n(o).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):_("",!0),t("div",Re,[t("table",Ye,[qe,t("div",He,[t("tbody",Ze,[(d(!0),c(z,null,D(y.value,(e,i)=>(d(),c("tr",{key:i},[t("td",Ge,[t("div",Je,[l(ve,{name:"check",checked:e.check,"onUpdate:checked":r=>e.check=r,onChange:r=>W(r,i)},null,8,["checked","onUpdate:checked","onChange"])])]),t("td",Ke,[t("span",{class:v([e.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},m(e.invoice_type==="purchase"?"PURCHASE":"SALES"),3)]),t("td",Qe,m(e.invoice_no),1),t("td",We,m(e.total_amount),1),t("td",Xe,m(e.pending_amount),1),t("td",et,[l(P,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":r=>e.amount=r,onChange:r=>U("invoice."+i+".amount"),class:v({error:n(o).errors[`invoice.${i}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),n(o).errors[`invoice.${i}.amount`]?(d(),c("p",tt,m(n(o).errors[`invoice.${i}.amount`]),1)):_("",!0)]),t("td",ot,m(M(e.date)),1)]))),128))])])])]),t("div",st,[l(f,{for:"note",value:"Settlement Summary"}),t("div",at,[t("div",nt,[(d(!0),c(z,null,D(y.value.filter(e=>e.check),e=>(d(),c("div",{key:e.id,class:"flex justify-between items-center"},[t("div",lt,[t("span",{class:v([e.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium"])},m(e.invoice_type==="purchase"?"P":"S"),3),t("span",null,m(e.invoice_no),1)]),t("span",{class:v([e.invoice_type==="purchase"?"text-blue-600":"text-green-600","font-medium"])},m(e.invoice_type==="purchase"?"+":"-")+"₹"+m(w(parseFloat(e.amount||0))),3)]))),128)),it,t("div",rt,[dt,t("span",{class:v(A.value>=0?"text-blue-600":"text-red-600")}," ₹"+m(w(Math.abs(A.value)))+" "+m(A.value>=0?"(Pay)":"(Receive)"),3)])])]),n(o).errors.settled_amount?(d(),c("p",ct,m(n(o).errors.settled_amount),1)):_("",!0)]),p.value=="No"?(d(),c("div",mt,[l(f,{for:"note",value:"Note"}),l(me,{id:"note",type:"text",rows:2,modelValue:n(o).note,"onUpdate:modelValue":s[12]||(s[12]=e=>n(o).note=e)},null,8,["modelValue"])])):_("",!0)]),x.value.length>0&&p.value=="Yes"?(d(),c("table",ut,[pt,t("tbody",_t,[(d(!0),c(z,null,D(x.value,(e,i)=>{var r,u,h,N,L,R,Y,q;return d(),c("tr",{key:i},[t("td",vt,m(M(e.date)),1),t("td",yt,[t("div",ft,[t("div",ht,m((u=(r=e.paymentpaid)==null?void 0:r.bank_info)!=null&&u.bank_name?(N=(h=e.paymentpaid)==null?void 0:h.bank_info)==null?void 0:N.bank_name:"Cash")+" - "+m((R=(L=e.paymentpaid)==null?void 0:L.bank_info)!=null&&R.account_number?(q=(Y=e.paymentpaid)==null?void 0:Y.bank_info)==null?void 0:q.account_number:""),1)])]),t("td",gt,m(w(e.amount)),1),t("td",xt,m(w(e.unused_amount)),1)])}),128))])])):_("",!0)]),t("div",bt,[t("div",wt,[l(de,{href:a.route("receipt.index")},{svg:$(()=>[kt]),_:1},8,["href"]),l(ce,{disabled:n(o).processing},{default:$(()=>[ae("Save")]),_:1},8,["disabled"]),l(ne,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:$(()=>[n(o).recentlySuccessful?(d(),c("p",Vt,"Saved.")):_("",!0)]),_:1})])])],40,we)])])]),_:1})],64))}},Bt=_e(Ct,[["__scopeId","data-v-080ae9a0"]]);export{Bt as default};
