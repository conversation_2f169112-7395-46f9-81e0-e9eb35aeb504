<script setup>
import AdminLayout from '@/Layouts/AdminLayout.vue';
import CustomButton from '@/Components/CustomButton.vue';
import { Head , useForm} from '@inertiajs/vue3';

const props = defineProps(['permissions']);

const form = useForm({});

</script>

<template>
    <Head title="Reports" />

    <AdminLayout>
        <div class="animate-top">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Reports</h1>
            </div>
            <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
            </div>
        </div>
        <div class="border-gray-900" style="height: 500px;">
            <div class="mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                <div class="sm:col-span-2" v-if="permissions.canEngineerBusinessReport">
                    <CustomButton :href="route('engineer-business.report')">
                        <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H6zm0 2h12v16H6V4zm2 3v2h8V7H8zm0 4v2h8v-2H8zm0 4v2h5v-2H8z"/>
                        </svg>
                        <span class="font-semibold text-lg ml-4">Engineer Business Report</span>
                    </CustomButton>
                </div>
                <div class="sm:col-span-2">
                    <CustomButton :href="route('invoice.pending-amount')">
                        <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H6zm0 2h12v16H6V4zm2 3v2h8V7H8zm0 4v2h8v-2H8zm0 4v2h5v-2H8z"/>
                                        </svg>
                        <span class="font-semibold text-lg ml-4">Invoice Pending Amount</span>
                    </CustomButton>
                </div>
                <div class="sm:col-span-2" v-if="permissions.canSnsStockReport">
                    <CustomButton :href="route('customer-transaction.report')" >
                        <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H6zm0 2h12v16H6V4zm2 3v2h8V7H8zm0 4v2h8v-2H8zm0 4v2h5v-2H8z"/>
                        </svg>
                        <span class="font-semibold text-lg ml-4">Customers Pending Amount</span>
                    </CustomButton>
                </div>
                <div class="sm:col-span-2" v-if="permissions.canSnsStockReport">
                    <CustomButton :href="route('sns-stock.report')">
                        <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H6zm0 2h12v16H6V4zm2 3v2h8V7H8zm0 4v2h8v-2H8zm0 4v2h5v-2H8z"/>
                        </svg>
                        <span class="font-semibold text-lg ml-4">Stock Report</span>
                    </CustomButton>
                </div>
                <div class="sm:col-span-2"  v-if="permissions.canSnsStockReport">
                    <CustomButton :href="route('sns-sales.report')">
                        <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H6zm0 2h12v16H6V4zm2 3v2h8V7H8zm0 4v2h8v-2H8zm0 4v2h5v-2H8z"/>
                        </svg>
                        <span class="font-semibold text-lg ml-4"> Sales Report(Company)</span>
                    </CustomButton>
                </div>
                <div class="sm:col-span-2"  v-if="permissions.canSnsStockReport">
                    <CustomButton :href="route('sns-cusomersales-report')">
                        <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H6zm0 2h12v16H6V4zm2 3v2h8V7H8zm0 4v2h8v-2H8zm0 4v2h5v-2H8z"/>
                        </svg>
                        <span class="font-semibold text-lg ml-4"> Sales Report(Customer)</span>
                    </CustomButton>
                </div>
                <div class="sm:col-span-2"  v-if="permissions.canGSTData">
                    <CustomButton :href="route('gst-sales-data')">
                        <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H6zm0 2h12v16H6V4zm2 3v2h8V7H8zm0 4v2h8v-2H8zm0 4v2h5v-2H8z"/>
                        </svg>
                        <span class="font-semibold text-lg ml-4">GST Sales Data</span>
                    </CustomButton>
                </div>
                <div class="sm:col-span-2" v-if="permissions.canGSTData">
                    <CustomButton :href="route('gst-purchase-data')">
                        <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H6zm0 2h12v16H6V4zm2 3v2h8V7H8zm0 4v2h8v-2H8zm0 4v2h5v-2H8z"/>
                        </svg>
                        <span class="font-semibold text-lg ml-4"> GST Purchase Data</span>
                    </CustomButton>
                </div>
                <div class="sm:col-span-2" v-if="permissions.canGSTData">
                    <CustomButton :href="route('hsn.sales.summary')">
                        <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H6zm0 2h12v16H6V4zm2 3v2h8V7H8zm0 4v2h8v-2H8zm0 4v2h5v-2H8z"/>
                        </svg>
                        <span class="font-semibold text-lg ml-4"> HSN Sales Summary</span>
                    </CustomButton>
                </div>
                <div class="sm:col-span-2"  v-if="permissions.canSalesReport">
                    <CustomButton :href="route('sales.report')">
                        <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H6zm0 2h12v16H6V4zm2 3v2h8V7H8zm0 4v2h8v-2H8zm0 4v2h5v-2H8z"/>
                        </svg>
                        <span class="font-semibold text-lg ml-4"> Sales Book</span>
                    </CustomButton>
                </div>
                <div class="sm:col-span-2"  v-if="permissions.canPurchaseReport">
                    <CustomButton :href="route('purchase.report')">
                        <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H6zm0 2h12v16H6V4zm2 3v2h8V7H8zm0 4v2h8v-2H8zm0 4v2h5v-2H8z"/>
                        </svg>
                        <span class="font-semibold text-lg ml-4"> Purchase Book</span>
                    </CustomButton>
                </div>
                <div class="sm:col-span-2" v-if="permissions.canGSTData">
                    <CustomButton :href="route('tds.report')">
                        <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H6zm0 2h12v16H6V4zm2 3v2h8V7H8zm0 4v2h8v-2H8zm0 4v2h5v-2H8z"/>
                        </svg>
                        <span class="font-semibold text-lg ml-4"> TDS Data Report</span>
                    </CustomButton>
                </div>
                <div class="sm:col-span-2" v-if="permissions.canGSTData">
                    <CustomButton :href="route('creditnote.index')">
                        <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6H6zm12 18H6V4h7v5h5v11z"/>
                            <circle cx="16" cy="16" r="3" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M16 15v2m-1-1h2" stroke="currentColor" stroke-width="2" fill="none"/>
                        </svg>
                        <span class="font-semibold text-lg ml-4">Credit Note Data</span>
                    </CustomButton>
                </div>
                <div class="sm:col-span-2" v-if="permissions.canGSTData">
                    <CustomButton :href="route('reports.debitnote')">
                        <svg class="w-12 h-12 fill-current text-blue-600 inline-block" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6H6zm12 18H6V4h7v5h5v11z"/>
                            <circle cx="16" cy="16" r="3" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M16 15v2m-1-1h2" stroke="currentColor" stroke-width="2" fill="none"/>
                        </svg>
                        <span class="font-semibold text-lg ml-4">Debit Note Data</span>
                    </CustomButton>
                </div>
            </div>
        </div>
        </div>
    </AdminLayout>
</template>
