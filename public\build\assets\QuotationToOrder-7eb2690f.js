import{K as M,r as V,C as it,j as C,o as m,c as p,a as i,u as l,w as T,F as R,Z as dt,b as t,t as x,k as ct,v as ut,d as mt,n as _,f as y,i as pt,e as _t,g as A,s as gt,x as yt}from"./app-4c3f0163.js";import{_ as xt,a as vt}from"./AdminLayout-36b0d46a.js";import{_ as ht}from"./InputError-64c2d172.js";import{_ as w}from"./InputLabel-d6414ecf.js";import{P as ft}from"./PrimaryButton-353715d1.js";import{_ as b}from"./TextInput-e8957d69.js";import{_ as N}from"./TextArea-259ebf66.js";import{_ as U}from"./SearchableDropdown-84ab9b26.js";import{D as wt}from"./DangerButton-b3c50a37.js";import{_ as bt}from"./SecondaryButton-d521cdbf.js";import{M as Ft}from"./Modal-61735c0a.js";import{_ as Vt}from"./MultipleFileUpload-e24f2a14.js";import{_ as St}from"./FileViewer-e6911454.js";import{_ as H}from"./Checkbox-95e04efe.js";import{u as It}from"./index-b332ae81.js";import{_ as kt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const c=h=>(gt("data-v-89f604c5"),h=h(),yt(),h),Ct={class:"animate-top"},Tt={class:"sm:flex sm:items-center"},$t=c(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Quotation To Order")],-1)),Pt={class:"w-auto"},Ut={class:"flex space-x-2 items-center"},Gt=c(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6"},"Quotation Number:",-1)),Dt={class:"text-sm font-semibold text-gray-900 leading-6"},qt={class:"flex space-x-2 items-center"},Mt=c(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Date :",-1)),At=["onSubmit"],Nt={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Ot={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},jt={class:"sm:col-span-4"},zt={class:"relative mt-2"},Bt={class:"sm:col-span-4"},Qt={class:"relative mt-2"},Et={class:"sm:col-span-4"},Lt={class:"relative mt-2"},Rt={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border overflow-x-auto w-full"},Ht={class:"overflow-x-auto divide-y divide-gray-300 w-full"},Kt={scope:"col",class:""},Wt=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 text-left text-sm font-semibold text-gray-900"},"Model",-1)),Zt=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Image",-1)),Jt=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),Xt=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),Yt=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),te=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),ee=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),se={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},oe={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ae={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},le={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ne={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},re=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),ie=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),de=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),ce={class:"divide-y divide-gray-300 bg-white"},ue={class:"whitespace-nowrap py-3 text-sm text-gray-900"},me={class:"text-sm text-gray-900 leading-6 py-1.5"},pe={key:0,class:"text-red-500 text-xs absolute"},_e={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-80"},ge={class:"relative mt-2"},ye={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900"},xe={class:""},ve={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-96"},he={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},fe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},we={key:0,class:"text-red-500 text-xs absolute"},be={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Fe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Ve={class:"text-sm text-gray-900 leading-6 mt-2 py-1.5"},Se={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ie={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ke={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ce={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Te={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},$e={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Pe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-36"},Ue={class:"flex space-x-2"},Ge={class:"text-sm text-gray-900 leading-6 py-1.5"},De=["onClick"],qe=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Me=[qe],Ae={key:0,class:"mt-6 bg-white p-1 shadow sm:rounded-lg border"},Ne=c(()=>t("table",{class:"min-w-full divide-y divide-gray-300"},[t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"UPLOADED DOCUMENT "),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"REMOVE")])])],-1)),Oe=[Ne],je={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},ze={class:"items-center justify-between"},Be={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Qe={class:"inline-flex items-center justify-end w-full space-x-3"},Ee=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),Le={class:"text-base font-semibold text-gray-900"},Re={class:"inline-flex items-center justify-end w-full space-x-3"},He=c(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),Ke={class:"w-40"},We={class:"inline-flex items-center justify-end w-full space-x-3"},Ze=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),Je={class:"text-base font-semibold text-gray-900 w-w-32"},Xe={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},Ye=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),ts={class:"text-base font-semibold text-gray-900"},es={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},ss=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),os={class:"text-base font-semibold text-gray-900"},as={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},ls=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),ns={class:"text-base font-semibold text-gray-900"},rs={class:"inline-flex items-center justify-end w-full space-x-3"},is=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),ds={class:"text-base font-semibold text-gray-900"},cs={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},us={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},ms={class:"sm:col-span-10"},ps={class:"flex space-x-4"},_s={class:"w-full"},gs=c(()=>t("div",{class:"w-full"},null,-1)),ys={class:"w-full"},xs={class:"relative mt-2"},vs={class:"sm:col-span-10"},hs={class:"flex space-x-4"},fs={class:"w-full"},ws={class:"w-full"},bs={class:"w-full"},Fs={class:"sm:col-span-10"},Vs={class:"flex space-x-4"},Ss={class:"w-full"},Is={class:"w-full"},ks={class:"flex mt-6 items-center justify-between"},Cs={class:"ml-auto flex items-center justify-end gap-x-6"},Ts=c(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),$s={class:"p-6"},Ps=c(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this product? ",-1)),Us={class:"mt-6 flex justify-end"},Gs={__name:"QuotationToOrder",props:["customers","salesuser","products","filepath","category","organization","productpath"],setup(h){const O=h;M().props.filepath.view;const K=M().props.productpath.view,d=M().props.data[0],o=It("post","/convertOrder",{note:d.note,date:new Date().toISOString().slice(0,10),selectedProductItem:[],customer_id:d.customer_id,sales_user_id:d.sales_user_id,organization_id:d.organization_id,category:d.category,total_amount:"",quotation_number:d.quotation_number,quotation_id:d.id,document:d.documents,cgst:d.cgst,sgst:d.sgst,igst:d.igst,total_gst:d.total_gst,total_discount:d.total_discount,overall_discount:d.overall_discount,sub_total:d.sub_total,validity:d.validity,delivery:d.delivery,payment_terms:d.payment_terms,warranty:d.warranty}),W=()=>{o.sub_total=B.value,o.total_discount=Q.value,o.cgst=g.value=="CGST/SGST"?I.value/2:"0",o.sgst=g.value=="CGST/SGST"?I.value/2:"0",o.igst=g.value=="IGST"?I.value:"0",o.total_gst=I.value,o.total_amount=z.value,o.selectedProductItem=u.value,o.submit({preserveScroll:!0,onSuccess:()=>o.reset()})},u=V([{product_id:"",description:"",qty:"",price:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",discount:"",discount_amount:"",total_amount:"",quotation_detail_id:"",check:!1,image:""}]);it(()=>{u.value=d.quotation_detail.map(s=>{var a,e;return{product_id:s.product_id,quotation_detail_id:s.id,description:s.description,qty:s.qty,price:parseFloat(s.price).toFixed(2),total_price:parseFloat(s.total_price).toFixed(2),mrp:(e=(a=s.product)==null?void 0:a.serial_numbers[0])!=null&&e.mrp?parseFloat(s.product.serial_numbers[0].mrp).toFixed(2):"-",gst:parseFloat(s.gst).toFixed(2),sgst:parseFloat(s.gst/2).toFixed(2),gst_amount:parseFloat(s.gst_amount).toFixed(2),total_gst_amount:parseFloat(s.total_gst_amount).toFixed(2),total_amount:parseFloat(s.total_amount).toFixed(2),discount:parseFloat(s.discount).toFixed(2),discount_amount:parseFloat(s.discount_amount).toFixed(2)??"0",check:!1}}),u.value=d.quotation_detail.map(s=>{var e,r;const a=O.products.find(n=>n.id===s.product_id);return{check:!1,product_id:s.product_id,quotation_detail_id:s.id,description:s.description,image:a?a.image:null,qty:s.qty,price:parseFloat(s.price).toFixed(2),total_price:parseFloat(s.total_price).toFixed(2),mrp:(r=(e=s.product)==null?void 0:e.serial_numbers[0])!=null&&r.mrp?parseFloat(s.product.serial_numbers[0].mrp).toFixed(2):"-",gst:parseFloat(s.gst).toFixed(2),sgst:parseFloat(s.gst/2).toFixed(2),gst_amount:parseFloat(s.gst_amount).toFixed(2),total_gst_amount:parseFloat(s.total_gst_amount).toFixed(2),total_amount:parseFloat(s.total_amount).toFixed(2),discount:parseFloat(s.discount).toFixed(2),discount_amount:parseFloat(s.discount_amount).toFixed(2)||"0"}})});const g=V(d.customers.gst_type),Z=(s,a,e,r)=>{var v;const n=O.products.find(F=>F.id===s);n&&(u.value[e].product_id=n.id,u.value[e].price=parseFloat(n.price).toFixed(2),u.value[e].description=n.description,u.value[e].mrp=(v=n==null?void 0:n.serial_numbers[0])!=null&&v.mrp?parseFloat(n.serial_numbers[0].mrp).toFixed(2):"-",u.value[e].gst=parseFloat(n.gst).toFixed(2),u.value[e].sgst=parseFloat(n.gst/2).toFixed(2),u.value[e].image=n.image,u.value[e].discount="0.00",o.errors[`selectedProductItem.${e}.product_id`]=null,o.errors[`selectedProductItem.${e}.price`]=null,S(r))},G=V(!1),j=V(null),J=V(null),D=()=>{G.value=!1},X=()=>{o.get(route("removeproduct",{id:j.value,model:"QuotationDetail"}),{onSuccess:()=>{D(),u.value.splice(index,1)}})},Y=(s,a)=>{a!==void 0&&a!=""?(j.value=a,J.value=s,G.value=!0):u.value.splice(s,1)},tt=(s,a)=>{const e=parseFloat(s.price),r=parseFloat(s.discount)||0,n=g.value=="IGST"?parseFloat(s.gst):parseFloat(s.sgst*2),v=parseFloat(s.qty);let F=0,$=0;r>0?F=e*v:F=e*v*(1+n/100);const P=F*(r/100)||0,E=e*1*(n/100),q=(e*v-P)*(n/100);r>0?$=F-P+q:$=F-P;const L=e*v;return s.total_price=isNaN(L)?"":parseFloat(L).toFixed(2),s.gst_amount=isNaN(E)?"":parseFloat(E).toFixed(2),s.total_gst_amount=isNaN(q)?"":parseFloat(q).toFixed(2),s.discount_amount=isNaN(P)?"":parseFloat(P).toFixed(2),isNaN($)?"":parseFloat($).toFixed(2)},S=(s,a)=>{s.total_amount=tt(s)},z=C(()=>{const s=Math.round(u.value.reduce((e,r)=>e+(r.check&&r.total_amount?parseFloat(r.total_amount):0),0)),a=o.overall_discount?parseFloat(o.overall_discount):0;return s-a}),I=C(()=>u.value.reduce((s,a)=>s+(a.check&&a.total_gst_amount?parseFloat(a.total_gst_amount):0),0)),B=C(()=>u.value.reduce((s,a)=>s+(a.check&&a.total_price?parseFloat(a.total_price):0),0)),Q=C(()=>{const s=u.value.reduce((e,r)=>e+(r.check&&r.discount_amount?parseFloat(r.discount_amount):0),0),a=o.overall_discount?parseFloat(o.overall_discount):0;return s+a}),f=s=>{o.errors[s]=null};C(()=>{const s=new Date(d.date),a={year:"numeric",month:"long",day:"numeric"};return s.toLocaleDateString("en-US",a)});const et=s=>{o.document=s};V(null);const st=(s,a)=>{o.sales_user_id=s};V(null),V("custom");const k=s=>{let a=s.toFixed(2).toString(),[e,r]=a.split("."),n=e.substring(e.length-3),v=e.substring(0,e.length-3);return v!==""&&(n=","+n),`${v.replace(/\B(?=(\d{2})+(?!\d))/g,",")+n}.${r}`},ot=(s,a)=>{o.customer_id=s,o.errors.customer_id=null},at=(s,a)=>{o.category=s,o.errors.category=null},lt=(s,a)=>{o.organization_id=s,o.errors.organization_id=null},nt=C(()=>u.value.length>0&&u.value.every(s=>s.check)),rt=s=>{u.value.map(a=>{a.check=s})};return(s,a)=>(m(),p(R,null,[i(l(dt),{title:"Quotation"}),i(xt,null,{default:T(()=>[t("div",Ct,[t("div",Tt,[$t,t("div",Pt,[t("div",Ut,[Gt,t("span",Dt,x(l(d).quotation_number),1)]),t("div",qt,[Mt,ct(t("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":a[0]||(a[0]=e=>l(o).date=e),onChange:a[1]||(a[1]=e=>l(o).validate("date"))},null,544),[[ut,l(o).date]])])])]),t("form",{onSubmit:mt(W,["prevent"]),class:""},[t("div",Nt,[t("div",Ot,[t("div",jt,[i(w,{for:"company_name",value:"Organization"}),t("div",zt,[i(U,{options:h.organization,modelValue:l(o).organization_id,"onUpdate:modelValue":a[2]||(a[2]=e=>l(o).organization_id=e),onOnchange:lt,class:_({"error rounded-md":l(o).errors.organization_id}),editMode:"editMode"},null,8,["options","modelValue","class"])])]),t("div",Bt,[i(w,{for:"customer_id",value:"Customer Name"}),t("div",Qt,[i(U,{options:h.customers,modelValue:l(o).customer_id,"onUpdate:modelValue":a[3]||(a[3]=e=>l(o).customer_id=e),onOnchange:ot,class:_({"error rounded-md":l(o).errors.customer_id}),editMode:"editMode"},null,8,["options","modelValue","class"])])]),t("div",Et,[i(w,{for:"company_name",value:"Category"}),t("div",Lt,[i(U,{options:h.category,modelValue:l(o).category,"onUpdate:modelValue":a[4]||(a[4]=e=>l(o).category=e),onOnchange:at,class:_({"error rounded-md":l(o).errors.category}),editMode:"editMode"},null,8,["options","modelValue","class"])])])])]),t("div",Rt,[t("table",Ht,[t("thead",null,[t("tr",null,[t("th",Kt,[i(H,{checked:nt.value,"onUpdate:checked":rt},null,8,["checked"])]),Wt,Zt,Jt,Xt,Yt,te,ee,g.value=="IGST"?(m(),p("th",se,"IGST (%)")):y("",!0),g.value=="IGST"?(m(),p("th",oe,"IGST (₹)")):y("",!0),g.value=="CGST/SGST"?(m(),p("th",ae,"CGST (%)")):y("",!0),g.value=="CGST/SGST"?(m(),p("th",le,"SGST (%)")):y("",!0),g.value=="CGST/SGST"?(m(),p("th",ne,"Total GST (₹)")):y("",!0),re,ie,de])]),t("tbody",ce,[(m(!0),p(R,null,pt(u.value,(e,r)=>(m(),p("tr",{key:r},[t("td",ue,[t("div",me,[i(H,{name:"check",checked:e.check,"onUpdate:checked":n=>e.check=n},null,8,["checked","onUpdate:checked"])]),l(o).errors[`selectedProductItem.${r}.check`]?(m(),p("p",pe,x(l(o).errors[`selectedProductItem.${r}.check`]),1)):y("",!0)]),t("td",_e,[t("div",ge,[i(U,{options:h.products,modelValue:e.product_id,"onUpdate:modelValue":n=>e.product_id=n,onOnchange:(n,v)=>Z(n,v,r,e),onChange:a[5]||(a[5]=n=>l(o).validate("product_id")),class:_({"error rounded-md":l(o).errors[`selectedProductItem.${r}.product_id`]})},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class"])])]),t("td",ye,[t("div",xe,[i(St,{fileUrl:l(K)+e.image},null,8,["fileUrl"])])]),t("td",ve,[i(N,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":n=>e.description=n,autocomplete:"description",rows:2,onChange:n=>e.validate("description"),class:_({"error rounded-md":l(o).errors[`selectedProductItem.${r}.description`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),t("td",he,x(e.mrp??"-"),1),t("td",fe,[i(b,{id:"qty",type:"text",numeric:!0,modelValue:e.qty,"onUpdate:modelValue":n=>e.qty=n,autocomplete:"qty",onInput:n=>S(e,r),onChange:n=>f("selectedProductItem."+r+".qty"),class:_({error:l(o).errors[`selectedProductItem.${r}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"]),l(o).errors[`selectedProductItem.${r}.qty1`]?(m(),p("p",we,x(l(o).errors[`selectedProductItem.${r}.qty1`]),1)):y("",!0)]),t("td",be,[i(b,{id:"price",type:"text",modelValue:e.price,"onUpdate:modelValue":n=>e.price=n,autocomplete:"price",onInput:n=>S(e,r),onChange:n=>f("selectedProductItem."+r+".price"),class:_({error:l(o).errors[`selectedProductItem.${r}.price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Fe,[t("div",Ve,x(e.total_price),1)]),g.value=="IGST"?(m(),p("td",Se,[i(b,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":n=>e.gst=n,onInput:n=>S(e,r),onChange:n=>f("selectedProductItem."+r+".gst"),class:_({error:l(o).errors[`selectedProductItem.${r}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):y("",!0),g.value=="CGST/SGST"?(m(),p("td",Ie,[i(b,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":n=>e.sgst=n,onInput:n=>S(e,r),onChange:n=>f("selectedProductItem."+r+".gst"),class:_({error:l(o).errors[`selectedProductItem.${r}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):y("",!0),g.value=="CGST/SGST"?(m(),p("td",ke,[i(b,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":n=>e.sgst=n,onInput:n=>S(e,r),onChange:n=>f("selectedProductItem."+r+".gst"),class:_({error:l(o).errors[`selectedProductItem.${r}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):y("",!0),t("td",Ce,x(e.total_gst_amount),1),t("td",Te,[i(b,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":n=>e.discount=n,onInput:n=>S(e,r),onChange:n=>f("selectedProductItem."+r+".discount"),class:_({error:l(o).errors[`selectedProductItem.${r}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",$e,x(e.discount_amount),1),t("td",Pe,[t("div",Ue,[t("div",Ge,x(e.total_amount),1),t("button",{type:"button",class:"mt-1 flex",onClick:n=>Y(r,e.quotation_detail_id)},Me,8,De)])])]))),128))])])]),l(d).documents&&l(d).documents.length>0?(m(),p("div",Ae,Oe)):y("",!0),t("div",je,[t("div",ze,[t("div",Be,[t("div",Qe,[Ee,t("p",Le,x(k(B.value)),1)]),t("div",Re,[He,t("div",Ke,[i(b,{id:"overall_discount",type:"text",modelValue:l(o).overall_discount,"onUpdate:modelValue":a[6]||(a[6]=e=>l(o).overall_discount=e)},null,8,["modelValue"])])]),t("div",We,[Ze,t("p",Je,x(k(Q.value)),1)]),g.value=="IGST"?(m(),p("div",Xe,[Ye,t("p",ts,x(k(I.value)),1)])):y("",!0),g.value=="CGST/SGST"?(m(),p("div",es,[ss,t("p",os,x(k(I.value/2)),1)])):y("",!0),g.value=="CGST/SGST"?(m(),p("div",as,[ls,t("p",ns,x(k(I.value/2)),1)])):y("",!0),t("div",rs,[is,t("p",ds,x(k(z.value)),1)])])])]),t("div",cs,[t("div",us,[t("div",ms,[t("div",ps,[t("div",_s,[i(w,{for:"note",value:"Upload Documents"}),i(Vt,{inputId:"document",inputName:"document",onFiles:et})]),gs,t("div",ys,[i(w,{for:"company_name",value:"Sales Person"}),t("div",xs,[i(U,{options:h.salesuser,modelValue:l(o).sales_user_id,"onUpdate:modelValue":a[7]||(a[7]=e=>l(o).sales_user_id=e),onOnchange:st,class:_({"error rounded-md":l(o).errors.sales_user_id}),editMode:"editMode"},null,8,["options","modelValue","class"])])])])]),t("div",vs,[t("div",hs,[t("div",fs,[i(w,{for:"Validity",value:"Validity"}),i(b,{id:"price",type:"text",modelValue:l(o).validity,"onUpdate:modelValue":a[8]||(a[8]=e=>l(o).validity=e),onChange:a[9]||(a[9]=e=>f(l(o).errors.validity)),class:_({"error rounded-md":l(o).errors.validity})},null,8,["modelValue","class"])]),t("div",ws,[i(w,{for:"delivery",value:"Delivery"}),i(b,{id:"price",type:"text",modelValue:l(o).delivery,"onUpdate:modelValue":a[10]||(a[10]=e=>l(o).delivery=e),onChange:a[11]||(a[11]=e=>f(l(o).errors.delivery)),class:_({"error rounded-md":l(o).errors.delivery})},null,8,["modelValue","class"])]),t("div",bs,[i(w,{for:"warranty",value:"Warranty"}),i(b,{id:"price",type:"text",modelValue:l(o).warranty,"onUpdate:modelValue":a[12]||(a[12]=e=>l(o).warranty=e),onChange:a[13]||(a[13]=e=>f(l(o).errors.warranty)),class:_({"error rounded-md":l(o).errors.warranty})},null,8,["modelValue","class"])])])]),t("div",Fs,[t("div",Vs,[t("div",Ss,[i(w,{for:"payment_terms",value:"Payment terms"}),i(N,{id:"price",type:"text",rows:4,modelValue:l(o).payment_terms,"onUpdate:modelValue":a[14]||(a[14]=e=>l(o).payment_terms=e),onChange:a[15]||(a[15]=e=>f(l(o).errors.payment_terms)),class:_({"error rounded-md":l(o).errors.payment_terms})},null,8,["modelValue","class"])]),t("div",Is,[i(w,{for:"note",value:"Note"}),i(N,{id:"note",type:"text",rows:4,modelValue:l(o).note,"onUpdate:modelValue":a[16]||(a[16]=e=>l(o).note=e),autocomplete:"note",onChange:a[17]||(a[17]=e=>l(o).validate("note"))},null,8,["modelValue"]),l(o).invalid("note")?(m(),_t(ht,{key:0,class:"",message:l(o).errors.note},null,8,["message"])):y("",!0)])])])])]),t("div",ks,[t("div",Cs,[i(vt,{href:s.route("quotation.index")},{svg:T(()=>[Ts]),_:1},8,["href"]),i(ft,{disabled:l(o).processing},{default:T(()=>[A("Submit")]),_:1},8,["disabled"])])])],40,At)]),i(Ft,{show:G.value,onClose:D},{default:T(()=>[t("div",$s,[Ps,t("div",Us,[i(bt,{onClick:D},{default:T(()=>[A(" Cancel")]),_:1}),i(wt,{class:"ml-3",onClick:X},{default:T(()=>[A(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}},Js=kt(Gs,[["__scopeId","data-v-89f604c5"]]);export{Js as default};
