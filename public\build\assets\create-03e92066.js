import{h as V,r as d,o as i,c as r,a,u as l,w as c,F as b,Z as S,b as e,g,d as $,T as M,f as x,i as N,t as w}from"./app-6cdaf2bc.js";import{_ as B,a as F}from"./AdminLayout-b73e8538.js";import{_ as f}from"./InputLabel-38b98ddd.js";import{_ as T}from"./TextInput-61ab2d6e.js";import{P as k}from"./PrimaryButton-b7e37df1.js";import{Q as U}from"./vue-quill.snow-7ffd82f9.js";import{M as D}from"./Modal-8b2a2aa4.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const L={class:"animate-top"},P={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Q={class:"text-2xl font-semibold leading-7 text-gray-900 flex justify-between items-center"},I={class:"ml-auto flex items-center justify-end gap-x-6"},Z=["onSubmit"],q={class:"border-b border-gray-900/10 pb-12"},z={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},A={class:"sm:col-span-3"},G={class:"sm:col-span-3"},H={class:"sm:col-span-6"},J={class:"flex mt-6 items-center justify-between"},K={class:"ml-auto flex items-center justify-end gap-x-6"},O=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),R={key:0,class:"text-sm text-gray-600"},W={class:"p-6 relative"},X=e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1),Y=[X],ee=e("h2",{class:"text-lg font-medium text-gray-900"},"Email Tags",-1),te={class:"mt-4 overflow-x-auto sm:rounded-lg"},se={class:"w-full text-sm text-left text-gray-500"},oe=e("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50"},[e("tr",null,[e("th",{class:"px-4 py-2 text-gray-900"},"Tag Name"),e("th",{class:"px-4 py-2 text-gray-900"},"Description")])],-1),ae={key:0},le=e("td",{colspan:"2",class:"px-4 py-4 text-center text-gray-500"}," No Email Tags Found. ",-1),ne=[le],ie={class:"divide-y divide-gray-300 bg-white"},re={class:"px-4 py-2"},ce={class:"flex items-center space-x-2"},de=["onClick"],me={key:0,class:"text-green-600 text-xs"},ue={class:"px-4 py-2"},pe={class:"flex items-center space-x-2"},ke={__name:"create",props:["tags"],setup(v){const o=V({email_subject:"",template_name:"",content:""}),m=d(!1),h=d(null),C=n=>{h.value=n,m.value=!0},y=()=>{m.value=!1,h.value=null},u=d(null),p=d(""),j=(n,t,s)=>{navigator.clipboard.writeText(n).then(()=>{u.value=t,p.value=s,setTimeout(()=>{u.value=null,p.value=""},2e3)})},E=()=>{console.log("Submitting:",o),o.post(route("emailtemplates.store"),{preserveScroll:!0,onSuccess:()=>{console.log("Form submitted successfully"),o.reset()},onError:n=>console.log("Errors:",n)})};return(n,t)=>(i(),r(b,null,[a(l(S),{title:"Email Template"}),a(B,null,{default:c(()=>[e("div",L,[e("div",P,[e("h2",Q,[g(" Create Email Template "),e("div",I,[a(k,{onClick:t[0]||(t[0]=s=>C(n.tag))},{default:c(()=>[g(" Email Tags ")]),_:1})])]),e("form",{onSubmit:$(E,["prevent"]),class:""},[e("div",q,[e("div",z,[e("div",A,[a(f,{for:"email_subject",value:"Email Subject",class:"block text-sm font-medium text-gray-700"}),a(T,{id:"email_subject",type:"text",modelValue:l(o).email_subject,"onUpdate:modelValue":t[1]||(t[1]=s=>l(o).email_subject=s),autocomplete:"email_subject",onChange:t[2]||(t[2]=s=>l(o).validate("email_subject"))},null,8,["modelValue"])]),e("div",G,[a(f,{for:"template_name",value:"Template Name",class:"block text-sm font-medium text-gray-700"}),a(T,{id:"template_name",type:"text",modelValue:l(o).template_name,"onUpdate:modelValue":t[3]||(t[3]=s=>l(o).template_name=s),autocomplete:"template_name",onChange:t[4]||(t[4]=s=>l(o).validate("template_name"))},null,8,["modelValue"])]),e("div",H,[a(f,{value:"Email Content",class:"block text-sm font-medium text-gray-700"}),a(l(U),{content:l(o).content,"onUpdate:content":t[5]||(t[5]=s=>l(o).content=s),contentType:"html",theme:"snow",toolbar:"essential"},null,8,["content"])])])]),e("div",J,[e("div",K,[a(F,{href:n.route("emailtemplates.index")},{svg:c(()=>[O]),_:1},8,["href"]),a(k,{disabled:l(o).processing},{default:c(()=>[g("Save")]),_:1},8,["disabled"]),a(M,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:c(()=>[l(o).recentlySuccessful?(i(),r("p",R,"Saved.")):x("",!0)]),_:1})])])],40,Z)])]),a(D,{show:m.value,onClose:y},{default:c(()=>[e("div",W,[(i(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6 text-gray-500 absolute top-4 right-4 cursor-pointer hover:text-red-500",onClick:y},Y)),ee,e("div",te,[e("table",se,[oe,v.tags.length===0?(i(),r("tr",ae,ne)):x("",!0),e("tbody",ie,[(i(!0),r(b,null,N(v.tags,(s,_)=>(i(),r("tr",{key:_},[e("td",re,[e("div",ce,[e("span",null,w(s.name),1),e("span",{onClick:_e=>j(s.name,_,"name"),class:"cursor-pointer"}," 📋 ",8,de)]),u.value===_&&p.value==="name"?(i(),r("span",me," Copied! ")):x("",!0)]),e("td",ue,[e("div",pe,[e("span",null,w(s.description),1)])])]))),128))])])])])]),_:1},8,["show"])]),_:1})],64))}};export{ke as default};
