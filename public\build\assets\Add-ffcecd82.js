import{r as w,j as G,l as E,o as d,c as u,a as l,u as n,w as I,F as P,Z as se,b as s,t as v,f,d as ae,n as y,k as ne,v as le,i as M,g as ie,T as re,s as de,x as ue}from"./app-6a429cee.js";import{_ as ce,a as me}from"./AdminLayout-dc64724f.js";import{_ as h}from"./InputLabel-5e6ac969.js";import{P as _e}from"./PrimaryButton-c589c744.js";import{_ as V}from"./TextInput-94a28154.js";import{_ as pe}from"./TextArea-217f7d79.js";import{_ as ve}from"./RadioButton-4e48f1b7.js";import{_ as T}from"./SearchableDropdown-aa57848c.js";import{u as fe}from"./index-beae658c.js";/* empty css                                                                          */import{_ as ye}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as he}from"./Checkbox-4f9a3a6d.js";const A=b=>(de("data-v-c1b704ae"),b=b(),ue(),b),ge={class:"h-screen animate-top"},xe={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},be={class:"sm:flex sm:items-center"},ke=A(()=>s("div",{class:"sm:flex-auto"},[s("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Receive Payment")],-1)),we={class:"flex items-center justify-between"},Ve={key:0,class:"text-base font-semibold leading-6 text-gray-900"},Fe=["onSubmit"],Ce={class:"border-b border-gray-900/10 pb-12"},Ae={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},Ne={class:"sm:col-span-3"},Se={class:"relative mt-2"},$e={class:"sm:col-span-3"},Ue={class:"relative mt-2"},ze={class:"sm:col-span-2"},Ie={class:"relative mt-2"},Pe={key:0,class:"sm:col-span-3"},Te={class:"relative mt-2"},De={key:1,class:"sm:col-span-3"},Oe={key:2,class:"sm:col-span-2"},Be={key:3,class:"sm:col-span-1"},Ee={key:4,class:"sm:col-span-1"},Me={key:5,class:"sm:col-span-1"},je={key:6,class:"sm:col-span-3"},Re={key:7,class:"sm:col-span-2"},Le={class:"mt-4 flex justify-start"},Ye={class:"text-base font-semibold"},qe={key:8,class:"sm:col-span-2"},He={key:9,class:"sm:col-span-2"},Ze={key:10,class:"sm:col-span-2"},Ge={class:"relative mt-2"},Je={key:11,class:"sm:col-span-3"},Ke={class:"sm:col-span-6"},Qe={class:"overflow-x-auto divide-y divide-gray-300 w-full"},We=A(()=>s("div",{class:"w-full"},[s("thead",{class:"w-full"},[s("tr",{class:""},[s("th",{scope:"col",class:""}),s("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),s("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),s("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),s("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1)),Xe={style:{"overflow-y":"auto","max-height":"318px"}},et={class:"divide-y divide-gray-300 bg-white"},tt={class:"whitespace-nowrap px-2 text-sm text-gray-900"},ot={class:"text-sm text-gray-900 leading-6 py-1.5"},st={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},at={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},nt={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},lt={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},it={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},rt={key:0,class:"text-red-500 text-xs absolute"},dt={class:"whitespace-nowrap px-2 text-sm text-gray-900"},ut={class:"sm:col-span-2"},ct={class:"mt-2 p-3 bg-gray-50 rounded-md"},mt={class:"space-y-2 text-sm"},_t={class:"flex items-center gap-2"},pt=A(()=>s("hr",{class:"my-2"},null,-1)),vt={class:"flex justify-between items-center font-semibold"},ft=A(()=>s("span",null,"Settlement:",-1)),yt={key:0,class:"text-red-500 text-xs mt-1"},ht={key:12,class:"sm:col-span-6"},gt={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},xt=A(()=>s("thead",null,[s("tr",null,[s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),s("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1)),bt={class:"divide-y divide-gray-300 bg-white"},kt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},wt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},Vt={class:"flex flex-col"},Ft={class:"text-sm text-gray-900"},Ct={class:"whitespace-nowrap py-3 text-sm text-gray-900"},At={class:"whitespace-nowrap py-3 text-sm text-gray-900"},Nt={class:"flex mt-6 items-center justify-between"},St={class:"ml-auto flex items-center justify-end gap-x-6"},$t=A(()=>s("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),Ut={key:0,class:"text-sm text-gray-600"},zt={__name:"Add",props:["paymentType","bankinfo","organization","customers","invoices","credit"],setup(b){const N=b;w([]);const t=fe("post","/receipt",{organization_id:"",customer_id:"",payment_type:"",date:"",note:"",amount:0,tds_amount:0,discount_amount:0,round_off:0,check_number:"",bank_name:"",org_bank_id:"",invoice:[],settled_amount:"",advance_amount:"",is_credit:"",credit_data:[],total_unused_amount:""}),S=w(""),J=()=>{t.settled_amount=U.value,t.advance_amount=L.value,t.total_unused_amount=$.value,t.is_credit=p.value,t.invoice=x.value,t.credit_data=k.value,t.submit({preserveScroll:!0,onSuccess:()=>t.reset()})},K=(a,o)=>{S.value=o,t.payment_type=a,t.errors.payment_type=null,o==="Cash"?t.note="Cash":t.note==="Cash"&&(t.note="")},z=w([]),k=w([]),$=w(""),j=w([]),Q=(a,o)=>{const e=N.bankinfo.filter(i=>i.organization_id===a);j.value=e,t.customer_id&&R(t.customer_id,a);const c=N.credit.filter(i=>i.organization_id===a&&i.customer_id===t.customer_id);k.value=c,$.value=k.value.reduce((i,m)=>i+m.unused_amount,0),t.organization_id=a,t.errors.organization_id=null},W=(a,o)=>{R(a,t.organization_id);const e=N.credit.filter(c=>c.customer_id===a&&c.organization_id===t.organization_id);k.value=e,$.value=k.value.reduce((c,i)=>c+i.unused_amount,0),t.customer_id=a,t.errors.customer_id=null},R=(a,o)=>{if(!a||!o){z.value=[];return}const e=N.customers.find(m=>m.id===a),c=e==null?void 0:e.party_id,i=N.invoices.filter(m=>{const r=m.organization_id===o;return m.invoice_type==="sales"?r&&m.customer_id===a:m.invoice_type==="purchase"&&c?r&&m.party_id===c:!1});z.value=i},X=(a,o)=>{t.org_bank_id=a,t.errors.org_bank_id=null},U=G(()=>x.value.reduce((a,o)=>{if(o.check&&o.amount){const e=parseFloat(o.amount);return o.invoice_type==="sales"?a+e:a-e}return a},0)),L=G(()=>{const a=parseFloat(t.amount||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0),o=parseFloat(t.round_off||0),e=U.value;return a-e-o}),D=()=>{},F=a=>{let o=a.toFixed(2).toString(),[e,c]=o.split("."),i=e.substring(e.length-3),m=e.substring(0,e.length-3);return m!==""&&(i=","+i),`${m.replace(/\B(?=(\d{2})+(?!\d))/g,",")+i}.${c}`},Y=a=>{const o=new Date(a),e={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",e)},p=w("No"),ee=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],te=a=>{const o=p.value==="Yes"?parseFloat($.value||0):parseFloat(t.amount||0)+parseFloat(t.round_off||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0);if(!x.value[a].check){x.value[a].amount=0,O(o);return}O(o)},O=a=>{x.value.forEach(r=>{r.check&&(r.amount=0)});const o=x.value.filter(r=>r.check);if(o.length===0||oe(a,o))return;const c=o.filter(r=>r.invoice_type==="sales"),i=o.filter(r=>r.invoice_type==="purchase");let m=a;for(const r of c){if(m<=0)break;const _=parseFloat(r.pending_amount||0),g=Math.min(_,m);r.amount=g.toFixed(2),m-=g}for(const r of i){if(m<=0)break;const _=parseFloat(r.pending_amount||0),g=Math.min(_,m);r.amount=g.toFixed(2),m-=g}},oe=(a,o)=>{const e=o.filter(_=>_.invoice_type==="sales"),c=o.filter(_=>_.invoice_type==="purchase"),i=e.reduce((_,g)=>_+parseFloat(g.pending_amount||0),0),m=c.reduce((_,g)=>_+parseFloat(g.pending_amount||0),0),r=i-m;return Math.abs(a-Math.abs(r))<=1?(o.forEach(_=>{_.amount=parseFloat(_.pending_amount||0).toFixed(2)}),!0):r>0&&a>=r?(o.forEach(_=>{_.amount=parseFloat(_.pending_amount||0).toFixed(2)}),!0):!1},x=w([{id:"",date:"",invoice_no:"",total_amount:"",pending_amount:"",check:!1,amount:0}]),B=()=>{x.value=z.value.map(a=>({id:a.id,date:a.date,invoice_no:a.invoice_no,total_amount:parseFloat(a.total_amount||0).toFixed(2),pending_amount:parseFloat(a.pending_amount||0).toFixed(2),invoice_type:a.invoice_type||"sales",check:!1,amount:"0.00"}))};E(z,()=>{B()}),E(p,()=>{B()}),E(()=>t.amount,()=>{if(p.value==="No"){B();const a=parseFloat(t.amount||0)+parseFloat(t.round_off||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0);O(a)}});const C=a=>{t.errors[a]=null,t.errors.settled_amount=null};return(a,o)=>(d(),u(P,null,[l(n(se),{title:"Receipt"}),l(ce,null,{default:I(()=>[s("div",ge,[s("div",xe,[s("div",be,[ke,s("div",we,[k.value.length>0?(d(),u("div",Ve," Credits Available: ₹"+v(F($.value)),1)):f("",!0)])]),s("form",{onSubmit:ae(J,["prevent"]),class:""},[s("div",Ce,[s("div",Ae,[s("div",Ne,[l(h,{for:"payment_type",value:"Organization"}),s("div",Se,[l(T,{options:b.organization,modelValue:n(t).organization_id,"onUpdate:modelValue":o[0]||(o[0]=e=>n(t).organization_id=e),onOnchange:Q,class:y({"error rounded-md":n(t).errors.organization_id})},null,8,["options","modelValue","class"])])]),s("div",$e,[l(h,{for:"payment_type",value:"Customer"}),s("div",Ue,[l(T,{options:b.customers,modelValue:n(t).customer_id,"onUpdate:modelValue":o[1]||(o[1]=e=>n(t).customer_id=e),onOnchange:W,class:y({"error rounded-md":n(t).errors.customer_id})},null,8,["options","modelValue","class"])])]),s("div",ze,[l(h,{for:"role_id",value:"Payment Through Credit ?"}),s("div",Ie,[l(ve,{modelValue:p.value,"onUpdate:modelValue":o[2]||(o[2]=e=>p.value=e),options:ee},null,8,["modelValue"])])]),p.value=="No"?(d(),u("div",Pe,[l(h,{for:"payment_type",value:"Payment Type"}),s("div",Te,[l(T,{options:b.paymentType,modelValue:n(t).payment_type,"onUpdate:modelValue":o[3]||(o[3]=e=>n(t).payment_type=e),onOnchange:K,class:y({"error rounded-md":n(t).errors.payment_type})},null,8,["options","modelValue","class"])])])):f("",!0),p.value=="No"?(d(),u("div",De,[l(h,{for:"date",value:"Payment Date"}),ne(s("input",{"onUpdate:modelValue":o[4]||(o[4]=e=>n(t).date=e),class:y(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":n(t).errors.date}]),type:"date",onChange:o[5]||(o[5]=e=>C("date"))},null,34),[[le,n(t).date]])])):f("",!0),p.value=="No"?(d(),u("div",Oe)):f("",!0),p.value=="No"?(d(),u("div",Be,[l(h,{for:"tds_amount",value:"TDS Amount"}),l(V,{type:"text",onChange:o[6]||(o[6]=e=>C("tds_amount")),onInput:o[7]||(o[7]=e=>D()),modelValue:n(t).tds_amount,"onUpdate:modelValue":o[8]||(o[8]=e=>n(t).tds_amount=e),class:y({"error rounded-md":n(t).errors.tds_amount})},null,8,["modelValue","class"])])):f("",!0),p.value=="No"?(d(),u("div",Ee,[l(h,{for:"discount_amount",value:"Discount Amount"}),l(V,{type:"text",onChange:o[9]||(o[9]=e=>C("discount_amount")),onInput:o[10]||(o[10]=e=>D()),modelValue:n(t).discount_amount,"onUpdate:modelValue":o[11]||(o[11]=e=>n(t).discount_amount=e),class:y({"error rounded-md":n(t).errors.discount_amount})},null,8,["modelValue","class"])])):f("",!0),p.value=="No"?(d(),u("div",Me,[l(h,{for:"round_off",value:"Round Off"}),l(V,{type:"text",onChange:o[12]||(o[12]=e=>C("round_off")),modelValue:n(t).round_off,"onUpdate:modelValue":o[13]||(o[13]=e=>n(t).round_off=e),class:y({"error rounded-md":n(t).errors.round_off})},null,8,["modelValue","class"])])):f("",!0),p.value=="No"?(d(),u("div",je,[l(h,{for:"amount",value:"Amount"}),l(V,{id:"amount",type:"text",onChange:o[14]||(o[14]=e=>C("amount")),onInput:o[15]||(o[15]=e=>D()),modelValue:n(t).amount,"onUpdate:modelValue":o[16]||(o[16]=e=>n(t).amount=e),class:y({"error rounded-md":n(t).errors.amount})},null,8,["modelValue","class"])])):f("",!0),p.value=="No"?(d(),u("div",Re,[l(h,{for:"advance",value:"Advance(Ref) Amount"}),s("div",Le,[s("p",Ye,v(F(L.value)),1)])])):f("",!0),S.value=="Cheque"&&p.value=="No"?(d(),u("div",qe,[l(h,{for:"check_number",value:"Cheque Number"}),l(V,{id:"check_number",type:"text",modelValue:n(t).check_number,"onUpdate:modelValue":o[17]||(o[17]=e=>n(t).check_number=e),class:y({"error rounded-md":n(t).errors["data.check_number"]})},null,8,["modelValue","class"])])):f("",!0),S.value=="Cheque"&&p.value=="No"?(d(),u("div",He,[l(h,{for:"bank_name",value:"Bank Name"}),l(V,{id:"bank_name",type:"text",modelValue:n(t).bank_name,"onUpdate:modelValue":o[18]||(o[18]=e=>n(t).bank_name=e),class:y({"error rounded-md":n(t).errors["data.bank_name"]})},null,8,["modelValue","class"])])):f("",!0),S.value!="Cash"&&p.value=="No"?(d(),u("div",Ze,[l(h,{for:"org_bank_id",value:"Our Bank"}),s("div",Ge,[l(T,{options:j.value,modelValue:n(t).org_bank_id,"onUpdate:modelValue":o[19]||(o[19]=e=>n(t).org_bank_id=e),onOnchange:X,class:y({"error rounded-md":n(t).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):f("",!0),S.value!="Cash"&&p.value=="No"?(d(),u("div",Je)):f("",!0),s("div",Ke,[s("table",Qe,[We,s("div",Xe,[s("tbody",et,[(d(!0),u(P,null,M(x.value,(e,c)=>(d(),u("tr",{key:c},[s("td",tt,[s("div",ot,[l(he,{name:"check",checked:e.check,"onUpdate:checked":i=>e.check=i,onChange:i=>te(c)},null,8,["checked","onUpdate:checked","onChange"])])]),s("td",st,[s("span",{class:y([e.invoice_type==="sales"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},v(e.invoice_type==="sales"?"SALES":"PURCHASE"),3)]),s("td",at,v(e.invoice_no),1),s("td",nt,v(e.total_amount),1),s("td",lt,v(e.pending_amount),1),s("td",it,[l(V,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":i=>e.amount=i,onChange:i=>C("invoice."+c+".amount"),class:y({error:n(t).errors[`invoice.${c}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),n(t).errors[`invoice.${c}.amount`]?(d(),u("p",rt,v(n(t).errors[`invoice.${c}.amount`]),1)):f("",!0)]),s("td",dt,v(Y(e.date)),1)]))),128))])])])]),s("div",ut,[l(h,{for:"note",value:"Settlement Summary"}),s("div",ct,[s("div",mt,[(d(!0),u(P,null,M(x.value.filter(e=>e.check),e=>(d(),u("div",{key:e.id,class:"flex justify-between items-center"},[s("div",_t,[s("span",{class:y([e.invoice_type==="sales"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800","inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium"])},v(e.invoice_type==="sales"?"S":"P"),3),s("span",null,v(e.invoice_no),1)]),s("span",{class:y([e.invoice_type==="sales"?"text-green-600":"text-blue-600","font-medium"])},v(e.invoice_type==="sales"?"+":"-")+"₹"+v(F(parseFloat(e.amount||0))),3)]))),128)),pt,s("div",vt,[ft,s("span",{class:y(U.value>=0?"text-green-600":"text-red-600")}," ₹"+v(F(Math.abs(U.value)))+" "+v(U.value>=0?"(Receive)":"(Pay)"),3)])])]),n(t).errors.settled_amount?(d(),u("p",yt,v(n(t).errors.settled_amount),1)):f("",!0)]),p.value=="No"?(d(),u("div",ht,[l(h,{for:"note",value:"Note"}),l(pe,{id:"note",type:"text",rows:2,modelValue:n(t).note,"onUpdate:modelValue":o[20]||(o[20]=e=>n(t).note=e)},null,8,["modelValue"])])):f("",!0)]),k.value.length>0&&p.value=="Yes"?(d(),u("table",gt,[xt,s("tbody",bt,[(d(!0),u(P,null,M(k.value,(e,c)=>{var i,m,r,_,g,q,H,Z;return d(),u("tr",{key:c},[s("td",kt,v(Y(e.date)),1),s("td",wt,[s("div",Vt,[s("div",Ft,v((m=(i=e.paymentreceive)==null?void 0:i.bank_info)!=null&&m.bank_name?(_=(r=e.paymentreceive)==null?void 0:r.bank_info)==null?void 0:_.bank_name:"Cash")+" - "+v((q=(g=e.paymentreceive)==null?void 0:g.bank_info)!=null&&q.account_number?(Z=(H=e.paymentreceive)==null?void 0:H.bank_info)==null?void 0:Z.account_number:""),1)])]),s("td",Ct,v(F(e.amount)),1),s("td",At,v(F(e.unused_amount)),1)])}),128))])])):f("",!0)]),s("div",Nt,[s("div",St,[l(me,{href:a.route("receipt.index")},{svg:I(()=>[$t]),_:1},8,["href"]),l(_e,{disabled:n(t).processing},{default:I(()=>[ie("Save")]),_:1},8,["disabled"]),l(re,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:I(()=>[n(t).recentlySuccessful?(d(),u("p",Ut,"Saved.")):f("",!0)]),_:1})])])],40,Fe)])])]),_:1})],64))}},qt=ye(zt,[["__scopeId","data-v-c1b704ae"]]);export{qt as default};
