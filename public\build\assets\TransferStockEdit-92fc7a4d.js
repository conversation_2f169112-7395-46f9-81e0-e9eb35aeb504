import{K as nt,r as g,C as kt,j,l as Tt,o as p,c as x,a as d,u as i,w,F as z,Z as $t,b as t,t as _,k as Dt,v as Ut,d as Gt,f as v,i as it,g as T,n as C,e as Nt,s as jt,x as Mt,q as At}from"./app-6a429cee.js";import{_ as Et,a as Ot}from"./AdminLayout-dc64724f.js";import{_ as dt}from"./InputError-17731bba.js";import{_ as S}from"./InputLabel-5e6ac969.js";import{P as ct}from"./PrimaryButton-c589c744.js";import{_ as f}from"./TextInput-94a28154.js";import{_ as qt}from"./TextArea-217f7d79.js";import{_ as L}from"./SearchableDropdown-aa57848c.js";import{D as rt}from"./DangerButton-c7881a4e.js";import{_ as R}from"./SecondaryButton-9a822eb1.js";import{M as H}from"./Modal-b05cc76d.js";import{_ as Bt}from"./FileViewer-9c05cdb2.js";import{_ as zt}from"./MultipleFileUpload-dd7eb917.js";import{u as Lt}from"./index-beae658c.js";import{_ as Rt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const r=$=>(jt("data-v-480e83d8"),$=$(),Mt(),$),Ht={class:"animate-top"},Qt={class:"sm:flex sm:items-center"},Wt=r(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Invoice")],-1)),Kt={class:"w-auto"},Xt={class:"flex space-x-2 items-center"},Yt={class:"text-sm font-semibold text-gray-900"},Zt={class:"flex space-x-2 items-center"},Jt=["onSubmit"],te={class:"mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},ee={class:"inline-flex items-start space-x-6 justify-start w-full"},se={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},oe={class:"inline-flex items-center justify-start w-full space-x-2"},le=r(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Customer Name:",-1)),ae={class:"text-sm leading-6 text-gray-700"},ne={class:"inline-flex items-center justify-start w-full space-x-2"},ie=r(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"GST No:",-1)),de={class:"text-sm leading-6 text-gray-700"},ce={class:"inline-flex items-center justify-start w-full space-x-2"},re=r(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Email:",-1)),ue={class:"text-sm leading-6 text-gray-700"},_e={class:"inline-flex items-center justify-start w-full space-x-2"},me=r(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1)),pe={class:"text-sm leading-6 text-gray-700"},xe={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},fe={class:"inline-flex items-center justify-start w-full space-x-2"},he=r(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Invoice Number:",-1)),ve={class:"text-sm leading-6 text-gray-700"},ge={class:"inline-flex items-center justify-start w-full space-x-2"},ye=r(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Category:",-1)),we={class:"text-sm leading-6 text-gray-700"},be={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border w-full"},Ve={class:"overflow-x-auto w-full"},Se={class:"overflow-x-auto divide-y divide-gray-300",style:{"margin-bottom":"160px"}},Ce=r(()=>t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Product Name",-1)),Ie=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Batch",-1)),Fe=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"HSN",-1)),Pe=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"EXP",-1)),ke=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),Te=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),$e=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),De=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),Ue=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Sell Price (₹)",-1)),Ge=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),Ne={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},je={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Me={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Ae={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Ee={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Oe=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),qe=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),Be=r(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),ze={class:"divide-y divide-gray-300 bg-white"},Le={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-96"},Re={class:"relative mt-2"},He={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-60"},Qe={class:"relative mt-2"},We={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ke={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Xe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},Ye={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ze={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Je={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ts={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},es={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},ss={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},os={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ls={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},as={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ns={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},is={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ds={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 flex space-x-2 min-w-48"},cs={class:"px-3 py-3 text-sm text-gray-900"},rs=["onClick"],us=r(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),_s=[us],ms={class:"flex items-center justify-between"},ps={key:0,class:""},xs=r(()=>t("span",{class:"text-lg font-semibold text-gray-900 leading-6"},"PREVIOUS INVOICE",-1)),fs=[xs],hs={class:"ml-auto flex items-center justify-end gap-x-6"},vs={key:0,class:"flex justify-between"},gs={class:"flex space-x-2 items-center"},ys=r(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"PRODUCT:",-1)),ws={class:"text-sm font-semibold text-gray-700 leading-6"},bs={class:"flex space-x-2 items-center"},Vs=r(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"HSN Code:",-1)),Ss={class:"text-sm font-semibold text-gray-700 leading-6"},Cs={class:"flex space-x-2 items-center"},Is=r(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"QTY:",-1)),Fs={class:"text-sm font-semibold text-gray-700 leading-6"},Ps={class:"flex space-x-2 items-center"},ks=r(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"PRICE (₹):",-1)),Ts={class:"text-sm font-semibold text-gray-700 leading-6"},$s={class:"flex space-x-2 items-center"},Ds=r(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"GST (%):",-1)),Us={class:"text-sm font-semibold text-gray-700 leading-6"},Gs={class:"flex space-x-2 items-center"},Ns=r(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"TOTAL AMOUNT (₹):",-1)),js={class:"text-sm font-semibold text-gray-700 leading-6"},Ms=r(()=>t("div",null,null,-1)),As={key:0,class:"mt-6 bg-white p-1 shadow sm:rounded-lg border"},Es={class:"min-w-full divide-y divide-gray-300"},Os=r(()=>t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"UPLOADED DOCUMENT"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1)),qs={class:"divide-y divide-gray-300 bg-white"},Bs={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},zs={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},Ls=["onClick"],Rs=r(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Hs=[Rs],Qs=["onClick"],Ws=r(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),Ks=[Ws],Xs=["onClick"],Ys=r(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),Zs=[Ys],Js={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},to={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},eo={class:"sm:col-span-3 space-y-4"},so={class:"flex space-x-4"},oo={class:"w-full"},lo={class:"w-full"},ao={class:"relative mt-2"},no={class:"flex space-x-4"},io={class:"w-full"},co={class:"w-full"},ro={class:"w-full"},uo={class:"flex space-x-4"},_o={class:"w-full"},mo={class:"w-full"},po={class:"w-full"},xo={class:"flex space-x-4"},fo={class:"w-full"},ho={class:"w-full"},vo={class:"sm:col-span-3"},go={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},yo={class:"inline-flex items-center justify-end w-full space-x-3"},wo=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),bo={class:"text-base font-semibold text-gray-900 w-w-32"},Vo={class:"inline-flex items-center justify-end w-full space-x-3"},So=r(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Discount On Sub Total(₹):",-1)),Co={class:"w-40"},Io={class:"inline-flex items-center justify-end w-full space-x-3"},Fo=r(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),Po={class:"w-40"},ko={class:"inline-flex items-center justify-end w-full space-x-3"},To=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),$o={class:"text-base font-semibold text-gray-900 w-w-32"},Do={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},Uo=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),Go={class:"text-base font-semibold text-gray-900 w-w-32"},No={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},jo=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),Mo={class:"text-base font-semibold text-gray-900 w-w-32"},Ao={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},Eo=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),Oo={class:"text-base font-semibold text-gray-900 w-w-32"},qo={class:"inline-flex items-center justify-end w-full space-x-3"},Bo=r(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),zo={class:"text-base font-semibold text-gray-900 w-w-32"},Lo={class:"flex mt-6 items-center justify-between"},Ro={class:"ml-auto flex items-center justify-end gap-x-6"},Ho=r(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),Qo={class:"p-6"},Wo=r(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to Remove this product? ",-1)),Ko={class:"mt-6 flex justify-end"},Xo={class:"p-6"},Yo=r(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this document? ",-1)),Zo={class:"mt-6 flex justify-end"},Jo={class:"p-6"},tl={class:"mt-6 px-4 flex justify-end"},el={__name:"TransferStockEdit",props:["customers","serialno","filepath","products","salesuser","invoice_details"],setup($){const D=$,Q=nt().props.filepath.view,c=nt().props.data[0],W=g([]),ut=D.products.filter(s=>s.serial_numbers.some(e=>e.organization_id===c.organization_id));W.value=ut;const K=g([]),_t=D.serialno;K.value=_t;const u=g([{invoice_detail_id:"",editmode:"",serial_number_id:"",product_id:"",product_name:"",item_code:"",hsn_code:"",price:"",sell_price:"",discount:"",discount_amount:"",discount_before_tax_product:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",qty:"",expiry_date:"",mrp:"",description:""}]),b=g({product_name:"",hsn_code:"",qty:"",price:"",gst:"",total_amount:""});kt(()=>{u.value=c.invoice_detail.map(s=>({invoice_detail_id:s.id,editmode:"editMode",serial_number_id:s.serial_number_id,product_name:s.serialnumbers.product.name,hsn_code:s.serialnumbers.product.hsn_code,item_code:s.serialnumbers.product.item_code,discount:parseFloat(s.discount).toFixed(2),price:parseFloat(s.price).toFixed(2),total_price:parseFloat(s.total_price).toFixed(2),gst:parseFloat(s.gst).toFixed(2),sgst:parseFloat(s.gst/2).toFixed(2),gst_amount:parseFloat(s.gst_amount).toFixed(2),total_gst_amount:parseFloat(s.total_gst_amount).toFixed(2),total_amount:parseFloat(s.total_amount).toFixed(2),product_id:s.serialnumbers.product.id,qty:s.qty,expiry_date:s.serialnumbers.expiry_date,mrp:s.serialnumbers.mrp?parseFloat(s.serialnumbers.mrp).toFixed(2):"-",description:s.description,sell_price:parseFloat(s.price).toFixed(2),discount_amount:parseFloat(s.discount_amount).toFixed(2)??"0"}))});const h=g(c.customers.gst_type),mt=()=>{u.value.push({invoice_detail_id:"",serial_number_id:"",product_name:"",hsn_code:""})},pt=(s,a,e)=>D.serialno.filter(l=>l.product_id===s&&l.organization_id===c.organization_id),xt=async(s,a,e)=>{const n=D.serialno.filter(l=>l.product_id===s&&l.organization_id===c.organization_id);K.value=n,u.value[e].product_id=s;try{const l=await At.post("/api/invoices/previous",{customer_id:o.customer_id,organization_id:o.organization_id,product_id:s});l.data.success?b.value=l.data.data:b.value={product_name:"",hsn_code:"",qty:"",price:"",gst:"",total_amount:""}}catch(l){console.error("Error fetching previous invoice:",l),b.value={product_name:"",hsn_code:"",qty:"",price:"",gst:"",total_amount:""}}},ft=(s,a,e)=>{const n=D.serialno.find(l=>l.id===s);n&&(u.value[e].qty="",u.value[e].serial_number_id=n.id,u.value[e].product_name=n.product.name,u.value[e].item_code=n.product.item_code,u.value[e].expiry_date=n.expiry_date,u.value[e].mrp=n.mrp?parseFloat(n.mrp).toFixed(2):"-",u.value[e].price=parseFloat(n.purchase_price).toFixed(2),u.value[e].hsn_code=n.product.hsn_code,u.value[e].discount="0.00",u.value[e].sell_price="",u.value[e].total_price=parseFloat(n.purchase_price).toFixed(2),u.value[e].gst=parseFloat(n.product.gst).toFixed(2),u.value[e].sgst=parseFloat(n.product.gst/2).toFixed(2),u.value[e].gst_amount="",u.value[e].total_gst_amount="",u.value[e].total_amount="",u.value[e].description="",o.errors[`selectedProductItem.${e}.serial_number_id`]=null)},M=g(!1),X=g(null),ht=g(null),A=()=>{M.value=!1},vt=()=>{o.get(route("removetransferproduct",{id:X.value}),{onSuccess:()=>{A(),u.value.splice(index,1)}})},gt=(s,a)=>{a!==void 0&&a!=""?(X.value=a,ht.value=s,M.value=!0):u.value.splice(s,1)},P=s=>{o.errors[s]=null},yt=s=>{o.document=s},E=g(!1),Y=g(null),wt=s=>{Y.value=s,E.value=!0},bt=()=>{o.get(route("removedocument",{id:Y.value,name:"Invoice"}),{onSuccess:()=>{O()}})},O=()=>{E.value=!1},q=g(!1),Z=g(null),Vt=g("custom"),St=s=>{Z.value=s,q.value=!0},J=()=>{q.value=!1},Ct=s=>{const a=window.location.origin+Q+s,e=document.createElement("a");e.href=a,e.setAttribute("download",s),document.body.appendChild(e),e.click(),document.body.removeChild(e)},I=s=>{if(s==null||isNaN(s))return"0.00";let a=Number(s).toFixed(2),[e,n]=a.split("."),l=e.substring(e.length-3),m=e.substring(0,e.length-3);return m!==""&&(l=","+l),`${m.replace(/\B(?=(\d{2})+(?!\d))/g,",")+l}.${n}`},It=(s,a)=>{const e=parseFloat(s.sell_price),n=parseFloat(s.discount_before_tax_product)||0,l=parseFloat(s.discount)||0,m=h.value=="IGST"?s.gst:parseFloat(s.sgst*2),V=parseFloat(s.qty);let y=0,N=0;l>0||n>0?y=e*V:y=e*V*(1+m/100);const U=y*(l/100)||0,lt=e*1*(m/100),B=(e*V-U-n)*(m/100);l>0||n>0?N=y-U-n+B:N=y-U;const at=e*V;return s.total_price=isNaN(at)?"":parseFloat(at).toFixed(2),s.gst_amount=isNaN(lt)?"":parseFloat(lt).toFixed(2),s.total_gst_amount=isNaN(B)?"":parseFloat(B).toFixed(2),s.discount_amount=isNaN(U)?"":parseFloat(U).toFixed(2),s.gst=m,isNaN(N)?"":parseFloat(N).toFixed(2)},F=(s,a)=>{ot(),s.total_amount=It(s)},tt=j(()=>{const s=Math.round(u.value.reduce((e,n)=>e+(n.total_amount?parseFloat(n.total_amount):0),0)),a=o.overall_discount?parseFloat(o.overall_discount):0;return s-a}),k=j(()=>u.value.reduce((s,a)=>s+(a.total_gst_amount?parseFloat(a.total_gst_amount):0),0)),et=j(()=>u.value.reduce((s,a)=>s+(a.total_price?parseFloat(a.total_price):0),0)),st=j(()=>{const s=u.value.reduce((n,l)=>n+(l.discount_amount?parseFloat(l.discount_amount):0),0),a=o.overall_discount?parseFloat(o.overall_discount):0,e=o.discount_before_tax?parseFloat(o.discount_before_tax):0;return s+a+e}),o=Lt("post","/invoice",{stock_transfer:"yes",date:c.date,purchase_order_id:c.purchase_order_id,purchase_order_receive_id:c.purchase_order_receive_id,organization_id:c.organization.id,invoice_id:c.id,category:c.category,note:c.note,sales_user_id:c.sales_user_id,selectedProductItem:[],customer_id:c.customer_id,invoice_no:c.invoice_no,document:c.documents,cgst:c.cgst,sgst:c.sgst,igst:c.igst,total_gst:c.total_gst,sub_total:c.sub_total,total_amount:c.total_amount,total_discount:c.total_discount,overall_discount:c.overall_discount,discount_before_tax:c.discount_before_tax,dispatch:c.dispatch,transport:c.transport,patient_name:c.patient_name,customer_po_date:c.customer_po_date,customer_po_number:c.customer_po_number,eway_bill:c.eway_bill,due_days:c.due_days,cr_dr_note:c.cr_dr_note}),Ft=(s,a)=>{o.sales_user_id=s},Pt=()=>{o.sub_total=et.value,o.invoice_no=D.invoice_no,o.cgst=h.value=="CGST/SGST"?k.value/2:"0",o.sgst=h.value=="CGST/SGST"?k.value/2:"0",o.igst=h.value=="IGST"?k.value:"0",o.total_gst=k.value,o.total_amount=tt.value,o.total_discount=st.value,o.selectedProductItem=u.value,o.submit({preserveScroll:!0,onSuccess:()=>o.reset()})},G=(s,a)=>{const e=a.length,n=a.reduce((V,y)=>V+(y.total_price?parseFloat(y.total_price):0),0),l=u.value.reduce((V,y)=>V+(y.total_price?parseFloat(y.total_price):0),0),m=s*n/l/e;a.forEach(V=>{V.discount_before_tax_product=m})},ot=()=>{const s=parseFloat(o.discount_before_tax)||0,a=u.value.filter(m=>m.gst==5&&m.total_price>0),e=u.value.filter(m=>m.gst==12&&m.total_price>0),n=u.value.filter(m=>m.gst==18&&m.total_price>0),l=u.value.filter(m=>m.gst==28&&m.total_price>0);G(s,a),G(s,e),G(s,n),G(s,l)};return Tt(()=>o.discount_before_tax,s=>{ot(),u.value.forEach(a=>{F(a)})}),(s,a)=>(p(),x(z,null,[d(i($t),{title:"Invoice"}),d(Et,null,{default:w(()=>[t("div",Ht,[t("div",Qt,[Wt,t("div",Kt,[t("div",Xt,[t("p",Yt,_(i(c).organization.name),1),t("div",Zt,[Dt(t("input",{class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":a[0]||(a[0]=e=>i(o).date=e),onChange:a[1]||(a[1]=e=>i(o).validate("date"))},null,544),[[Ut,i(o).date]])])])])]),t("form",{onSubmit:Gt(Pt,["prevent"]),class:""},[t("div",te,[t("div",ee,[t("div",se,[t("div",oe,[le,t("p",ae,_(i(c).customers.customer_name),1)]),t("div",ne,[ie,t("p",de,_(i(c).customers.gst_no??"-"),1)]),t("div",ce,[re,t("p",ue,_(i(c).customers.email??"-"),1)]),t("div",_e,[me,t("p",pe,_(i(c).customers.contact_no??"-"),1)])]),t("div",xe,[t("div",fe,[he,t("span",ve,_(i(c).invoice_no),1)]),t("div",ge,[ye,t("p",we,_(i(c).category??"-"),1)])])])]),t("div",be,[t("div",Ve,[t("table",Se,[t("thead",null,[t("tr",null,[Ce,Ie,Fe,Pe,ke,Te,$e,De,Ue,Ge,h.value=="IGST"?(p(),x("th",Ne,"IGST (%)")):v("",!0),h.value=="IGST"?(p(),x("th",je,"IGST (₹)")):v("",!0),h.value=="CGST/SGST"?(p(),x("th",Me,"CGST (%)")):v("",!0),h.value=="CGST/SGST"?(p(),x("th",Ae,"SGST (%)")):v("",!0),h.value=="CGST/SGST"?(p(),x("th",Ee,"Total GST (₹)")):v("",!0),Oe,qe,Be])]),t("tbody",ze,[(p(!0),x(z,null,it(u.value,(e,n)=>(p(),x("tr",{key:n},[t("td",Le,[t("div",Re,[d(L,{options:W.value,modelValue:e.product_id,"onUpdate:modelValue":l=>e.product_id=l,onOnchange:(l,m)=>xt(l,m,n),onChange:a[2]||(a[2]=l=>i(o).validate("product_id")),class:C({"error rounded-md":i(o).errors[`selectedProductItem.${n}.product_id`]}),editMode:e.editmode},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class","editMode"])])]),t("td",He,[t("div",Qe,[d(L,{options:pt(e.product_id),modelValue:e.serial_number_id,"onUpdate:modelValue":l=>e.serial_number_id=l,onOnchange:(l,m)=>ft(l,m,n),onChange:a[3]||(a[3]=l=>i(o).validate("serial_number_id")),class:C({"error rounded-md":i(o).errors[`selectedProductItem.${n}.serial_number_id`]}),editMode:e.editmode},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class","editMode"])])]),t("td",We,_(e.hsn_code??"-"),1),t("td",Ke,_(e.expiry_date??"-"),1),t("td",Xe,[d(f,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":l=>e.description=l,onInput:l=>F(e,n),onChange:l=>P("selectedProductItem."+n+".description"),class:C({error:i(o).errors[`selectedProductItem.${n}.description`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Ye,_(e.mrp??"-"),1),t("td",Ze,_(e.price),1),t("td",Je,[d(f,{id:"qty",type:"text",modelValue:e.qty,"onUpdate:modelValue":l=>e.qty=l,onInput:l=>F(e,n),onChange:l=>P("selectedProductItem."+n+".qty"),disabled:e.editmode,class:C({error:i(o).errors[`selectedProductItem.${n}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","disabled","class"]),d(dt,{message:i(o).errors[`selectedProductItem.${n}.qty`]},null,8,["message"])]),t("td",ts,[d(f,{id:"sell_price",type:"text",modelValue:e.sell_price,"onUpdate:modelValue":l=>e.sell_price=l,onInput:l=>F(e,n),onChange:l=>P("selectedProductItem."+n+".sell_price"),disabled:e.editmode,class:C({error:i(o).errors[`selectedProductItem.${n}.sell_price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","disabled","class"])]),t("td",es,_(e.total_price),1),h.value=="IGST"?(p(),x("td",ss,[d(f,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":l=>e.gst=l,onInput:l=>F(e,n),onChange:l=>P("selectedProductItem."+n+".gst"),disabled:e.editmode,class:C({error:i(o).errors[`selectedProductItem.${n}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","disabled","class"])])):v("",!0),h.value=="CGST/SGST"?(p(),x("td",os,[d(f,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":l=>e.sgst=l,onInput:l=>F(e,n),onChange:l=>P("selectedProductItem."+n+".gst"),disabled:e.editmode,class:C({error:i(o).errors[`selectedProductItem.${n}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","disabled","class"])])):v("",!0),h.value=="CGST/SGST"?(p(),x("td",ls,[d(f,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":l=>e.sgst=l,onInput:l=>F(e,n),onChange:l=>P("selectedProductItem."+n+".gst"),disabled:e.editmode,class:C({error:i(o).errors[`selectedProductItem.${n}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","disabled","class"])])):v("",!0),t("td",as,_(e.total_gst_amount),1),t("td",ns,[d(f,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":l=>e.discount=l,onInput:l=>F(e,n),disabled:e.editmode,onChange:l=>P("selectedProductItem."+n+".discount"),class:C({error:i(o).errors[`selectedProductItem.${n}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","disabled","onChange","class"])]),t("td",is,_(e.discount_amount),1),t("td",ds,[t("div",cs,_(e.total_amount),1),t("button",{type:"button",class:"mt-1",onClick:l=>gt(n,e.invoice_detail_id)},_s,8,rs)])]))),128))])])]),t("div",ms,[b.value.product_name?(p(),x("div",ps,fs)):v("",!0),t("div",hs,[d(ct,{onClick:mt,type:"button"},{default:w(()=>[T("Add Product")]),_:1})])]),b.value.product_name?(p(),x("div",vs,[t("div",null,[t("div",gs,[ys,t("span",ws,_(b.value.product_name),1)]),t("div",bs,[Vs,t("span",Ss,_(b.value.hsn_code),1)]),t("div",Cs,[Is,t("span",Fs,_(b.value.qty),1)])]),t("div",null,[t("div",Ps,[ks,t("span",Ts,_(I(b.value.price)),1)]),t("div",$s,[Ds,t("span",Us,_(I(b.value.gst)),1)]),t("div",Gs,[Ns,t("span",js,_(I(b.value.total_amount)),1)])]),Ms])):v("",!0)]),i(c).documents&&i(c).documents.length>0?(p(),x("div",As,[t("table",Es,[Os,t("tbody",qs,[(p(!0),x(z,null,it(i(c).documents,(e,n)=>(p(),x("tr",{key:i(c).id,class:""},[t("td",Bs,_(e.orignal_name),1),t("td",zs,[t("button",{type:"button",onClick:l=>wt(e.id)},Hs,8,Ls),t("button",{type:"button",onClick:l=>St(e.name)},Ks,8,Qs),t("button",{type:"button",onClick:l=>Ct(e.name)},Zs,8,Xs)])]))),128))])])])):v("",!0),t("div",Js,[t("div",to,[t("div",eo,[t("div",so,[t("div",oo,[d(S,{for:"note",value:"Upload Documents"}),d(zt,{inputId:"document",inputName:"document",uploadedFiles:i(o).document,onFiles:yt},null,8,["uploadedFiles"])]),t("div",lo,[d(S,{for:"sales_user_id",value:"Sales Person"}),t("div",ao,[d(L,{options:$.salesuser,modelValue:i(o).sales_user_id,"onUpdate:modelValue":a[4]||(a[4]=e=>i(o).sales_user_id=e),onOnchange:Ft,class:C({"error rounded-md":i(o).errors.sales_user_id})},null,8,["options","modelValue","class"])])])]),t("div",no,[t("div",io,[d(S,{for:"company_name",value:"Transport"}),d(f,{id:"gst",type:"text",modelValue:i(o).dispatch,"onUpdate:modelValue":a[5]||(a[5]=e=>i(o).dispatch=e)},null,8,["modelValue"])]),t("div",co,[d(S,{for:"company_name",value:"Dispatch"}),d(f,{id:"transport",type:"text",modelValue:i(o).transport,"onUpdate:modelValue":a[6]||(a[6]=e=>i(o).transport=e)},null,8,["modelValue"])]),t("div",ro,[d(S,{for:"eway_bill",value:"Eway Bill"}),d(f,{id:"eway_bill",type:"text",modelValue:i(o).eway_bill,"onUpdate:modelValue":a[7]||(a[7]=e=>i(o).eway_bill=e)},null,8,["modelValue"])])]),t("div",uo,[t("div",_o,[d(S,{for:"company_name",value:"PO Number"}),d(f,{id:"gst",type:"text",modelValue:i(o).customer_po_number,"onUpdate:modelValue":a[8]||(a[8]=e=>i(o).customer_po_number=e)},null,8,["modelValue"])]),t("div",mo,[d(S,{for:"company_name",value:"PO Date"}),d(f,{id:"customer_po_date",type:"date",modelValue:i(o).customer_po_date,"onUpdate:modelValue":a[9]||(a[9]=e=>i(o).customer_po_date=e)},null,8,["modelValue"])]),t("div",po,[d(S,{for:"due_days",value:"Due Days"}),d(f,{id:"due_days",type:"text",modelValue:i(o).due_days,"onUpdate:modelValue":a[10]||(a[10]=e=>i(o).due_days=e)},null,8,["modelValue"])])]),t("div",xo,[t("div",fo,[d(S,{for:"patient_name",value:"Patient Name"}),d(f,{id:"patient_name",type:"text",modelValue:i(o).patient_name,"onUpdate:modelValue":a[11]||(a[11]=e=>i(o).patient_name=e)},null,8,["modelValue"])]),t("div",ho,[d(S,{for:"cr_dr_note",value:"CR DR Note"}),d(f,{id:"cr_dr_note",type:"text",modelValue:i(o).cr_dr_note,"onUpdate:modelValue":a[12]||(a[12]=e=>i(o).cr_dr_note=e)},null,8,["modelValue"])])]),t("div",null,[d(S,{for:"note",value:"Note"}),d(qt,{id:"note",type:"text",modelValue:i(o).note,"onUpdate:modelValue":a[13]||(a[13]=e=>i(o).note=e),onChange:a[14]||(a[14]=e=>i(o).validate("note"))},null,8,["modelValue"]),i(o).invalid("note")?(p(),Nt(dt,{key:0,class:"",message:i(o).errors.note},null,8,["message"])):v("",!0)])]),t("div",vo,[t("div",go,[t("div",yo,[wo,t("p",bo,_(I(et.value)),1)]),t("div",Vo,[So,t("div",Co,[d(f,{id:"discount_before_tax",type:"text",modelValue:i(o).discount_before_tax,"onUpdate:modelValue":a[15]||(a[15]=e=>i(o).discount_before_tax=e)},null,8,["modelValue"])])]),t("div",Io,[Fo,t("div",Po,[d(f,{id:"overall_discount",type:"text",modelValue:i(o).overall_discount,"onUpdate:modelValue":a[16]||(a[16]=e=>i(o).overall_discount=e)},null,8,["modelValue"])])]),t("div",ko,[To,t("p",$o,_(I(st.value)),1)]),h.value=="IGST"?(p(),x("div",Do,[Uo,t("p",Go,_(I(k.value)),1)])):v("",!0),h.value=="CGST/SGST"?(p(),x("div",No,[jo,t("p",Mo,_(I(k.value/2)),1)])):v("",!0),h.value=="CGST/SGST"?(p(),x("div",Ao,[Eo,t("p",Oo,_(I(k.value/2)),1)])):v("",!0),t("div",qo,[Bo,t("p",zo,_(I(tt.value)),1)])])])])]),t("div",Lo,[t("div",Ro,[d(Ot,{href:s.route("invoice.index")},{svg:w(()=>[Ho]),_:1},8,["href"]),d(ct,{disabled:i(o).processing},{default:w(()=>[T("Submit")]),_:1},8,["disabled"])])])],40,Jt)]),d(H,{show:M.value,onClose:A},{default:w(()=>[t("div",Qo,[Wo,t("div",Ko,[d(R,{onClick:A},{default:w(()=>[T(" Cancel ")]),_:1}),d(rt,{class:"ml-3",onClick:vt},{default:w(()=>[T(" Delete ")]),_:1})])])]),_:1},8,["show"]),d(H,{show:E.value,onClose:O},{default:w(()=>[t("div",Xo,[Yo,t("div",Zo,[d(R,{onClick:O},{default:w(()=>[T(" Cancel ")]),_:1}),d(rt,{class:"ml-3",onClick:bt},{default:w(()=>[T(" Delete ")]),_:1})])])]),_:1},8,["show"]),d(H,{show:q.value,onClose:J,maxWidth:Vt.value},{default:w(()=>[t("div",Jo,[d(Bt,{fileUrl:i(Q)+Z.value},null,8,["fileUrl"]),t("div",tl,[d(R,{onClick:J},{default:w(()=>[T(" Cancel ")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},vl=Rt(el,[["__scopeId","data-v-480e83d8"]]);export{vl as default};
