import{_ as o}from"./AdminLayout-e15be38d.js";import i from"./DeleteUserForm-fc16c798.js";import m from"./UpdatePasswordForm-a1ba2e7d.js";import r from"./UpdateProfileInformationForm-517ae9bf.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-16701445.js";import"./DangerButton-9b74ae84.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-11376965.js";import"./InputLabel-d69efee6.js";import"./Modal-754de2c3.js";/* empty css                                                              */import"./SecondaryButton-1012464f.js";import"./TextInput-764e3400.js";import"./PrimaryButton-eddb8b77.js";import"./TextArea-b68da786.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
