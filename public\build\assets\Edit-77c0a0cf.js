import{r as V,j as q,l as I,m as ee,o as u,c as p,a as d,u as i,w as O,F as D,Z as te,b as t,t as m,f,d as oe,n as h,k as ae,v as ne,i as L,g as se,T as ie}from"./app-ce7743ab.js";import{_ as le,a as re}from"./AdminLayout-6af2fc6a.js";import{_ as x}from"./InputLabel-3aa35471.js";import{P as de}from"./PrimaryButton-6ff8a943.js";import{_ as j}from"./TextInput-65921831.js";import{_ as ce}from"./TextArea-5fab1749.js";import{_ as me}from"./RadioButton-2a9849cb.js";import{_ as B}from"./SearchableDropdown-6fd7fbbe.js";import{u as ue}from"./index-588ba5dc.js";/* empty css                                                                          */import{_ as pe}from"./Checkbox-540f8602.js";import"./_plugin-vue_export-helper-c27b6911.js";const _e={class:"h-screen animate-top"},ye={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},ve={class:"sm:flex sm:items-center"},fe=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Payment")],-1),he={class:"flex items-center justify-between"},ge={key:0,class:"text-base font-semibold leading-6 text-gray-900"},xe=["onSubmit"],be={class:"border-b border-gray-900/10 pb-12"},we={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},ke={class:"sm:col-span-3"},Ve={class:"relative mt-2"},Fe={class:"sm:col-span-3"},ze={class:"relative mt-2"},Ae={class:"sm:col-span-2 hidden"},Ne={class:"relative mt-2"},Ce={key:0,class:"sm:col-span-3"},Se={class:"relative mt-2"},Pe={key:1,class:"sm:col-span-1"},Ue={key:2,class:"sm:col-span-2"},Te={key:3,class:"sm:col-span-2"},$e={class:"mt-4 flex justify-start"},Ee={class:"text-base font-semibold"},Me={key:4,class:"sm:col-span-3"},Oe={key:5,class:"sm:col-span-3"},De={key:6,class:"sm:col-span-3"},je={key:7,class:"sm:col-span-3"},Be={class:"relative mt-2"},Ie={class:"sm:col-span-6"},Le={class:"overflow-x-auto divide-y divide-gray-300 w-full"},Re=t("div",{class:"w-full"},[t("thead",{class:"w-full"},[t("tr",{class:""},[t("th",{scope:"col",class:""}),t("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),t("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),t("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),t("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1),Ye={style:{"overflow-y":"auto","max-height":"318px"}},qe={class:"divide-y divide-gray-300 bg-white"},He={class:"whitespace-nowrap px-2 text-sm text-gray-900"},Ze={class:"text-sm text-gray-900 leading-6 py-1.5"},Ge={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},Je={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Ke={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Qe={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},We={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},Xe={key:0,class:"text-red-500 text-xs absolute"},et={class:"whitespace-nowrap px-2 text-sm text-gray-900"},tt={class:"sm:col-span-2"},ot={class:"mt-2 p-3 bg-gray-50 rounded-md"},at={class:"space-y-2 text-sm"},nt={class:"flex items-center gap-2"},st=t("hr",{class:"my-2"},null,-1),it={class:"flex justify-between items-center font-semibold"},lt=t("span",null,"Settlement:",-1),rt={class:"flex justify-between items-center font-semibold"},dt=t("span",null,"Advance Amount:",-1),ct={key:0,class:"text-red-500 text-xs absolute"},mt={key:8,class:"sm:col-span-6"},ut={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},pt=t("thead",null,[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1),_t={class:"divide-y divide-gray-300 bg-white"},yt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},vt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ft={class:"flex flex-col"},ht={class:"text-sm text-gray-900"},gt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},xt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},bt={class:"flex mt-6 items-center justify-between"},wt={class:"ml-auto flex items-center justify-end gap-x-6"},kt=t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Vt={key:0,class:"text-sm text-gray-600"},Ot={__name:"Edit",props:["payment","paymentType","bankinfo","organization","companies","invoices","credit"],setup(P){const l=P;V([]);const U=V([]),H=l.bankinfo.filter(n=>n.organization_id===l.payment.organization_id);U.value=H;const o=ue("post","/payment",{id:l.payment.id,organization_id:l.payment.organization_id,company_id:l.payment.company_id,payment_type:l.payment.payment_type,date:l.payment.date,note:l.payment.note,amount:l.payment.amount,discount_amount:l.payment.discount_amount||0,round_off:l.payment.round_off||0,check_number:l.payment.check_number,org_bank_id:l.payment.org_bank_id,invoice:[],settled_amount:"",advance_amount:"",is_credit:"No",credit_data:[],total_unused_amount:""});o._method="PUT";const T=V(l.payment.payment_type),Z=()=>{o.settled_amount=N.value,o.advance_amount=C.value,o.total_unused_amount=z.value,o.is_credit=y.value,o.invoice=b.value,o.credit_data=w.value,o.submit({preserveScroll:!0,onSuccess:()=>{}})},G=(n,a)=>{T.value=n,o.payment_type=n,o.errors.payment_type=null,a==="Cash"?o.note="Cash":o.note==="Cash"&&(o.note="")},A=V([]),w=V([]),z=V(""),R=(n,a)=>{const e=l.bankinfo.filter(c=>c.organization_id===n);U.value=e;const r=l.invoices.filter(c=>c.purchase_order&&c.purchase_order.organization_id===n&&c.purchase_order.company_id===o.company_id);A.value=r;const s=l.credit.filter(c=>c.organization_id===n&&c.company_id===o.company_id);w.value=s,z.value=w.value.reduce((c,g)=>c+g.unused_amount,0),o.organization_id=n,o.errors.organization_id=null},J=(n,a)=>{o.company_id=n;const e=l.invoices.filter(s=>s.purchase_order&&s.purchase_order.organization_id===o.organization_id&&s.purchase_order.company_id===n);A.value=e;const r=l.credit.filter(s=>s.company_id===n&&s.organization_id===o.organization_id);w.value=r,z.value=w.value.reduce((s,c)=>s+c.unused_amount,0),o.errors.company_id=null},K=(n,a)=>{o.org_bank_id=n,o.errors.org_bank_id=null},N=q(()=>{const n=b.value.reduce((a,e)=>{if(e.check&&e.amount){const r=parseFloat(e.amount);return e.invoice_type==="purchase"?a+r:a-r}return a},0);return parseFloat(n.toFixed(2))}),C=q(()=>{const n=parseFloat(o.amount||0),a=parseFloat(o.round_off||0),e=N.value;return n>e?n-e-a:0}),F=n=>{let a=n.toFixed(2).toString(),[e,r]=a.split("."),s=e.substring(e.length-3),c=e.substring(0,e.length-3);return c!==""&&(s=","+s),`${c.replace(/\B(?=(\d{2})+(?!\d))/g,",")+s}.${r}`},Y=n=>{const a=new Date(n),e={year:"numeric",month:"short",day:"numeric"};return a.toLocaleDateString("en-US",e)},Q=(n,a)=>{const e=y.value==="Yes"?parseFloat(z.value||0):parseFloat(o.amount||0)+parseFloat(o.round_off||0);if(!b.value[a].check){b.value[a].amount=0;return}const r=b.value.filter(v=>v.check),s=r.some(v=>v.invoice_type==="purchase"),c=r.some(v=>v.invoice_type==="sales");if(s&&c&&r.length>1&&W(e,r))return;let g=e;b.value.forEach((v,M)=>{if(v.check&&M!==a&&parseFloat(v.amount||0)>0){const S=parseFloat(v.amount||0);v.invoice_type==="purchase"?g-=S:v.invoice_type==="sales"&&(g+=S)}});const _=parseFloat(b.value[a].pending_amount||0),k=Math.min(_,Math.max(0,g));b.value[a].amount=k.toFixed(2)},W=(n,a)=>{const e=a.filter(_=>_.invoice_type==="purchase"),r=a.filter(_=>_.invoice_type==="sales"),s=e.reduce((_,k)=>_+parseFloat(k.pending_amount||0),0),c=r.reduce((_,k)=>_+parseFloat(k.pending_amount||0),0),g=s-c;return Math.abs(n-Math.abs(g))<=1?(a.forEach(_=>{_.amount=parseFloat(_.pending_amount||0).toFixed(2)}),!0):g>0&&n>=g?(a.forEach(_=>{_.amount=parseFloat(_.pending_amount||0).toFixed(2)}),!0):!1},b=V([]),$=()=>{b.value=A.value.map(n=>{const a=n.invoice_type||"purchase",e=l.payment.invoice_data.some(s=>s.id===n.id&&(s.invoice_type||"purchase")===a),r=e?l.payment.invoice_data.find(s=>s.id===n.id&&(s.invoice_type||"purchase")===a).amount:0;return{id:n.id,date:n.customer_invoice_date||n.date,invoice_no:n.customer_invoice_no||n.invoice_no,total_amount:parseFloat(n.total_amount||0).toFixed(2),pending_amount:parseFloat(n.original_pending_amount||n.pending_amount||0).toFixed(2),invoice_type:a,check:e,amount:e?r.toString():"0.00"}})},y=V("No");I(A,()=>{$()}),I(y,()=>{$()}),I(()=>o.amount,()=>{y.value==="No"&&$()});const E=n=>{o.errors[n]=null,o.errors.settled_amount=null},X=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}];return ee(()=>{R(l.payment.organization_id),A.value=l.invoices,$();const n=l.credit.filter(e=>e.organization_id===l.payment.organization_id&&e.company_id===l.payment.company_id);w.value=n,z.value=w.value.reduce((e,r)=>e+r.unused_amount,0);const a=l.bankinfo.filter(e=>e.organization_id===l.payment.organization_id);U.value=a}),(n,a)=>(u(),p(D,null,[d(i(te),{title:"Edit Payment"}),d(le,null,{default:O(()=>[t("div",_e,[t("div",ye,[t("div",ve,[fe,t("div",he,[w.value.length>0?(u(),p("div",ge," Credits Available: ₹"+m(F(z.value)),1)):f("",!0)])]),t("form",{onSubmit:oe(Z,["prevent"]),class:""},[t("div",be,[t("div",we,[t("div",ke,[d(x,{for:"payment_type",value:"Organization"}),t("div",Ve,[d(B,{options:P.organization,modelValue:i(o).organization_id,"onUpdate:modelValue":a[0]||(a[0]=e=>i(o).organization_id=e),onOnchange:R,class:h({"error rounded-md":i(o).errors.organization_id}),editMode:"editMode"},null,8,["options","modelValue","class"])])]),t("div",Fe,[d(x,{for:"payment_type",value:"Company"}),t("div",ze,[d(B,{options:P.companies,modelValue:i(o).company_id,"onUpdate:modelValue":a[1]||(a[1]=e=>i(o).company_id=e),onOnchange:J,class:h({"error rounded-md":i(o).errors.company_id}),editMode:"editMode"},null,8,["options","modelValue","class"])])]),t("div",Ae,[d(x,{for:"role_id",value:"Payment Through Credit ?"}),t("div",Ne,[d(me,{modelValue:y.value,"onUpdate:modelValue":a[2]||(a[2]=e=>y.value=e),options:X},null,8,["modelValue"])])]),y.value=="No"?(u(),p("div",Ce,[d(x,{for:"payment_type",value:"Payment Type"}),t("div",Se,[d(B,{options:P.paymentType,modelValue:i(o).payment_type,"onUpdate:modelValue":a[3]||(a[3]=e=>i(o).payment_type=e),onOnchange:G,class:h({"error rounded-md":i(o).errors.payment_type})},null,8,["options","modelValue","class"])])])):f("",!0),y.value=="No"?(u(),p("div",Pe,[d(x,{for:"round_off",value:"Round Off"}),d(j,{type:"text",onChange:a[4]||(a[4]=e=>E("round_off")),modelValue:i(o).round_off,"onUpdate:modelValue":a[5]||(a[5]=e=>i(o).round_off=e),class:h({"error rounded-md":i(o).errors.round_off})},null,8,["modelValue","class"])])):f("",!0),y.value=="No"?(u(),p("div",Ue,[d(x,{for:"amount",value:"Amount"}),d(j,{id:"amount",type:"text",onChange:a[6]||(a[6]=e=>E("amount")),modelValue:i(o).amount,"onUpdate:modelValue":a[7]||(a[7]=e=>i(o).amount=e),class:h({"error rounded-md":i(o).errors.amount})},null,8,["modelValue","class"])])):f("",!0),y.value=="No"?(u(),p("div",Te,[d(x,{for:"advance",value:"Advance(Ref) Amount"}),t("div",$e,[t("p",Ee,m(F(C.value)),1)])])):f("",!0),T.value=="check"&&y.value=="No"?(u(),p("div",Me,[d(x,{for:"check_number",value:"Cheque Number"}),d(j,{id:"check_number",type:"text",modelValue:i(o).check_number,"onUpdate:modelValue":a[8]||(a[8]=e=>i(o).check_number=e),class:h({"error rounded-md":i(o).errors["data.check_number"]})},null,8,["modelValue","class"])])):f("",!0),y.value=="No"?(u(),p("div",Oe,[d(x,{for:"date",value:"Payment Date"}),ae(t("input",{"onUpdate:modelValue":a[9]||(a[9]=e=>i(o).date=e),class:h(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":i(o).errors.date}]),type:"date",onChange:a[10]||(a[10]=e=>E("date"))},null,34),[[ne,i(o).date]])])):f("",!0),T.value=="cash"&&y.value=="No"?(u(),p("div",De)):f("",!0),T.value!="cash"&&y.value=="No"?(u(),p("div",je,[d(x,{for:"org_bank_id",value:"Our Bank"}),t("div",Be,[d(B,{options:U.value,modelValue:i(o).org_bank_id,"onUpdate:modelValue":a[11]||(a[11]=e=>i(o).org_bank_id=e),onOnchange:K,class:h({"error rounded-md":i(o).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):f("",!0),t("div",Ie,[t("table",Le,[Re,t("div",Ye,[t("tbody",qe,[(u(!0),p(D,null,L(b.value,(e,r)=>(u(),p("tr",{key:r},[t("td",He,[t("div",Ze,[d(pe,{name:"check",checked:e.check,"onUpdate:checked":s=>e.check=s,onChange:s=>Q(s,r)},null,8,["checked","onUpdate:checked","onChange"])])]),t("td",Ge,[t("span",{class:h([e.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},m(e.invoice_type==="purchase"?"PURCHASE":"SALES"),3)]),t("td",Je,m(e.invoice_no),1),t("td",Ke,m(e.total_amount),1),t("td",Qe,m(e.pending_amount),1),t("td",We,[d(j,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":s=>e.amount=s,onChange:s=>E("invoice."+r+".amount"),class:h({error:i(o).errors[`invoice.${r}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),i(o).errors[`invoice.${r}.amount`]?(u(),p("p",Xe,m(i(o).errors[`invoice.${r}.amount`]),1)):f("",!0)]),t("td",et,m(Y(e.date)),1)]))),128))])])])]),t("div",tt,[d(x,{for:"note",value:"Net Settlement Summary"}),t("div",ot,[t("div",at,[(u(!0),p(D,null,L(b.value.filter(e=>e.check),e=>(u(),p("div",{key:e.id,class:"flex justify-between items-center"},[t("div",nt,[t("span",{class:h([e.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium"])},m(e.invoice_type==="purchase"?"P":"S"),3),t("span",null,m(e.invoice_no),1)]),t("span",{class:h([e.invoice_type==="purchase"?"text-blue-600":"text-green-600","font-medium"])},m(e.invoice_type==="purchase"?"+":"-")+"₹"+m(F(parseFloat(e.amount||0))),3)]))),128)),st,t("div",it,[lt,t("span",{class:h(N.value>=0?"text-blue-600":"text-red-600")}," ₹"+m(F(Math.abs(N.value)))+" "+m(N.value>=0?"(Pay)":"(Receive)"),3)]),t("div",rt,[dt,t("span",{class:h(C.value>=0?"text-green-600":"text-red-600")},m(C.value>0?"+":"")+"₹"+m(F(parseFloat(C.value||0))),3)])])]),i(o).errors.settled_amount?(u(),p("p",ct,m(i(o).errors.settled_amount),1)):f("",!0)]),y.value=="No"?(u(),p("div",mt,[d(x,{for:"note",value:"Note"}),d(ce,{id:"note",type:"text",rows:2,modelValue:i(o).note,"onUpdate:modelValue":a[12]||(a[12]=e=>i(o).note=e)},null,8,["modelValue"])])):f("",!0)]),w.value.length>0&&y.value=="Yes"?(u(),p("table",ut,[pt,t("tbody",_t,[(u(!0),p(D,null,L(w.value,(e,r)=>{var s,c,g,_,k,v,M,S;return u(),p("tr",{key:r},[t("td",yt,m(Y(e.date)),1),t("td",vt,[t("div",ft,[t("div",ht,m((c=(s=e.paymentpaid)==null?void 0:s.bank_info)!=null&&c.bank_name?(_=(g=e.paymentpaid)==null?void 0:g.bank_info)==null?void 0:_.bank_name:"Cash")+" - "+m((v=(k=e.paymentpaid)==null?void 0:k.bank_info)!=null&&v.account_number?(S=(M=e.paymentpaid)==null?void 0:M.bank_info)==null?void 0:S.account_number:""),1)])]),t("td",gt,m(F(e.amount)),1),t("td",xt,m(F(e.unused_amount)),1)])}),128))])])):f("",!0)]),t("div",bt,[t("div",wt,[d(re,{href:n.route("payment.index")},{svg:O(()=>[kt]),_:1},8,["href"]),d(de,{disabled:i(o).processing},{default:O(()=>[se("Update")]),_:1},8,["disabled"]),d(ie,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:O(()=>[i(o).recentlySuccessful?(u(),p("p",Vt,"Saved.")):f("",!0)]),_:1})])])],40,xe)])])]),_:1})],64))}};export{Ot as default};
