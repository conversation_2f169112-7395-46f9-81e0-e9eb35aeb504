import{K as m,h as p,o as d,c,a as t,u as s,w as i,F as _,Z as u,b as e,d as f,g as y,T as g,f as b}from"./app-21e66fd5.js";import{_ as h,a as v}from"./AdminLayout-db62264f.js";import{_ as x}from"./InputError-01d93b90.js";import{_ as k}from"./InputLabel-4a50badc.js";import{P as V}from"./PrimaryButton-ed35dcb4.js";import{_ as $}from"./TextInput-625f6add.js";import"./_plugin-vue_export-helper-c27b6911.js";const w={class:"animate-top bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},C=e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Checklist",-1),T={class:"border-b border-gray-900/10 pb-12"},j={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},B={class:"sm:col-span-6"},N={class:"flex mt-6 items-center justify-between"},S={class:"ml-auto flex items-center justify-end gap-x-6"},E=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),F={key:0,class:"text-sm text-gray-600"},z={__name:"Edit",props:["data"],setup(P){const n=m().props.data,a=p({id:n.id,type:n.type});return(l,o)=>(d(),c(_,null,[t(s(u),{title:"Jobcard Checklist"}),t(h,null,{default:i(()=>[e("div",w,[C,e("form",{onSubmit:o[2]||(o[2]=f(r=>s(a).patch(l.route("jobcard-checklist.update")),["prevent"]))},[e("div",T,[e("div",j,[e("div",B,[t(k,{for:"type",value:"Type"}),t($,{id:"type",type:"text",modelValue:s(a).type,"onUpdate:modelValue":o[0]||(o[0]=r=>s(a).type=r),autocomplete:"type",onChange:o[1]||(o[1]=r=>s(a).validate("type"))},null,8,["modelValue"]),t(x,{class:"",message:s(a).errors.type},null,8,["message"])])])]),e("div",N,[e("div",S,[t(v,{href:l.route("jobcard-checklist.index")},{svg:i(()=>[E]),_:1},8,["href"]),t(V,{disabled:s(a).processing},{default:i(()=>[y("Save")]),_:1},8,["disabled"]),t(g,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:i(()=>[s(a).recentlySuccessful?(d(),c("p",F,"Saved.")):b("",!0)]),_:1})])])],32)])]),_:1})],64))}};export{z as default};
