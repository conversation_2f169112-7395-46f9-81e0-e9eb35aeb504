<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import CreateButton from '@/Components/CreateButton.vue';
import { Head , usePage} from '@inertiajs/vue3';
import Modal from '@/Components/Modal.vue';
import FileViewer from '@/Components/FileViewer.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';

const props = defineProps(['data']);
const poData = usePage().props.data[0];
const totalQty = computed(() => {
    if (props.data[0].credit_note_details) {
        return props.data[0].credit_note_details.reduce((acc, poData) => acc + poData.qty, 0);
    }
    return 0;
});

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
     let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

</script>

<template>
    <Head title="Credit Note"/>
    <AdminLayout>
    <form @submit.prevent="submit" class="">
        <div class="animate-top">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Credit Note Detail</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div>
                        <p class="text-sm font-semibold text-gray-900">{{ data[0].organization.name }}</p>
                    </div>
                    <div class="flex justify-end w-20">
                        <CreateButton :href="route('creditnote.index')">
                            Back
                        </CreateButton>
                    </div>
                </div>
            </div>
            <div class="mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6">
                <div class="inline-flex items-start space-x-6 justify-start w-full">
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-28">Customer:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.customer_name ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-28">GST No:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.gst_no  ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-28">Email:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.email ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-28">Contact No:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].customers.contact_no ?? '-'}}</p>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-40">Invoice Number:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].invoice.invoice_no ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-40">Credit Note Number:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].credit_note_no ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-40">Debit Note Number:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].debit_note_number ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-40">Credit Note Date:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ formatDate(data[0].date) ?? '-'}}</p>
                        </div>
                        <div class="inline-flex items-center justify-start w-full space-x-2">
                            <p class="text-sm font-semibold text-gray-900 w-40">Reason:</p>
                            <p class="text-sm leading-6 text-gray-700">{{ data[0].reason ?? '-'}}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                            <tr class="border-b-2">
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Sr No</th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Item Code</th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Name</th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">MRP (₹)</th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Price (₹)</th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">QTY</th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Total Price (₹)</th>
                                <th v-if="data[0].customers.gst_type =='IGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">IGST (%)</th>
                                <th v-if="data[0].customers.gst_type =='IGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">IGST (₹)</th>
                                <th v-if="data[0].customers.gst_type =='CGST/SGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">CGST (%)</th>
                                <th v-if="data[0].customers.gst_type =='CGST/SGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">SGST (%)</th>
                                <th v-if="data[0].customers.gst_type =='CGST/SGST'" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Total GST (₹)</th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">Total Amount (₹)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="odd:bg-white even:bg-gray-50 border-b"  v-for="(product, index)  in data[0].credit_note_details" :key="index">
                                <td class="px-4 py-2.5 min-w-20">{{ index + 1 }}</td>
                                <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-32">{{ product.product.item_code ?? '-' }}</td>
                                <td class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-32">{{ product.product.name ?? '-' }}</td>
                                <td class="px-4 py-2.5 min-w-28">{{ (product.product?.serial_numbers[0]?.mrp) ? formatAmount(product.product.serial_numbers[0].mrp): '-' }}</td>
                                <td class="px-4 py-2.5 min-w-28">{{ formatAmount(product.price)}}</td>
                                <td class="px-4 py-2.5">{{ product.qty }}</td>
                                <td class="px-4 py-2.5 min-w-36">{{ formatAmount(product.total_price)}}</td>
                                <td  v-if="data[0].customers.gst_type =='IGST'" class="px-4 py-2.5 min-w-24">{{ product.gst }}</td>
                                <td  v-if="data[0].customers.gst_type =='IGST'" class="px-4 py-2.5 min-w-24">{{ formatAmount(product.gst_amount) }}</td>
                                <td  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-24">{{ product.gst/2 }}</td>
                                <td  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-24">{{ product.gst/2 }}</td>
                                <td  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 min-w-32">{{ formatAmount(product.total_gst_amount) }}</td>
                                <td class="px-4 py-2.5 min-w-44">{{ formatAmount(product.total_amount) }}</td>
                            </tr>
                            <tr class="bg-white border-b">
                                <th class="px-4 py-2.5 min-w-20"></th>
                                <th class="px-4 py-2.5 min-w-32"></th>
                                <th class="px-4 py-2.5 min-w-60"></th>
                                <th class="px-4 py-2.5"></th>
                                <th class="px-4 py-2.5 min-w-28"></th>
                                <th class="px-4 py-2.5 text-gray-900 min-w-22">{{totalQty}}</th>
                                <th class="px-4 py-2.5 text-gray-900 min-w-24">{{ formatAmount(data[0].sub_total) }}</th>
                                <th  v-if="data[0].customers.gst_type =='IGST'" class="px-4 py-2.5 text-gray-900 min-w-24"></th>
                                <th  v-if="data[0].customers.gst_type =='IGST'" class="px-4 py-2.5 text-gray-900 min-w-24">{{ formatAmount(data[0].total_gst) }}</th>
                                <th  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 text-gray-900 min-w-24"></th>
                                <th  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 text-gray-900 min-w-24"></th>
                                <th  v-if="data[0].customers.gst_type =='CGST/SGST'" class="px-4 py-2.5 text-gray-900 min-w-24">{{ formatAmount(data[0].total_gst) }}</th>
                                <th class="px-4 py-2.5 text-gray-900 min-w-44">{{  formatAmount(data[0].total_amount) }}</th>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </form>
</AdminLayout>

</template>
