import{r as T,j as P,o as u,c as m,a as i,u as r,w as $,F as M,Z as nt,b as e,t as p,k as rt,v as it,d as ct,n as y,f as g,i as dt,g as E,e as ut,s as mt,x as _t}from"./app-6cdaf2bc.js";import{_ as pt,a as gt}from"./AdminLayout-b73e8538.js";import{_ as yt}from"./InputError-5c3c98c2.js";import{_ as w}from"./InputLabel-38b98ddd.js";import{P as H}from"./PrimaryButton-b7e37df1.js";import{_ as f}from"./TextInput-61ab2d6e.js";import{_ as ht}from"./TextArea-8bab3e6c.js";import{_ as C}from"./SearchableDropdown-8ff24da4.js";import{_ as xt}from"./MultipleFileUpload-cb399142.js";import{u as vt}from"./index-839fed2d.js";import{_ as ft}from"./_plugin-vue_export-helper-c27b6911.js";const d=v=>(mt("data-v-d474d799"),v=v(),_t(),v),wt={class:"animate-top"},bt={class:"sm:flex sm:items-center"},St=d(()=>e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Challan")],-1)),Vt={class:"w-auto"},It={class:"flex space-x-2"},Tt=d(()=>e("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Challan Number:",-1)),Ct={key:0,class:"text-sm font-semibold text-gray-900 leading-6"},Pt={key:1,class:"text-sm font-semibold text-gray-900 leading-6"},$t={class:"flex space-x-2 items-center"},Gt=d(()=>e("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Date :",-1)),Ft=["onSubmit"],kt={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Ut={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Nt={class:"sm:col-span-4"},zt={class:"relative mt-2"},At={class:"sm:col-span-4"},Dt={class:"relative mt-2"},jt={class:"sm:col-span-4"},Ot={class:"relative mt-2"},qt={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border space-y-2"},Bt={class:"overflow-x-auto w-full"},Lt={class:"overflow-x-auto divide-y divide-gray-300 w-full",style:{"margin-bottom":"160px"}},Mt=d(()=>e("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Product Name",-1)),Et=d(()=>e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Batch",-1)),Ht=d(()=>e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"HSN",-1)),Rt=d(()=>e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"EXP",-1)),Qt=d(()=>e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),Xt=d(()=>e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),Zt=d(()=>e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),Jt=d(()=>e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),Kt=d(()=>e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Sell Price (₹)",-1)),Wt=d(()=>e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),Yt={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},te={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ee={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},se={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},oe={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ae=d(()=>e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),le=d(()=>e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),ne=d(()=>e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),re={class:"divide-y divide-gray-300 bg-white"},ie={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-80"},ce={class:"relative mt-2"},de={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-60"},ue={class:"relative mt-2"},me={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},_e={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},pe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},ge={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ye={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},he={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},xe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},ve={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},fe={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},we={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},be={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Se={key:3,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ve={key:4,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ie={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Te={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ce={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 flex space-x-2 min-w-48"},Pe={class:"px-3 py-3 text-sm text-gray-900"},$e=["onClick"],Ge=d(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Fe=[Ge],ke={class:"mt-12 flex items-center justify-between"},Ue={class:"ml-auto flex items-center justify-end gap-x-6"},Ne={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},ze={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},Ae={class:"sm:col-span-3 space-y-4"},De={class:"flex space-x-4"},je={class:"w-full"},Oe={class:"w-full"},qe={class:"relative mt-2"},Be={class:"flex space-x-4"},Le={class:"w-full"},Me={class:"w-full"},Ee={class:"sm:col-span-3"},He={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Re={class:"inline-flex items-center justify-end w-full space-x-3"},Qe=d(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),Xe={class:"text-base font-semibold text-gray-900 w-32"},Ze={class:"inline-flex items-center justify-end w-full space-x-3"},Je=d(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),Ke={class:"text-base font-semibold text-gray-900 w-32"},We={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},Ye=d(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),ts={class:"text-base font-semibold text-gray-900 w-32"},es={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},ss=d(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),os={class:"text-base font-semibold text-gray-900 w-32"},as={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},ls=d(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),ns={class:"text-base font-semibold text-gray-900 w-32"},rs={class:"inline-flex items-center justify-end w-full space-x-3"},is=d(()=>e("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),cs={class:"text-base font-semibold text-gray-900 w-32"},ds={class:"flex items-center justify-between"},us={class:"ml-auto flex items-center justify-end gap-x-6"},ms=d(()=>e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),_s={__name:"Add",props:["customers","salesuser","challan_number","demo_challan_number","serialno","products","organization","category"],setup(v){const x=v,G=T(),F=T(),c=T([{serial_number_id:"",product_id:"",product_name:"",item_code:"",expiry_date:"",mrp:"",price:"",hsn_code:"",sell_price:"",discount:"",discount_amount:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",qty:"",description:""}]),R=(a,n)=>{s.customer_id=a,s.errors.customer_id=null;const t=x.customers.find(o=>o.id===a);t&&(_.value=t.gst_type)},Q=(a,n)=>{s.sales_user_id=a,s.errors.sales_user_id=null},X=T([]),Z=(a,n,t)=>{s.errors[`selectedProductItem.${t}.product_id`]=null,s.product_id=a;const o=x.serialno.filter(l=>l.product_id===a&&l.organization_id===s.organization_id);X.value=o,c.value[t].product_id=a},_=T(null),J=(a,n,t)=>{const o=x.serialno.find(l=>l.id===a);o&&(c.value[t].qty="",c.value[t].serial_number_id=o.id,c.value[t].product_name=o.product.name,c.value[t].item_code=o.product.item_code,c.value[t].expiry_date=o.expiry_date,c.value[t].mrp=o.mrp?parseFloat(o.mrp).toFixed(2):"-",c.value[t].price=parseFloat(o.purchase_price).toFixed(2),c.value[t].hsn_code=o.product.hsn_code,c.value[t].discount=0,c.value[t].sell_price=0,c.value[t].total_price=parseFloat(o.purchase_price).toFixed(2),c.value[t].gst=parseFloat(o.product.gst).toFixed(2),c.value[t].sgst=parseFloat(o.product.gst/2).toFixed(2),c.value[t].gst_amount=0,c.value[t].total_gst_amount=0,c.value[t].total_amount=0,c.value[t].description="",s.errors[`selectedProductItem.${t}.serial_number_id`]=null)},K=(a,n)=>{const t=parseFloat(a.sell_price),o=parseFloat(a.discount)||0,l=_.value=="IGST"?parseFloat(a.gst):parseFloat(a.sgst*2),h=parseFloat(a.qty),j=t*h*(1+l/100),O=t*1*(l/100),q=t*h*(l/100),U=j*(o/100)||0,B=j-U,L=t*h;return a.total_price=isNaN(L)?"":parseFloat(L).toFixed(2),a.gst_amount=isNaN(O)?"":parseFloat(O).toFixed(2),a.total_gst_amount=isNaN(q)?"":parseFloat(q).toFixed(2),a.discount_amount=isNaN(U)?"":parseFloat(U).toFixed(2),isNaN(B)?"":parseFloat(B).toFixed(2)},b=(a,n)=>{a.total_amount=K(a)},N=P(()=>Math.round(c.value.reduce((a,n)=>a+(n.total_amount?parseFloat(n.total_amount):0),0))),S=P(()=>c.value.reduce((a,n)=>a+(n.total_gst_amount?parseFloat(n.total_gst_amount):0),0)),z=P(()=>c.value.reduce((a,n)=>a+(n.total_price?parseFloat(n.total_price):0),0)),A=P(()=>c.value.reduce((a,n)=>a+(n.discount_amount?parseFloat(n.discount_amount):0),0)),s=vt("post","/challan",{note:"",date:new Date().toISOString().slice(0,10),selectedProductItem:[],customer_id:"",category:"",sales_user_id:"",challan_number:"",document:"",cgst:"",sgst:"",igst:"",total_gst:"",sub_total:"",total_amount:"",total_discount:"",organization_id:"",dispatch:"",transport:""}),D=()=>{c.value=[{serial_number_id:"",product_id:"",product_name:"",item_code:"",expiry_date:"",mrp:"",price:"",hsn_code:"",sell_price:"",discount:"",discount_amount:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",qty:"",description:""}]},k=T([]),W=(a,n)=>{D(),x.products.filter(o=>o.category===a);const t=x.products.filter(o=>o.sales_products.some(h=>h.organization_id===s.organization_id));k.value=t,s.category=a,s.errors.category=null},Y=(a,n)=>{D(),x.products.filter(o=>o.category===s.category);const t=x.products.filter(o=>o.sales_products.some(h=>h.organization_id===a));k.value=t,s.organization_id=a,s.errors.organization_id=null,G.value=x.challan_number[a],F.value=x.demo_challan_number[a]},tt=()=>{s.sub_total=z.value,s.challan_number=s.category=="Demo"?F.value:G.value,s.cgst=_.value=="CGST/SGST"?S.value/2:"0",s.sgst=_.value=="CGST/SGST"?S.value/2:"0",s.igst=_.value=="IGST"?S.value:"0",s.total_gst=S.value,s.total_amount=N.value,s.total_discount=A.value,s.selectedProductItem=c.value,s.submit({preserveScroll:!0,onSuccess:()=>s.reset()})},et=()=>{c.value.push({serial_number_id:"",product_id:"",product_name:"",expiry_date:"",mrp:"",price:"",hsn_code:""})},st=a=>{c.value.splice(a,1)},V=a=>{s.errors[a]=null};P(()=>{const a=new Date,n={year:"numeric",month:"long",day:"numeric"};return a.toLocaleDateString("en-US",n)});const ot=a=>{s.document=a},I=a=>{const[n,t]=a.toFixed(2).toString().split(".");return n.replace(/\B(?=(\d{3})+(?!\d))/g,",")+(t?"."+t:"")},at=(a,n,t)=>x.serialno.filter(l=>l.product_id===a&&l.organization_id===s.organization_id),lt=a=>a;return(a,n)=>(u(),m(M,null,[i(r(nt),{title:"Challan"}),i(pt,null,{default:$(()=>[e("div",wt,[e("div",bt,[St,e("div",Vt,[e("div",It,[Tt,r(s).category=="Demo"?(u(),m("span",Ct,p(F.value),1)):(u(),m("span",Pt,p(G.value),1))]),e("div",$t,[Gt,rt(e("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":n[0]||(n[0]=t=>r(s).date=t),onChange:n[1]||(n[1]=t=>r(s).validate("date"))},null,544),[[it,r(s).date]])])])]),e("form",{onSubmit:ct(tt,["prevent"]),class:"mt-6 space-y-6"},[e("div",kt,[e("div",Ut,[e("div",Nt,[i(w,{for:"company_name",value:"Organization"}),e("div",zt,[i(C,{options:v.organization,modelValue:r(s).organization_id,"onUpdate:modelValue":n[2]||(n[2]=t=>r(s).organization_id=t),onOnchange:Y,class:y({"error rounded-md":r(s).errors.organization_id})},null,8,["options","modelValue","class"])])]),e("div",At,[i(w,{for:"customer_id",value:"Customer Name"}),e("div",Dt,[i(C,{options:v.customers,modelValue:r(s).customer_id,"onUpdate:modelValue":n[3]||(n[3]=t=>r(s).customer_id=t),onOnchange:R,class:y({"error rounded-md":r(s).errors.customer_id})},null,8,["options","modelValue","class"])])]),e("div",jt,[i(w,{for:"company_name",value:"Category"}),e("div",Ot,[i(C,{options:v.category,modelValue:r(s).category,"onUpdate:modelValue":n[4]||(n[4]=t=>r(s).category=t),onOnchange:W,class:y({"error rounded-md":r(s).errors.category})},null,8,["options","modelValue","class"])])])])]),e("div",qt,[e("div",Bt,[e("table",Lt,[e("thead",null,[e("tr",null,[Mt,Et,Ht,Rt,Qt,Xt,Zt,Jt,Kt,Wt,_.value=="IGST"?(u(),m("th",Yt,"IGST (%)")):g("",!0),_.value=="IGST"?(u(),m("th",te,"IGST (₹)")):g("",!0),_.value=="CGST/SGST"?(u(),m("th",ee,"CGST (%)")):g("",!0),_.value=="CGST/SGST"?(u(),m("th",se,"SGST (%)")):g("",!0),_.value=="CGST/SGST"?(u(),m("th",oe,"Total GST (₹)")):g("",!0),ae,le,ne])]),e("tbody",re,[(u(!0),m(M,null,dt(c.value,(t,o)=>(u(),m("tr",{key:o},[e("td",ie,[e("div",ce,[i(C,{options:lt(k.value),modelValue:t.product_id,"onUpdate:modelValue":l=>t.product_id=l,onOnchange:(l,h)=>Z(l,h,o),onChange:n[5]||(n[5]=l=>r(s).validate("product_id")),class:y({"error rounded-md":r(s).errors[`selectedProductItem.${o}.product_id`]})},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class"])])]),e("td",de,[e("div",ue,[i(C,{options:at(t.product_id),modelValue:t.serial_number_id,"onUpdate:modelValue":l=>t.serial_number_id=l,onOnchange:(l,h)=>J(l,h,o),onChange:n[6]||(n[6]=l=>r(s).validate("serial_number_id")),class:y({"error rounded-md":r(s).errors[`selectedProductItem.${o}.serial_number_id`]})},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class"])])]),e("td",me,p(t.hsn_code??"-"),1),e("td",_e,p(t.expiry_date??"-"),1),e("td",pe,[i(f,{id:"description",type:"text",modelValue:t.description,"onUpdate:modelValue":l=>t.description=l,onInput:l=>b(t,o),onChange:l=>V("selectedProductItem."+o+".description"),class:y({error:r(s).errors[`selectedProductItem.${o}.description`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),e("td",ge,p(t.mrp??"-"),1),e("td",ye,p(t.price),1),e("td",he,[i(f,{id:"qty",type:"text",modelValue:t.qty,"onUpdate:modelValue":l=>t.qty=l,numeric:!0,onInput:l=>b(t,o),onChange:l=>V("selectedProductItem."+o+".qty"),class:y({error:r(s).errors[`selectedProductItem.${o}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),e("td",xe,[i(f,{id:"sell_price",type:"text",modelValue:t.sell_price,"onUpdate:modelValue":l=>t.sell_price=l,onInput:l=>b(t,o),onChange:l=>V("selectedProductItem."+o+".sell_price"),class:y({error:r(s).errors[`selectedProductItem.${o}.sell_price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),e("td",ve,p(t.total_price),1),_.value=="IGST"?(u(),m("td",fe,[i(f,{id:"gst",type:"text",modelValue:t.gst,"onUpdate:modelValue":l=>t.gst=l,onInput:l=>b(t,o),onChange:l=>V("selectedProductItem."+o+".gst"),class:y({error:r(s).errors[`selectedProductItem.${o}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):g("",!0),_.value=="CGST/SGST"?(u(),m("td",we,[i(f,{id:"gst",type:"text",modelValue:t.sgst,"onUpdate:modelValue":l=>t.sgst=l,onInput:l=>b(t,o),onChange:l=>V("selectedProductItem."+o+".gst"),class:y({error:r(s).errors[`selectedProductItem.${o}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):g("",!0),_.value=="CGST/SGST"?(u(),m("td",be,[i(f,{id:"gst",type:"text",modelValue:t.sgst,"onUpdate:modelValue":l=>t.sgst=l,onInput:l=>b(t,o),onChange:l=>V("selectedProductItem."+o+".gst"),class:y({error:r(s).errors[`selectedProductItem.${o}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):g("",!0),_.value=="IGST"?(u(),m("td",Se,p(t.total_gst_amount),1)):g("",!0),_.value=="CGST/SGST"?(u(),m("td",Ve,p(t.total_gst_amount),1)):g("",!0),e("td",Ie,[i(f,{id:"discount",type:"text",modelValue:t.discount,"onUpdate:modelValue":l=>t.discount=l,onInput:l=>b(t,o),onChange:l=>V("selectedProductItem."+o+".discount"),class:y({error:r(s).errors[`selectedProductItem.${o}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),e("td",Te,p(t.discount_amount),1),e("td",Ce,[e("div",Pe,p(t.total_amount),1),o!=0?(u(),m("button",{key:0,type:"button",class:"mt-1",onClick:l=>st(o)},Fe,8,$e)):g("",!0)])]))),128))])])]),e("div",ke,[e("div",Ue,[i(H,{onClick:et,type:"button"},{default:$(()=>[E("Add Product")]),_:1})])])]),e("div",Ne,[e("div",ze,[e("div",Ae,[e("div",De,[e("div",je,[i(w,{for:"note",value:"Upload Documents"}),i(xt,{inputId:"document",inputName:"document",onFiles:ot})]),e("div",Oe,[i(w,{for:"sales_user_id",value:"Sales Person"}),e("div",qe,[i(C,{options:v.salesuser,modelValue:r(s).sales_user_id,"onUpdate:modelValue":n[7]||(n[7]=t=>r(s).sales_user_id=t),onOnchange:Q,class:y({"error rounded-md":r(s).errors.sales_user_id})},null,8,["options","modelValue","class"])])])]),e("div",Be,[e("div",Le,[i(w,{for:"company_name",value:"Transport"}),i(f,{id:"gst",type:"text",modelValue:r(s).transport,"onUpdate:modelValue":n[8]||(n[8]=t=>r(s).transport=t)},null,8,["modelValue"])]),e("div",Me,[i(w,{for:"company_name",value:"Dispatch"}),i(f,{id:"transport",type:"text",modelValue:r(s).dispatch,"onUpdate:modelValue":n[9]||(n[9]=t=>r(s).dispatch=t)},null,8,["modelValue"])])]),e("div",null,[i(w,{for:"note",value:"Note"}),i(ht,{id:"note",type:"text",modelValue:r(s).note,"onUpdate:modelValue":n[10]||(n[10]=t=>r(s).note=t),onChange:n[11]||(n[11]=t=>r(s).validate("note"))},null,8,["modelValue"]),r(s).invalid("note")?(u(),ut(yt,{key:0,class:"",message:r(s).errors.note},null,8,["message"])):g("",!0)])]),e("div",Ee,[e("div",He,[e("div",Re,[Qe,e("p",Xe,p(I(z.value)),1)]),e("div",Ze,[Je,e("p",Ke,p(I(A.value)),1)]),_.value=="IGST"?(u(),m("div",We,[Ye,e("p",ts,p(I(S.value)),1)])):g("",!0),_.value=="CGST/SGST"?(u(),m("div",es,[ss,e("p",os,p(I(S.value/2)),1)])):g("",!0),_.value=="CGST/SGST"?(u(),m("div",as,[ls,e("p",ns,p(I(S.value/2)),1)])):g("",!0),e("div",rs,[is,e("p",cs,p(I(N.value)),1)])])])])]),e("div",ds,[e("div",us,[i(gt,{href:a.route("challan.index")},{svg:$(()=>[ms]),_:1},8,["href"]),i(H,{disabled:r(s).processing},{default:$(()=>[E("Submit")]),_:1},8,["disabled"])])])],40,Ft)])]),_:1})],64))}},Is=ft(_s,[["__scopeId","data-v-d474d799"]]);export{Is as default};
