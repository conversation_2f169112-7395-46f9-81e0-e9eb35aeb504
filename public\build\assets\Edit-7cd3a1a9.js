import{_ as o}from"./AdminLayout-6af2fc6a.js";import i from"./DeleteUserForm-85afd4fc.js";import m from"./UpdatePasswordForm-367bfaff.js";import r from"./UpdateProfileInformationForm-fe147ad2.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-ce7743ab.js";import"./DangerButton-ca58e5a5.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-473f1c1e.js";import"./InputLabel-3aa35471.js";import"./Modal-599968f2.js";/* empty css                                                              */import"./SecondaryButton-aec1a882.js";import"./TextInput-65921831.js";import"./PrimaryButton-6ff8a943.js";import"./TextArea-5fab1749.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
