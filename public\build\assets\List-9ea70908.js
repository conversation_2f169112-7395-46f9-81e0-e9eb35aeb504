import{_ as v,b,a as u}from"./AdminLayout-e15be38d.js";import{_ as h}from"./CreateButton-cebe4e7b.js";import{_ as k}from"./SecondaryButton-1012464f.js";import{D as C}from"./DangerButton-9b74ae84.js";import{M}from"./Modal-754de2c3.js";import{_ as A}from"./Pagination-ffdaf57f.js";import{h as B,r as x,o,c as n,a,u as T,w as e,F as _,Z as L,b as t,g as r,f as i,i as N,e as f,t as j}from"./app-********.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const $={class:"animate-top"},V={class:"sm:flex sm:items-center"},z=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Account Types")],-1),E={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},D={class:"flex justify-end w-20"},O={key:0,class:"flex justify-end"},U={class:"mt-8 overflow-x-auto sm:rounded-lg"},F={class:"shadow sm:rounded-lg"},H={class:"w-full text-sm text-left rtl:text-right text-gray-500"},I=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"ACCOUNT NAME"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"ACTION")])],-1),S={key:0},Z={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},q=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1),G=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1),J=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1),K=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1),P={class:"items-center px-4 py-2.5"},Q={class:"flex items-center justify-start gap-4"},R=t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),W=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),X=t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),Y=["onClick"],tt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),et=t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),st=[tt,et],ot=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 6l4 4M4 6l4-4M20 18H4m16 0l-4-4m4 4l-4 4"})],-1),at=t("span",{class:"text-sm text-gray-700 leading-5"}," View Transaction ",-1),nt={key:1},lt=t("tr",{class:"bg-white"},[t("td",{colspan:"8",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),ct=[lt],rt={class:"p-6"},it=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),dt={class:"mt-6 flex justify-end"},kt={__name:"List",props:["data","permissions"],setup(s){const y=B({}),d=x(!1),p=x(null),g=l=>{p.value=l,d.value=!0},m=()=>{d.value=!1},w=()=>{y.delete(route("account-type.destroy",{id:p.value}),{onSuccess:()=>m()})};return(l,mt)=>(o(),n(_,null,[a(T(L),{title:"Account Types"}),a(v,null,{default:e(()=>[t("div",$,[t("div",V,[z,t("div",E,[t("div",D,[a(h,{href:l.route("setting")},{default:e(()=>[r(" Back ")]),_:1},8,["href"])]),s.permissions.canCreateAccountTypes?(o(),n("div",O,[a(h,{href:l.route("account-type.create")},{default:e(()=>[r(" Add Account ")]),_:1},8,["href"])])):i("",!0)])]),t("div",U,[t("div",F,[t("table",H,[I,s.data.data&&s.data.data.length>0?(o(),n("tbody",S,[(o(!0),n(_,null,N(s.data.data,(c,pt)=>(o(),n("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:c.id},[t("td",Z,j(c.name??"-"),1),q,G,J,K,t("td",P,[t("div",Q,[a(b,{align:"right",width:"48"},{trigger:e(()=>[R]),content:e(()=>[s.permissions.canEditAccountTypes?(o(),f(u,{key:0,href:l.route("account-type.edit",{id:c.id})},{svg:e(()=>[W]),text:e(()=>[X]),_:2},1032,["href"])):i("",!0),s.permissions.canDeleteAccountTypes?(o(),n("button",{key:1,type:"button",onClick:ut=>g(c.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},st,8,Y)):i("",!0),a(u,{href:l.route("account-type.transactions",{id:c.id})},{svg:e(()=>[ot]),text:e(()=>[at]),_:2},1032,["href"])]),_:2},1024)])])]))),128))])):(o(),n("tbody",nt,ct))])])]),s.data.data&&s.data.data.length>0?(o(),f(A,{key:0,class:"mt-6",links:s.data.links},null,8,["links"])):i("",!0)]),a(M,{show:d.value,onClose:m},{default:e(()=>[t("div",rt,[it,t("div",dt,[a(k,{onClick:m},{default:e(()=>[r(" Cancel ")]),_:1}),a(C,{class:"ml-3",onClick:w},{default:e(()=>[r(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{kt as default};
