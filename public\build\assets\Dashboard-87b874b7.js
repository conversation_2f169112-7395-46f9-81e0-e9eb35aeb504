var Eo=Object.defineProperty;var Fo=(i,t,e)=>t in i?Eo(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var M=(i,t,e)=>(Fo(i,typeof t!="symbol"?t+"":t,e),e);import{_ as zo,a as xi}from"./AdminLayout-595ad5a7.js";import{_ as Bo}from"./SimpleDropdown-f072c5ba.js";import{r as ui,m as Xi,l as Ki,o as j,c as $,b as _,h as Vo,j as Re,C as Ho,a as ft,u as No,w as se,F as ne,Z as Wo,t as z,f as bt,i as Ie,e as jo,s as $o,x as Yo}from"./app-97275a91.js";import{_ as Uo}from"./Pagination-5e2f223d.js";import{_ as Xo}from"./_plugin-vue_export-helper-c27b6911.js";/*!
 * @kurkle/color v0.3.2
 * https://github.com/kurkle/color#readme
 * (c) 2023 Jukka Ku<PERSON>ela
 * Released under the MIT License
 */function Oe(i){return i+.5|0}const St=(i,t,e)=>Math.max(Math.min(i,e),t);function he(i){return St(Oe(i*2.55),0,255)}function Ot(i){return St(Oe(i*255),0,255)}function vt(i){return St(Oe(i/2.55)/100,0,1)}function fs(i){return St(Oe(i*100),0,100)}const lt={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Ti=[..."0123456789ABCDEF"],Ko=i=>Ti[i&15],qo=i=>Ti[(i&240)>>4]+Ti[i&15],Ee=i=>(i&240)>>4===(i&15),Go=i=>Ee(i.r)&&Ee(i.g)&&Ee(i.b)&&Ee(i.a);function Zo(i){var t=i.length,e;return i[0]==="#"&&(t===4||t===5?e={r:255&lt[i[1]]*17,g:255&lt[i[2]]*17,b:255&lt[i[3]]*17,a:t===5?lt[i[4]]*17:255}:(t===7||t===9)&&(e={r:lt[i[1]]<<4|lt[i[2]],g:lt[i[3]]<<4|lt[i[4]],b:lt[i[5]]<<4|lt[i[6]],a:t===9?lt[i[7]]<<4|lt[i[8]]:255})),e}const Jo=(i,t)=>i<255?t(i):"";function Qo(i){var t=Go(i)?Ko:qo;return i?"#"+t(i.r)+t(i.g)+t(i.b)+Jo(i.a,t):void 0}const ta=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Bn(i,t,e){const s=t*Math.min(e,1-e),n=(o,a=(o+i/30)%12)=>e-s*Math.max(Math.min(a-3,9-a,1),-1);return[n(0),n(8),n(4)]}function ea(i,t,e){const s=(n,o=(n+i/60)%6)=>e-e*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function ia(i,t,e){const s=Bn(i,1,.5);let n;for(t+e>1&&(n=1/(t+e),t*=n,e*=n),n=0;n<3;n++)s[n]*=1-t-e,s[n]+=t;return s}function sa(i,t,e,s,n){return i===n?(t-e)/s+(t<e?6:0):t===n?(e-i)/s+2:(i-t)/s+4}function qi(i){const e=i.r/255,s=i.g/255,n=i.b/255,o=Math.max(e,s,n),a=Math.min(e,s,n),r=(o+a)/2;let l,c,h;return o!==a&&(h=o-a,c=r>.5?h/(2-o-a):h/(o+a),l=sa(e,s,n,h,o),l=l*60+.5),[l|0,c||0,r]}function Gi(i,t,e,s){return(Array.isArray(t)?i(t[0],t[1],t[2]):i(t,e,s)).map(Ot)}function Zi(i,t,e){return Gi(Bn,i,t,e)}function na(i,t,e){return Gi(ia,i,t,e)}function oa(i,t,e){return Gi(ea,i,t,e)}function Vn(i){return(i%360+360)%360}function aa(i){const t=ta.exec(i);let e=255,s;if(!t)return;t[5]!==s&&(e=t[6]?he(+t[5]):Ot(+t[5]));const n=Vn(+t[2]),o=+t[3]/100,a=+t[4]/100;return t[1]==="hwb"?s=na(n,o,a):t[1]==="hsv"?s=oa(n,o,a):s=Zi(n,o,a),{r:s[0],g:s[1],b:s[2],a:e}}function ra(i,t){var e=qi(i);e[0]=Vn(e[0]+t),e=Zi(e),i.r=e[0],i.g=e[1],i.b=e[2]}function la(i){if(!i)return;const t=qi(i),e=t[0],s=fs(t[1]),n=fs(t[2]);return i.a<255?`hsla(${e}, ${s}%, ${n}%, ${vt(i.a)})`:`hsl(${e}, ${s}%, ${n}%)`}const gs={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},ps={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function ca(){const i={},t=Object.keys(ps),e=Object.keys(gs);let s,n,o,a,r;for(s=0;s<t.length;s++){for(a=r=t[s],n=0;n<e.length;n++)o=e[n],r=r.replace(o,gs[o]);o=parseInt(ps[a],16),i[r]=[o>>16&255,o>>8&255,o&255]}return i}let Fe;function ha(i){Fe||(Fe=ca(),Fe.transparent=[0,0,0,0]);const t=Fe[i.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const da=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function ua(i){const t=da.exec(i);let e=255,s,n,o;if(t){if(t[7]!==s){const a=+t[7];e=t[8]?he(a):St(a*255,0,255)}return s=+t[1],n=+t[3],o=+t[5],s=255&(t[2]?he(s):St(s,0,255)),n=255&(t[4]?he(n):St(n,0,255)),o=255&(t[6]?he(o):St(o,0,255)),{r:s,g:n,b:o,a:e}}}function fa(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${vt(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}const _i=i=>i<=.0031308?i*12.92:Math.pow(i,1/2.4)*1.055-.055,qt=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function ga(i,t,e){const s=qt(vt(i.r)),n=qt(vt(i.g)),o=qt(vt(i.b));return{r:Ot(_i(s+e*(qt(vt(t.r))-s))),g:Ot(_i(n+e*(qt(vt(t.g))-n))),b:Ot(_i(o+e*(qt(vt(t.b))-o))),a:i.a+e*(t.a-i.a)}}function ze(i,t,e){if(i){let s=qi(i);s[t]=Math.max(0,Math.min(s[t]+s[t]*e,t===0?360:1)),s=Zi(s),i.r=s[0],i.g=s[1],i.b=s[2]}}function Hn(i,t){return i&&Object.assign(t||{},i)}function ms(i){var t={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(t={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(t.a=Ot(i[3]))):(t=Hn(i,{r:0,g:0,b:0,a:1}),t.a=Ot(t.a)),t}function pa(i){return i.charAt(0)==="r"?ua(i):aa(i)}class ve{constructor(t){if(t instanceof ve)return t;const e=typeof t;let s;e==="object"?s=ms(t):e==="string"&&(s=Zo(t)||ha(t)||pa(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=Hn(this._rgb);return t&&(t.a=vt(t.a)),t}set rgb(t){this._rgb=ms(t)}rgbString(){return this._valid?fa(this._rgb):void 0}hexString(){return this._valid?Qo(this._rgb):void 0}hslString(){return this._valid?la(this._rgb):void 0}mix(t,e){if(t){const s=this.rgb,n=t.rgb;let o;const a=e===o?.5:e,r=2*a-1,l=s.a-n.a,c=((r*l===-1?r:(r+l)/(1+r*l))+1)/2;o=1-c,s.r=255&c*s.r+o*n.r+.5,s.g=255&c*s.g+o*n.g+.5,s.b=255&c*s.b+o*n.b+.5,s.a=a*s.a+(1-a)*n.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=ga(this._rgb,t._rgb,e)),this}clone(){return new ve(this.rgb)}alpha(t){return this._rgb.a=Ot(t),this}clearer(t){const e=this._rgb;return e.a*=1-t,this}greyscale(){const t=this._rgb,e=Oe(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){const e=this._rgb;return e.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return ze(this._rgb,2,t),this}darken(t){return ze(this._rgb,2,-t),this}saturate(t){return ze(this._rgb,1,t),this}desaturate(t){return ze(this._rgb,1,-t),this}rotate(t){return ra(this._rgb,t),this}}/*!
 * Chart.js v4.4.3
 * https://www.chartjs.org
 * (c) 2024 Chart.js Contributors
 * Released under the MIT License
 */function xt(){}const ma=(()=>{let i=0;return()=>i++})();function R(i){return i===null||typeof i>"u"}function V(i){if(Array.isArray&&Array.isArray(i))return!0;const t=Object.prototype.toString.call(i);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function T(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function Y(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function ot(i,t){return Y(i)?i:t}function C(i,t){return typeof i>"u"?t:i}const ba=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100:+i/t,Nn=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*t:+i;function B(i,t,e){if(i&&typeof i.call=="function")return i.apply(e,t)}function E(i,t,e,s){let n,o,a;if(V(i))if(o=i.length,s)for(n=o-1;n>=0;n--)t.call(e,i[n],n);else for(n=0;n<o;n++)t.call(e,i[n],n);else if(T(i))for(a=Object.keys(i),o=a.length,n=0;n<o;n++)t.call(e,i[a[n]],a[n])}function ni(i,t){let e,s,n,o;if(!i||!t||i.length!==t.length)return!1;for(e=0,s=i.length;e<s;++e)if(n=i[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function oi(i){if(V(i))return i.map(oi);if(T(i)){const t=Object.create(null),e=Object.keys(i),s=e.length;let n=0;for(;n<s;++n)t[e[n]]=oi(i[e[n]]);return t}return i}function Wn(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function xa(i,t,e,s){if(!Wn(i))return;const n=t[i],o=e[i];T(n)&&T(o)?we(n,o,s):t[i]=oi(o)}function we(i,t,e){const s=V(t)?t:[t],n=s.length;if(!T(i))return i;e=e||{};const o=e.merger||xa;let a;for(let r=0;r<n;++r){if(a=s[r],!T(a))continue;const l=Object.keys(a);for(let c=0,h=l.length;c<h;++c)o(l[c],i,a,e)}return i}function me(i,t){return we(i,t,{merger:_a})}function _a(i,t,e){if(!Wn(i))return;const s=t[i],n=e[i];T(s)&&T(n)?me(s,n):Object.prototype.hasOwnProperty.call(t,i)||(t[i]=oi(n))}const bs={"":i=>i,x:i=>i.x,y:i=>i.y};function ya(i){const t=i.split("."),e=[];let s="";for(const n of t)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(e.push(s),s="");return e}function va(i){const t=ya(i);return e=>{for(const s of t){if(s==="")break;e=e&&e[s]}return e}}function At(i,t){return(bs[t]||(bs[t]=va(t)))(i)}function Ji(i){return i.charAt(0).toUpperCase()+i.slice(1)}const Me=i=>typeof i<"u",Tt=i=>typeof i=="function",xs=(i,t)=>{if(i.size!==t.size)return!1;for(const e of i)if(!t.has(e))return!1;return!0};function wa(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}const N=Math.PI,H=2*N,Ma=H+N,ai=Number.POSITIVE_INFINITY,ka=N/180,K=N/2,Et=N/4,_s=N*2/3,Pt=Math.log10,mt=Math.sign;function be(i,t,e){return Math.abs(i-t)<e}function ys(i){const t=Math.round(i);i=be(i,t,i/1e3)?t:i;const e=Math.pow(10,Math.floor(Pt(i))),s=i/e;return(s<=1?1:s<=2?2:s<=5?5:10)*e}function Sa(i){const t=[],e=Math.sqrt(i);let s;for(s=1;s<e;s++)i%s===0&&(t.push(s),t.push(i/s));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function Jt(i){return!isNaN(parseFloat(i))&&isFinite(i)}function Pa(i,t){const e=Math.round(i);return e-t<=i&&e+t>=i}function jn(i,t,e){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function ht(i){return i*(N/180)}function Qi(i){return i*(180/N)}function vs(i){if(!Y(i))return;let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}function $n(i,t){const e=t.x-i.x,s=t.y-i.y,n=Math.sqrt(e*e+s*s);let o=Math.atan2(s,e);return o<-.5*N&&(o+=H),{angle:o,distance:n}}function Li(i,t){return Math.sqrt(Math.pow(t.x-i.x,2)+Math.pow(t.y-i.y,2))}function Da(i,t){return(i-t+Ma)%H-N}function at(i){return(i%H+H)%H}function ke(i,t,e,s){const n=at(i),o=at(t),a=at(e),r=at(o-n),l=at(a-n),c=at(n-o),h=at(n-a);return n===o||n===a||s&&o===a||r>l&&c<h}function G(i,t,e){return Math.max(t,Math.min(e,i))}function Ca(i){return G(i,-32768,32767)}function wt(i,t,e,s=1e-6){return i>=Math.min(t,e)-s&&i<=Math.max(t,e)+s}function ts(i,t,e){e=e||(a=>i[a]<t);let s=i.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,e(o)?n=o:s=o;return{lo:n,hi:s}}const Mt=(i,t,e,s)=>ts(i,e,s?n=>{const o=i[n][t];return o<e||o===e&&i[n+1][t]===e}:n=>i[n][t]<e),Oa=(i,t,e)=>ts(i,e,s=>i[s][t]>=e);function Aa(i,t,e){let s=0,n=i.length;for(;s<n&&i[s]<t;)s++;for(;n>s&&i[n-1]>e;)n--;return s>0||n<i.length?i.slice(s,n):i}const Yn=["push","pop","shift","splice","unshift"];function Ta(i,t){if(i._chartjs){i._chartjs.listeners.push(t);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),Yn.forEach(e=>{const s="_onData"+Ji(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(...o){const a=n.apply(this,o);return i._chartjs.listeners.forEach(r=>{typeof r[s]=="function"&&r[s](...o)}),a}})})}function ws(i,t){const e=i._chartjs;if(!e)return;const s=e.listeners,n=s.indexOf(t);n!==-1&&s.splice(n,1),!(s.length>0)&&(Yn.forEach(o=>{delete i[o]}),delete i._chartjs)}function Un(i){const t=new Set(i);return t.size===i.length?i:Array.from(t)}const Xn=function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame}();function Kn(i,t){let e=[],s=!1;return function(...n){e=n,s||(s=!0,Xn.call(window,()=>{s=!1,i.apply(t,e)}))}}function La(i,t){let e;return function(...s){return t?(clearTimeout(e),e=setTimeout(i,t,s)):i.apply(this,s),t}}const es=i=>i==="start"?"left":i==="end"?"right":"center",Q=(i,t,e)=>i==="start"?t:i==="end"?e:(t+e)/2,Ra=(i,t,e,s)=>i===(s?"left":"right")?e:i==="center"?(t+e)/2:t;function qn(i,t,e){const s=t.length;let n=0,o=s;if(i._sorted){const{iScale:a,_parsed:r}=i,l=a.axis,{min:c,max:h,minDefined:d,maxDefined:u}=a.getUserBounds();d&&(n=G(Math.min(Mt(r,l,c).lo,e?s:Mt(t,l,a.getPixelForValue(c)).lo),0,s-1)),u?o=G(Math.max(Mt(r,a.axis,h,!0).hi+1,e?0:Mt(t,l,a.getPixelForValue(h),!0).hi+1),n,s)-n:o=s-n}return{start:n,count:o}}function Gn(i){const{xScale:t,yScale:e,_scaleRanges:s}=i,n={xmin:t.min,xmax:t.max,ymin:e.min,ymax:e.max};if(!s)return i._scaleRanges=n,!0;const o=s.xmin!==t.min||s.xmax!==t.max||s.ymin!==e.min||s.ymax!==e.max;return Object.assign(s,n),o}const Be=i=>i===0||i===1,Ms=(i,t,e)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-t)*H/e)),ks=(i,t,e)=>Math.pow(2,-10*i)*Math.sin((i-t)*H/e)+1,xe={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*K)+1,easeOutSine:i=>Math.sin(i*K),easeInOutSine:i=>-.5*(Math.cos(N*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>Be(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>Be(i)?i:Ms(i,.075,.3),easeOutElastic:i=>Be(i)?i:ks(i,.075,.3),easeInOutElastic(i){return Be(i)?i:i<.5?.5*Ms(i*2,.1125,.45):.5+.5*ks(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let t=1.70158;return(i/=.5)<1?.5*(i*i*(((t*=1.525)+1)*i-t)):.5*((i-=2)*i*(((t*=1.525)+1)*i+t)+2)},easeInBounce:i=>1-xe.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?xe.easeInBounce(i*2)*.5:xe.easeOutBounce(i*2-1)*.5+.5};function is(i){if(i&&typeof i=="object"){const t=i.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Ss(i){return is(i)?i:new ve(i)}function yi(i){return is(i)?i:new ve(i).saturate(.5).darken(.1).hexString()}const Ia=["x","y","borderWidth","radius","tension"],Ea=["color","borderColor","backgroundColor"];function Fa(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),i.set("animations",{colors:{type:"color",properties:Ea},numbers:{type:"number",properties:Ia}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function za(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const Ps=new Map;function Ba(i,t){t=t||{};const e=i+JSON.stringify(t);let s=Ps.get(e);return s||(s=new Intl.NumberFormat(i,t),Ps.set(e,s)),s}function Ae(i,t,e){return Ba(t,e).format(i)}const Zn={values(i){return V(i)?i:""+i},numeric(i,t,e){if(i===0)return"0";const s=this.chart.options.locale;let n,o=i;if(e.length>1){const c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=Va(i,e)}const a=Pt(Math.abs(o)),r=isNaN(a)?1:Math.max(Math.min(-1*Math.floor(a),20),0),l={notation:n,minimumFractionDigits:r,maximumFractionDigits:r};return Object.assign(l,this.options.ticks.format),Ae(i,s,l)},logarithmic(i,t,e){if(i===0)return"0";const s=e[t].significand||i/Math.pow(10,Math.floor(Pt(i)));return[1,2,3,5,10,15].includes(s)||t>.8*e.length?Zn.numeric.call(this,i,t,e):""}};function Va(i,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&i!==Math.floor(i)&&(e=i-Math.floor(i)),e}var fi={formatters:Zn};function Ha(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:fi.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const $t=Object.create(null),Ri=Object.create(null);function _e(i,t){if(!t)return i;const e=t.split(".");for(let s=0,n=e.length;s<n;++s){const o=e[s];i=i[o]||(i[o]=Object.create(null))}return i}function vi(i,t,e){return typeof t=="string"?we(_e(i,t),e):we(_e(i,""),t)}class Na{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>yi(n.backgroundColor),this.hoverBorderColor=(s,n)=>yi(n.borderColor),this.hoverColor=(s,n)=>yi(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return vi(this,t,e)}get(t){return _e(this,t)}describe(t,e){return vi(Ri,t,e)}override(t,e){return vi($t,t,e)}route(t,e,s,n){const o=_e(this,t),a=_e(this,s),r="_"+e;Object.defineProperties(o,{[r]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){const l=this[r],c=a[n];return T(l)?Object.assign({},c,l):C(l,c)},set(l){this[r]=l}}})}apply(t){t.forEach(e=>e(this))}}var U=new Na({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Fa,za,Ha]);function Wa(i){return!i||R(i.size)||R(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function ri(i,t,e,s,n){let o=t[n];return o||(o=t[n]=i.measureText(n).width,e.push(n)),o>s&&(s=o),s}function ja(i,t,e,s){s=s||{};let n=s.data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==t&&(n=s.data={},o=s.garbageCollect=[],s.font=t),i.save(),i.font=t;let a=0;const r=e.length;let l,c,h,d,u;for(l=0;l<r;l++)if(d=e[l],d!=null&&!V(d))a=ri(i,n,o,a,d);else if(V(d))for(c=0,h=d.length;c<h;c++)u=d[c],u!=null&&!V(u)&&(a=ri(i,n,o,a,u));i.restore();const f=o.length/2;if(f>e.length){for(l=0;l<f;l++)delete n[o[l]];o.splice(0,f)}return a}function Ft(i,t,e){const s=i.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*s)/s+n}function Ds(i,t){!t&&!i||(t=t||i.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,i.width,i.height),t.restore())}function Ii(i,t,e,s){Jn(i,t,e,s,null)}function Jn(i,t,e,s,n){let o,a,r,l,c,h,d,u;const f=t.pointStyle,g=t.rotation,m=t.radius;let p=(g||0)*ka;if(f&&typeof f=="object"&&(o=f.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){i.save(),i.translate(e,s),i.rotate(p),i.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),i.restore();return}if(!(isNaN(m)||m<=0)){switch(i.beginPath(),f){default:n?i.ellipse(e,s,n/2,m,0,0,H):i.arc(e,s,m,0,H),i.closePath();break;case"triangle":h=n?n/2:m,i.moveTo(e+Math.sin(p)*h,s-Math.cos(p)*m),p+=_s,i.lineTo(e+Math.sin(p)*h,s-Math.cos(p)*m),p+=_s,i.lineTo(e+Math.sin(p)*h,s-Math.cos(p)*m),i.closePath();break;case"rectRounded":c=m*.516,l=m-c,a=Math.cos(p+Et)*l,d=Math.cos(p+Et)*(n?n/2-c:l),r=Math.sin(p+Et)*l,u=Math.sin(p+Et)*(n?n/2-c:l),i.arc(e-d,s-r,c,p-N,p-K),i.arc(e+u,s-a,c,p-K,p),i.arc(e+d,s+r,c,p,p+K),i.arc(e-u,s+a,c,p+K,p+N),i.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*m,h=n?n/2:l,i.rect(e-h,s-l,2*h,2*l);break}p+=Et;case"rectRot":d=Math.cos(p)*(n?n/2:m),a=Math.cos(p)*m,r=Math.sin(p)*m,u=Math.sin(p)*(n?n/2:m),i.moveTo(e-d,s-r),i.lineTo(e+u,s-a),i.lineTo(e+d,s+r),i.lineTo(e-u,s+a),i.closePath();break;case"crossRot":p+=Et;case"cross":d=Math.cos(p)*(n?n/2:m),a=Math.cos(p)*m,r=Math.sin(p)*m,u=Math.sin(p)*(n?n/2:m),i.moveTo(e-d,s-r),i.lineTo(e+d,s+r),i.moveTo(e+u,s-a),i.lineTo(e-u,s+a);break;case"star":d=Math.cos(p)*(n?n/2:m),a=Math.cos(p)*m,r=Math.sin(p)*m,u=Math.sin(p)*(n?n/2:m),i.moveTo(e-d,s-r),i.lineTo(e+d,s+r),i.moveTo(e+u,s-a),i.lineTo(e-u,s+a),p+=Et,d=Math.cos(p)*(n?n/2:m),a=Math.cos(p)*m,r=Math.sin(p)*m,u=Math.sin(p)*(n?n/2:m),i.moveTo(e-d,s-r),i.lineTo(e+d,s+r),i.moveTo(e+u,s-a),i.lineTo(e-u,s+a);break;case"line":a=n?n/2:Math.cos(p)*m,r=Math.sin(p)*m,i.moveTo(e-a,s-r),i.lineTo(e+a,s+r);break;case"dash":i.moveTo(e,s),i.lineTo(e+Math.cos(p)*(n?n/2:m),s+Math.sin(p)*m);break;case!1:i.closePath();break}i.fill(),t.borderWidth>0&&i.stroke()}}function kt(i,t,e){return e=e||.5,!t||i&&i.x>t.left-e&&i.x<t.right+e&&i.y>t.top-e&&i.y<t.bottom+e}function gi(i,t){i.save(),i.beginPath(),i.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),i.clip()}function pi(i){i.restore()}function $a(i,t,e,s,n){if(!t)return i.lineTo(e.x,e.y);if(n==="middle"){const o=(t.x+e.x)/2;i.lineTo(o,t.y),i.lineTo(o,e.y)}else n==="after"!=!!s?i.lineTo(t.x,e.y):i.lineTo(e.x,t.y);i.lineTo(e.x,e.y)}function Ya(i,t,e,s){if(!t)return i.lineTo(e.x,e.y);i.bezierCurveTo(s?t.cp1x:t.cp2x,s?t.cp1y:t.cp2y,s?e.cp2x:e.cp1x,s?e.cp2y:e.cp1y,e.x,e.y)}function Ua(i,t){t.translation&&i.translate(t.translation[0],t.translation[1]),R(t.rotation)||i.rotate(t.rotation),t.color&&(i.fillStyle=t.color),t.textAlign&&(i.textAlign=t.textAlign),t.textBaseline&&(i.textBaseline=t.textBaseline)}function Xa(i,t,e,s,n){if(n.strikethrough||n.underline){const o=i.measureText(s),a=t-o.actualBoundingBoxLeft,r=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,c=e+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(a,h),i.lineTo(r,h),i.stroke()}}function Ka(i,t){const e=i.fillStyle;i.fillStyle=t.color,i.fillRect(t.left,t.top,t.width,t.height),i.fillStyle=e}function Yt(i,t,e,s,n,o={}){const a=V(t)?t:[t],r=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(i.save(),i.font=n.string,Ua(i,o),l=0;l<a.length;++l)c=a[l],o.backdrop&&Ka(i,o.backdrop),r&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),R(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(c,e,s,o.maxWidth)),i.fillText(c,e,s,o.maxWidth),Xa(i,e,s,c,o),s+=Number(n.lineHeight);i.restore()}function Se(i,t){const{x:e,y:s,w:n,h:o,radius:a}=t;i.arc(e+a.topLeft,s+a.topLeft,a.topLeft,1.5*N,N,!0),i.lineTo(e,s+o-a.bottomLeft),i.arc(e+a.bottomLeft,s+o-a.bottomLeft,a.bottomLeft,N,K,!0),i.lineTo(e+n-a.bottomRight,s+o),i.arc(e+n-a.bottomRight,s+o-a.bottomRight,a.bottomRight,K,0,!0),i.lineTo(e+n,s+a.topRight),i.arc(e+n-a.topRight,s+a.topRight,a.topRight,0,-K,!0),i.lineTo(e+a.topLeft,s)}const qa=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Ga=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Za(i,t){const e=(""+i).match(qa);if(!e||e[1]==="normal")return t*1.2;switch(i=+e[2],e[3]){case"px":return i;case"%":i/=100;break}return t*i}const Ja=i=>+i||0;function ss(i,t){const e={},s=T(t),n=s?Object.keys(t):t,o=T(i)?s?a=>C(i[a],i[t[a]]):a=>i[a]:()=>i;for(const a of n)e[a]=Ja(o(a));return e}function Qn(i){return ss(i,{top:"y",right:"x",bottom:"y",left:"x"})}function Wt(i){return ss(i,["topLeft","topRight","bottomLeft","bottomRight"])}function et(i){const t=Qn(i);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function q(i,t){i=i||{},t=t||U.font;let e=C(i.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let s=C(i.style,t.style);s&&!(""+s).match(Ga)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:C(i.family,t.family),lineHeight:Za(C(i.lineHeight,t.lineHeight),e),size:e,style:s,weight:C(i.weight,t.weight),string:""};return n.string=Wa(n),n}function de(i,t,e,s){let n=!0,o,a,r;for(o=0,a=i.length;o<a;++o)if(r=i[o],r!==void 0&&(t!==void 0&&typeof r=="function"&&(r=r(t),n=!1),e!==void 0&&V(r)&&(r=r[e%r.length],n=!1),r!==void 0))return s&&!n&&(s.cacheable=!1),r}function Qa(i,t,e){const{min:s,max:n}=i,o=Nn(t,(n-s)/2),a=(r,l)=>e&&r===0?0:r+l;return{min:a(s,-Math.abs(o)),max:a(n,o)}}function Lt(i,t){return Object.assign(Object.create(i),t)}function ns(i,t=[""],e,s,n=()=>i[0]){const o=e||i;typeof s>"u"&&(s=so("_fallback",i));const a={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:o,_fallback:s,_getTarget:n,override:r=>ns([r,...i],t,o,s)};return new Proxy(a,{deleteProperty(r,l){return delete r[l],delete r._keys,delete i[0][l],!0},get(r,l){return eo(r,l,()=>rr(l,t,i,r))},getOwnPropertyDescriptor(r,l){return Reflect.getOwnPropertyDescriptor(r._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(r,l){return Os(r).includes(l)},ownKeys(r){return Os(r)},set(r,l,c){const h=r._storage||(r._storage=n());return r[l]=h[l]=c,delete r._keys,!0}})}function Qt(i,t,e,s){const n={_cacheable:!1,_proxy:i,_context:t,_subProxy:e,_stack:new Set,_descriptors:to(i,s),setContext:o=>Qt(i,o,e,s),override:o=>Qt(i.override(o),t,e,s)};return new Proxy(n,{deleteProperty(o,a){return delete o[a],delete i[a],!0},get(o,a,r){return eo(o,a,()=>er(o,a,r))},getOwnPropertyDescriptor(o,a){return o._descriptors.allKeys?Reflect.has(i,a)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,a)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(o,a){return Reflect.has(i,a)},ownKeys(){return Reflect.ownKeys(i)},set(o,a,r){return i[a]=r,delete o[a],!0}})}function to(i,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:s=t.indexable,_allKeys:n=t.allKeys}=i;return{allKeys:n,scriptable:e,indexable:s,isScriptable:Tt(e)?e:()=>e,isIndexable:Tt(s)?s:()=>s}}const tr=(i,t)=>i?i+Ji(t):t,os=(i,t)=>T(t)&&i!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function eo(i,t,e){if(Object.prototype.hasOwnProperty.call(i,t)||t==="constructor")return i[t];const s=e();return i[t]=s,s}function er(i,t,e){const{_proxy:s,_context:n,_subProxy:o,_descriptors:a}=i;let r=s[t];return Tt(r)&&a.isScriptable(t)&&(r=ir(t,r,i,e)),V(r)&&r.length&&(r=sr(t,r,i,a.isIndexable)),os(t,r)&&(r=Qt(r,n,o&&o[t],a)),r}function ir(i,t,e,s){const{_proxy:n,_context:o,_subProxy:a,_stack:r}=e;if(r.has(i))throw new Error("Recursion detected: "+Array.from(r).join("->")+"->"+i);r.add(i);let l=t(o,a||s);return r.delete(i),os(i,l)&&(l=as(n._scopes,n,i,l)),l}function sr(i,t,e,s){const{_proxy:n,_context:o,_subProxy:a,_descriptors:r}=e;if(typeof o.index<"u"&&s(i))return t[o.index%t.length];if(T(t[0])){const l=t,c=n._scopes.filter(h=>h!==l);t=[];for(const h of l){const d=as(c,n,i,h);t.push(Qt(d,o,a&&a[i],r))}}return t}function io(i,t,e){return Tt(i)?i(t,e):i}const nr=(i,t)=>i===!0?t:typeof i=="string"?At(t,i):void 0;function or(i,t,e,s,n){for(const o of t){const a=nr(e,o);if(a){i.add(a);const r=io(a._fallback,e,n);if(typeof r<"u"&&r!==e&&r!==s)return r}else if(a===!1&&typeof s<"u"&&e!==s)return null}return!1}function as(i,t,e,s){const n=t._rootScopes,o=io(t._fallback,e,s),a=[...i,...n],r=new Set;r.add(s);let l=Cs(r,a,e,o||e,s);return l===null||typeof o<"u"&&o!==e&&(l=Cs(r,a,o,l,s),l===null)?!1:ns(Array.from(r),[""],n,o,()=>ar(t,e,s))}function Cs(i,t,e,s,n){for(;e;)e=or(i,t,e,s,n);return e}function ar(i,t,e){const s=i._getTarget();t in s||(s[t]={});const n=s[t];return V(n)&&T(e)?e:n||{}}function rr(i,t,e,s){let n;for(const o of t)if(n=so(tr(o,i),e),typeof n<"u")return os(i,n)?as(e,s,i,n):n}function so(i,t){for(const e of t){if(!e)continue;const s=e[i];if(typeof s<"u")return s}}function Os(i){let t=i._keys;return t||(t=i._keys=lr(i._scopes)),t}function lr(i){const t=new Set;for(const e of i)for(const s of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(s);return Array.from(t)}function no(i,t,e,s){const{iScale:n}=i,{key:o="r"}=this._parsing,a=new Array(s);let r,l,c,h;for(r=0,l=s;r<l;++r)c=r+e,h=t[c],a[r]={r:n.parse(At(h,o),c)};return a}const cr=Number.EPSILON||1e-14,te=(i,t)=>t<i.length&&!i[t].skip&&i[t],oo=i=>i==="x"?"y":"x";function hr(i,t,e,s){const n=i.skip?t:i,o=t,a=e.skip?t:e,r=Li(o,n),l=Li(a,o);let c=r/(r+l),h=l/(r+l);c=isNaN(c)?0:c,h=isNaN(h)?0:h;const d=s*c,u=s*h;return{previous:{x:o.x-d*(a.x-n.x),y:o.y-d*(a.y-n.y)},next:{x:o.x+u*(a.x-n.x),y:o.y+u*(a.y-n.y)}}}function dr(i,t,e){const s=i.length;let n,o,a,r,l,c=te(i,0);for(let h=0;h<s-1;++h)if(l=c,c=te(i,h+1),!(!l||!c)){if(be(t[h],0,cr)){e[h]=e[h+1]=0;continue}n=e[h]/t[h],o=e[h+1]/t[h],r=Math.pow(n,2)+Math.pow(o,2),!(r<=9)&&(a=3/Math.sqrt(r),e[h]=n*a*t[h],e[h+1]=o*a*t[h])}}function ur(i,t,e="x"){const s=oo(e),n=i.length;let o,a,r,l=te(i,0);for(let c=0;c<n;++c){if(a=r,r=l,l=te(i,c+1),!r)continue;const h=r[e],d=r[s];a&&(o=(h-a[e])/3,r[`cp1${e}`]=h-o,r[`cp1${s}`]=d-o*t[c]),l&&(o=(l[e]-h)/3,r[`cp2${e}`]=h+o,r[`cp2${s}`]=d+o*t[c])}}function fr(i,t="x"){const e=oo(t),s=i.length,n=Array(s).fill(0),o=Array(s);let a,r,l,c=te(i,0);for(a=0;a<s;++a)if(r=l,l=c,c=te(i,a+1),!!l){if(c){const h=c[t]-l[t];n[a]=h!==0?(c[e]-l[e])/h:0}o[a]=r?c?mt(n[a-1])!==mt(n[a])?0:(n[a-1]+n[a])/2:n[a-1]:n[a]}dr(i,n,o),ur(i,o,t)}function Ve(i,t,e){return Math.max(Math.min(i,e),t)}function gr(i,t){let e,s,n,o,a,r=kt(i[0],t);for(e=0,s=i.length;e<s;++e)a=o,o=r,r=e<s-1&&kt(i[e+1],t),o&&(n=i[e],a&&(n.cp1x=Ve(n.cp1x,t.left,t.right),n.cp1y=Ve(n.cp1y,t.top,t.bottom)),r&&(n.cp2x=Ve(n.cp2x,t.left,t.right),n.cp2y=Ve(n.cp2y,t.top,t.bottom)))}function pr(i,t,e,s,n){let o,a,r,l;if(t.spanGaps&&(i=i.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")fr(i,n);else{let c=s?i[i.length-1]:i[0];for(o=0,a=i.length;o<a;++o)r=i[o],l=hr(c,r,i[Math.min(o+1,a-(s?0:1))%a],t.tension),r.cp1x=l.previous.x,r.cp1y=l.previous.y,r.cp2x=l.next.x,r.cp2y=l.next.y,c=r}t.capBezierPoints&&gr(i,e)}function rs(){return typeof window<"u"&&typeof document<"u"}function ls(i){let t=i.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function li(i,t,e){let s;return typeof i=="string"?(s=parseInt(i,10),i.indexOf("%")!==-1&&(s=s/100*t.parentNode[e])):s=i,s}const mi=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function mr(i,t){return mi(i).getPropertyValue(t)}const br=["top","right","bottom","left"];function jt(i,t,e){const s={};e=e?"-"+e:"";for(let n=0;n<4;n++){const o=br[n];s[o]=parseFloat(i[t+"-"+o+e])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const xr=(i,t,e)=>(i>0||t>0)&&(!e||!e.shadowRoot);function _r(i,t){const e=i.touches,s=e&&e.length?e[0]:i,{offsetX:n,offsetY:o}=s;let a=!1,r,l;if(xr(n,o,i.target))r=n,l=o;else{const c=t.getBoundingClientRect();r=s.clientX-c.left,l=s.clientY-c.top,a=!0}return{x:r,y:l,box:a}}function Vt(i,t){if("native"in i)return i;const{canvas:e,currentDevicePixelRatio:s}=t,n=mi(e),o=n.boxSizing==="border-box",a=jt(n,"padding"),r=jt(n,"border","width"),{x:l,y:c,box:h}=_r(i,e),d=a.left+(h&&r.left),u=a.top+(h&&r.top);let{width:f,height:g}=t;return o&&(f-=a.width+r.width,g-=a.height+r.height),{x:Math.round((l-d)/f*e.width/s),y:Math.round((c-u)/g*e.height/s)}}function yr(i,t,e){let s,n;if(t===void 0||e===void 0){const o=i&&ls(i);if(!o)t=i.clientWidth,e=i.clientHeight;else{const a=o.getBoundingClientRect(),r=mi(o),l=jt(r,"border","width"),c=jt(r,"padding");t=a.width-c.width-l.width,e=a.height-c.height-l.height,s=li(r.maxWidth,o,"clientWidth"),n=li(r.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:s||ai,maxHeight:n||ai}}const He=i=>Math.round(i*10)/10;function vr(i,t,e,s){const n=mi(i),o=jt(n,"margin"),a=li(n.maxWidth,i,"clientWidth")||ai,r=li(n.maxHeight,i,"clientHeight")||ai,l=yr(i,t,e);let{width:c,height:h}=l;if(n.boxSizing==="content-box"){const u=jt(n,"border","width"),f=jt(n,"padding");c-=f.width+u.width,h-=f.height+u.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=He(Math.min(c,a,l.maxWidth)),h=He(Math.min(h,r,l.maxHeight)),c&&!h&&(h=He(c/2)),(t!==void 0||e!==void 0)&&s&&l.height&&h>l.height&&(h=l.height,c=He(Math.floor(h*s))),{width:c,height:h}}function As(i,t,e){const s=t||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const a=i.canvas;return a.style&&(e||!a.style.height&&!a.style.width)&&(a.style.height=`${i.height}px`,a.style.width=`${i.width}px`),i.currentDevicePixelRatio!==s||a.height!==n||a.width!==o?(i.currentDevicePixelRatio=s,a.height=n,a.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0):!1}const wr=function(){let i=!1;try{const t={get passive(){return i=!0,!1}};rs()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return i}();function Ts(i,t){const e=mr(i,t),s=e&&e.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function Ht(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:i.y+e*(t.y-i.y)}}function Mr(i,t,e,s){return{x:i.x+e*(t.x-i.x),y:s==="middle"?e<.5?i.y:t.y:s==="after"?e<1?i.y:t.y:e>0?t.y:i.y}}function kr(i,t,e,s){const n={x:i.cp2x,y:i.cp2y},o={x:t.cp1x,y:t.cp1y},a=Ht(i,n,e),r=Ht(n,o,e),l=Ht(o,t,e),c=Ht(a,r,e),h=Ht(r,l,e);return Ht(c,h,e)}const Sr=function(i,t){return{x(e){return i+i+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,s){return e-s},leftForLtr(e,s){return e-s}}},Pr=function(){return{x(i){return i},setWidth(i){},textAlign(i){return i},xPlus(i,t){return i+t},leftForLtr(i,t){return i}}};function Zt(i,t,e){return i?Sr(t,e):Pr()}function ao(i,t){let e,s;(t==="ltr"||t==="rtl")&&(e=i.canvas.style,s=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),i.prevTextDirection=s)}function ro(i,t){t!==void 0&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",t[0],t[1]))}function lo(i){return i==="angle"?{between:ke,compare:Da,normalize:at}:{between:wt,compare:(t,e)=>t-e,normalize:t=>t}}function Ls({start:i,end:t,count:e,loop:s,style:n}){return{start:i%e,end:t%e,loop:s&&(t-i+1)%e===0,style:n}}function Dr(i,t,e){const{property:s,start:n,end:o}=e,{between:a,normalize:r}=lo(s),l=t.length;let{start:c,end:h,loop:d}=i,u,f;if(d){for(c+=l,h+=l,u=0,f=l;u<f&&a(r(t[c%l][s]),n,o);++u)c--,h--;c%=l,h%=l}return h<c&&(h+=l),{start:c,end:h,loop:d,style:i.style}}function co(i,t,e){if(!e)return[i];const{property:s,start:n,end:o}=e,a=t.length,{compare:r,between:l,normalize:c}=lo(s),{start:h,end:d,loop:u,style:f}=Dr(i,t,e),g=[];let m=!1,p=null,b,x,v;const w=()=>l(n,v,b)&&r(n,v)!==0,y=()=>r(o,b)===0||l(o,v,b),k=()=>m||w(),S=()=>!m||y();for(let P=h,D=h;P<=d;++P)x=t[P%a],!x.skip&&(b=c(x[s]),b!==v&&(m=l(b,n,o),p===null&&k()&&(p=r(b,n)===0?P:D),p!==null&&S()&&(g.push(Ls({start:p,end:P,loop:u,count:a,style:f})),p=null),D=P,v=b));return p!==null&&g.push(Ls({start:p,end:d,loop:u,count:a,style:f})),g}function ho(i,t){const e=[],s=i.segments;for(let n=0;n<s.length;n++){const o=co(s[n],i.points,t);o.length&&e.push(...o)}return e}function Cr(i,t,e,s){let n=0,o=t-1;if(e&&!s)for(;n<t&&!i[n].skip;)n++;for(;n<t&&i[n].skip;)n++;for(n%=t,e&&(o+=n);o>n&&i[o%t].skip;)o--;return o%=t,{start:n,end:o}}function Or(i,t,e,s){const n=i.length,o=[];let a=t,r=i[t],l;for(l=t+1;l<=e;++l){const c=i[l%n];c.skip||c.stop?r.skip||(s=!1,o.push({start:t%n,end:(l-1)%n,loop:s}),t=a=c.stop?l:null):(a=l,r.skip&&(t=l)),r=c}return a!==null&&o.push({start:t%n,end:a%n,loop:s}),o}function Ar(i,t){const e=i.points,s=i.options.spanGaps,n=e.length;if(!n)return[];const o=!!i._loop,{start:a,end:r}=Cr(e,n,o,s);if(s===!0)return Rs(i,[{start:a,end:r,loop:o}],e,t);const l=r<a?r+n:r,c=!!i._fullLoop&&a===0&&r===n-1;return Rs(i,Or(e,a,l,c),e,t)}function Rs(i,t,e,s){return!s||!s.setContext||!e?t:Tr(i,t,e,s)}function Tr(i,t,e,s){const n=i._chart.getContext(),o=Is(i.options),{_datasetIndex:a,options:{spanGaps:r}}=i,l=e.length,c=[];let h=o,d=t[0].start,u=d;function f(g,m,p,b){const x=r?-1:1;if(g!==m){for(g+=l;e[g%l].skip;)g-=x;for(;e[m%l].skip;)m+=x;g%l!==m%l&&(c.push({start:g%l,end:m%l,loop:p,style:b}),h=b,d=m%l)}}for(const g of t){d=r?d:g.start;let m=e[d%l],p;for(u=d+1;u<=g.end;u++){const b=e[u%l];p=Is(s.setContext(Lt(n,{type:"segment",p0:m,p1:b,p0DataIndex:(u-1)%l,p1DataIndex:u%l,datasetIndex:a}))),Lr(p,h)&&f(d,u-1,g.loop,h),m=b,h=p}d<u-1&&f(d,u-1,g.loop,h)}return c}function Is(i){return{backgroundColor:i.backgroundColor,borderCapStyle:i.borderCapStyle,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderJoinStyle:i.borderJoinStyle,borderWidth:i.borderWidth,borderColor:i.borderColor}}function Lr(i,t){if(!t)return!1;const e=[],s=function(n,o){return is(o)?(e.includes(o)||e.push(o),e.indexOf(o)):o};return JSON.stringify(i,s)!==JSON.stringify(t,s)}/*!
 * Chart.js v4.4.3
 * https://www.chartjs.org
 * (c) 2024 Chart.js Contributors
 * Released under the MIT License
 */class Rr{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,s,n){const o=e.listeners[n],a=e.duration;o.forEach(r=>r({chart:t,initial:e.initial,numSteps:a,currentStep:Math.min(s-e.start,a)}))}_refresh(){this._request||(this._running=!0,this._request=Xn.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let a=o.length-1,r=!1,l;for(;a>=0;--a)l=o[a],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),r=!0):(o[a]=o[o.length-1],o.pop());r&&(n.draw(),this._notify(n,s,t,"progress")),o.length||(s.running=!1,this._notify(n,s,t,"complete"),s.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let s=e.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,s)),s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const s=e.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var _t=new Rr;const Es="transparent",Ir={boolean(i,t,e){return e>.5?t:i},color(i,t,e){const s=Ss(i||Es),n=s.valid&&Ss(t||Es);return n&&n.valid?n.mix(s,e).hexString():t},number(i,t,e){return i+(t-i)*e}};class Er{constructor(t,e,s,n){const o=e[s];n=de([t.to,n,o,t.from]);const a=de([t.from,o,n]);this._active=!0,this._fn=t.fn||Ir[t.type||typeof a],this._easing=xe[t.easing]||xe.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=s,this._from=a,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,a=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(a,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=de([t.to,e,n,t.from]),this._from=de([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,s=this._duration,n=this._prop,o=this._from,a=this._loop,r=this._to;let l;if(this._active=o!==r&&(a||e<s),!this._active){this._target[n]=r,this._notify(!0);return}if(e<0){this._target[n]=o;return}l=e/s%2,l=a&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,r,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,s)=>{t.push({res:e,rej:s})})}_notify(t){const e=t?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][e]()}}class uo{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!T(t))return;const e=Object.keys(U.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const o=t[n];if(!T(o))return;const a={};for(const r of e)a[r]=o[r];(V(o.properties)&&o.properties||[n]).forEach(r=>{(r===n||!s.has(r))&&s.set(r,a)})})}_animateOptions(t,e){const s=e.options,n=zr(t,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&Fr(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,e){const s=this._properties,n=[],o=t.$animations||(t.$animations={}),a=Object.keys(e),r=Date.now();let l;for(l=a.length-1;l>=0;--l){const c=a[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(t,e));continue}const h=e[c];let d=o[c];const u=s.get(c);if(d)if(u&&d.active()){d.update(u,h,r);continue}else d.cancel();if(!u||!u.duration){t[c]=h;continue}o[c]=d=new Er(u,t,c,h),n.push(d)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const s=this._createAnimations(t,e);if(s.length)return _t.add(this._chart,s),!0}}function Fr(i,t){const e=[],s=Object.keys(t);for(let n=0;n<s.length;n++){const o=i[s[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function zr(i,t){if(!t)return;let e=i.options;if(!e){i.options=t;return}return e.$shared&&(i.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function Fs(i,t){const e=i&&i.options||{},s=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:s?o:n,end:s?n:o}}function Br(i,t,e){if(e===!1)return!1;const s=Fs(i,e),n=Fs(t,e);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function Vr(i){let t,e,s,n;return T(i)?(t=i.top,e=i.right,s=i.bottom,n=i.left):t=e=s=n=i,{top:t,right:e,bottom:s,left:n,disabled:i===!1}}function fo(i,t){const e=[],s=i._getSortedDatasetMetas(t);let n,o;for(n=0,o=s.length;n<o;++n)e.push(s[n].index);return e}function zs(i,t,e,s={}){const n=i.keys,o=s.mode==="single";let a,r,l,c;if(t!==null){for(a=0,r=n.length;a<r;++a){if(l=+n[a],l===e){if(s.all)continue;break}c=i.values[l],Y(c)&&(o||t===0||mt(t)===mt(c))&&(t+=c)}return t}}function Hr(i,t){const{iScale:e,vScale:s}=t,n=e.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",a=Object.keys(i),r=new Array(a.length);let l,c,h;for(l=0,c=a.length;l<c;++l)h=a[l],r[l]={[n]:h,[o]:i[h]};return r}function Bs(i,t){const e=i&&i.options.stacked;return e||e===void 0&&t.stack!==void 0}function Nr(i,t,e){return`${i.id}.${t.id}.${e.stack||e.type}`}function Wr(i){const{min:t,max:e,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function jr(i,t,e){const s=i[t]||(i[t]={});return s[e]||(s[e]={})}function Vs(i,t,e,s){for(const n of t.getMatchingVisibleMetas(s).reverse()){const o=i[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function Hs(i,t){const{chart:e,_cachedMeta:s}=i,n=e._stacks||(e._stacks={}),{iScale:o,vScale:a,index:r}=s,l=o.axis,c=a.axis,h=Nr(o,a,s),d=t.length;let u;for(let f=0;f<d;++f){const g=t[f],{[l]:m,[c]:p}=g,b=g._stacks||(g._stacks={});u=b[c]=jr(n,h,m),u[r]=p,u._top=Vs(u,a,!0,s.type),u._bottom=Vs(u,a,!1,s.type);const x=u._visualValues||(u._visualValues={});x[r]=p}}function wi(i,t){const e=i.scales;return Object.keys(e).filter(s=>e[s].axis===t).shift()}function $r(i,t){return Lt(i,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function Yr(i,t,e){return Lt(i,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function oe(i,t){const e=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){t=t||i._parsed;for(const n of t){const o=n._stacks;if(!o||o[s]===void 0||o[s][e]===void 0)return;delete o[s][e],o[s]._visualValues!==void 0&&o[s]._visualValues[e]!==void 0&&delete o[s]._visualValues[e]}}}const Mi=i=>i==="reset"||i==="none",Ns=(i,t)=>t?i:Object.assign({},i),Ur=(i,t,e)=>i&&!t.hidden&&t._stacked&&{keys:fo(e,!0),values:null};class dt{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Bs(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&oe(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,s=this.getDataset(),n=(d,u,f,g)=>d==="x"?u:d==="r"?g:f,o=e.xAxisID=C(s.xAxisID,wi(t,"x")),a=e.yAxisID=C(s.yAxisID,wi(t,"y")),r=e.rAxisID=C(s.rAxisID,wi(t,"r")),l=e.indexAxis,c=e.iAxisID=n(l,o,a,r),h=e.vAxisID=n(l,a,o,r);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(a),e.rScale=this.getScaleForId(r),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&ws(this._data,this),t._stacked&&oe(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),s=this._data;if(T(e)){const n=this._cachedMeta;this._data=Hr(e,n)}else if(s!==e){if(s){ws(s,this);const n=this._cachedMeta;oe(n),n._parsed=[]}e&&Object.isExtensible(e)&&Ta(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,s=this.getDataset();let n=!1;this._dataCheck();const o=e._stacked;e._stacked=Bs(e.vScale,e),e.stack!==s.stack&&(n=!0,oe(e),e.stack=s.stack),this._resyncElements(t),(n||o!==e._stacked)&&Hs(this,e._parsed)}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:s,_data:n}=this,{iScale:o,_stacked:a}=s,r=o.axis;let l=t===0&&e===n.length?!0:s._sorted,c=t>0&&s._parsed[t-1],h,d,u;if(this._parsing===!1)s._parsed=n,s._sorted=!0,u=n;else{V(n[t])?u=this.parseArrayData(s,n,t,e):T(n[t])?u=this.parseObjectData(s,n,t,e):u=this.parsePrimitiveData(s,n,t,e);const f=()=>d[r]===null||c&&d[r]<c[r];for(h=0;h<e;++h)s._parsed[h+t]=d=u[h],l&&(f()&&(l=!1),c=d);s._sorted=l}a&&Hs(this,u)}parsePrimitiveData(t,e,s,n){const{iScale:o,vScale:a}=t,r=o.axis,l=a.axis,c=o.getLabels(),h=o===a,d=new Array(n);let u,f,g;for(u=0,f=n;u<f;++u)g=u+s,d[u]={[r]:h||o.parse(c[g],g),[l]:a.parse(e[g],g)};return d}parseArrayData(t,e,s,n){const{xScale:o,yScale:a}=t,r=new Array(n);let l,c,h,d;for(l=0,c=n;l<c;++l)h=l+s,d=e[h],r[l]={x:o.parse(d[0],h),y:a.parse(d[1],h)};return r}parseObjectData(t,e,s,n){const{xScale:o,yScale:a}=t,{xAxisKey:r="x",yAxisKey:l="y"}=this._parsing,c=new Array(n);let h,d,u,f;for(h=0,d=n;h<d;++h)u=h+s,f=e[u],c[h]={x:o.parse(At(f,r),u),y:a.parse(At(f,l),u)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){const n=this.chart,o=this._cachedMeta,a=e[t.axis],r={keys:fo(n,!0),values:e._stacks[t.axis]._visualValues};return zs(r,a,o.index,{mode:s})}updateRangeFromParsed(t,e,s,n){const o=s[e.axis];let a=o===null?NaN:o;const r=n&&s._stacks[e.axis];n&&r&&(n.values=r,a=zs(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,a),t.max=Math.max(t.max,a)}getMinMax(t,e){const s=this._cachedMeta,n=s._parsed,o=s._sorted&&t===s.iScale,a=n.length,r=this._getOtherScale(t),l=Ur(e,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=Wr(r);let u,f;function g(){f=n[u];const m=f[r.axis];return!Y(f[t.axis])||h>m||d<m}for(u=0;u<a&&!(!g()&&(this.updateRangeFromParsed(c,t,f,l),o));++u);if(o){for(u=a-1;u>=0;--u)if(!g()){this.updateRangeFromParsed(c,t,f,l);break}}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed,s=[];let n,o,a;for(n=0,o=e.length;n<o;++n)a=e[n][t.axis],Y(a)&&s.push(a);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,s=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=Vr(C(this.options.clip,Br(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,s=this._cachedMeta,n=s.data||[],o=e.chartArea,a=[],r=this._drawStart||0,l=this._drawCount||n.length-r,c=this.options.drawActiveElementsOnTop;let h;for(s.dataset&&s.dataset.draw(t,o,r,l),h=r;h<r+l;++h){const d=n[h];d.hidden||(d.active&&c?a.push(d):d.draw(t,o))}for(h=0;h<a.length;++h)a[h].draw(t,o)}getStyle(t,e){const s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){const n=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const a=this._cachedMeta.data[t];o=a.$context||(a.$context=Yr(this.getContext(),t,a)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=$r(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){const n=e==="active",o=this._cachedDataOpts,a=t+"-"+e,r=o[a],l=this.enableOptionSharing&&Me(s);if(r)return Ns(r,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),d=n?[`${t}Hover`,"hover",t,""]:[t,""],u=c.getOptionScopes(this.getDataset(),h),f=Object.keys(U.elements[t]),g=()=>this.getContext(s,n,e),m=c.resolveNamedOptions(u,f,g,d);return m.$shared&&(m.$shared=l,o[a]=Object.freeze(Ns(m,l))),m}_resolveAnimations(t,e,s){const n=this.chart,o=this._cachedDataOpts,a=`animation-${e}`,r=o[a];if(r)return r;let l;if(n.options.animation!==!1){const h=this.chart.config,d=h.datasetAnimationScopeKeys(this._type,e),u=h.getOptionScopes(this.getDataset(),d);l=h.createResolver(u,this.getContext(t,s,e))}const c=new uo(n,l&&l.animations);return l&&l._cacheable&&(o[a]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||Mi(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const s=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(s),a=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,s),{sharedOptions:o,includeOptions:a}}updateElement(t,e,s,n){Mi(n)?Object.assign(t,s):this._resolveAnimations(e,n).update(t,s)}updateSharedOptions(t,e,s){t&&!Mi(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,n){t.active=n;const o=this.getStyle(e,n);this._resolveAnimations(e,s,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,s=this._cachedMeta.data;for(const[r,l,c]of this._syncList)this[r](l,c);this._syncList=[];const n=s.length,o=e.length,a=Math.min(o,n);a&&this.parse(0,a),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,s=!0){const n=this._cachedMeta,o=n.data,a=t+e;let r;const l=c=>{for(c.length+=e,r=c.length-1;r>=a;r--)c[r]=c[r-e]};for(l(o),r=t;r<a;++r)o[r]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),s&&this.updateElements(o,t,e,"reset")}updateElements(t,e,s,n){}_removeElements(t,e){const s=this._cachedMeta;if(this._parsing){const n=s._parsed.splice(t,e);s._stacked&&oe(s,n)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,s,n]=t;this[e](s,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}M(dt,"defaults",{}),M(dt,"datasetElementType",null),M(dt,"dataElementType",null);function Xr(i,t){if(!i._cache.$bar){const e=i.getMatchingVisibleMetas(t);let s=[];for(let n=0,o=e.length;n<o;n++)s=s.concat(e[n].controller.getAllParsedValues(i));i._cache.$bar=Un(s.sort((n,o)=>n-o))}return i._cache.$bar}function Kr(i){const t=i.iScale,e=Xr(t,i.type);let s=t._length,n,o,a,r;const l=()=>{a===32767||a===-32768||(Me(r)&&(s=Math.min(s,Math.abs(a-r)||s)),r=a)};for(n=0,o=e.length;n<o;++n)a=t.getPixelForValue(e[n]),l();for(r=void 0,n=0,o=t.ticks.length;n<o;++n)a=t.getPixelForTick(n),l();return s}function qr(i,t,e,s){const n=e.barThickness;let o,a;return R(n)?(o=t.min*e.categoryPercentage,a=e.barPercentage):(o=n*s,a=1),{chunk:o/s,ratio:a,start:t.pixels[i]-o/2}}function Gr(i,t,e,s){const n=t.pixels,o=n[i];let a=i>0?n[i-1]:null,r=i<n.length-1?n[i+1]:null;const l=e.categoryPercentage;a===null&&(a=o-(r===null?t.end-t.start:r-o)),r===null&&(r=o+o-a);const c=o-(o-Math.min(a,r))/2*l;return{chunk:Math.abs(r-a)/2*l/s,ratio:e.barPercentage,start:c}}function Zr(i,t,e,s){const n=e.parse(i[0],s),o=e.parse(i[1],s),a=Math.min(n,o),r=Math.max(n,o);let l=a,c=r;Math.abs(a)>Math.abs(r)&&(l=r,c=a),t[e.axis]=c,t._custom={barStart:l,barEnd:c,start:n,end:o,min:a,max:r}}function go(i,t,e,s){return V(i)?Zr(i,t,e,s):t[e.axis]=e.parse(i,s),t}function Ws(i,t,e,s){const n=i.iScale,o=i.vScale,a=n.getLabels(),r=n===o,l=[];let c,h,d,u;for(c=e,h=e+s;c<h;++c)u=t[c],d={},d[n.axis]=r||n.parse(a[c],c),l.push(go(u,d,o,c));return l}function ki(i){return i&&i.barStart!==void 0&&i.barEnd!==void 0}function Jr(i,t,e){return i!==0?mt(i):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function Qr(i){let t,e,s,n,o;return i.horizontal?(t=i.base>i.x,e="left",s="right"):(t=i.base<i.y,e="bottom",s="top"),t?(n="end",o="start"):(n="start",o="end"),{start:e,end:s,reverse:t,top:n,bottom:o}}function tl(i,t,e,s){let n=t.borderSkipped;const o={};if(!n){i.borderSkipped=o;return}if(n===!0){i.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:a,end:r,reverse:l,top:c,bottom:h}=Qr(i);n==="middle"&&e&&(i.enableBorderRadius=!0,(e._top||0)===s?n=c:(e._bottom||0)===s?n=h:(o[js(h,a,r,l)]=!0,n=c)),o[js(n,a,r,l)]=!0,i.borderSkipped=o}function js(i,t,e,s){return s?(i=el(i,t,e),i=$s(i,e,t)):i=$s(i,t,e),i}function el(i,t,e){return i===t?e:i===e?t:i}function $s(i,t,e){return i==="start"?t:i==="end"?e:i}function il(i,{inflateAmount:t},e){i.inflateAmount=t==="auto"?e===1?.33:0:t}class qe extends dt{parsePrimitiveData(t,e,s,n){return Ws(t,e,s,n)}parseArrayData(t,e,s,n){return Ws(t,e,s,n)}parseObjectData(t,e,s,n){const{iScale:o,vScale:a}=t,{xAxisKey:r="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?r:l,h=a.axis==="x"?r:l,d=[];let u,f,g,m;for(u=s,f=s+n;u<f;++u)m=e[u],g={},g[o.axis]=o.parse(At(m,c),u),d.push(go(At(m,h),g,a,u));return d}updateRangeFromParsed(t,e,s,n){super.updateRangeFromParsed(t,e,s,n);const o=s._custom;o&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:s,vScale:n}=e,o=this.getParsed(t),a=o._custom,r=ki(a)?"["+a.start+", "+a.end+"]":""+n.getLabelForValue(o[n.axis]);return{label:""+s.getLabelForValue(o[s.axis]),value:r}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,s,n){const o=n==="reset",{index:a,_cachedMeta:{vScale:r}}=this,l=r.getBasePixel(),c=r.isHorizontal(),h=this._getRuler(),{sharedOptions:d,includeOptions:u}=this._getSharedOptions(e,n);for(let f=e;f<e+s;f++){const g=this.getParsed(f),m=o||R(g[r.axis])?{base:l,head:l}:this._calculateBarValuePixels(f),p=this._calculateBarIndexPixels(f,h),b=(g._stacks||{})[r.axis],x={horizontal:c,base:m.base,enableBorderRadius:!b||ki(g._custom)||a===b._top||a===b._bottom,x:c?m.head:p.center,y:c?p.center:m.head,height:c?p.size:Math.abs(m.size),width:c?Math.abs(m.size):p.size};u&&(x.options=d||this.resolveDataElementOptions(f,t[f].active?"active":n));const v=x.options||t[f].options;tl(x,v,b,a),il(x,v,h.ratio),this.updateElement(t[f],f,x,n)}}_getStacks(t,e){const{iScale:s}=this._cachedMeta,n=s.getMatchingVisibleMetas(this._type).filter(l=>l.controller.options.grouped),o=s.options.stacked,a=[],r=l=>{const c=l.controller.getParsed(e),h=c&&c[l.vScale.axis];if(R(h)||isNaN(h))return!0};for(const l of n)if(!(e!==void 0&&r(l))&&((o===!1||a.indexOf(l.stack)===-1||o===void 0&&l.stack===void 0)&&a.push(l.stack),l.index===t))break;return a.length||a.push(void 0),a}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,s){const n=this._getStacks(t,s),o=e!==void 0?n.indexOf(e):-1;return o===-1?n.length-1:o}_getRuler(){const t=this.options,e=this._cachedMeta,s=e.iScale,n=[];let o,a;for(o=0,a=e.data.length;o<a;++o)n.push(s.getPixelForValue(this.getParsed(o)[s.axis],o));const r=t.barThickness;return{min:r||Kr(e),pixels:n,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:t.grouped,ratio:r?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:s,index:n},options:{base:o,minBarLength:a}}=this,r=o||0,l=this.getParsed(t),c=l._custom,h=ki(c);let d=l[e.axis],u=0,f=s?this.applyStack(e,l,s):d,g,m;f!==d&&(u=f-d,f=d),h&&(d=c.barStart,f=c.barEnd-c.barStart,d!==0&&mt(d)!==mt(c.barEnd)&&(u=0),u+=d);const p=!R(o)&&!h?o:u;let b=e.getPixelForValue(p);if(this.chart.getDataVisibility(t)?g=e.getPixelForValue(u+f):g=b,m=g-b,Math.abs(m)<a){m=Jr(m,e,r)*a,d===r&&(b-=m/2);const x=e.getPixelForDecimal(0),v=e.getPixelForDecimal(1),w=Math.min(x,v),y=Math.max(x,v);b=Math.max(Math.min(b,y),w),g=b+m,s&&!h&&(l._stacks[e.axis]._visualValues[n]=e.getValueForPixel(g)-e.getValueForPixel(b))}if(b===e.getPixelForValue(r)){const x=mt(m)*e.getLineWidthForValue(r)/2;b+=x,m-=x}return{size:m,base:b,head:g,center:g+m/2}}_calculateBarIndexPixels(t,e){const s=e.scale,n=this.options,o=n.skipNull,a=C(n.maxBarThickness,1/0);let r,l;if(e.grouped){const c=o?this._getStackCount(t):e.stackCount,h=n.barThickness==="flex"?Gr(t,e,n,c):qr(t,e,n,c),d=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0);r=h.start+h.chunk*d+h.chunk/2,l=Math.min(a,h.chunk*h.ratio)}else r=s.getPixelForValue(this.getParsed(t)[s.axis],t),l=Math.min(a,e.min*e.ratio);return{base:r-l/2,head:r+l/2,center:r,size:l}}draw(){const t=this._cachedMeta,e=t.vScale,s=t.data,n=s.length;let o=0;for(;o<n;++o)this.getParsed(o)[e.axis]!==null&&!s[o].hidden&&s[o].draw(this._ctx)}}M(qe,"id","bar"),M(qe,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),M(qe,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});class Ge extends dt{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,e,s,n){const o=super.parsePrimitiveData(t,e,s,n);for(let a=0;a<o.length;a++)o[a]._custom=this.resolveDataElementOptions(a+s).radius;return o}parseArrayData(t,e,s,n){const o=super.parseArrayData(t,e,s,n);for(let a=0;a<o.length;a++){const r=e[s+a];o[a]._custom=C(r[2],this.resolveDataElementOptions(a+s).radius)}return o}parseObjectData(t,e,s,n){const o=super.parseObjectData(t,e,s,n);for(let a=0;a<o.length;a++){const r=e[s+a];o[a]._custom=C(r&&r.r&&+r.r,this.resolveDataElementOptions(a+s).radius)}return o}getMaxOverflow(){const t=this._cachedMeta.data;let e=0;for(let s=t.length-1;s>=0;--s)e=Math.max(e,t[s].size(this.resolveDataElementOptions(s))/2);return e>0&&e}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart.data.labels||[],{xScale:n,yScale:o}=e,a=this.getParsed(t),r=n.getLabelForValue(a.x),l=o.getLabelForValue(a.y),c=a._custom;return{label:s[t]||"",value:"("+r+", "+l+(c?", "+c:"")+")"}}update(t){const e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,s,n){const o=n==="reset",{iScale:a,vScale:r}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(e,n),h=a.axis,d=r.axis;for(let u=e;u<e+s;u++){const f=t[u],g=!o&&this.getParsed(u),m={},p=m[h]=o?a.getPixelForDecimal(.5):a.getPixelForValue(g[h]),b=m[d]=o?r.getBasePixel():r.getPixelForValue(g[d]);m.skip=isNaN(p)||isNaN(b),c&&(m.options=l||this.resolveDataElementOptions(u,f.active?"active":n),o&&(m.options.radius=0)),this.updateElement(f,u,m,n)}}resolveDataElementOptions(t,e){const s=this.getParsed(t);let n=super.resolveDataElementOptions(t,e);n.$shared&&(n=Object.assign({},n,{$shared:!1}));const o=n.radius;return e!=="active"&&(n.radius=0),n.radius+=C(s&&s._custom,o),n}}M(Ge,"id","bubble"),M(Ge,"defaults",{datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}}),M(Ge,"overrides",{scales:{x:{type:"linear"},y:{type:"linear"}}});function sl(i,t,e){let s=1,n=1,o=0,a=0;if(t<H){const r=i,l=r+t,c=Math.cos(r),h=Math.sin(r),d=Math.cos(l),u=Math.sin(l),f=(v,w,y)=>ke(v,r,l,!0)?1:Math.max(w,w*e,y,y*e),g=(v,w,y)=>ke(v,r,l,!0)?-1:Math.min(w,w*e,y,y*e),m=f(0,c,d),p=f(K,h,u),b=g(N,c,d),x=g(N+K,h,u);s=(m-b)/2,n=(p-x)/2,o=-(m+b)/2,a=-(p+x)/2}return{ratioX:s,ratioY:n,offsetX:o,offsetY:a}}class Nt extends dt{constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const s=this.getDataset().data,n=this._cachedMeta;if(this._parsing===!1)n._parsed=s;else{let o=l=>+s[l];if(T(s[t])){const{key:l="value"}=this._parsing;o=c=>+At(s[c],l)}let a,r;for(a=t,r=t+e;a<r;++a)n._parsed[a]=o(a)}}_getRotation(){return ht(this.options.rotation-90)}_getCircumference(){return ht(this.options.circumference)}_getRotationExtents(){let t=H,e=-H;for(let s=0;s<this.chart.data.datasets.length;++s)if(this.chart.isDatasetVisible(s)&&this.chart.getDatasetMeta(s).type===this._type){const n=this.chart.getDatasetMeta(s).controller,o=n._getRotation(),a=n._getCircumference();t=Math.min(t,o),e=Math.max(e,o+a)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:s}=e,n=this._cachedMeta,o=n.data,a=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,r=Math.max((Math.min(s.width,s.height)-a)/2,0),l=Math.min(ba(this.options.cutout,r),1),c=this._getRingWeight(this.index),{circumference:h,rotation:d}=this._getRotationExtents(),{ratioX:u,ratioY:f,offsetX:g,offsetY:m}=sl(d,h,l),p=(s.width-a)/u,b=(s.height-a)/f,x=Math.max(Math.min(p,b)/2,0),v=Nn(this.options.radius,x),w=Math.max(v*l,0),y=(v-w)/this._getVisibleDatasetWeightTotal();this.offsetX=g*v,this.offsetY=m*v,n.total=this.calculateTotal(),this.outerRadius=v-y*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-y*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,e){const s=this.options,n=this._cachedMeta,o=this._getCircumference();return e&&s.animation.animateRotate||!this.chart.getDataVisibility(t)||n._parsed[t]===null||n.data[t].hidden?0:this.calculateCircumference(n._parsed[t]*o/H)}updateElements(t,e,s,n){const o=n==="reset",a=this.chart,r=a.chartArea,c=a.options.animation,h=(r.left+r.right)/2,d=(r.top+r.bottom)/2,u=o&&c.animateScale,f=u?0:this.innerRadius,g=u?0:this.outerRadius,{sharedOptions:m,includeOptions:p}=this._getSharedOptions(e,n);let b=this._getRotation(),x;for(x=0;x<e;++x)b+=this._circumference(x,o);for(x=e;x<e+s;++x){const v=this._circumference(x,o),w=t[x],y={x:h+this.offsetX,y:d+this.offsetY,startAngle:b,endAngle:b+v,circumference:v,outerRadius:g,innerRadius:f};p&&(y.options=m||this.resolveDataElementOptions(x,w.active?"active":n)),b+=v,this.updateElement(w,x,y,n)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let s=0,n;for(n=0;n<e.length;n++){const o=t._parsed[n];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(n)&&!e[n].hidden&&(s+=Math.abs(o))}return s}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?H*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart,n=s.data.labels||[],o=Ae(e._parsed[t],s.options.locale);return{label:n[t]||"",value:o}}getMaxBorderWidth(t){let e=0;const s=this.chart;let n,o,a,r,l;if(!t){for(n=0,o=s.data.datasets.length;n<o;++n)if(s.isDatasetVisible(n)){a=s.getDatasetMeta(n),t=a.data,r=a.controller;break}}if(!t)return 0;for(n=0,o=t.length;n<o;++n)l=r.resolveDataElementOptions(n),l.borderAlign!=="inner"&&(e=Math.max(e,l.borderWidth||0,l.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let s=0,n=t.length;s<n;++s){const o=this.resolveDataElementOptions(s);e=Math.max(e,o.offset||0,o.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let s=0;s<t;++s)this.chart.isDatasetVisible(s)&&(e+=this._getRingWeight(s));return e}_getRingWeight(t){return Math.max(C(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}M(Nt,"id","doughnut"),M(Nt,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),M(Nt,"descriptors",{_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")}),M(Nt,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:s,color:n}}=t.legend.options;return e.labels.map((o,a)=>{const l=t.getDatasetMeta(0).controller.getStyle(a);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index),s.chart.update()}}}});class Ze extends dt{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:s,data:n=[],_dataset:o}=e,a=this.chart._animationsDisabled;let{start:r,count:l}=qn(e,n,a);this._drawStart=r,this._drawCount=l,Gn(e)&&(r=0,l=n.length),s._chart=this.chart,s._datasetIndex=this.index,s._decimated=!!o._decimated,s.points=n;const c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(s,void 0,{animated:!a,options:c},t),this.updateElements(n,r,l,t)}updateElements(t,e,s,n){const o=n==="reset",{iScale:a,vScale:r,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:h,includeOptions:d}=this._getSharedOptions(e,n),u=a.axis,f=r.axis,{spanGaps:g,segment:m}=this.options,p=Jt(g)?g:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||o||n==="none",x=e+s,v=t.length;let w=e>0&&this.getParsed(e-1);for(let y=0;y<v;++y){const k=t[y],S=b?k:{};if(y<e||y>=x){S.skip=!0;continue}const P=this.getParsed(y),D=R(P[f]),O=S[u]=a.getPixelForValue(P[u],y),A=S[f]=o||D?r.getBasePixel():r.getPixelForValue(l?this.applyStack(r,P,l):P[f],y);S.skip=isNaN(O)||isNaN(A)||D,S.stop=y>0&&Math.abs(P[u]-w[u])>p,m&&(S.parsed=P,S.raw=c.data[y]),d&&(S.options=h||this.resolveDataElementOptions(y,k.active?"active":n)),b||this.updateElement(k,y,S,n),w=P}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,s=e.options&&e.options.borderWidth||0,n=t.data||[];if(!n.length)return s;const o=n[0].size(this.resolveDataElementOptions(0)),a=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(s,o,a)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}M(Ze,"id","line"),M(Ze,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),M(Ze,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});class ye extends dt{constructor(t,e){super(t,e),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart,n=s.data.labels||[],o=Ae(e._parsed[t].r,s.options.locale);return{label:n[t]||"",value:o}}parseObjectData(t,e,s,n){return no.bind(this)(t,e,s,n)}update(t){const e=this._cachedMeta.data;this._updateRadius(),this.updateElements(e,0,e.length,t)}getMinMax(){const t=this._cachedMeta,e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((s,n)=>{const o=this.getParsed(n).r;!isNaN(o)&&this.chart.getDataVisibility(n)&&(o<e.min&&(e.min=o),o>e.max&&(e.max=o))}),e}_updateRadius(){const t=this.chart,e=t.chartArea,s=t.options,n=Math.min(e.right-e.left,e.bottom-e.top),o=Math.max(n/2,0),a=Math.max(s.cutoutPercentage?o/100*s.cutoutPercentage:1,0),r=(o-a)/t.getVisibleDatasetCount();this.outerRadius=o-r*this.index,this.innerRadius=this.outerRadius-r}updateElements(t,e,s,n){const o=n==="reset",a=this.chart,l=a.options.animation,c=this._cachedMeta.rScale,h=c.xCenter,d=c.yCenter,u=c.getIndexAngle(0)-.5*N;let f=u,g;const m=360/this.countVisibleElements();for(g=0;g<e;++g)f+=this._computeAngle(g,n,m);for(g=e;g<e+s;g++){const p=t[g];let b=f,x=f+this._computeAngle(g,n,m),v=a.getDataVisibility(g)?c.getDistanceFromCenterForValue(this.getParsed(g).r):0;f=x,o&&(l.animateScale&&(v=0),l.animateRotate&&(b=x=u));const w={x:h,y:d,innerRadius:0,outerRadius:v,startAngle:b,endAngle:x,options:this.resolveDataElementOptions(g,p.active?"active":n)};this.updateElement(p,g,w,n)}}countVisibleElements(){const t=this._cachedMeta;let e=0;return t.data.forEach((s,n)=>{!isNaN(this.getParsed(n).r)&&this.chart.getDataVisibility(n)&&e++}),e}_computeAngle(t,e,s){return this.chart.getDataVisibility(t)?ht(this.resolveDataElementOptions(t,e).angle||s):0}}M(ye,"id","polarArea"),M(ye,"defaults",{dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0}),M(ye,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:s,color:n}}=t.legend.options;return e.labels.map((o,a)=>{const l=t.getDatasetMeta(0).controller.getStyle(a);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(a),index:a}})}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index),s.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}});class Ei extends Nt{}M(Ei,"id","pie"),M(Ei,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});class Je extends dt{getLabelAndValue(t){const e=this._cachedMeta.vScale,s=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(s[e.axis])}}parseObjectData(t,e,s,n){return no.bind(this)(t,e,s,n)}update(t){const e=this._cachedMeta,s=e.dataset,n=e.data||[],o=e.iScale.getLabels();if(s.points=n,t!=="resize"){const a=this.resolveDatasetElementOptions(t);this.options.showLine||(a.borderWidth=0);const r={_loop:!0,_fullLoop:o.length===n.length,options:a};this.updateElement(s,void 0,r,t)}this.updateElements(n,0,n.length,t)}updateElements(t,e,s,n){const o=this._cachedMeta.rScale,a=n==="reset";for(let r=e;r<e+s;r++){const l=t[r],c=this.resolveDataElementOptions(r,l.active?"active":n),h=o.getPointPositionForValue(r,this.getParsed(r).r),d=a?o.xCenter:h.x,u=a?o.yCenter:h.y,f={x:d,y:u,angle:h.angle,skip:isNaN(d)||isNaN(u),options:c};this.updateElement(l,r,f,n)}}}M(Je,"id","radar"),M(Je,"defaults",{datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}}),M(Je,"overrides",{aspectRatio:1,scales:{r:{type:"radialLinear"}}});class Qe extends dt{getLabelAndValue(t){const e=this._cachedMeta,s=this.chart.data.labels||[],{xScale:n,yScale:o}=e,a=this.getParsed(t),r=n.getLabelForValue(a.x),l=o.getLabelForValue(a.y);return{label:s[t]||"",value:"("+r+", "+l+")"}}update(t){const e=this._cachedMeta,{data:s=[]}=e,n=this.chart._animationsDisabled;let{start:o,count:a}=qn(e,s,n);if(this._drawStart=o,this._drawCount=a,Gn(e)&&(o=0,a=s.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:r,_dataset:l}=e;r._chart=this.chart,r._datasetIndex=this.index,r._decimated=!!l._decimated,r.points=s;const c=this.resolveDatasetElementOptions(t);c.segment=this.options.segment,this.updateElement(r,void 0,{animated:!n,options:c},t)}else this.datasetElementType&&(delete e.dataset,this.datasetElementType=!1);this.updateElements(s,o,a,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,e,s,n){const o=n==="reset",{iScale:a,vScale:r,_stacked:l,_dataset:c}=this._cachedMeta,h=this.resolveDataElementOptions(e,n),d=this.getSharedOptions(h),u=this.includeOptions(n,d),f=a.axis,g=r.axis,{spanGaps:m,segment:p}=this.options,b=Jt(m)?m:Number.POSITIVE_INFINITY,x=this.chart._animationsDisabled||o||n==="none";let v=e>0&&this.getParsed(e-1);for(let w=e;w<e+s;++w){const y=t[w],k=this.getParsed(w),S=x?y:{},P=R(k[g]),D=S[f]=a.getPixelForValue(k[f],w),O=S[g]=o||P?r.getBasePixel():r.getPixelForValue(l?this.applyStack(r,k,l):k[g],w);S.skip=isNaN(D)||isNaN(O)||P,S.stop=w>0&&Math.abs(k[f]-v[f])>b,p&&(S.parsed=k,S.raw=c.data[w]),u&&(S.options=d||this.resolveDataElementOptions(w,y.active?"active":n)),x||this.updateElement(y,w,S,n),v=k}this.updateSharedOptions(d,n,h)}getMaxOverflow(){const t=this._cachedMeta,e=t.data||[];if(!this.options.showLine){let r=0;for(let l=e.length-1;l>=0;--l)r=Math.max(r,e[l].size(this.resolveDataElementOptions(l))/2);return r>0&&r}const s=t.dataset,n=s.options&&s.options.borderWidth||0;if(!e.length)return n;const o=e[0].size(this.resolveDataElementOptions(0)),a=e[e.length-1].size(this.resolveDataElementOptions(e.length-1));return Math.max(n,o,a)/2}}M(Qe,"id","scatter"),M(Qe,"defaults",{datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1}),M(Qe,"overrides",{interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}});var nl=Object.freeze({__proto__:null,BarController:qe,BubbleController:Ge,DoughnutController:Nt,LineController:Ze,PieController:Ei,PolarAreaController:ye,RadarController:Je,ScatterController:Qe});function zt(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class cs{constructor(t){M(this,"options");this.options=t||{}}static override(t){Object.assign(cs.prototype,t)}init(){}formats(){return zt()}parse(){return zt()}format(){return zt()}add(){return zt()}diff(){return zt()}startOf(){return zt()}endOf(){return zt()}}var ol={_date:cs};function al(i,t,e,s){const{controller:n,data:o,_sorted:a}=i,r=n._cachedMeta.iScale;if(r&&t===r.axis&&t!=="r"&&a&&o.length){const l=r._reversePixels?Oa:Mt;if(s){if(n._sharedOptions){const c=o[0],h=typeof c.getRange=="function"&&c.getRange(t);if(h){const d=l(o,t,e-h),u=l(o,t,e+h);return{lo:d.lo,hi:u.hi}}}}else return l(o,t,e)}return{lo:0,hi:o.length-1}}function Te(i,t,e,s,n){const o=i.getSortedVisibleDatasetMetas(),a=e[t];for(let r=0,l=o.length;r<l;++r){const{index:c,data:h}=o[r],{lo:d,hi:u}=al(o[r],t,a,n);for(let f=d;f<=u;++f){const g=h[f];g.skip||s(g,c,f)}}}function rl(i){const t=i.indexOf("x")!==-1,e=i.indexOf("y")!==-1;return function(s,n){const o=t?Math.abs(s.x-n.x):0,a=e?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(a,2))}}function Si(i,t,e,s,n){const o=[];return!n&&!i.isPointInArea(t)||Te(i,e,t,function(r,l,c){!n&&!kt(r,i.chartArea,0)||r.inRange(t.x,t.y,s)&&o.push({element:r,datasetIndex:l,index:c})},!0),o}function ll(i,t,e,s){let n=[];function o(a,r,l){const{startAngle:c,endAngle:h}=a.getProps(["startAngle","endAngle"],s),{angle:d}=$n(a,{x:t.x,y:t.y});ke(d,c,h)&&n.push({element:a,datasetIndex:r,index:l})}return Te(i,e,t,o),n}function cl(i,t,e,s,n,o){let a=[];const r=rl(e);let l=Number.POSITIVE_INFINITY;function c(h,d,u){const f=h.inRange(t.x,t.y,n);if(s&&!f)return;const g=h.getCenterPoint(n);if(!(!!o||i.isPointInArea(g))&&!f)return;const p=r(t,g);p<l?(a=[{element:h,datasetIndex:d,index:u}],l=p):p===l&&a.push({element:h,datasetIndex:d,index:u})}return Te(i,e,t,c),a}function Pi(i,t,e,s,n,o){return!o&&!i.isPointInArea(t)?[]:e==="r"&&!s?ll(i,t,e,n):cl(i,t,e,s,n,o)}function Ys(i,t,e,s,n){const o=[],a=e==="x"?"inXRange":"inYRange";let r=!1;return Te(i,e,t,(l,c,h)=>{l[a](t[e],n)&&(o.push({element:l,datasetIndex:c,index:h}),r=r||l.inRange(t.x,t.y,n))}),s&&!r?[]:o}var hl={evaluateInteractionItems:Te,modes:{index(i,t,e,s){const n=Vt(t,i),o=e.axis||"x",a=e.includeInvisible||!1,r=e.intersect?Si(i,n,o,s,a):Pi(i,n,o,!1,s,a),l=[];return r.length?(i.getSortedVisibleDatasetMetas().forEach(c=>{const h=r[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(i,t,e,s){const n=Vt(t,i),o=e.axis||"xy",a=e.includeInvisible||!1;let r=e.intersect?Si(i,n,o,s,a):Pi(i,n,o,!1,s,a);if(r.length>0){const l=r[0].datasetIndex,c=i.getDatasetMeta(l).data;r=[];for(let h=0;h<c.length;++h)r.push({element:c[h],datasetIndex:l,index:h})}return r},point(i,t,e,s){const n=Vt(t,i),o=e.axis||"xy",a=e.includeInvisible||!1;return Si(i,n,o,s,a)},nearest(i,t,e,s){const n=Vt(t,i),o=e.axis||"xy",a=e.includeInvisible||!1;return Pi(i,n,o,e.intersect,s,a)},x(i,t,e,s){const n=Vt(t,i);return Ys(i,n,"x",e.intersect,s)},y(i,t,e,s){const n=Vt(t,i);return Ys(i,n,"y",e.intersect,s)}}};const po=["left","top","right","bottom"];function ae(i,t){return i.filter(e=>e.pos===t)}function Us(i,t){return i.filter(e=>po.indexOf(e.pos)===-1&&e.box.axis===t)}function re(i,t){return i.sort((e,s)=>{const n=t?s:e,o=t?e:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function dl(i){const t=[];let e,s,n,o,a,r;for(e=0,s=(i||[]).length;e<s;++e)n=i[e],{position:o,options:{stack:a,stackWeight:r=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:a&&o+a,stackWeight:r});return t}function ul(i){const t={};for(const e of i){const{stack:s,pos:n,stackWeight:o}=e;if(!s||!po.includes(n))continue;const a=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});a.count++,a.weight+=o}return t}function fl(i,t){const e=ul(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=t;let o,a,r;for(o=0,a=i.length;o<a;++o){r=i[o];const{fullSize:l}=r.box,c=e[r.stack],h=c&&r.stackWeight/c.weight;r.horizontal?(r.width=h?h*s:l&&t.availableWidth,r.height=n):(r.width=s,r.height=h?h*n:l&&t.availableHeight)}return e}function gl(i){const t=dl(i),e=re(t.filter(c=>c.box.fullSize),!0),s=re(ae(t,"left"),!0),n=re(ae(t,"right")),o=re(ae(t,"top"),!0),a=re(ae(t,"bottom")),r=Us(t,"x"),l=Us(t,"y");return{fullSize:e,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(a).concat(r),chartArea:ae(t,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(a).concat(r)}}function Xs(i,t,e,s){return Math.max(i[e],t[e])+Math.max(i[s],t[s])}function mo(i,t){i.top=Math.max(i.top,t.top),i.left=Math.max(i.left,t.left),i.bottom=Math.max(i.bottom,t.bottom),i.right=Math.max(i.right,t.right)}function pl(i,t,e,s){const{pos:n,box:o}=e,a=i.maxPadding;if(!T(n)){e.size&&(i[n]-=e.size);const d=s[e.stack]||{size:0,count:1};d.size=Math.max(d.size,e.horizontal?o.height:o.width),e.size=d.size/d.count,i[n]+=e.size}o.getPadding&&mo(a,o.getPadding());const r=Math.max(0,t.outerWidth-Xs(a,i,"left","right")),l=Math.max(0,t.outerHeight-Xs(a,i,"top","bottom")),c=r!==i.w,h=l!==i.h;return i.w=r,i.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function ml(i){const t=i.maxPadding;function e(s){const n=Math.max(t[s]-i[s],0);return i[s]+=n,n}i.y+=e("top"),i.x+=e("left"),e("right"),e("bottom")}function bl(i,t){const e=t.maxPadding;function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(a=>{o[a]=Math.max(t[a],e[a])}),o}return s(i?["left","right"]:["top","bottom"])}function ue(i,t,e,s){const n=[];let o,a,r,l,c,h;for(o=0,a=i.length,c=0;o<a;++o){r=i[o],l=r.box,l.update(r.width||t.w,r.height||t.h,bl(r.horizontal,t));const{same:d,other:u}=pl(t,e,r,s);c|=d&&n.length,h=h||u,l.fullSize||n.push(r)}return c&&ue(n,t,e,s)||h}function Ne(i,t,e,s,n){i.top=e,i.left=t,i.right=t+s,i.bottom=e+n,i.width=s,i.height=n}function Ks(i,t,e,s){const n=e.padding;let{x:o,y:a}=t;for(const r of i){const l=r.box,c=s[r.stack]||{count:1,placed:0,weight:1},h=r.stackWeight/c.weight||1;if(r.horizontal){const d=t.w*h,u=c.size||l.height;Me(c.start)&&(a=c.start),l.fullSize?Ne(l,n.left,a,e.outerWidth-n.right-n.left,u):Ne(l,t.left+c.placed,a,d,u),c.start=a,c.placed+=d,a=l.bottom}else{const d=t.h*h,u=c.size||l.width;Me(c.start)&&(o=c.start),l.fullSize?Ne(l,o,n.top,u,e.outerHeight-n.bottom-n.top):Ne(l,o,t.top+c.placed,u,d),c.start=o,c.placed+=d,o=l.right}}t.x=o,t.y=a}var tt={addBox(i,t){i.boxes||(i.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},i.boxes.push(t)},removeBox(i,t){const e=i.boxes?i.boxes.indexOf(t):-1;e!==-1&&i.boxes.splice(e,1)},configure(i,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(i,t,e,s){if(!i)return;const n=et(i.options.layout.padding),o=Math.max(t-n.width,0),a=Math.max(e-n.height,0),r=gl(i.boxes),l=r.vertical,c=r.horizontal;E(i.boxes,m=>{typeof m.beforeLayout=="function"&&m.beforeLayout()});const h=l.reduce((m,p)=>p.box.options&&p.box.options.display===!1?m:m+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:a,vBoxMaxWidth:o/2/h,hBoxMaxHeight:a/2}),u=Object.assign({},n);mo(u,et(s));const f=Object.assign({maxPadding:u,w:o,h:a,x:n.left,y:n.top},n),g=fl(l.concat(c),d);ue(r.fullSize,f,d,g),ue(l,f,d,g),ue(c,f,d,g)&&ue(l,f,d,g),ml(f),Ks(r.leftAndTop,f,d,g),f.x+=f.w,f.y+=f.h,Ks(r.rightAndBottom,f,d,g),i.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},E(r.chartArea,m=>{const p=m.box;Object.assign(p,i.chartArea),p.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class bo{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,n){return e=Math.max(0,e||t.width),s=s||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):s)}}isAttached(t){return!0}updateConfig(t){}}class xl extends bo{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const ti="$chartjs",_l={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},qs=i=>i===null||i==="";function yl(i,t){const e=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[ti]={initial:{height:s,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",qs(n)){const o=Ts(i,"width");o!==void 0&&(i.width=o)}if(qs(s))if(i.style.height==="")i.height=i.width/(t||2);else{const o=Ts(i,"height");o!==void 0&&(i.height=o)}return i}const xo=wr?{passive:!0}:!1;function vl(i,t,e){i&&i.addEventListener(t,e,xo)}function wl(i,t,e){i&&i.canvas&&i.canvas.removeEventListener(t,e,xo)}function Ml(i,t){const e=_l[i.type]||i.type,{x:s,y:n}=Vt(i,t);return{type:e,chart:t,native:i,x:s!==void 0?s:null,y:n!==void 0?n:null}}function ci(i,t){for(const e of i)if(e===t||e.contains(t))return!0}function kl(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let a=!1;for(const r of o)a=a||ci(r.addedNodes,s),a=a&&!ci(r.removedNodes,s);a&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function Sl(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let a=!1;for(const r of o)a=a||ci(r.removedNodes,s),a=a&&!ci(r.addedNodes,s);a&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}const Pe=new Map;let Gs=0;function _o(){const i=window.devicePixelRatio;i!==Gs&&(Gs=i,Pe.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function Pl(i,t){Pe.size||window.addEventListener("resize",_o),Pe.set(i,t)}function Dl(i){Pe.delete(i),Pe.size||window.removeEventListener("resize",_o)}function Cl(i,t,e){const s=i.canvas,n=s&&ls(s);if(!n)return;const o=Kn((r,l)=>{const c=n.clientWidth;e(r,l),c<n.clientWidth&&e()},window),a=new ResizeObserver(r=>{const l=r[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return a.observe(n),Pl(i,o),a}function Di(i,t,e){e&&e.disconnect(),t==="resize"&&Dl(i)}function Ol(i,t,e){const s=i.canvas,n=Kn(o=>{i.ctx!==null&&e(Ml(o,i))},i);return vl(s,t,n),n}class Al extends bo{acquireContext(t,e){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(yl(t,e),s):null}releaseContext(t){const e=t.canvas;if(!e[ti])return!1;const s=e[ti].initial;["height","width"].forEach(o=>{const a=s[o];R(a)?e.removeAttribute(o):e.setAttribute(o,a)});const n=s.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[ti],!0}addEventListener(t,e,s){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),a={attach:kl,detach:Sl,resize:Cl}[e]||Ol;n[e]=a(t,e,s)}removeEventListener(t,e){const s=t.$proxies||(t.$proxies={}),n=s[e];if(!n)return;({attach:Di,detach:Di,resize:Di}[e]||wl)(t,e,n),s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,n){return vr(t,e,s,n)}isAttached(t){const e=t&&ls(t);return!!(e&&e.isConnected)}}function Tl(i){return!rs()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?xl:Al}class ut{constructor(){M(this,"x");M(this,"y");M(this,"active",!1);M(this,"options");M(this,"$animations")}tooltipPosition(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return Jt(this.x)&&Jt(this.y)}getProps(t,e){const s=this.$animations;if(!e||!s)return this;const n={};return t.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}M(ut,"defaults",{}),M(ut,"defaultRoutes");function Ll(i,t){const e=i.options.ticks,s=Rl(i),n=Math.min(e.maxTicksLimit||s,s),o=e.major.enabled?El(t):[],a=o.length,r=o[0],l=o[a-1],c=[];if(a>n)return Fl(t,c,o,a/n),c;const h=Il(o,t,n);if(a>0){let d,u;const f=a>1?Math.round((l-r)/(a-1)):null;for(We(t,c,h,R(f)?0:r-f,r),d=0,u=a-1;d<u;d++)We(t,c,h,o[d],o[d+1]);return We(t,c,h,l,R(f)?t.length:l+f),c}return We(t,c,h),c}function Rl(i){const t=i.options.offset,e=i._tickSize(),s=i._length/e+(t?0:1),n=i._maxLength/e;return Math.floor(Math.min(s,n))}function Il(i,t,e){const s=zl(i),n=t.length/e;if(!s)return Math.max(n,1);const o=Sa(s);for(let a=0,r=o.length-1;a<r;a++){const l=o[a];if(l>n)return l}return Math.max(n,1)}function El(i){const t=[];let e,s;for(e=0,s=i.length;e<s;e++)i[e].major&&t.push(e);return t}function Fl(i,t,e,s){let n=0,o=e[0],a;for(s=Math.ceil(s),a=0;a<i.length;a++)a===o&&(t.push(i[a]),n++,o=e[n*s])}function We(i,t,e,s,n){const o=C(s,0),a=Math.min(C(n,i.length),i.length);let r=0,l,c,h;for(e=Math.ceil(e),n&&(l=n-s,e=l/Math.floor(l/e)),h=o;h<0;)r++,h=Math.round(o+r*e);for(c=Math.max(o,0);c<a;c++)c===h&&(t.push(i[c]),r++,h=Math.round(o+r*e))}function zl(i){const t=i.length;let e,s;if(t<2)return!1;for(s=i[0],e=1;e<t;++e)if(i[e]-i[e-1]!==s)return!1;return s}const Bl=i=>i==="left"?"right":i==="right"?"left":i,Zs=(i,t,e)=>t==="top"||t==="left"?i[t]+e:i[t]-e,Js=(i,t)=>Math.min(t||i,i);function Qs(i,t){const e=[],s=i.length/t,n=i.length;let o=0;for(;o<n;o+=s)e.push(i[Math.floor(o)]);return e}function Vl(i,t,e){const s=i.ticks.length,n=Math.min(t,s-1),o=i._startPixel,a=i._endPixel,r=1e-6;let l=i.getPixelForTick(n),c;if(!(e&&(s===1?c=Math.max(l-o,a-l):t===0?c=(i.getPixelForTick(1)-l)/2:c=(l-i.getPixelForTick(n-1))/2,l+=n<t?c:-c,l<o-r||l>a+r)))return l}function Hl(i,t){E(i,e=>{const s=e.gc,n=s.length/2;let o;if(n>t){for(o=0;o<n;++o)delete e.data[s[o]];s.splice(0,n)}})}function le(i){return i.drawTicks?i.tickLength:0}function tn(i,t){if(!i.display)return 0;const e=q(i.font,t),s=et(i.padding);return(V(i.text)?i.text.length:1)*e.lineHeight+s.height}function Nl(i,t){return Lt(i,{scale:t,type:"scale"})}function Wl(i,t,e){return Lt(i,{tick:e,index:t,type:"tick"})}function jl(i,t,e){let s=es(i);return(e&&t!=="right"||!e&&t==="right")&&(s=Bl(s)),s}function $l(i,t,e,s){const{top:n,left:o,bottom:a,right:r,chart:l}=i,{chartArea:c,scales:h}=l;let d=0,u,f,g;const m=a-n,p=r-o;if(i.isHorizontal()){if(f=Q(s,o,r),T(e)){const b=Object.keys(e)[0],x=e[b];g=h[b].getPixelForValue(x)+m-t}else e==="center"?g=(c.bottom+c.top)/2+m-t:g=Zs(i,e,t);u=r-o}else{if(T(e)){const b=Object.keys(e)[0],x=e[b];f=h[b].getPixelForValue(x)-p+t}else e==="center"?f=(c.left+c.right)/2-p+t:f=Zs(i,e,t);g=Q(s,a,n),d=e==="left"?-K:K}return{titleX:f,titleY:g,maxWidth:u,rotation:d}}class Ut extends ut{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:n}=this;return t=ot(t,Number.POSITIVE_INFINITY),e=ot(e,Number.NEGATIVE_INFINITY),s=ot(s,Number.POSITIVE_INFINITY),n=ot(n,Number.NEGATIVE_INFINITY),{min:ot(t,s),max:ot(e,n),minDefined:Y(t),maxDefined:Y(e)}}getMinMax(t){let{min:e,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),a;if(n&&o)return{min:e,max:s};const r=this.getMatchingVisibleMetas();for(let l=0,c=r.length;l<c;++l)a=r[l].controller.getMinMax(this,t),n||(e=Math.min(e,a.min)),o||(s=Math.max(s,a.max));return e=o&&e>s?s:e,s=n&&e>s?e:s,{min:ot(e,ot(s,e)),max:ot(s,ot(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){B(this.options.beforeUpdate,[this])}update(t,e,s){const{beginAtZero:n,grace:o,ticks:a}=this.options,r=a.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Qa(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=r<this.ticks.length;this._convertTicksToLabels(l?Qs(this.ticks,r):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),a.display&&(a.autoSkip||a.source==="auto")&&(this.ticks=Ll(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,s;this.isHorizontal()?(e=this.left,s=this.right):(e=this.top,s=this.bottom,t=!t),this._startPixel=e,this._endPixel=s,this._reversePixels=t,this._length=s-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){B(this.options.afterUpdate,[this])}beforeSetDimensions(){B(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){B(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),B(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){B(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s],o.label=B(e.callback,[o.value,s,t],this)}afterTickToLabelConversion(){B(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){B(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,s=Js(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,o=e.maxRotation;let a=n,r,l,c;if(!this._isVisible()||!e.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}const h=this._getLabelSizes(),d=h.widest.width,u=h.highest.height,f=G(this.chart.width-d,0,this.maxWidth);r=t.offset?this.maxWidth/s:f/(s-1),d+6>r&&(r=f/(s-(t.offset?.5:1)),l=this.maxHeight-le(t.grid)-e.padding-tn(t.title,this.chart.options.font),c=Math.sqrt(d*d+u*u),a=Qi(Math.min(Math.asin(G((h.highest.height+6)/r,-1,1)),Math.asin(G(l/c,-1,1))-Math.asin(G(u/c,-1,1)))),a=Math.max(n,Math.min(o,a))),this.labelRotation=a}afterCalculateLabelRotation(){B(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){B(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:s,title:n,grid:o}}=this,a=this._isVisible(),r=this.isHorizontal();if(a){const l=tn(n,e.options.font);if(r?(t.width=this.maxWidth,t.height=le(o)+l):(t.height=this.maxHeight,t.width=le(o)+l),s.display&&this.ticks.length){const{first:c,last:h,widest:d,highest:u}=this._getLabelSizes(),f=s.padding*2,g=ht(this.labelRotation),m=Math.cos(g),p=Math.sin(g);if(r){const b=s.mirror?0:p*d.width+m*u.height;t.height=Math.min(this.maxHeight,t.height+b+f)}else{const b=s.mirror?0:m*d.width+p*u.height;t.width=Math.min(this.maxWidth,t.width+b+f)}this._calculatePadding(c,h,p,m)}}this._handleMargins(),r?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,s,n){const{ticks:{align:o,padding:a},position:r}=this.options,l=this.labelRotation!==0,c=r!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let u=0,f=0;l?c?(u=n*t.width,f=s*e.height):(u=s*t.height,f=n*e.width):o==="start"?f=e.width:o==="end"?u=t.width:o!=="inner"&&(u=t.width/2,f=e.width/2),this.paddingLeft=Math.max((u-h+a)*this.width/(this.width-h),0),this.paddingRight=Math.max((f-d+a)*this.width/(this.width-d),0)}else{let h=e.height/2,d=t.height/2;o==="start"?(h=0,d=t.height):o==="end"&&(h=e.height,d=0),this.paddingTop=h+a,this.paddingBottom=d+a}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){B(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)R(t[e].label)&&(t.splice(e,1),s--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let s=this.ticks;e<s.length&&(s=Qs(s,e)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,s){const{ctx:n,_longestTextCache:o}=this,a=[],r=[],l=Math.floor(e/Js(e,s));let c=0,h=0,d,u,f,g,m,p,b,x,v,w,y;for(d=0;d<e;d+=l){if(g=t[d].label,m=this._resolveTickFontOptions(d),n.font=p=m.string,b=o[p]=o[p]||{data:{},gc:[]},x=m.lineHeight,v=w=0,!R(g)&&!V(g))v=ri(n,b.data,b.gc,v,g),w=x;else if(V(g))for(u=0,f=g.length;u<f;++u)y=g[u],!R(y)&&!V(y)&&(v=ri(n,b.data,b.gc,v,y),w+=x);a.push(v),r.push(w),c=Math.max(v,c),h=Math.max(w,h)}Hl(o,e);const k=a.indexOf(c),S=r.indexOf(h),P=D=>({width:a[D]||0,height:r[D]||0});return{first:P(0),last:P(e-1),widest:P(k),highest:P(S),widths:a,heights:r}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return Ca(this._alignToPixels?Ft(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const s=e[t];return s.$context||(s.$context=Wl(this.getContext(),t,s))}return this.$context||(this.$context=Nl(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=ht(this.labelRotation),s=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),a=t.autoSkipPadding||0,r=o?o.widest.width+a:0,l=o?o.highest.height+a:0;return this.isHorizontal()?l*s>r*n?r/s:l/n:l*n<r*s?l/s:r/n}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,s=this.chart,n=this.options,{grid:o,position:a,border:r}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),u=le(o),f=[],g=r.setContext(this.getContext()),m=g.display?g.width:0,p=m/2,b=function(W){return Ft(s,W,m)};let x,v,w,y,k,S,P,D,O,A,L,Z;if(a==="top")x=b(this.bottom),S=this.bottom-u,D=x-p,A=b(t.top)+p,Z=t.bottom;else if(a==="bottom")x=b(this.top),A=t.top,Z=b(t.bottom)-p,S=x+p,D=this.top+u;else if(a==="left")x=b(this.right),k=this.right-u,P=x-p,O=b(t.left)+p,L=t.right;else if(a==="right")x=b(this.left),O=t.left,L=b(t.right)-p,k=x+p,P=this.left+u;else if(e==="x"){if(a==="center")x=b((t.top+t.bottom)/2+.5);else if(T(a)){const W=Object.keys(a)[0],X=a[W];x=b(this.chart.scales[W].getPixelForValue(X))}A=t.top,Z=t.bottom,S=x+p,D=S+u}else if(e==="y"){if(a==="center")x=b((t.left+t.right)/2);else if(T(a)){const W=Object.keys(a)[0],X=a[W];x=b(this.chart.scales[W].getPixelForValue(X))}k=x-p,P=k-u,O=t.left,L=t.right}const nt=C(n.ticks.maxTicksLimit,d),F=Math.max(1,Math.ceil(d/nt));for(v=0;v<d;v+=F){const W=this.getContext(v),X=o.setContext(W),ct=r.setContext(W),J=X.lineWidth,Xt=X.color,Le=ct.dash||[],Kt=ct.dashOffset,ee=X.tickWidth,Rt=X.tickColor,ie=X.tickBorderDash||[],It=X.tickBorderDashOffset;w=Vl(this,v,l),w!==void 0&&(y=Ft(s,w,J),c?k=P=O=L=y:S=D=A=Z=y,f.push({tx1:k,ty1:S,tx2:P,ty2:D,x1:O,y1:A,x2:L,y2:Z,width:J,color:Xt,borderDash:Le,borderDashOffset:Kt,tickWidth:ee,tickColor:Rt,tickBorderDash:ie,tickBorderDashOffset:It}))}return this._ticksLength=d,this._borderValue=x,f}_computeLabelItems(t){const e=this.axis,s=this.options,{position:n,ticks:o}=s,a=this.isHorizontal(),r=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,u=le(s.grid),f=u+h,g=d?-h:f,m=-ht(this.labelRotation),p=[];let b,x,v,w,y,k,S,P,D,O,A,L,Z="middle";if(n==="top")k=this.bottom-g,S=this._getXAxisLabelAlignment();else if(n==="bottom")k=this.top+g,S=this._getXAxisLabelAlignment();else if(n==="left"){const F=this._getYAxisLabelAlignment(u);S=F.textAlign,y=F.x}else if(n==="right"){const F=this._getYAxisLabelAlignment(u);S=F.textAlign,y=F.x}else if(e==="x"){if(n==="center")k=(t.top+t.bottom)/2+f;else if(T(n)){const F=Object.keys(n)[0],W=n[F];k=this.chart.scales[F].getPixelForValue(W)+f}S=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")y=(t.left+t.right)/2-f;else if(T(n)){const F=Object.keys(n)[0],W=n[F];y=this.chart.scales[F].getPixelForValue(W)}S=this._getYAxisLabelAlignment(u).textAlign}e==="y"&&(l==="start"?Z="top":l==="end"&&(Z="bottom"));const nt=this._getLabelSizes();for(b=0,x=r.length;b<x;++b){v=r[b],w=v.label;const F=o.setContext(this.getContext(b));P=this.getPixelForTick(b)+o.labelOffset,D=this._resolveTickFontOptions(b),O=D.lineHeight,A=V(w)?w.length:1;const W=A/2,X=F.color,ct=F.textStrokeColor,J=F.textStrokeWidth;let Xt=S;a?(y=P,S==="inner"&&(b===x-1?Xt=this.options.reverse?"left":"right":b===0?Xt=this.options.reverse?"right":"left":Xt="center"),n==="top"?c==="near"||m!==0?L=-A*O+O/2:c==="center"?L=-nt.highest.height/2-W*O+O:L=-nt.highest.height+O/2:c==="near"||m!==0?L=O/2:c==="center"?L=nt.highest.height/2-W*O:L=nt.highest.height-A*O,d&&(L*=-1),m!==0&&!F.showLabelBackdrop&&(y+=O/2*Math.sin(m))):(k=P,L=(1-A)*O/2);let Le;if(F.showLabelBackdrop){const Kt=et(F.backdropPadding),ee=nt.heights[b],Rt=nt.widths[b];let ie=L-Kt.top,It=0-Kt.left;switch(Z){case"middle":ie-=ee/2;break;case"bottom":ie-=ee;break}switch(S){case"center":It-=Rt/2;break;case"right":It-=Rt;break;case"inner":b===x-1?It-=Rt:b>0&&(It-=Rt/2);break}Le={left:It,top:ie,width:Rt+Kt.width,height:ee+Kt.height,color:F.backdropColor}}p.push({label:w,font:D,textOffset:L,options:{rotation:m,color:X,strokeColor:ct,strokeWidth:J,textAlign:Xt,textBaseline:Z,translation:[y,k],backdrop:Le}})}return p}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-ht(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,a=this._getLabelSizes(),r=t+o,l=a.widest.width;let c,h;return e==="left"?n?(h=this.right+o,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-r,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?n?(h=this.left+o,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+r,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:s,top:n,width:o,height:a}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(s,n,o,a),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){const e=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,a;const r=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(e.display)for(o=0,a=n.length;o<a;++o){const l=n[o];e.drawOnChartArea&&r({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&r({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),a=s.display?o.width:0;if(!a)return;const r=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,d,u;this.isHorizontal()?(c=Ft(t,this.left,a)-a/2,h=Ft(t,this.right,r)+r/2,d=u=l):(d=Ft(t,this.top,a)-a/2,u=Ft(t,this.bottom,r)+r/2,c=h=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(c,d),e.lineTo(h,u),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&gi(s,n);const o=this.getLabelItems(t);for(const a of o){const r=a.options,l=a.font,c=a.label,h=a.textOffset;Yt(s,c,0,h,l,r)}n&&pi(s)}drawTitle(){const{ctx:t,options:{position:e,title:s,reverse:n}}=this;if(!s.display)return;const o=q(s.font),a=et(s.padding),r=s.align;let l=o.lineHeight/2;e==="bottom"||e==="center"||T(e)?(l+=a.bottom,V(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=a.top;const{titleX:c,titleY:h,maxWidth:d,rotation:u}=$l(this,l,e,r);Yt(t,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:u,textAlign:jl(r,e,n),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,s=C(t.grid&&t.grid.z,-1),n=C(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Ut.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,a;for(o=0,a=e.length;o<a;++o){const r=e[o];r[s]===this.id&&(!t||r.type===t)&&n.push(r)}return n}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return q(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class je{constructor(t,e,s){this.type=t,this.scope=e,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let s;Xl(e)&&(s=this.register(e));const n=this.items,o=t.id,a=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,Yl(t,a,s),this.override&&U.override(t.id,t.overrides)),a}get(t){return this.items[t]}unregister(t){const e=this.items,s=t.id,n=this.scope;s in e&&delete e[s],n&&s in U[n]&&(delete U[n][s],this.override&&delete $t[s])}}function Yl(i,t,e){const s=we(Object.create(null),[e?U.get(e):{},U.get(t),i.defaults]);U.set(t,s),i.defaultRoutes&&Ul(t,i.defaultRoutes),i.descriptors&&U.describe(t,i.descriptors)}function Ul(i,t){Object.keys(t).forEach(e=>{const s=e.split("."),n=s.pop(),o=[i].concat(s).join("."),a=t[e].split("."),r=a.pop(),l=a.join(".");U.route(o,n,l,r)})}function Xl(i){return"id"in i&&"defaults"in i}class Kl{constructor(){this.controllers=new je(dt,"datasets",!0),this.elements=new je(ut,"elements"),this.plugins=new je(Object,"plugins"),this.scales=new je(Ut,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):E(n,a=>{const r=s||this._getRegistryForType(a);this._exec(t,r,a)})})}_exec(t,e,s){const n=Ji(t);B(s["before"+n],[],s),e[t](s),B(s["after"+n],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){const n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return n}}var pt=new Kl;class ql{constructor(){this._init=[]}notify(t,e,s,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=n?this._descriptors(t).filter(n):this._descriptors(t),a=this._notify(o,t,e,s);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),a}_notify(t,e,s,n){n=n||{};for(const o of t){const a=o.plugin,r=a[s],l=[e,n,o.options];if(B(r,l,a)===!1&&n.cancelable)return!1}return!0}invalidate(){R(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const s=t&&t.config,n=C(s.options&&s.options.plugins,{}),o=Gl(s);return n===!1&&!e?[]:Jl(t,o,n,e)}_notifyStateChanges(t){const e=this._oldCache||[],s=this._cache,n=(o,a)=>o.filter(r=>!a.some(l=>r.plugin.id===l.plugin.id));this._notify(n(e,s),t,"stop"),this._notify(n(s,e),t,"start")}}function Gl(i){const t={},e=[],s=Object.keys(pt.plugins.items);for(let o=0;o<s.length;o++)e.push(pt.getPlugin(s[o]));const n=i.plugins||[];for(let o=0;o<n.length;o++){const a=n[o];e.indexOf(a)===-1&&(e.push(a),t[a.id]=!0)}return{plugins:e,localIds:t}}function Zl(i,t){return!t&&i===!1?null:i===!0?{}:i}function Jl(i,{plugins:t,localIds:e},s,n){const o=[],a=i.getContext();for(const r of t){const l=r.id,c=Zl(s[l],n);c!==null&&o.push({plugin:r,options:Ql(i.config,{plugin:r,local:e[l]},c,a)})}return o}function Ql(i,{plugin:t,local:e},s,n){const o=i.pluginScopeKeys(t),a=i.getOptionScopes(s,o);return e&&t.defaults&&a.push(t.defaults),i.createResolver(a,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Fi(i,t){const e=U.datasets[i]||{};return((t.datasets||{})[i]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function tc(i,t){let e=i;return i==="_index_"?e=t:i==="_value_"&&(e=t==="x"?"y":"x"),e}function ec(i,t){return i===t?"_index_":"_value_"}function en(i){if(i==="x"||i==="y"||i==="r")return i}function ic(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function zi(i,...t){if(en(i))return i;for(const e of t){const s=e.axis||ic(e.position)||i.length>1&&en(i[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function sn(i,t,e){if(e[t+"AxisID"]===i)return{axis:t}}function sc(i,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(s=>s.xAxisID===i||s.yAxisID===i);if(e.length)return sn(i,"x",e[0])||sn(i,"y",e[0])}return{}}function nc(i,t){const e=$t[i.type]||{scales:{}},s=t.scales||{},n=Fi(i.type,t),o=Object.create(null);return Object.keys(s).forEach(a=>{const r=s[a];if(!T(r))return console.error(`Invalid scale configuration for scale: ${a}`);if(r._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${a}`);const l=zi(a,r,sc(a,i),U.scales[r.type]),c=ec(l,n),h=e.scales||{};o[a]=me(Object.create(null),[{axis:l},r,h[l],h[c]])}),i.data.datasets.forEach(a=>{const r=a.type||i.type,l=a.indexAxis||Fi(r,t),h=($t[r]||{}).scales||{};Object.keys(h).forEach(d=>{const u=tc(d,l),f=a[u+"AxisID"]||u;o[f]=o[f]||Object.create(null),me(o[f],[{axis:u},s[f],h[d]])})}),Object.keys(o).forEach(a=>{const r=o[a];me(r,[U.scales[r.type],U.scale])}),o}function yo(i){const t=i.options||(i.options={});t.plugins=C(t.plugins,{}),t.scales=nc(i,t)}function vo(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function oc(i){return i=i||{},i.data=vo(i.data),yo(i),i}const nn=new Map,wo=new Set;function $e(i,t){let e=nn.get(i);return e||(e=t(),nn.set(i,e),wo.add(e)),e}const ce=(i,t,e)=>{const s=At(t,e);s!==void 0&&i.add(s)};class ac{constructor(t){this._config=oc(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=vo(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),yo(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return $e(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return $e(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return $e(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,s=this.type;return $e(`${s}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const s=this._scopeCache;let n=s.get(t);return(!n||e)&&(n=new Map,s.set(t,n)),n}getOptionScopes(t,e,s){const{options:n,type:o}=this,a=this._cachedScopes(t,s),r=a.get(e);if(r)return r;const l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(d=>ce(l,t,d))),h.forEach(d=>ce(l,n,d)),h.forEach(d=>ce(l,$t[o]||{},d)),h.forEach(d=>ce(l,U,d)),h.forEach(d=>ce(l,Ri,d))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),wo.has(e)&&a.set(e,c),c}chartOptionScopes(){const{options:t,type:e}=this;return[t,$t[e]||{},U.datasets[e]||{},{type:e},U,Ri]}resolveNamedOptions(t,e,s,n=[""]){const o={$shared:!0},{resolver:a,subPrefixes:r}=on(this._resolverCache,t,n);let l=a;if(lc(a,e)){o.$shared=!1,s=Tt(s)?s():s;const c=this.createResolver(t,s,r);l=Qt(a,s,c)}for(const c of e)o[c]=l[c];return o}createResolver(t,e,s=[""],n){const{resolver:o}=on(this._resolverCache,t,s);return T(e)?Qt(o,e,void 0,n):o}}function on(i,t,e){let s=i.get(t);s||(s=new Map,i.set(t,s));const n=e.join();let o=s.get(n);return o||(o={resolver:ns(t,e),subPrefixes:e.filter(r=>!r.toLowerCase().includes("hover"))},s.set(n,o)),o}const rc=i=>T(i)&&Object.getOwnPropertyNames(i).some(t=>Tt(i[t]));function lc(i,t){const{isScriptable:e,isIndexable:s}=to(i);for(const n of t){const o=e(n),a=s(n),r=(a||o)&&i[n];if(o&&(Tt(r)||rc(r))||a&&V(r))return!0}return!1}var cc="4.4.3";const hc=["top","bottom","left","right","chartArea"];function an(i,t){return i==="top"||i==="bottom"||hc.indexOf(i)===-1&&t==="x"}function rn(i,t){return function(e,s){return e[i]===s[i]?e[t]-s[t]:e[i]-s[i]}}function ln(i){const t=i.chart,e=t.options.animation;t.notifyPlugins("afterRender"),B(e&&e.onComplete,[i],t)}function dc(i){const t=i.chart,e=t.options.animation;B(e&&e.onProgress,[i],t)}function Mo(i){return rs()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const ei={},cn=i=>{const t=Mo(i);return Object.values(ei).filter(e=>e.canvas===t).pop()};function uc(i,t,e){const s=Object.keys(i);for(const n of s){const o=+n;if(o>=t){const a=i[n];delete i[n],(e>0||o>t)&&(i[o+e]=a)}}}function fc(i,t,e,s){return!e||i.type==="mouseout"?null:s?t:i}function Ye(i,t,e){return i.options.clip?i[e]:t[e]}function gc(i,t){const{xScale:e,yScale:s}=i;return e&&s?{left:Ye(e,t,"left"),right:Ye(e,t,"right"),top:Ye(s,t,"top"),bottom:Ye(s,t,"bottom")}:t}class rt{static register(...t){pt.add(...t),hn()}static unregister(...t){pt.remove(...t),hn()}constructor(t,e){const s=this.config=new ac(e),n=Mo(t),o=cn(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const a=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||Tl(n)),this.platform.updateConfig(s);const r=this.platform.acquireContext(n,a.aspectRatio),l=r&&r.canvas,c=l&&l.height,h=l&&l.width;if(this.id=ma(),this.ctx=r,this.canvas=l,this.width=h,this.height=c,this._options=a,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new ql,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=La(d=>this.update(d),a.resizeDelay||0),this._dataChanges=[],ei[this.id]=this,!r||!l){console.error("Failed to create chart: can't acquire context from the given item");return}_t.listen(this,"complete",ln),_t.listen(this,"progress",dc),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:n,_aspectRatio:o}=this;return R(t)?e&&o?o:n?s/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return pt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():As(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Ds(this.canvas,this.ctx),this}stop(){return _t.stop(this),this}resize(t,e){_t.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const s=this.options,n=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,a=this.platform.getMaximumSize(n,t,e,o),r=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=a.width,this.height=a.height,this._aspectRatio=this.aspectRatio,As(this,r,!0)&&(this.notifyPlugins("resize",{size:a}),B(s.onResize,[this,a],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};E(e,(s,n)=>{s.id=n})}buildOrUpdateScales(){const t=this.options,e=t.scales,s=this.scales,n=Object.keys(s).reduce((a,r)=>(a[r]=!1,a),{});let o=[];e&&(o=o.concat(Object.keys(e).map(a=>{const r=e[a],l=zi(a,r),c=l==="r",h=l==="x";return{options:r,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),E(o,a=>{const r=a.options,l=r.id,c=zi(l,r),h=C(r.type,a.dtype);(r.position===void 0||an(r.position,c)!==an(a.dposition))&&(r.position=a.dposition),n[l]=!0;let d=null;if(l in s&&s[l].type===h)d=s[l];else{const u=pt.getScale(h);d=new u({id:l,type:h,ctx:this.ctx,chart:this}),s[d.id]=d}d.init(r,t)}),E(n,(a,r)=>{a||delete s[r]}),E(s,a=>{tt.configure(this,a,a.options),tt.addBox(this,a)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,s=t.length;if(t.sort((n,o)=>n.index-o.index),s>e){for(let n=e;n<s;++n)this._destroyDatasetMeta(n);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort(rn("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((s,n)=>{e.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let s,n;for(this._removeUnreferencedMetasets(),s=0,n=e.length;s<n;s++){const o=e[s];let a=this.getDatasetMeta(s);const r=o.type||this.config.type;if(a.type&&a.type!==r&&(this._destroyDatasetMeta(s),a=this.getDatasetMeta(s)),a.type=r,a.indexAxis=o.indexAxis||Fi(r,this.options),a.order=o.order||0,a.index=s,a.label=""+o.label,a.visible=this.isDatasetVisible(s),a.controller)a.controller.updateIndex(s),a.controller.linkScales();else{const l=pt.getController(r),{datasetElementType:c,dataElementType:h}=U.datasets[r];Object.assign(l,{dataElementType:pt.getElement(h),datasetElementType:c&&pt.getElement(c)}),a.controller=new l(this,s),t.push(a.controller)}}return this._updateMetasets(),t}_resetElements(){E(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let a=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:d}=this.getDatasetMeta(c),u=!n&&o.indexOf(d)===-1;d.buildOrUpdateElements(u),a=Math.max(+d.getMaxOverflow(),a)}a=this._minPadding=s.layout.autoPadding?a:0,this._updateLayout(a),n||E(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(rn("z","_idx"));const{_active:r,_lastEvent:l}=this;l?this._eventHandler(l,!0):r.length&&this._updateHoverStyles(r,r,!0),this.render()}_updateScales(){E(this.scales,t=>{tt.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!xs(e,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:s,start:n,count:o}of e){const a=s==="_removeElements"?-o:o;uc(t,n,a)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,s=o=>new Set(t.filter(a=>a[0]===o).map((a,r)=>r+","+a.splice(1).join(","))),n=s(0);for(let o=1;o<e;o++)if(!xs(n,s(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;tt.update(this,this.width,this.height,t);const e=this.chartArea,s=e.width<=0||e.height<=0;this._layers=[],E(this.boxes,n=>{s&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,s=this.data.datasets.length;e<s;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,Tt(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const s=this.getDatasetMeta(t),n={meta:s,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(s.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(_t.has(this)?this.attached&&!_t.running(this)&&_t.start(this):(this.draw(),ln({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:n}=this._resizeBeforeDraw;this._resize(s,n),this._resizeBeforeDraw=null}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,s=[];let n,o;for(n=0,o=e.length;n<o;++n){const a=e[n];(!t||a.visible)&&s.push(a)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,s=t._clip,n=!s.disabled,o=gc(t,this.chartArea),a={meta:t,index:t.index,cancelable:!0};this.notifyPlugins("beforeDatasetDraw",a)!==!1&&(n&&gi(e,{left:s.left===!1?0:o.left-s.left,right:s.right===!1?this.width:o.right+s.right,top:s.top===!1?0:o.top-s.top,bottom:s.bottom===!1?this.height:o.bottom+s.bottom}),t.controller.draw(),n&&pi(e),a.cancelable=!1,this.notifyPlugins("afterDatasetDraw",a))}isPointInArea(t){return kt(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,n){const o=hl.modes[e];return typeof o=="function"?o(this,t,s,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],s=this._metasets;let n=s.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},s.push(n)),n}getContext(){return this.$context||(this.$context=Lt(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){const s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){const n=s?"show":"hide",o=this.getDatasetMeta(t),a=o.controller._resolveAnimations(void 0,n);Me(e)?(o.data[e].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),a.update(o,{visible:s}),this.update(r=>r.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),_t.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Ds(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete ei[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,s=(o,a)=>{e.addEventListener(this,o,a),t[o]=a},n=(o,a,r)=>{o.offsetX=a,o.offsetY=r,this._eventHandler(o)};E(this.options.events,o=>s(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,s=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},n=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let a;const r=()=>{n("attach",r),this.attached=!0,this.resize(),s("resize",o),s("detach",a)};a=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),s("attach",r)},e.isAttached(this.canvas)?r():a()}unbindEvents(){E(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},E(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,s){const n=s?"set":"remove";let o,a,r,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),r=0,l=t.length;r<l;++r){a=t[r];const c=a&&this.getDatasetMeta(a.datasetIndex).controller;c&&c[n+"HoverStyle"](a.element,a.datasetIndex,a.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],s=t.map(({datasetIndex:o,index:a})=>{const r=this.getDatasetMeta(o);if(!r)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:r.data[a],index:a}});!ni(s,e)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,e))}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,s){const n=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(d=>h.datasetIndex===d.datasetIndex&&h.index===d.index)),a=o(e,t),r=s?t:o(t,e);a.length&&this.updateHoverStyle(a,n.mode,!1),r.length&&n.mode&&this.updateHoverStyle(r,n.mode,!0)}_eventHandler(t,e){const s={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=a=>(a.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,n)===!1)return;const o=this._handleEvent(t,e,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,n),(o||s.changed)&&this.render(),this}_handleEvent(t,e,s){const{_active:n=[],options:o}=this,a=e,r=this._getActiveElements(t,n,s,a),l=wa(t),c=fc(t,this._lastEvent,s,l);s&&(this._lastEvent=null,B(o.onHover,[t,r,this],this),l&&B(o.onClick,[t,r,this],this));const h=!ni(r,n);return(h||e)&&(this._active=r,this._updateHoverStyles(r,n,e)),this._lastEvent=c,h}_getActiveElements(t,e,s,n){if(t.type==="mouseout")return[];if(!s)return e;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}}M(rt,"defaults",U),M(rt,"instances",ei),M(rt,"overrides",$t),M(rt,"registry",pt),M(rt,"version",cc),M(rt,"getChart",cn);function hn(){return E(rt.instances,i=>i._plugins.invalidate())}function pc(i,t,e){const{startAngle:s,pixelMargin:n,x:o,y:a,outerRadius:r,innerRadius:l}=t;let c=n/r;i.beginPath(),i.arc(o,a,r,s-c,e+c),l>n?(c=n/l,i.arc(o,a,l,e+c,s-c,!0)):i.arc(o,a,n,e+K,s-K),i.closePath(),i.clip()}function mc(i){return ss(i,["outerStart","outerEnd","innerStart","innerEnd"])}function bc(i,t,e,s){const n=mc(i.options.borderRadius),o=(e-t)/2,a=Math.min(o,s*t/2),r=l=>{const c=(e-Math.min(o,l))*s/2;return G(l,0,Math.min(o,c))};return{outerStart:r(n.outerStart),outerEnd:r(n.outerEnd),innerStart:G(n.innerStart,0,a),innerEnd:G(n.innerEnd,0,a)}}function Gt(i,t,e,s){return{x:e+i*Math.cos(t),y:s+i*Math.sin(t)}}function hi(i,t,e,s,n,o){const{x:a,y:r,startAngle:l,pixelMargin:c,innerRadius:h}=t,d=Math.max(t.outerRadius+s+e-c,0),u=h>0?h+s+e+c:0;let f=0;const g=n-l;if(s){const F=h>0?h-s:0,W=d>0?d-s:0,X=(F+W)/2,ct=X!==0?g*X/(X+s):g;f=(g-ct)/2}const m=Math.max(.001,g*d-e/N)/d,p=(g-m)/2,b=l+p+f,x=n-p-f,{outerStart:v,outerEnd:w,innerStart:y,innerEnd:k}=bc(t,u,d,x-b),S=d-v,P=d-w,D=b+v/S,O=x-w/P,A=u+y,L=u+k,Z=b+y/A,nt=x-k/L;if(i.beginPath(),o){const F=(D+O)/2;if(i.arc(a,r,d,D,F),i.arc(a,r,d,F,O),w>0){const J=Gt(P,O,a,r);i.arc(J.x,J.y,w,O,x+K)}const W=Gt(L,x,a,r);if(i.lineTo(W.x,W.y),k>0){const J=Gt(L,nt,a,r);i.arc(J.x,J.y,k,x+K,nt+Math.PI)}const X=(x-k/u+(b+y/u))/2;if(i.arc(a,r,u,x-k/u,X,!0),i.arc(a,r,u,X,b+y/u,!0),y>0){const J=Gt(A,Z,a,r);i.arc(J.x,J.y,y,Z+Math.PI,b-K)}const ct=Gt(S,b,a,r);if(i.lineTo(ct.x,ct.y),v>0){const J=Gt(S,D,a,r);i.arc(J.x,J.y,v,b-K,D)}}else{i.moveTo(a,r);const F=Math.cos(D)*d+a,W=Math.sin(D)*d+r;i.lineTo(F,W);const X=Math.cos(O)*d+a,ct=Math.sin(O)*d+r;i.lineTo(X,ct)}i.closePath()}function xc(i,t,e,s,n){const{fullCircles:o,startAngle:a,circumference:r}=t;let l=t.endAngle;if(o){hi(i,t,e,s,l,n);for(let c=0;c<o;++c)i.fill();isNaN(r)||(l=a+(r%H||H))}return hi(i,t,e,s,l,n),i.fill(),l}function _c(i,t,e,s,n){const{fullCircles:o,startAngle:a,circumference:r,options:l}=t,{borderWidth:c,borderJoinStyle:h,borderDash:d,borderDashOffset:u}=l,f=l.borderAlign==="inner";if(!c)return;i.setLineDash(d||[]),i.lineDashOffset=u,f?(i.lineWidth=c*2,i.lineJoin=h||"round"):(i.lineWidth=c,i.lineJoin=h||"bevel");let g=t.endAngle;if(o){hi(i,t,e,s,g,n);for(let m=0;m<o;++m)i.stroke();isNaN(r)||(g=a+(r%H||H))}f&&pc(i,t,g),o||(hi(i,t,e,s,g,n),i.stroke())}class fe extends ut{constructor(e){super();M(this,"circumference");M(this,"endAngle");M(this,"fullCircles");M(this,"innerRadius");M(this,"outerRadius");M(this,"pixelMargin");M(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,e&&Object.assign(this,e)}inRange(e,s,n){const o=this.getProps(["x","y"],n),{angle:a,distance:r}=$n(o,{x:e,y:s}),{startAngle:l,endAngle:c,innerRadius:h,outerRadius:d,circumference:u}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],n),f=(this.options.spacing+this.options.borderWidth)/2,m=C(u,c-l)>=H||ke(a,l,c),p=wt(r,h+f,d+f);return m&&p}getCenterPoint(e){const{x:s,y:n,startAngle:o,endAngle:a,innerRadius:r,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],e),{offset:c,spacing:h}=this.options,d=(o+a)/2,u=(r+l+h+c)/2;return{x:s+Math.cos(d)*u,y:n+Math.sin(d)*u}}tooltipPosition(e){return this.getCenterPoint(e)}draw(e){const{options:s,circumference:n}=this,o=(s.offset||0)/4,a=(s.spacing||0)/2,r=s.circular;if(this.pixelMargin=s.borderAlign==="inner"?.33:0,this.fullCircles=n>H?Math.floor(n/H):0,n===0||this.innerRadius<0||this.outerRadius<0)return;e.save();const l=(this.startAngle+this.endAngle)/2;e.translate(Math.cos(l)*o,Math.sin(l)*o);const c=1-Math.sin(Math.min(N,n||0)),h=o*c;e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,xc(e,this,h,a,r),_c(e,this,h,a,r),e.restore()}}M(fe,"id","arc"),M(fe,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),M(fe,"defaultRoutes",{backgroundColor:"backgroundColor"}),M(fe,"descriptors",{_scriptable:!0,_indexable:e=>e!=="borderDash"});function ko(i,t,e=t){i.lineCap=C(e.borderCapStyle,t.borderCapStyle),i.setLineDash(C(e.borderDash,t.borderDash)),i.lineDashOffset=C(e.borderDashOffset,t.borderDashOffset),i.lineJoin=C(e.borderJoinStyle,t.borderJoinStyle),i.lineWidth=C(e.borderWidth,t.borderWidth),i.strokeStyle=C(e.borderColor,t.borderColor)}function yc(i,t,e){i.lineTo(e.x,e.y)}function vc(i){return i.stepped?$a:i.tension||i.cubicInterpolationMode==="monotone"?Ya:yc}function So(i,t,e={}){const s=i.length,{start:n=0,end:o=s-1}=e,{start:a,end:r}=t,l=Math.max(n,a),c=Math.min(o,r),h=n<a&&o<a||n>r&&o>r;return{count:s,start:l,loop:t.loop,ilen:c<l&&!h?s+c-l:c-l}}function wc(i,t,e,s){const{points:n,options:o}=t,{count:a,start:r,loop:l,ilen:c}=So(n,e,s),h=vc(o);let{move:d=!0,reverse:u}=s||{},f,g,m;for(f=0;f<=c;++f)g=n[(r+(u?c-f:f))%a],!g.skip&&(d?(i.moveTo(g.x,g.y),d=!1):h(i,m,g,u,o.stepped),m=g);return l&&(g=n[(r+(u?c:0))%a],h(i,m,g,u,o.stepped)),!!l}function Mc(i,t,e,s){const n=t.points,{count:o,start:a,ilen:r}=So(n,e,s),{move:l=!0,reverse:c}=s||{};let h=0,d=0,u,f,g,m,p,b;const x=w=>(a+(c?r-w:w))%o,v=()=>{m!==p&&(i.lineTo(h,p),i.lineTo(h,m),i.lineTo(h,b))};for(l&&(f=n[x(0)],i.moveTo(f.x,f.y)),u=0;u<=r;++u){if(f=n[x(u)],f.skip)continue;const w=f.x,y=f.y,k=w|0;k===g?(y<m?m=y:y>p&&(p=y),h=(d*h+w)/++d):(v(),i.lineTo(w,y),g=k,d=0,m=p=y),b=y}v()}function Bi(i){const t=i.options,e=t.borderDash&&t.borderDash.length;return!i._decimated&&!i._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!e?Mc:wc}function kc(i){return i.stepped?Mr:i.tension||i.cubicInterpolationMode==="monotone"?kr:Ht}function Sc(i,t,e,s){let n=t._path;n||(n=t._path=new Path2D,t.path(n,e,s)&&n.closePath()),ko(i,t.options),i.stroke(n)}function Pc(i,t,e,s){const{segments:n,options:o}=t,a=Bi(t);for(const r of n)ko(i,o,r.style),i.beginPath(),a(i,t,r,{start:e,end:e+s-1})&&i.closePath(),i.stroke()}const Dc=typeof Path2D=="function";function Cc(i,t,e,s){Dc&&!t.options.segment?Sc(i,t,e,s):Pc(i,t,e,s)}class Dt extends ut{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const n=s.spanGaps?this._loop:this._fullLoop;pr(this._points,s,t,n,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=Ar(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,s=t.length;return s&&e[t[s-1].end]}interpolate(t,e){const s=this.options,n=t[e],o=this.points,a=ho(this,{property:e,start:n,end:n});if(!a.length)return;const r=[],l=kc(s);let c,h;for(c=0,h=a.length;c<h;++c){const{start:d,end:u}=a[c],f=o[d],g=o[u];if(f===g){r.push(f);continue}const m=Math.abs((n-f[e])/(g[e]-f[e])),p=l(f,g,m,s.stepped);p[e]=t[e],r.push(p)}return r.length===1?r[0]:r}pathSegment(t,e,s){return Bi(this)(t,this,e,s)}path(t,e,s){const n=this.segments,o=Bi(this);let a=this._loop;e=e||0,s=s||this.points.length-e;for(const r of n)a&=o(t,this,r,{start:e,end:e+s-1});return!!a}draw(t,e,s,n){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),Cc(t,this,s,n),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}M(Dt,"id","line"),M(Dt,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),M(Dt,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),M(Dt,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function dn(i,t,e,s){const n=i.options,{[e]:o}=i.getProps([e],s);return Math.abs(t-o)<n.radius+n.hitRadius}class ii extends ut{constructor(e){super();M(this,"parsed");M(this,"skip");M(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,e&&Object.assign(this,e)}inRange(e,s,n){const o=this.options,{x:a,y:r}=this.getProps(["x","y"],n);return Math.pow(e-a,2)+Math.pow(s-r,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(e,s){return dn(this,e,"x",s)}inYRange(e,s){return dn(this,e,"y",s)}getCenterPoint(e){const{x:s,y:n}=this.getProps(["x","y"],e);return{x:s,y:n}}size(e){e=e||this.options||{};let s=e.radius||0;s=Math.max(s,s&&e.hoverRadius||0);const n=s&&e.borderWidth||0;return(s+n)*2}draw(e,s){const n=this.options;this.skip||n.radius<.1||!kt(this,s,this.size(n)/2)||(e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.fillStyle=n.backgroundColor,Ii(e,n,this.x,this.y))}getRange(){const e=this.options||{};return e.radius+e.hitRadius}}M(ii,"id","point"),M(ii,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),M(ii,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Po(i,t){const{x:e,y:s,base:n,width:o,height:a}=i.getProps(["x","y","base","width","height"],t);let r,l,c,h,d;return i.horizontal?(d=a/2,r=Math.min(e,n),l=Math.max(e,n),c=s-d,h=s+d):(d=o/2,r=e-d,l=e+d,c=Math.min(s,n),h=Math.max(s,n)),{left:r,top:c,right:l,bottom:h}}function Ct(i,t,e,s){return i?0:G(t,e,s)}function Oc(i,t,e){const s=i.options.borderWidth,n=i.borderSkipped,o=Qn(s);return{t:Ct(n.top,o.top,0,e),r:Ct(n.right,o.right,0,t),b:Ct(n.bottom,o.bottom,0,e),l:Ct(n.left,o.left,0,t)}}function Ac(i,t,e){const{enableBorderRadius:s}=i.getProps(["enableBorderRadius"]),n=i.options.borderRadius,o=Wt(n),a=Math.min(t,e),r=i.borderSkipped,l=s||T(n);return{topLeft:Ct(!l||r.top||r.left,o.topLeft,0,a),topRight:Ct(!l||r.top||r.right,o.topRight,0,a),bottomLeft:Ct(!l||r.bottom||r.left,o.bottomLeft,0,a),bottomRight:Ct(!l||r.bottom||r.right,o.bottomRight,0,a)}}function Tc(i){const t=Po(i),e=t.right-t.left,s=t.bottom-t.top,n=Oc(i,e/2,s/2),o=Ac(i,e/2,s/2);return{outer:{x:t.left,y:t.top,w:e,h:s,radius:o},inner:{x:t.left+n.l,y:t.top+n.t,w:e-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function Ci(i,t,e,s){const n=t===null,o=e===null,r=i&&!(n&&o)&&Po(i,s);return r&&(n||wt(t,r.left,r.right))&&(o||wt(e,r.top,r.bottom))}function Lc(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}function Rc(i,t){i.rect(t.x,t.y,t.w,t.h)}function Oi(i,t,e={}){const s=i.x!==e.x?-t:0,n=i.y!==e.y?-t:0,o=(i.x+i.w!==e.x+e.w?t:0)-s,a=(i.y+i.h!==e.y+e.h?t:0)-n;return{x:i.x+s,y:i.y+n,w:i.w+o,h:i.h+a,radius:i.radius}}class si extends ut{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:a}=Tc(this),r=Lc(a.radius)?Se:Rc;t.save(),(a.w!==o.w||a.h!==o.h)&&(t.beginPath(),r(t,Oi(a,e,o)),t.clip(),r(t,Oi(o,-e,a)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),r(t,Oi(o,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,s){return Ci(this,t,e,s)}inXRange(t,e){return Ci(this,t,null,e)}inYRange(t,e){return Ci(this,null,t,e)}getCenterPoint(t){const{x:e,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(e+n)/2:e,y:o?s:(s+n)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}M(si,"id","bar"),M(si,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),M(si,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});var Ic=Object.freeze({__proto__:null,ArcElement:fe,BarElement:si,LineElement:Dt,PointElement:ii});const Vi=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],un=Vi.map(i=>i.replace("rgb(","rgba(").replace(")",", 0.5)"));function Do(i){return Vi[i%Vi.length]}function Co(i){return un[i%un.length]}function Ec(i,t){return i.borderColor=Do(t),i.backgroundColor=Co(t),++t}function Fc(i,t){return i.backgroundColor=i.data.map(()=>Do(t++)),t}function zc(i,t){return i.backgroundColor=i.data.map(()=>Co(t++)),t}function Bc(i){let t=0;return(e,s)=>{const n=i.getDatasetMeta(s).controller;n instanceof Nt?t=Fc(e,t):n instanceof ye?t=zc(e,t):n&&(t=Ec(e,t))}}function fn(i){let t;for(t in i)if(i[t].borderColor||i[t].backgroundColor)return!0;return!1}function Vc(i){return i&&(i.borderColor||i.backgroundColor)}var Hc={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(i,t,e){if(!e.enabled)return;const{data:{datasets:s},options:n}=i.config,{elements:o}=n;if(!e.forceOverride&&(fn(s)||Vc(n)||o&&fn(o)))return;const a=Bc(i);s.forEach(a)}};function Nc(i,t,e,s,n){const o=n.samples||s;if(o>=e)return i.slice(t,t+e);const a=[],r=(e-2)/(o-2);let l=0;const c=t+e-1;let h=t,d,u,f,g,m;for(a[l++]=i[h],d=0;d<o-2;d++){let p=0,b=0,x;const v=Math.floor((d+1)*r)+1+t,w=Math.min(Math.floor((d+2)*r)+1,e)+t,y=w-v;for(x=v;x<w;x++)p+=i[x].x,b+=i[x].y;p/=y,b/=y;const k=Math.floor(d*r)+1+t,S=Math.min(Math.floor((d+1)*r)+1,e)+t,{x:P,y:D}=i[h];for(f=g=-1,x=k;x<S;x++)g=.5*Math.abs((P-p)*(i[x].y-D)-(P-i[x].x)*(b-D)),g>f&&(f=g,u=i[x],m=x);a[l++]=u,h=m}return a[l++]=i[c],a}function Wc(i,t,e,s){let n=0,o=0,a,r,l,c,h,d,u,f,g,m;const p=[],b=t+e-1,x=i[t].x,w=i[b].x-x;for(a=t;a<t+e;++a){r=i[a],l=(r.x-x)/w*s,c=r.y;const y=l|0;if(y===h)c<g?(g=c,d=a):c>m&&(m=c,u=a),n=(o*n+r.x)/++o;else{const k=a-1;if(!R(d)&&!R(u)){const S=Math.min(d,u),P=Math.max(d,u);S!==f&&S!==k&&p.push({...i[S],x:n}),P!==f&&P!==k&&p.push({...i[P],x:n})}a>0&&k!==f&&p.push(i[k]),p.push(r),h=y,o=0,g=m=c,d=u=f=a}}return p}function Oo(i){if(i._decimated){const t=i._data;delete i._decimated,delete i._data,Object.defineProperty(i,"data",{configurable:!0,enumerable:!0,writable:!0,value:t})}}function gn(i){i.data.datasets.forEach(t=>{Oo(t)})}function jc(i,t){const e=t.length;let s=0,n;const{iScale:o}=i,{min:a,max:r,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(s=G(Mt(t,o.axis,a).lo,0,e-1)),c?n=G(Mt(t,o.axis,r).hi+1,s,e)-s:n=e-s,{start:s,count:n}}var $c={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(i,t,e)=>{if(!e.enabled){gn(i);return}const s=i.width;i.data.datasets.forEach((n,o)=>{const{_data:a,indexAxis:r}=n,l=i.getDatasetMeta(o),c=a||n.data;if(de([r,i.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;const h=i.scales[l.xAxisID];if(h.type!=="linear"&&h.type!=="time"||i.options.parsing)return;let{start:d,count:u}=jc(l,c);const f=e.threshold||4*s;if(u<=f){Oo(n);return}R(a)&&(n._data=c,delete n.data,Object.defineProperty(n,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(m){this._data=m}}));let g;switch(e.algorithm){case"lttb":g=Nc(c,d,u,s,e);break;case"min-max":g=Wc(c,d,u,s);break;default:throw new Error(`Unsupported decimation algorithm '${e.algorithm}'`)}n._decimated=g})},destroy(i){gn(i)}};function Yc(i,t,e){const s=i.segments,n=i.points,o=t.points,a=[];for(const r of s){let{start:l,end:c}=r;c=hs(l,c,n);const h=Hi(e,n[l],n[c],r.loop);if(!t.segments){a.push({source:r,target:h,start:n[l],end:n[c]});continue}const d=ho(t,h);for(const u of d){const f=Hi(e,o[u.start],o[u.end],u.loop),g=co(r,n,f);for(const m of g)a.push({source:m,target:u,start:{[e]:pn(h,f,"start",Math.max)},end:{[e]:pn(h,f,"end",Math.min)}})}}return a}function Hi(i,t,e,s){if(s)return;let n=t[i],o=e[i];return i==="angle"&&(n=at(n),o=at(o)),{property:i,start:n,end:o}}function Uc(i,t){const{x:e=null,y:s=null}=i||{},n=t.points,o=[];return t.segments.forEach(({start:a,end:r})=>{r=hs(a,r,n);const l=n[a],c=n[r];s!==null?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):e!==null&&(o.push({x:e,y:l.y}),o.push({x:e,y:c.y}))}),o}function hs(i,t,e){for(;t>i;t--){const s=e[t];if(!isNaN(s.x)&&!isNaN(s.y))break}return t}function pn(i,t,e,s){return i&&t?s(i[e],t[e]):i?i[e]:t?t[e]:0}function Ao(i,t){let e=[],s=!1;return V(i)?(s=!0,e=i):e=Uc(i,t),e.length?new Dt({points:e,options:{tension:0},_loop:s,_fullLoop:s}):null}function mn(i){return i&&i.fill!==!1}function Xc(i,t,e){let n=i[t].fill;const o=[t];let a;if(!e)return n;for(;n!==!1&&o.indexOf(n)===-1;){if(!Y(n))return n;if(a=i[n],!a)return!1;if(a.visible)return n;o.push(n),n=a.fill}return!1}function Kc(i,t,e){const s=Jc(i);if(T(s))return isNaN(s.value)?!1:s;let n=parseFloat(s);return Y(n)&&Math.floor(n)===n?qc(s[0],t,n,e):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function qc(i,t,e,s){return(i==="-"||i==="+")&&(e=t+e),e===t||e<0||e>=s?!1:e}function Gc(i,t){let e=null;return i==="start"?e=t.bottom:i==="end"?e=t.top:T(i)?e=t.getPixelForValue(i.value):t.getBasePixel&&(e=t.getBasePixel()),e}function Zc(i,t,e){let s;return i==="start"?s=e:i==="end"?s=t.options.reverse?t.min:t.max:T(i)?s=i.value:s=t.getBaseValue(),s}function Jc(i){const t=i.options,e=t.fill;let s=C(e&&e.target,e);return s===void 0&&(s=!!t.backgroundColor),s===!1||s===null?!1:s===!0?"origin":s}function Qc(i){const{scale:t,index:e,line:s}=i,n=[],o=s.segments,a=s.points,r=th(t,e);r.push(Ao({x:null,y:t.bottom},s));for(let l=0;l<o.length;l++){const c=o[l];for(let h=c.start;h<=c.end;h++)eh(n,a[h],r)}return new Dt({points:n,options:{}})}function th(i,t){const e=[],s=i.getMatchingVisibleMetas("line");for(let n=0;n<s.length;n++){const o=s[n];if(o.index===t)break;o.hidden||e.unshift(o.dataset)}return e}function eh(i,t,e){const s=[];for(let n=0;n<e.length;n++){const o=e[n],{first:a,last:r,point:l}=ih(o,t,"x");if(!(!l||a&&r)){if(a)s.unshift(l);else if(i.push(l),!r)break}}i.push(...s)}function ih(i,t,e){const s=i.interpolate(t,e);if(!s)return{};const n=s[e],o=i.segments,a=i.points;let r=!1,l=!1;for(let c=0;c<o.length;c++){const h=o[c],d=a[h.start][e],u=a[h.end][e];if(wt(n,d,u)){r=n===d,l=n===u;break}}return{first:r,last:l,point:s}}class To{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,e,s){const{x:n,y:o,radius:a}=this;return e=e||{start:0,end:H},t.arc(n,o,a,e.end,e.start,!0),!s.bounds}interpolate(t){const{x:e,y:s,radius:n}=this,o=t.angle;return{x:e+Math.cos(o)*n,y:s+Math.sin(o)*n,angle:o}}}function sh(i){const{chart:t,fill:e,line:s}=i;if(Y(e))return nh(t,e);if(e==="stack")return Qc(i);if(e==="shape")return!0;const n=oh(i);return n instanceof To?n:Ao(n,s)}function nh(i,t){const e=i.getDatasetMeta(t);return e&&i.isDatasetVisible(t)?e.dataset:null}function oh(i){return(i.scale||{}).getPointPositionForValue?rh(i):ah(i)}function ah(i){const{scale:t={},fill:e}=i,s=Gc(e,t);if(Y(s)){const n=t.isHorizontal();return{x:n?s:null,y:n?null:s}}return null}function rh(i){const{scale:t,fill:e}=i,s=t.options,n=t.getLabels().length,o=s.reverse?t.max:t.min,a=Zc(e,t,o),r=[];if(s.grid.circular){const l=t.getPointPositionForValue(0,o);return new To({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(a)})}for(let l=0;l<n;++l)r.push(t.getPointPositionForValue(l,a));return r}function Ai(i,t,e){const s=sh(t),{line:n,scale:o,axis:a}=t,r=n.options,l=r.fill,c=r.backgroundColor,{above:h=c,below:d=c}=l||{};s&&n.points.length&&(gi(i,e),lh(i,{line:n,target:s,above:h,below:d,area:e,scale:o,axis:a}),pi(i))}function lh(i,t){const{line:e,target:s,above:n,below:o,area:a,scale:r}=t,l=e._loop?"angle":t.axis;i.save(),l==="x"&&o!==n&&(bn(i,s,a.top),xn(i,{line:e,target:s,color:n,scale:r,property:l}),i.restore(),i.save(),bn(i,s,a.bottom)),xn(i,{line:e,target:s,color:o,scale:r,property:l}),i.restore()}function bn(i,t,e){const{segments:s,points:n}=t;let o=!0,a=!1;i.beginPath();for(const r of s){const{start:l,end:c}=r,h=n[l],d=n[hs(l,c,n)];o?(i.moveTo(h.x,h.y),o=!1):(i.lineTo(h.x,e),i.lineTo(h.x,h.y)),a=!!t.pathSegment(i,r,{move:a}),a?i.closePath():i.lineTo(d.x,e)}i.lineTo(t.first().x,e),i.closePath(),i.clip()}function xn(i,t){const{line:e,target:s,property:n,color:o,scale:a}=t,r=Yc(e,s,n);for(const{source:l,target:c,start:h,end:d}of r){const{style:{backgroundColor:u=o}={}}=l,f=s!==!0;i.save(),i.fillStyle=u,ch(i,a,f&&Hi(n,h,d)),i.beginPath();const g=!!e.pathSegment(i,l);let m;if(f){g?i.closePath():_n(i,s,d,n);const p=!!s.pathSegment(i,c,{move:g,reverse:!0});m=g&&p,m||_n(i,s,h,n)}i.closePath(),i.fill(m?"evenodd":"nonzero"),i.restore()}}function ch(i,t,e){const{top:s,bottom:n}=t.chart.chartArea,{property:o,start:a,end:r}=e||{};o==="x"&&(i.beginPath(),i.rect(a,s,r-a,n-s),i.clip())}function _n(i,t,e,s){const n=t.interpolate(e,s);n&&i.lineTo(n.x,n.y)}var hh={id:"filler",afterDatasetsUpdate(i,t,e){const s=(i.data.datasets||[]).length,n=[];let o,a,r,l;for(a=0;a<s;++a)o=i.getDatasetMeta(a),r=o.dataset,l=null,r&&r.options&&r instanceof Dt&&(l={visible:i.isDatasetVisible(a),index:a,fill:Kc(r,a,s),chart:i,axis:o.controller.options.indexAxis,scale:o.vScale,line:r}),o.$filler=l,n.push(l);for(a=0;a<s;++a)l=n[a],!(!l||l.fill===!1)&&(l.fill=Xc(n,a,e.propagate))},beforeDraw(i,t,e){const s=e.drawTime==="beforeDraw",n=i.getSortedVisibleDatasetMetas(),o=i.chartArea;for(let a=n.length-1;a>=0;--a){const r=n[a].$filler;r&&(r.line.updateControlPoints(o,r.axis),s&&r.fill&&Ai(i.ctx,r,o))}},beforeDatasetsDraw(i,t,e){if(e.drawTime!=="beforeDatasetsDraw")return;const s=i.getSortedVisibleDatasetMetas();for(let n=s.length-1;n>=0;--n){const o=s[n].$filler;mn(o)&&Ai(i.ctx,o,i.chartArea)}},beforeDatasetDraw(i,t,e){const s=t.meta.$filler;!mn(s)||e.drawTime!=="beforeDatasetDraw"||Ai(i.ctx,s,i.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const yn=(i,t)=>{let{boxHeight:e=t,boxWidth:s=t}=i;return i.usePointStyle&&(e=Math.min(e,t),s=i.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:e,itemHeight:Math.max(t,e)}},dh=(i,t)=>i!==null&&t!==null&&i.datasetIndex===t.datasetIndex&&i.index===t.index;class vn extends ut{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,s){this.maxWidth=t,this.maxHeight=e,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=B(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(s=>t.filter(s,this.chart.data))),t.sort&&(e=e.sort((s,n)=>t.sort(s,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,n=q(s.font),o=n.size,a=this._computeTitleHeight(),{boxWidth:r,itemHeight:l}=yn(s,o);let c,h;e.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(a,o,r,l)+10):(h=this.maxHeight,c=this._fitCols(a,n,r,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,s,n){const{ctx:o,maxWidth:a,options:{labels:{padding:r}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+r;let d=t;o.textAlign="left",o.textBaseline="middle";let u=-1,f=-h;return this.legendItems.forEach((g,m)=>{const p=s+e/2+o.measureText(g.text).width;(m===0||c[c.length-1]+p+2*r>a)&&(d+=h,c[c.length-(m>0?0:1)]=0,f+=h,u++),l[m]={left:0,top:f,row:u,width:p,height:n},c[c.length-1]+=p+r}),d}_fitCols(t,e,s,n){const{ctx:o,maxHeight:a,options:{labels:{padding:r}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=a-t;let d=r,u=0,f=0,g=0,m=0;return this.legendItems.forEach((p,b)=>{const{itemWidth:x,itemHeight:v}=uh(s,e,o,p,n);b>0&&f+v+2*r>h&&(d+=u+r,c.push({width:u,height:f}),g+=u+r,m++,u=f=0),l[b]={left:g,top:f,col:m,width:x,height:v},u=Math.max(u,x),f+=v+r}),d+=u,c.push({width:u,height:f}),d}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:s,labels:{padding:n},rtl:o}}=this,a=Zt(o,this.left,this.width);if(this.isHorizontal()){let r=0,l=Q(s,this.left+n,this.right-this.lineWidths[r]);for(const c of e)r!==c.row&&(r=c.row,l=Q(s,this.left+n,this.right-this.lineWidths[r])),c.top+=this.top+t+n,c.left=a.leftForLtr(a.x(l),c.width),l+=c.width+n}else{let r=0,l=Q(s,this.top+t+n,this.bottom-this.columnSizes[r].height);for(const c of e)c.col!==r&&(r=c.col,l=Q(s,this.top+t+n,this.bottom-this.columnSizes[r].height)),c.top=l,c.left+=this.left+n,c.left=a.leftForLtr(a.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;gi(t,this),this._draw(),pi(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:s,ctx:n}=this,{align:o,labels:a}=t,r=U.color,l=Zt(t.rtl,this.left,this.width),c=q(a.font),{padding:h}=a,d=c.size,u=d/2;let f;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;const{boxWidth:g,boxHeight:m,itemHeight:p}=yn(a,d),b=function(k,S,P){if(isNaN(g)||g<=0||isNaN(m)||m<0)return;n.save();const D=C(P.lineWidth,1);if(n.fillStyle=C(P.fillStyle,r),n.lineCap=C(P.lineCap,"butt"),n.lineDashOffset=C(P.lineDashOffset,0),n.lineJoin=C(P.lineJoin,"miter"),n.lineWidth=D,n.strokeStyle=C(P.strokeStyle,r),n.setLineDash(C(P.lineDash,[])),a.usePointStyle){const O={radius:m*Math.SQRT2/2,pointStyle:P.pointStyle,rotation:P.rotation,borderWidth:D},A=l.xPlus(k,g/2),L=S+u;Jn(n,O,A,L,a.pointStyleWidth&&g)}else{const O=S+Math.max((d-m)/2,0),A=l.leftForLtr(k,g),L=Wt(P.borderRadius);n.beginPath(),Object.values(L).some(Z=>Z!==0)?Se(n,{x:A,y:O,w:g,h:m,radius:L}):n.rect(A,O,g,m),n.fill(),D!==0&&n.stroke()}n.restore()},x=function(k,S,P){Yt(n,P.text,k,S+p/2,c,{strikethrough:P.hidden,textAlign:l.textAlign(P.textAlign)})},v=this.isHorizontal(),w=this._computeTitleHeight();v?f={x:Q(o,this.left+h,this.right-s[0]),y:this.top+h+w,line:0}:f={x:this.left+h,y:Q(o,this.top+w+h,this.bottom-e[0].height),line:0},ao(this.ctx,t.textDirection);const y=p+h;this.legendItems.forEach((k,S)=>{n.strokeStyle=k.fontColor,n.fillStyle=k.fontColor;const P=n.measureText(k.text).width,D=l.textAlign(k.textAlign||(k.textAlign=a.textAlign)),O=g+u+P;let A=f.x,L=f.y;l.setWidth(this.width),v?S>0&&A+O+h>this.right&&(L=f.y+=y,f.line++,A=f.x=Q(o,this.left+h,this.right-s[f.line])):S>0&&L+y>this.bottom&&(A=f.x=A+e[f.line].width+h,f.line++,L=f.y=Q(o,this.top+w+h,this.bottom-e[f.line].height));const Z=l.x(A);if(b(Z,L,k),A=Ra(D,A+g+u,v?A+O:this.right,t.rtl),x(l.x(A),L,k),v)f.x+=O+h;else if(typeof k.text!="string"){const nt=c.lineHeight;f.y+=Lo(k,nt)+h}else f.y+=y}),ro(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,s=q(e.font),n=et(e.padding);if(!e.display)return;const o=Zt(t.rtl,this.left,this.width),a=this.ctx,r=e.position,l=s.size/2,c=n.top+l;let h,d=this.left,u=this.width;if(this.isHorizontal())u=Math.max(...this.lineWidths),h=this.top+c,d=Q(t.align,d,this.right-u);else{const g=this.columnSizes.reduce((m,p)=>Math.max(m,p.height),0);h=c+Q(t.align,this.top,this.bottom-g-t.labels.padding-this._computeTitleHeight())}const f=Q(r,d,d+u);a.textAlign=o.textAlign(es(r)),a.textBaseline="middle",a.strokeStyle=e.color,a.fillStyle=e.color,a.font=s.string,Yt(a,e.text,f,h,s)}_computeTitleHeight(){const t=this.options.title,e=q(t.font),s=et(t.padding);return t.display?e.lineHeight+s.height:0}_getLegendItemAt(t,e){let s,n,o;if(wt(t,this.left,this.right)&&wt(e,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],wt(t,n.left,n.left+n.width)&&wt(e,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(t){const e=this.options;if(!ph(t.type,e))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const n=this._hoveredItem,o=dh(n,s);n&&!o&&B(e.onLeave,[t,n,this],this),this._hoveredItem=s,s&&!o&&B(e.onHover,[t,s,this],this)}else s&&B(e.onClick,[t,s,this],this)}}function uh(i,t,e,s,n){const o=fh(s,i,t,e),a=gh(n,s,t.lineHeight);return{itemWidth:o,itemHeight:a}}function fh(i,t,e,s){let n=i.text;return n&&typeof n!="string"&&(n=n.reduce((o,a)=>o.length>a.length?o:a)),t+e.size/2+s.measureText(n).width}function gh(i,t,e){let s=i;return typeof t.text!="string"&&(s=Lo(t,e)),s}function Lo(i,t){const e=i.text?i.text.length:0;return t*e}function ph(i,t){return!!((i==="mousemove"||i==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(i==="click"||i==="mouseup"))}var mh={id:"legend",_element:vn,start(i,t,e){const s=i.legend=new vn({ctx:i.ctx,options:e,chart:i});tt.configure(i,s,e),tt.addBox(i,s)},stop(i){tt.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,t,e){const s=i.legend;tt.configure(i,s,e),s.options=e},afterUpdate(i){const t=i.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(i,t){t.replay||i.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,t,e){const s=t.datasetIndex,n=e.chart;n.isDatasetVisible(s)?(n.hide(s),t.hidden=!0):(n.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const t=i.data.datasets,{labels:{usePointStyle:e,pointStyle:s,textAlign:n,color:o,useBorderRadius:a,borderRadius:r}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(e?0:void 0),h=et(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:a&&(r||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};class ds extends ut{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;const n=V(s.text)?s.text.length:1;this._padding=et(s.padding);const o=n*q(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:s,bottom:n,right:o,options:a}=this,r=a.align;let l=0,c,h,d;return this.isHorizontal()?(h=Q(r,s,o),d=e+t,c=o-s):(a.position==="left"?(h=s+t,d=Q(r,n,e),l=N*-.5):(h=o-t,d=Q(r,e,n),l=N*.5),c=n-e),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const s=q(e.font),o=s.lineHeight/2+this._padding.top,{titleX:a,titleY:r,maxWidth:l,rotation:c}=this._drawArgs(o);Yt(t,e.text,0,0,s,{color:e.color,maxWidth:l,rotation:c,textAlign:es(e.align),textBaseline:"middle",translation:[a,r]})}}function bh(i,t){const e=new ds({ctx:i.ctx,options:t,chart:i});tt.configure(i,e,t),tt.addBox(i,e),i.titleBlock=e}var xh={id:"title",_element:ds,start(i,t,e){bh(i,e)},stop(i){const t=i.titleBlock;tt.removeBox(i,t),delete i.titleBlock},beforeUpdate(i,t,e){const s=i.titleBlock;tt.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Ue=new WeakMap;var _h={id:"subtitle",start(i,t,e){const s=new ds({ctx:i.ctx,options:e,chart:i});tt.configure(i,s,e),tt.addBox(i,s),Ue.set(i,s)},stop(i){tt.removeBox(i,Ue.get(i)),Ue.delete(i)},beforeUpdate(i,t,e){const s=Ue.get(i);tt.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const ge={average(i){if(!i.length)return!1;let t,e,s=new Set,n=0,o=0;for(t=0,e=i.length;t<e;++t){const r=i[t].element;if(r&&r.hasValue()){const l=r.tooltipPosition();s.add(l.x),n+=l.y,++o}}return{x:[...s].reduce((r,l)=>r+l)/s.size,y:n/o}},nearest(i,t){if(!i.length)return!1;let e=t.x,s=t.y,n=Number.POSITIVE_INFINITY,o,a,r;for(o=0,a=i.length;o<a;++o){const l=i[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),h=Li(t,c);h<n&&(n=h,r=l)}}if(r){const l=r.tooltipPosition();e=l.x,s=l.y}return{x:e,y:s}}};function gt(i,t){return t&&(V(t)?Array.prototype.push.apply(i,t):i.push(t)),i}function yt(i){return(typeof i=="string"||i instanceof String)&&i.indexOf(`
`)>-1?i.split(`
`):i}function yh(i,t){const{element:e,datasetIndex:s,index:n}=t,o=i.getDatasetMeta(s).controller,{label:a,value:r}=o.getLabelAndValue(n);return{chart:i,label:a,parsed:o.getParsed(n),raw:i.data.datasets[s].data[n],formattedValue:r,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:e}}function wn(i,t){const e=i.chart.ctx,{body:s,footer:n,title:o}=i,{boxWidth:a,boxHeight:r}=t,l=q(t.bodyFont),c=q(t.titleFont),h=q(t.footerFont),d=o.length,u=n.length,f=s.length,g=et(t.padding);let m=g.height,p=0,b=s.reduce((w,y)=>w+y.before.length+y.lines.length+y.after.length,0);if(b+=i.beforeBody.length+i.afterBody.length,d&&(m+=d*c.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),b){const w=t.displayColors?Math.max(r,l.lineHeight):l.lineHeight;m+=f*w+(b-f)*l.lineHeight+(b-1)*t.bodySpacing}u&&(m+=t.footerMarginTop+u*h.lineHeight+(u-1)*t.footerSpacing);let x=0;const v=function(w){p=Math.max(p,e.measureText(w).width+x)};return e.save(),e.font=c.string,E(i.title,v),e.font=l.string,E(i.beforeBody.concat(i.afterBody),v),x=t.displayColors?a+2+t.boxPadding:0,E(s,w=>{E(w.before,v),E(w.lines,v),E(w.after,v)}),x=0,e.font=h.string,E(i.footer,v),e.restore(),p+=g.width,{width:p,height:m}}function vh(i,t){const{y:e,height:s}=t;return e<s/2?"top":e>i.height-s/2?"bottom":"center"}function wh(i,t,e,s){const{x:n,width:o}=s,a=e.caretSize+e.caretPadding;if(i==="left"&&n+o+a>t.width||i==="right"&&n-o-a<0)return!0}function Mh(i,t,e,s){const{x:n,width:o}=e,{width:a,chartArea:{left:r,right:l}}=i;let c="center";return s==="center"?c=n<=(r+l)/2?"left":"right":n<=o/2?c="left":n>=a-o/2&&(c="right"),wh(c,i,t,e)&&(c="center"),c}function Mn(i,t,e){const s=e.yAlign||t.yAlign||vh(i,e);return{xAlign:e.xAlign||t.xAlign||Mh(i,t,e,s),yAlign:s}}function kh(i,t){let{x:e,width:s}=i;return t==="right"?e-=s:t==="center"&&(e-=s/2),e}function Sh(i,t,e){let{y:s,height:n}=i;return t==="top"?s+=e:t==="bottom"?s-=n+e:s-=n/2,s}function kn(i,t,e,s){const{caretSize:n,caretPadding:o,cornerRadius:a}=i,{xAlign:r,yAlign:l}=e,c=n+o,{topLeft:h,topRight:d,bottomLeft:u,bottomRight:f}=Wt(a);let g=kh(t,r);const m=Sh(t,l,c);return l==="center"?r==="left"?g+=c:r==="right"&&(g-=c):r==="left"?g-=Math.max(h,u)+n:r==="right"&&(g+=Math.max(d,f)+n),{x:G(g,0,s.width-t.width),y:G(m,0,s.height-t.height)}}function Xe(i,t,e){const s=et(e.padding);return t==="center"?i.x+i.width/2:t==="right"?i.x+i.width-s.right:i.x+s.left}function Sn(i){return gt([],yt(i))}function Ph(i,t,e){return Lt(i,{tooltip:t,tooltipItems:e,type:"tooltip"})}function Pn(i,t){const e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?i.override(e):i}const Ro={beforeTitle:xt,title(i){if(i.length>0){const t=i[0],e=t.chart.data.labels,s=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return e[t.dataIndex]}return""},afterTitle:xt,beforeBody:xt,beforeLabel:xt,label(i){if(this&&this.options&&this.options.mode==="dataset")return i.label+": "+i.formattedValue||i.formattedValue;let t=i.dataset.label||"";t&&(t+=": ");const e=i.formattedValue;return R(e)||(t+=e),t},labelColor(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:xt,afterBody:xt,beforeFooter:xt,footer:xt,afterFooter:xt};function it(i,t,e,s){const n=i[t].call(e,s);return typeof n>"u"?Ro[t].call(e,s):n}class Ni extends ut{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,s=this.options.setContext(this.getContext()),n=s.enabled&&e.options.animation&&s.animations,o=new uo(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=Ph(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:s}=e,n=it(s,"beforeTitle",this,t),o=it(s,"title",this,t),a=it(s,"afterTitle",this,t);let r=[];return r=gt(r,yt(n)),r=gt(r,yt(o)),r=gt(r,yt(a)),r}getBeforeBody(t,e){return Sn(it(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:s}=e,n=[];return E(t,o=>{const a={before:[],lines:[],after:[]},r=Pn(s,o);gt(a.before,yt(it(r,"beforeLabel",this,o))),gt(a.lines,it(r,"label",this,o)),gt(a.after,yt(it(r,"afterLabel",this,o))),n.push(a)}),n}getAfterBody(t,e){return Sn(it(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:s}=e,n=it(s,"beforeFooter",this,t),o=it(s,"footer",this,t),a=it(s,"afterFooter",this,t);let r=[];return r=gt(r,yt(n)),r=gt(r,yt(o)),r=gt(r,yt(a)),r}_createItems(t){const e=this._active,s=this.chart.data,n=[],o=[],a=[];let r=[],l,c;for(l=0,c=e.length;l<c;++l)r.push(yh(this.chart,e[l]));return t.filter&&(r=r.filter((h,d,u)=>t.filter(h,d,u,s))),t.itemSort&&(r=r.sort((h,d)=>t.itemSort(h,d,s))),E(r,h=>{const d=Pn(t.callbacks,h);n.push(it(d,"labelColor",this,h)),o.push(it(d,"labelPointStyle",this,h)),a.push(it(d,"labelTextColor",this,h))}),this.labelColors=n,this.labelPointStyles=o,this.labelTextColors=a,this.dataPoints=r,r}update(t,e){const s=this.options.setContext(this.getContext()),n=this._active;let o,a=[];if(!n.length)this.opacity!==0&&(o={opacity:0});else{const r=ge[s.position].call(this,n,this._eventPosition);a=this._createItems(s),this.title=this.getTitle(a,s),this.beforeBody=this.getBeforeBody(a,s),this.body=this.getBody(a,s),this.afterBody=this.getAfterBody(a,s),this.footer=this.getFooter(a,s);const l=this._size=wn(this,s),c=Object.assign({},r,l),h=Mn(this.chart,s,c),d=kn(s,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:d.x,y:d.y,width:l.width,height:l.height,caretX:r.x,caretY:r.y}}this._tooltipItems=a,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,s,n){const o=this.getCaretPosition(t,s,n);e.lineTo(o.x1,o.y1),e.lineTo(o.x2,o.y2),e.lineTo(o.x3,o.y3)}getCaretPosition(t,e,s){const{xAlign:n,yAlign:o}=this,{caretSize:a,cornerRadius:r}=s,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:d}=Wt(r),{x:u,y:f}=t,{width:g,height:m}=e;let p,b,x,v,w,y;return o==="center"?(w=f+m/2,n==="left"?(p=u,b=p-a,v=w+a,y=w-a):(p=u+g,b=p+a,v=w-a,y=w+a),x=p):(n==="left"?b=u+Math.max(l,h)+a:n==="right"?b=u+g-Math.max(c,d)-a:b=this.caretX,o==="top"?(v=f,w=v-a,p=b-a,x=b+a):(v=f+m,w=v+a,p=b+a,x=b-a),y=v),{x1:p,x2:b,x3:x,y1:v,y2:w,y3:y}}drawTitle(t,e,s){const n=this.title,o=n.length;let a,r,l;if(o){const c=Zt(s.rtl,this.x,this.width);for(t.x=Xe(this,s.titleAlign,s),e.textAlign=c.textAlign(s.titleAlign),e.textBaseline="middle",a=q(s.titleFont),r=s.titleSpacing,e.fillStyle=s.titleColor,e.font=a.string,l=0;l<o;++l)e.fillText(n[l],c.x(t.x),t.y+a.lineHeight/2),t.y+=a.lineHeight+r,l+1===o&&(t.y+=s.titleMarginBottom-r)}}_drawColorBox(t,e,s,n,o){const a=this.labelColors[s],r=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=o,h=q(o.bodyFont),d=Xe(this,"left",o),u=n.x(d),f=l<h.lineHeight?(h.lineHeight-l)/2:0,g=e.y+f;if(o.usePointStyle){const m={radius:Math.min(c,l)/2,pointStyle:r.pointStyle,rotation:r.rotation,borderWidth:1},p=n.leftForLtr(u,c)+c/2,b=g+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,Ii(t,m,p,b),t.strokeStyle=a.borderColor,t.fillStyle=a.backgroundColor,Ii(t,m,p,b)}else{t.lineWidth=T(a.borderWidth)?Math.max(...Object.values(a.borderWidth)):a.borderWidth||1,t.strokeStyle=a.borderColor,t.setLineDash(a.borderDash||[]),t.lineDashOffset=a.borderDashOffset||0;const m=n.leftForLtr(u,c),p=n.leftForLtr(n.xPlus(u,1),c-2),b=Wt(a.borderRadius);Object.values(b).some(x=>x!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,Se(t,{x:m,y:g,w:c,h:l,radius:b}),t.fill(),t.stroke(),t.fillStyle=a.backgroundColor,t.beginPath(),Se(t,{x:p,y:g+1,w:c-2,h:l-2,radius:b}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(m,g,c,l),t.strokeRect(m,g,c,l),t.fillStyle=a.backgroundColor,t.fillRect(p,g+1,c-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,e,s){const{body:n}=this,{bodySpacing:o,bodyAlign:a,displayColors:r,boxHeight:l,boxWidth:c,boxPadding:h}=s,d=q(s.bodyFont);let u=d.lineHeight,f=0;const g=Zt(s.rtl,this.x,this.width),m=function(P){e.fillText(P,g.x(t.x+f),t.y+u/2),t.y+=u+o},p=g.textAlign(a);let b,x,v,w,y,k,S;for(e.textAlign=a,e.textBaseline="middle",e.font=d.string,t.x=Xe(this,p,s),e.fillStyle=s.bodyColor,E(this.beforeBody,m),f=r&&p!=="right"?a==="center"?c/2+h:c+2+h:0,w=0,k=n.length;w<k;++w){for(b=n[w],x=this.labelTextColors[w],e.fillStyle=x,E(b.before,m),v=b.lines,r&&v.length&&(this._drawColorBox(e,t,w,g,s),u=Math.max(d.lineHeight,l)),y=0,S=v.length;y<S;++y)m(v[y]),u=d.lineHeight;E(b.after,m)}f=0,u=d.lineHeight,E(this.afterBody,m),t.y-=o}drawFooter(t,e,s){const n=this.footer,o=n.length;let a,r;if(o){const l=Zt(s.rtl,this.x,this.width);for(t.x=Xe(this,s.footerAlign,s),t.y+=s.footerMarginTop,e.textAlign=l.textAlign(s.footerAlign),e.textBaseline="middle",a=q(s.footerFont),e.fillStyle=s.footerColor,e.font=a.string,r=0;r<o;++r)e.fillText(n[r],l.x(t.x),t.y+a.lineHeight/2),t.y+=a.lineHeight+s.footerSpacing}}drawBackground(t,e,s,n){const{xAlign:o,yAlign:a}=this,{x:r,y:l}=t,{width:c,height:h}=s,{topLeft:d,topRight:u,bottomLeft:f,bottomRight:g}=Wt(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(r+d,l),a==="top"&&this.drawCaret(t,e,s,n),e.lineTo(r+c-u,l),e.quadraticCurveTo(r+c,l,r+c,l+u),a==="center"&&o==="right"&&this.drawCaret(t,e,s,n),e.lineTo(r+c,l+h-g),e.quadraticCurveTo(r+c,l+h,r+c-g,l+h),a==="bottom"&&this.drawCaret(t,e,s,n),e.lineTo(r+f,l+h),e.quadraticCurveTo(r,l+h,r,l+h-f),a==="center"&&o==="left"&&this.drawCaret(t,e,s,n),e.lineTo(r,l+d),e.quadraticCurveTo(r,l,r+d,l),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,s=this.$animations,n=s&&s.x,o=s&&s.y;if(n||o){const a=ge[t.position].call(this,this._active,this._eventPosition);if(!a)return;const r=this._size=wn(this,t),l=Object.assign({},a,this._size),c=Mn(e,t,l),h=kn(t,l,c,e);(n._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=r.width,this.height=r.height,this.caretX=a.x,this.caretY=a.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(e);const n={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const a=et(e.padding),r=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&r&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,n,e),ao(t,e.textDirection),o.y+=a.top,this.drawTitle(o,t,e),this.drawBody(o,t,e),this.drawFooter(o,t,e),ro(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const s=this._active,n=t.map(({datasetIndex:r,index:l})=>{const c=this.chart.getDatasetMeta(r);if(!c)throw new Error("Cannot find a dataset at index "+r);return{datasetIndex:r,element:c.data[l],index:l}}),o=!ni(s,n),a=this._positionChanged(n,e);(o||a)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,s=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const n=this.options,o=this._active||[],a=this._getActiveElements(t,o,e,s),r=this._positionChanged(a,t),l=e||!ni(a,o)||r;return l&&(this._active=a,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,s,n){const o=this.options;if(t.type==="mouseout")return[];if(!n)return e.filter(r=>this.chart.data.datasets[r.datasetIndex]&&this.chart.getDatasetMeta(r.datasetIndex).controller.getParsed(r.index)!==void 0);const a=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&a.reverse(),a}_positionChanged(t,e){const{caretX:s,caretY:n,options:o}=this,a=ge[o.position].call(this,t,e);return a!==!1&&(s!==a.x||n!==a.y)}}M(Ni,"positioners",ge);var Dh={id:"tooltip",_element:Ni,positioners:ge,afterInit(i,t,e){e&&(i.tooltip=new Ni({chart:i,options:e}))},beforeUpdate(i,t,e){i.tooltip&&i.tooltip.initialize(e)},reset(i,t,e){i.tooltip&&i.tooltip.initialize(e)},afterDraw(i){const t=i.tooltip;if(t&&t._willRender()){const e={tooltip:t};if(i.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",e)}},afterEvent(i,t){if(i.tooltip){const e=t.replay;i.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,t)=>t.bodyFont.size,boxWidth:(i,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Ro},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>i!=="filter"&&i!=="itemSort"&&i!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},Ch=Object.freeze({__proto__:null,Colors:Hc,Decimation:$c,Filler:hh,Legend:mh,SubTitle:_h,Title:xh,Tooltip:Dh});const Oh=(i,t,e,s)=>(typeof t=="string"?(e=i.push(t)-1,s.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function Ah(i,t,e,s){const n=i.indexOf(t);if(n===-1)return Oh(i,t,e,s);const o=i.lastIndexOf(t);return n!==o?e:n}const Th=(i,t)=>i===null?null:G(Math.round(i),0,t);function Dn(i){const t=this.getLabels();return i>=0&&i<t.length?t[i]:i}class Wi extends Ut{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const s=this.getLabels();for(const{index:n,label:o}of e)s[n]===o&&s.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(R(t))return null;const s=this.getLabels();return e=isFinite(e)&&s[e]===t?e:Ah(s,t,C(e,t),this._addedLabels),Th(e,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:s,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),e||(n=this.getLabels().length-1)),this.min=s,this.max=n}buildTicks(){const t=this.min,e=this.max,s=this.options.offset,n=[];let o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let a=t;a<=e;a++)n.push({value:a});return n}getLabelForValue(t){return Dn.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}M(Wi,"id","category"),M(Wi,"defaults",{ticks:{callback:Dn}});function Lh(i,t){const e=[],{bounds:n,step:o,min:a,max:r,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:u}=i,f=o||1,g=h-1,{min:m,max:p}=t,b=!R(a),x=!R(r),v=!R(c),w=(p-m)/(d+1);let y=ys((p-m)/g/f)*f,k,S,P,D;if(y<1e-14&&!b&&!x)return[{value:m},{value:p}];D=Math.ceil(p/y)-Math.floor(m/y),D>g&&(y=ys(D*y/g/f)*f),R(l)||(k=Math.pow(10,l),y=Math.ceil(y*k)/k),n==="ticks"?(S=Math.floor(m/y)*y,P=Math.ceil(p/y)*y):(S=m,P=p),b&&x&&o&&Pa((r-a)/o,y/1e3)?(D=Math.round(Math.min((r-a)/y,h)),y=(r-a)/D,S=a,P=r):v?(S=b?a:S,P=x?r:P,D=c-1,y=(P-S)/D):(D=(P-S)/y,be(D,Math.round(D),y/1e3)?D=Math.round(D):D=Math.ceil(D));const O=Math.max(vs(y),vs(S));k=Math.pow(10,R(l)?O:l),S=Math.round(S*k)/k,P=Math.round(P*k)/k;let A=0;for(b&&(u&&S!==a?(e.push({value:a}),S<a&&A++,be(Math.round((S+A*y)*k)/k,a,Cn(a,w,i))&&A++):S<a&&A++);A<D;++A){const L=Math.round((S+A*y)*k)/k;if(x&&L>r)break;e.push({value:L})}return x&&u&&P!==r?e.length&&be(e[e.length-1].value,r,Cn(r,w,i))?e[e.length-1].value=r:e.push({value:r}):(!x||P===r)&&e.push({value:P}),e}function Cn(i,t,{horizontal:e,minRotation:s}){const n=ht(s),o=(e?Math.sin(n):Math.cos(n))||.001,a=.75*t*(""+i).length;return Math.min(t/o,a)}class di extends Ut{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return R(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const a=l=>n=e?n:l,r=l=>o=s?o:l;if(t){const l=mt(n),c=mt(o);l<0&&c<0?r(0):l>0&&c>0&&a(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);r(o+l),t||a(n-l)}this.min=n,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:s}=t,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const n={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,a=Lh(n,o);return t.bounds==="ticks"&&jn(a,this,"value"),t.reverse?(a.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),a}configure(){const t=this.ticks;let e=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const n=(s-e)/Math.max(t.length-1,1)/2;e-=n,s+=n}this._startValue=e,this._endValue=s,this._valueRange=s-e}getLabelForValue(t){return Ae(t,this.chart.options.locale,this.options.ticks.format)}}class ji extends di{determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=Y(t)?t:0,this.max=Y(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,s=ht(this.options.ticks.minRotation),n=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}M(ji,"id","linear"),M(ji,"defaults",{ticks:{callback:fi.formatters.numeric}});const De=i=>Math.floor(Pt(i)),Bt=(i,t)=>Math.pow(10,De(i)+t);function On(i){return i/Math.pow(10,De(i))===1}function An(i,t,e){const s=Math.pow(10,e),n=Math.floor(i/s);return Math.ceil(t/s)-n}function Rh(i,t){const e=t-i;let s=De(e);for(;An(i,t,s)>10;)s++;for(;An(i,t,s)<10;)s--;return Math.min(s,De(i))}function Ih(i,{min:t,max:e}){t=ot(i.min,t);const s=[],n=De(t);let o=Rh(t,e),a=o<0?Math.pow(10,Math.abs(o)):1;const r=Math.pow(10,o),l=n>o?Math.pow(10,n):0,c=Math.round((t-l)*a)/a,h=Math.floor((t-l)/r/10)*r*10;let d=Math.floor((c-h)/Math.pow(10,o)),u=ot(i.min,Math.round((l+h+d*Math.pow(10,o))*a)/a);for(;u<e;)s.push({value:u,major:On(u),significand:d}),d>=10?d=d<15?15:20:d++,d>=20&&(o++,d=2,a=o>=0?1:a),u=Math.round((l+h+d*Math.pow(10,o))*a)/a;const f=ot(i.max,u);return s.push({value:f,major:On(f),significand:d}),s}class $i extends Ut{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,e){const s=di.prototype.parse.apply(this,[t,e]);if(s===0){this._zero=!0;return}return Y(s)&&s>0?s:null}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=Y(t)?Math.max(0,t):null,this.max=Y(e)?Math.max(0,e):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!Y(this._userMin)&&(this.min=t===Bt(this.min,0)?Bt(this.min,-1):Bt(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let s=this.min,n=this.max;const o=r=>s=t?s:r,a=r=>n=e?n:r;s===n&&(s<=0?(o(1),a(10)):(o(Bt(s,-1)),a(Bt(n,1)))),s<=0&&o(Bt(n,-1)),n<=0&&a(Bt(s,1)),this.min=s,this.max=n}buildTicks(){const t=this.options,e={min:this._userMin,max:this._userMax},s=Ih(e,this);return t.bounds==="ticks"&&jn(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(t){return t===void 0?"0":Ae(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=Pt(t),this._valueRange=Pt(this.max)-Pt(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(Pt(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}M($i,"id","logarithmic"),M($i,"defaults",{ticks:{callback:fi.formatters.logarithmic,major:{enabled:!0}}});function Yi(i){const t=i.ticks;if(t.display&&i.display){const e=et(t.backdropPadding);return C(t.font&&t.font.size,U.font.size)+e.height}return 0}function Eh(i,t,e){return e=V(e)?e:[e],{w:ja(i,t.string,e),h:e.length*t.lineHeight}}function Tn(i,t,e,s,n){return i===s||i===n?{start:t-e/2,end:t+e/2}:i<s||i>n?{start:t-e,end:t}:{start:t,end:t+e}}function Fh(i){const t={l:i.left+i._padding.left,r:i.right-i._padding.right,t:i.top+i._padding.top,b:i.bottom-i._padding.bottom},e=Object.assign({},t),s=[],n=[],o=i._pointLabels.length,a=i.options.pointLabels,r=a.centerPointLabels?N/o:0;for(let l=0;l<o;l++){const c=a.setContext(i.getPointLabelContext(l));n[l]=c.padding;const h=i.getPointPosition(l,i.drawingArea+n[l],r),d=q(c.font),u=Eh(i.ctx,d,i._pointLabels[l]);s[l]=u;const f=at(i.getIndexAngle(l)+r),g=Math.round(Qi(f)),m=Tn(g,h.x,u.w,0,180),p=Tn(g,h.y,u.h,90,270);zh(e,t,f,m,p)}i.setCenterPoint(t.l-e.l,e.r-t.r,t.t-e.t,e.b-t.b),i._pointLabelItems=Hh(i,s,n)}function zh(i,t,e,s,n){const o=Math.abs(Math.sin(e)),a=Math.abs(Math.cos(e));let r=0,l=0;s.start<t.l?(r=(t.l-s.start)/o,i.l=Math.min(i.l,t.l-r)):s.end>t.r&&(r=(s.end-t.r)/o,i.r=Math.max(i.r,t.r+r)),n.start<t.t?(l=(t.t-n.start)/a,i.t=Math.min(i.t,t.t-l)):n.end>t.b&&(l=(n.end-t.b)/a,i.b=Math.max(i.b,t.b+l))}function Bh(i,t,e){const s=i.drawingArea,{extra:n,additionalAngle:o,padding:a,size:r}=e,l=i.getPointPosition(t,s+n+a,o),c=Math.round(Qi(at(l.angle+K))),h=jh(l.y,r.h,c),d=Nh(c),u=Wh(l.x,r.w,d);return{visible:!0,x:l.x,y:h,textAlign:d,left:u,top:h,right:u+r.w,bottom:h+r.h}}function Vh(i,t){if(!t)return!0;const{left:e,top:s,right:n,bottom:o}=i;return!(kt({x:e,y:s},t)||kt({x:e,y:o},t)||kt({x:n,y:s},t)||kt({x:n,y:o},t))}function Hh(i,t,e){const s=[],n=i._pointLabels.length,o=i.options,{centerPointLabels:a,display:r}=o.pointLabels,l={extra:Yi(o)/2,additionalAngle:a?N/n:0};let c;for(let h=0;h<n;h++){l.padding=e[h],l.size=t[h];const d=Bh(i,h,l);s.push(d),r==="auto"&&(d.visible=Vh(d,c),d.visible&&(c=d))}return s}function Nh(i){return i===0||i===180?"center":i<180?"left":"right"}function Wh(i,t,e){return e==="right"?i-=t:e==="center"&&(i-=t/2),i}function jh(i,t,e){return e===90||e===270?i-=t/2:(e>270||e<90)&&(i-=t),i}function $h(i,t,e){const{left:s,top:n,right:o,bottom:a}=e,{backdropColor:r}=t;if(!R(r)){const l=Wt(t.borderRadius),c=et(t.backdropPadding);i.fillStyle=r;const h=s-c.left,d=n-c.top,u=o-s+c.width,f=a-n+c.height;Object.values(l).some(g=>g!==0)?(i.beginPath(),Se(i,{x:h,y:d,w:u,h:f,radius:l}),i.fill()):i.fillRect(h,d,u,f)}}function Yh(i,t){const{ctx:e,options:{pointLabels:s}}=i;for(let n=t-1;n>=0;n--){const o=i._pointLabelItems[n];if(!o.visible)continue;const a=s.setContext(i.getPointLabelContext(n));$h(e,a,o);const r=q(a.font),{x:l,y:c,textAlign:h}=o;Yt(e,i._pointLabels[n],l,c+r.lineHeight/2,r,{color:a.color,textAlign:h,textBaseline:"middle"})}}function Io(i,t,e,s){const{ctx:n}=i;if(e)n.arc(i.xCenter,i.yCenter,t,0,H);else{let o=i.getPointPosition(0,t);n.moveTo(o.x,o.y);for(let a=1;a<s;a++)o=i.getPointPosition(a,t),n.lineTo(o.x,o.y)}}function Uh(i,t,e,s,n){const o=i.ctx,a=t.circular,{color:r,lineWidth:l}=t;!a&&!s||!r||!l||e<0||(o.save(),o.strokeStyle=r,o.lineWidth=l,o.setLineDash(n.dash),o.lineDashOffset=n.dashOffset,o.beginPath(),Io(i,e,a,s),o.closePath(),o.stroke(),o.restore())}function Xh(i,t,e){return Lt(i,{label:e,index:t,type:"pointLabel"})}class pe extends di{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=et(Yi(this.options)/2),e=this.width=this.maxWidth-t.width,s=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left),this.yCenter=Math.floor(this.top+s/2+t.top),this.drawingArea=Math.floor(Math.min(e,s)/2)}determineDataLimits(){const{min:t,max:e}=this.getMinMax(!1);this.min=Y(t)&&!isNaN(t)?t:0,this.max=Y(e)&&!isNaN(e)?e:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Yi(this.options))}generateTickLabels(t){di.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((e,s)=>{const n=B(this.options.pointLabels.callback,[e,s],this);return n||n===0?n:""}).filter((e,s)=>this.chart.getDataVisibility(s))}fit(){const t=this.options;t.display&&t.pointLabels.display?Fh(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,s,n){this.xCenter+=Math.floor((t-e)/2),this.yCenter+=Math.floor((s-n)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,s,n))}getIndexAngle(t){const e=H/(this._pointLabels.length||1),s=this.options.startAngle||0;return at(t*e+ht(s))}getDistanceFromCenterForValue(t){if(R(t))return NaN;const e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(R(t))return NaN;const e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){const e=this._pointLabels||[];if(t>=0&&t<e.length){const s=e[t];return Xh(this.getContext(),t,s)}}getPointPosition(t,e,s=0){const n=this.getIndexAngle(t)-K+s;return{x:Math.cos(n)*e+this.xCenter,y:Math.sin(n)*e+this.yCenter,angle:n}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:e,top:s,right:n,bottom:o}=this._pointLabelItems[t];return{left:e,top:s,right:n,bottom:o}}drawBackground(){const{backgroundColor:t,grid:{circular:e}}=this.options;if(t){const s=this.ctx;s.save(),s.beginPath(),Io(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length),s.closePath(),s.fillStyle=t,s.fill(),s.restore()}}drawGrid(){const t=this.ctx,e=this.options,{angleLines:s,grid:n,border:o}=e,a=this._pointLabels.length;let r,l,c;if(e.pointLabels.display&&Yh(this,a),n.display&&this.ticks.forEach((h,d)=>{if(d!==0||d===0&&this.min<0){l=this.getDistanceFromCenterForValue(h.value);const u=this.getContext(d),f=n.setContext(u),g=o.setContext(u);Uh(this,f,l,a,g)}}),s.display){for(t.save(),r=a-1;r>=0;r--){const h=s.setContext(this.getPointLabelContext(r)),{color:d,lineWidth:u}=h;!u||!d||(t.lineWidth=u,t.strokeStyle=d,t.setLineDash(h.borderDash),t.lineDashOffset=h.borderDashOffset,l=this.getDistanceFromCenterForValue(e.ticks.reverse?this.min:this.max),c=this.getPointPosition(r,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,e=this.options,s=e.ticks;if(!s.display)return;const n=this.getIndexAngle(0);let o,a;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(n),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((r,l)=>{if(l===0&&this.min>=0&&!e.reverse)return;const c=s.setContext(this.getContext(l)),h=q(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=h.string,a=t.measureText(r.label).width,t.fillStyle=c.backdropColor;const d=et(c.backdropPadding);t.fillRect(-a/2-d.left,-o-h.size/2-d.top,a+d.width,h.size+d.height)}Yt(t,r.label,0,-o,h,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),t.restore()}drawTitle(){}}M(pe,"id","radialLinear"),M(pe,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:fi.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}}),M(pe,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),M(pe,"descriptors",{angleLines:{_fallback:"grid"}});const bi={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},st=Object.keys(bi);function Ln(i,t){return i-t}function Rn(i,t){if(R(t))return null;const e=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts;let a=t;return typeof s=="function"&&(a=s(a)),Y(a)||(a=typeof s=="string"?e.parse(a,s):e.parse(a)),a===null?null:(n&&(a=n==="week"&&(Jt(o)||o===!0)?e.startOf(a,"isoWeek",o):e.startOf(a,n)),+a)}function In(i,t,e,s){const n=st.length;for(let o=st.indexOf(i);o<n-1;++o){const a=bi[st[o]],r=a.steps?a.steps:Number.MAX_SAFE_INTEGER;if(a.common&&Math.ceil((e-t)/(r*a.size))<=s)return st[o]}return st[n-1]}function Kh(i,t,e,s,n){for(let o=st.length-1;o>=st.indexOf(e);o--){const a=st[o];if(bi[a].common&&i._adapter.diff(n,s,a)>=t-1)return a}return st[e?st.indexOf(e):0]}function qh(i){for(let t=st.indexOf(i)+1,e=st.length;t<e;++t)if(bi[st[t]].common)return st[t]}function En(i,t,e){if(!e)i[t]=!0;else if(e.length){const{lo:s,hi:n}=ts(e,t),o=e[s]>=t?e[s]:e[n];i[o]=!0}}function Gh(i,t,e,s){const n=i._adapter,o=+n.startOf(t[0].value,s),a=t[t.length-1].value;let r,l;for(r=o;r<=a;r=+n.add(r,1,s))l=e[r],l>=0&&(t[l].major=!0);return t}function Fn(i,t,e){const s=[],n={},o=t.length;let a,r;for(a=0;a<o;++a)r=t[a],n[r]=a,s.push({value:r,major:!1});return o===0||!e?s:Gh(i,s,n,e)}class Ce extends Ut{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const s=t.time||(t.time={}),n=this._adapter=new ol._date(t.adapters.date);n.init(e),me(s.displayFormats,n.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:Rn(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,s=t.time.unit||"day";let{min:n,max:o,minDefined:a,maxDefined:r}=this.getUserBounds();function l(c){!a&&!isNaN(c.min)&&(n=Math.min(n,c.min)),!r&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!a||!r)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=Y(n)&&!isNaN(n)?n:+e.startOf(Date.now(),s),o=Y(o)&&!isNaN(o)?o:+e.endOf(Date.now(),s)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],s=t[t.length-1]),{min:e,max:s}}buildTicks(){const t=this.options,e=t.time,s=t.ticks,n=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const o=this.min,a=this.max,r=Aa(n,o,a);return this._unit=e.unit||(s.autoSkip?In(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):Kh(this,r.length,e.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:qh(this._unit),this.initOffsets(n),t.reverse&&r.reverse(),Fn(this,r,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,s=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const a=t.length<3?.5:.25;e=G(e,0,a),s=G(s,0,a),this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){const t=this._adapter,e=this.min,s=this.max,n=this.options,o=n.time,a=o.unit||In(o.minUnit,e,s,this._getLabelCapacity(e)),r=C(n.ticks.stepSize,1),l=a==="week"?o.isoWeekday:!1,c=Jt(l)||l===!0,h={};let d=e,u,f;if(c&&(d=+t.startOf(d,"isoWeek",l)),d=+t.startOf(d,c?"day":a),t.diff(s,e,a)>1e5*r)throw new Error(e+" and "+s+" are too far apart with stepSize of "+r+" "+a);const g=n.ticks.source==="data"&&this.getDataTimestamps();for(u=d,f=0;u<s;u=+t.add(u,r,a),f++)En(h,u,g);return(u===s||n.bounds==="ticks"||f===1)&&En(h,u,g),Object.keys(h).sort(Ln).map(m=>+m)}getLabelForValue(t){const e=this._adapter,s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){const n=this.options.time.displayFormats,o=this._unit,a=e||n[o];return this._adapter.format(t,a)}_tickFormatFunction(t,e,s,n){const o=this.options,a=o.ticks.callback;if(a)return B(a,[t,e,s],this);const r=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&r[l],d=c&&r[c],u=s[e],f=c&&d&&u&&u.major;return this._adapter.format(t,n||(f?d:h))}generateTickLabels(t){let e,s,n;for(e=0,s=t.length;e<s;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,s=this.ctx.measureText(t).width,n=ht(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),a=Math.sin(n),r=this._resolveTickFontOptions(0).size;return{w:s*o+r*a,h:s*a+r*o}}_getLabelCapacity(t){const e=this.options.time,s=e.displayFormats,n=s[e.unit]||s.millisecond,o=this._tickFormatFunction(t,0,Fn(this,[t],this._majorUnit),n),a=this._getLabelSize(o),r=Math.floor(this.isHorizontal()?this.width/a.w:this.height/a.h)-1;return r>0?r:1}getDataTimestamps(){let t=this._cache.data||[],e,s;if(t.length)return t;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,s=n.length;e<s;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,s;if(t.length)return t;const n=this.getLabels();for(e=0,s=n.length;e<s;++e)t.push(Rn(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Un(t.sort(Ln))}}M(Ce,"id","time"),M(Ce,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function Ke(i,t,e){let s=0,n=i.length-1,o,a,r,l;e?(t>=i[s].pos&&t<=i[n].pos&&({lo:s,hi:n}=Mt(i,"pos",t)),{pos:o,time:r}=i[s],{pos:a,time:l}=i[n]):(t>=i[s].time&&t<=i[n].time&&({lo:s,hi:n}=Mt(i,"time",t)),{time:o,pos:r}=i[s],{time:a,pos:l}=i[n]);const c=a-o;return c?r+(l-r)*(t-o)/c:r}class Ui extends Ce{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=Ke(e,this.min),this._tableRange=Ke(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:s}=this,n=[],o=[];let a,r,l,c,h;for(a=0,r=t.length;a<r;++a)c=t[a],c>=e&&c<=s&&n.push(c);if(n.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(a=0,r=n.length;a<r;++a)h=n[a+1],l=n[a-1],c=n[a],Math.round((h+l)/2)!==c&&o.push({time:c,pos:a/(r-1)});return o}_generate(){const t=this.min,e=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(e)||s.length===1)&&s.push(e),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),s=this.getLabelTimestamps();return e.length&&s.length?t=this.normalize(e.concat(s)):t=e.length?e:s,t=this._cache.all=t,t}getDecimalForValue(t){return(Ke(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return Ke(this._table,s*this._tableRange+this._minPos,!0)}}M(Ui,"id","timeseries"),M(Ui,"defaults",Ce.defaults);var Zh=Object.freeze({__proto__:null,CategoryScale:Wi,LinearScale:ji,LogarithmicScale:$i,RadialLinearScale:pe,TimeScale:Ce,TimeSeriesScale:Ui});const us=[nl,Ic,Ch,Zh],Jh={class:"bg-white p-4 cursor-pointer hover:shadow-md shadow rounded-lg"},zn={__name:"BarChart",props:{chartData:{type:Object,required:!0},chartOptions:{type:Object,default:()=>{}}},setup(i){const t=i;rt.register(...us);const e=ui(null);let s=null;const n=()=>{s&&s.destroy(),s=new rt(e.value,{type:"bar",data:t.chartData,options:t.chartOptions})};return Xi(()=>{n()}),Ki(()=>t.chartData,o=>{n()}),(o,a)=>(j(),$("div",Jh,[_("canvas",{ref_key:"chartCanvas",ref:e},null,512)]))}},Qh={class:"bg-white p-4 cursor-pointer hover:shadow-md shadow rounded-lg"},td={__name:"PieChart",props:{chartData:{type:Object,required:!0},chartOptions:{type:Object,default:()=>{}}},setup(i){const t=i;rt.register(...us);const e=ui(null);let s=null;const n=()=>{s&&s.destroy(),s=new rt(e.value,{type:"pie",data:t.chartData,options:t.chartOptions})};return Xi(()=>{n()}),Ki(()=>t.chartData,o=>{n()}),(o,a)=>(j(),$("div",Qh,[_("canvas",{ref_key:"chartCanvas",ref:e},null,512)]))}},ed={class:"bg-white p-4 cursor-pointer hover:shadow-md shadow rounded-lg"},id={__name:"MultiLineChart",props:{chartData:{type:Object,required:!0},chartOptions:{type:Object,default:()=>({})}},setup(i){const t=i;rt.register(...us);const e=ui(null);let s=null;const n=()=>{s&&s.destroy(),s=new rt(e.value,{type:"line",data:t.chartData,options:{...t.chartOptions,responsive:!0,interaction:{mode:"index",intersect:!1},plugins:{tooltip:{mode:"index",intersect:!1,callbacks:{label:function(o){const a=o.parsed.y||0;return`${o.dataset.label}: ₹${a.toLocaleString("en-IN")}`}}},legend:{position:"top"},title:{display:!0,text:"Engineer Sales (April to March)"}},scales:{y:{beginAtZero:!0}}}})};return Xi(()=>{n()}),Ki(()=>t.chartData,()=>{n()}),(o,a)=>(j(),$("div",ed,[_("canvas",{ref_key:"chartCanvas",ref:e},null,512)]))}};const I=i=>($o("data-v-a1a4edf0"),i=i(),Yo(),i),sd=I(()=>_("h2",{class:"font-semibold text-xl text-gray-700 leading-tight"}," Dashboard ",-1)),nd={class:"animate-top"},od={class:"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-6"},ad=I(()=>_("div",{class:"items-start"},[_("h1",{class:"text-xl lg:text-2xl font-semibold leading-7 text-gray-900"},"Dashboard")],-1)),rd={class:"flex justify-end"},ld={class:"relative w-full lg:w-80"},cd={class:"mt-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"},hd={class:"w-full cursor-pointer hover:shadow-md shadow rounded-lg"},dd={class:"w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6"},ud=I(()=>_("svg",{class:"w-12 h-12 lg:w-16 lg:h-16 fill-current mr-4 flex-shrink-0",viewBox:"0 0 24 24"},[_("circle",{cx:"6",cy:"8",r:"3"}),_("path",{d:"M2 18v-1.5c0-1.8 2.5-3.5 4.5-3.5 1 0 2 .2 2.8.6-1.1.8-1.8 2.1-1.8 3.4V18H2z"}),_("circle",{cx:"12",cy:"7",r:"4"}),_("path",{d:"M12 12c-3.5 0-7 1.8-7 4v2h14v-2c0-2.2-3.5-4-7-4z"}),_("circle",{cx:"18",cy:"8",r:"3"}),_("path",{d:"M22 18v-1.5c0-1.8-2.5-3.5-4.5-3.5-1 0-2 .2-2.8.6 1.1.8 1.8 2.1 1.8 3.4V18H22z"})],-1)),fd={class:"text-gray-700 min-w-0"},gd={class:"font-semibold text-2xl lg:text-3xl"},pd=I(()=>_("p",{class:"text-sm lg:text-base"},"Total Customers",-1)),md={class:"w-full cursor-pointer hover:shadow-md shadow rounded-lg"},bd={class:"w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6"},xd=I(()=>_("svg",{class:"w-12 h-12 lg:w-16 lg:h-16 fill-current mr-4 flex-shrink-0",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[_("rect",{x:"3",y:"6",width:"18",height:"12",rx:"2",ry:"2",stroke:"currentColor",fill:"none"}),_("path",{d:"M6 10h12M6 14h12",stroke:"currentColor"}),_("path",{d:"M9 6V4h6v2M10 18v2h4v-2",stroke:"currentColor"})],-1)),_d={class:"text-gray-700 min-w-0"},yd={class:"font-semibold text-2xl lg:text-3xl"},vd=I(()=>_("p",{class:"text-sm lg:text-base"},"Total Products",-1)),wd={class:"w-full cursor-pointer hover:shadow-md shadow rounded-lg"},Md={class:"w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6"},kd=I(()=>_("svg",{class:"w-12 h-12 lg:w-16 lg:h-16 fill-current mr-4 flex-shrink-0",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[_("path",{d:"M3 21v-13c0-1.1.9-2 2-2h5V4c0-1.1.9-2 2-2h6c1.1 0 2 .9 2 2v17h2v2H1v-2h2zm2 0h4v-3h2v3h4v-5H5v5zm10-10h2v2h-2v-2zm0-3h2v2h-2v-2zm0-3h2v2h-2v-2z"})],-1)),Sd={class:"text-gray-700 min-w-0"},Pd={class:"font-semibold text-2xl lg:text-3xl"},Dd=I(()=>_("p",{class:"text-sm lg:text-base"},"Total Companies",-1)),Cd={class:"w-full cursor-pointer hover:shadow-md shadow rounded-lg"},Od={class:"w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6"},Ad=I(()=>_("svg",{class:"w-12 h-12 lg:w-16 lg:h-16 fill-current mr-4 flex-shrink-0",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[_("path",{d:"M6 2c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6H6zm7 7V3.5L18.5 9H13zM8 12h8v2H8v-2zm0 4h5v2H8v-2zm0-8h8v2H8V8z"})],-1)),Td={class:"text-gray-700 min-w-0"},Ld={class:"font-semibold text-2xl lg:text-3xl"},Rd=I(()=>_("p",{class:"text-sm lg:text-base"},"Total Quotations",-1)),Id={class:"w-full cursor-pointer hover:shadow-md shadow rounded-lg"},Ed={class:"w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6"},Fd=I(()=>_("svg",{class:"w-12 h-12 lg:w-16 lg:h-14 fill-current mr-4 flex-shrink-0",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[_("path",{d:"M3 3h2l1.6 6.6 1.4 5.4c.2.8.9 1.4 1.7 1.4h7c.8 0 1.5-.6 1.7-1.4l1.6-6.6H7.2l-.5-2H21V5H6.2L5.8 3H3zm5 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM9 10h8v2H9v-2zm0 4h5v2H9v-2z"})],-1)),zd={class:"text-gray-700 min-w-0"},Bd={class:"font-semibold text-2xl lg:text-3xl"},Vd=I(()=>_("p",{class:"text-sm lg:text-base"},"Total Orders",-1)),Hd={class:"w-full cursor-pointer hover:shadow-md shadow rounded-lg"},Nd={class:"w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6"},Wd=I(()=>_("svg",{class:"w-12 h-12 lg:w-16 lg:h-14 fill-current mr-4 flex-shrink-0",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[_("path",{d:"M6 2C5.447 2 5 2.447 5 3V21C5 21.553 5.447 22 6 22H18C18.553 22 19 21.553 19 21V3C19 2.447 18.553 2 18 2H6ZM7 4H17V20H7V4ZM9 7H15V9H9V7ZM9 11H15V13H9V11ZM9 15H15V17H9V15Z"})],-1)),jd={class:"text-gray-700 min-w-0"},$d={class:"font-semibold text-2xl lg:text-3xl"},Yd=I(()=>_("p",{class:"text-sm lg:text-base"},"Tax Invoice",-1)),Ud={class:"w-full cursor-pointer hover:shadow-md shadow rounded-lg"},Xd={class:"w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6"},Kd=I(()=>_("svg",{class:"w-12 h-12 lg:w-16 lg:h-14 fill-current mr-4 flex-shrink-0",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[_("path",{d:"M6 2C5.447 2 5 2.447 5 3V21C5 21.553 5.447 22 6 22H18C18.553 22 19 21.553 19 21V3C19 2.447 18.553 2 18 2H6ZM7 4H17V20H7V4ZM9 7H15V9H9V7ZM9 11H15V13H9V11ZM9 15H15V17H9V15Z"})],-1)),qd={class:"text-gray-700 min-w-0"},Gd={class:"font-semibold text-2xl lg:text-3xl"},Zd=I(()=>_("p",{class:"text-sm lg:text-base"},"Retail Invoice",-1)),Jd={class:"w-full cursor-pointer hover:shadow-md shadow rounded-lg"},Qd={class:"w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6"},tu=I(()=>_("svg",{class:"w-12 h-12 lg:w-16 lg:h-14 fill-current mr-4 flex-shrink-0",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[_("path",{d:"M12 1a4 4 0 0 0-4 4v2H5a2 2 0 0 0-2 2v11a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3V5a4 4 0 0 0-4-4zm-2 4a2 2 0 1 1 4 0v2h-4V5zm2 7.5c-1.4 0-2.5.9-2.5 2h1.5c0-.5.5-1 1-1s1 .5 1 1c0 .5-.3.7-.8 1l-.2.1c-.6.3-1.3.9-1.3 1.9h1.5c0-.5.3-.7.8-1l.2-.1c.6-.4 1.3-.9 1.3-1.9 0-1.2-1.1-2-2.5-2zM11 18v1h2v-1h-2z"})],-1)),eu={class:"text-gray-700 min-w-0"},iu={class:"font-semibold text-2xl lg:text-3xl"},su=I(()=>_("p",{class:"text-sm lg:text-base"},"Total Purchase",-1)),nu={class:"w-full cursor-pointer hover:shadow-md shadow rounded-lg"},ou={class:"w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6"},au=I(()=>_("svg",{class:"w-12 h-12 lg:w-16 lg:h-14 fill-current mr-4 flex-shrink-0",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[_("path",{d:"M3 5c0-1.1.9-2 2-2h14c1.1 0 2 .9 2 2v14c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2V5zm2 0v14h14V5H5zm2 4h10v2H7V9zm0 4h6v2H7v-2z"})],-1)),ru={class:"text-gray-700 min-w-0"},lu={class:"font-semibold text-2xl lg:text-3xl"},cu=I(()=>_("p",{class:"text-sm lg:text-base"},"Total Jobcard",-1)),hu={key:0,class:"mt-6 w-full"},du={key:1,class:"mt-6 flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6"},uu={class:"w-full lg:w-2/3"},fu={class:"space-y-6"},gu=I(()=>_("p",{class:"text-lg lg:text-xl font-semibold mb-4 lg:mb-6"}," Sales ",-1)),pu=I(()=>_("p",{class:"text-lg lg:text-xl font-semibold mb-4 lg:mb-6"}," Purchase ",-1)),mu={class:"w-full lg:w-1/3"},bu=I(()=>_("p",{class:"text-lg lg:text-xl font-semibold mb-4 lg:mb-6"}," Sales By Persons ",-1)),xu={class:"grid grid-cols-1 lg:grid-cols-3 gap-6 my-6"},_u={class:"w-full cursor-pointer"},yu=I(()=>_("p",{class:"text-xl font-semibold mb-4"}," Recent Quotations ",-1)),vu={key:0,class:"w-full bg-red-50 border rounded-lg p-4 items-center"},wu=I(()=>_("p",{class:"text-sm font-semibold text-red-500"}," No Records Found ",-1)),Mu=[wu],ku={key:1,class:"w-full bg-white border rounded-lg p-4 overflow-y-auto hover:shadow-md shadow rounded-lg",style:{"max-height":"420px"}},Su={class:"text-sm font-semibold"},Pu={class:"text-sm font-semibold text-gray-500"},Du={class:"font-semibold text-base text-green-600"},Cu={class:"w-full cursor-pointer"},Ou=I(()=>_("p",{class:"text-xl font-semibold mb-4"}," Recent Purchase ",-1)),Au={key:0,class:"w-full bg-red-50 border rounded-lg p-4 items-center"},Tu=I(()=>_("p",{class:"text-sm font-semibold text-red-500"}," No Records Found ",-1)),Lu=[Tu],Ru={key:1,class:"w-full bg-white border rounded-lg p-4 overflow-y-auto hover:shadow-md shadow rounded-lg",style:{"max-height":"420px"}},Iu={class:"text-sm font-semibold"},Eu={class:"text-sm font-semibold text-gray-500"},Fu={class:"font-semibold text-base text-green-600"},zu={class:"w-full cursor-pointer"},Bu=I(()=>_("p",{class:"text-xl font-semibold mb-4"}," Recent Invoices ",-1)),Vu={key:0,class:"w-full bg-red-50 border rounded-lg p-4 items-center"},Hu=I(()=>_("p",{class:"text-sm font-semibold text-red-500"}," No Records Found ",-1)),Nu=[Hu],Wu={key:1,class:"w-full bg-white border rounded-lg p-4 overflow-y-auto hover:shadow-md shadow rounded-lg",style:{"max-height":"420px"}},ju={class:"text-sm font-semibold"},$u={class:"text-sm font-semibold text-gray-500"},Yu={class:"font-semibold text-base text-green-600"},Uu=I(()=>_("p",{class:"text-xl font-semibold my-4"}," Low Stock Products ",-1)),Xu={class:"mt-6 overflow-x-auto sm:rounded-lg"},Ku={class:"shadow sm:rounded-lg"},qu={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Gu=I(()=>_("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[_("tr",{class:"border-b-2"},[_("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"PRODUCT NAME"),_("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"COMPANY"),_("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"STOCK")])],-1)),Zu={key:0,class:""},Ju={class:"whitespace-nowrap px-4 py-2.5 font-medium text-gray-900"},Qu={class:"whitespace-nowrap px-4 py-2.5"},tf={class:"whitespace-nowrap px-4 py-2.5"},ef={key:1},sf=I(()=>_("tr",{class:"bg-white"},[_("td",{colspan:"6",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),nf=[sf],of={__name:"Dashboard",props:["organizationId","permissions","organization","user","recentInvoices","date","customerCount","ordersCount","purchaseCount","jobCardCount","productCount","taxInvoiceCount","retailInvoiceCount","companyCount","quotationCount","customer_id","recentQuotations","recentPurchaseOrder","productsWithLowStock","pieChartData","salesDataByMonth","purchaseDataByMonth","salesEngdatasets"],setup(i){const t=i,e=Vo({}),s=Re(()=>({labels:["Apr","May","June","Jul","Aug","Sep","Oct","Nov","Dec","Jan","Feb","Mar"],datasets:[{label:"Sales",data:t.salesDataByMonth,backgroundColor:["rgba(255, 99, 132, 1)","rgba(54, 162, 235, 1)","rgba(255, 206, 86, 1)","rgba(75, 192, 192, 1)","rgba(153, 102, 255, 1)","rgba(201, 203, 207, 1)","rgba(255, 159, 64, 1)","rgba(100, 149, 237, 1)","rgba(255, 105, 180, 1)","rgba(60, 179, 113, 1)","rgba(147, 112, 219, 1)","rgba(0, 191, 255, 1)","rgba(220, 20, 60, 1)","rgba(255, 140, 0, 1)","rgba(70, 130, 180, 1)","rgba(199, 21, 133, 1)"]}]})),n=Re(()=>({labels:["Apr","May","June","Jul","Aug","Sep","Oct","Nov","Dec","Jan","Feb","Mar"],datasets:[{label:"Purchase",data:t.purchaseDataByMonth,backgroundColor:["rgba(255, 99, 132, 1)","rgba(54, 162, 235, 1)","rgba(255, 206, 86, 1)","rgba(75, 192, 192, 1)","rgba(153, 102, 255, 1)","rgba(201, 203, 207, 1)","rgba(255, 159, 64, 1)","rgba(100, 149, 237, 1)","rgba(255, 105, 180, 1)","rgba(60, 179, 113, 1)","rgba(147, 112, 219, 1)","rgba(0, 191, 255, 1)","rgba(220, 20, 60, 1)","rgba(255, 140, 0, 1)","rgba(70, 130, 180, 1)","rgba(199, 21, 133, 1)"]}]})),o={responsive:!0,scales:{x:{beginAtZero:!0},y:{beginAtZero:!0}},plugins:{legend:{position:"top"},tooltip:{callbacks:{label:g=>{let m=g.dataset.label||"";return m&&(m+=": "),g.parsed.y!==null&&(m+=new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",currencyDisplay:"symbol"}).format(g.parsed.y)),m}}}}},a=Re(()=>({labels:t.pieChartData.labels,datasets:[{label:"Votes",data:t.pieChartData.datasets[0].data,backgroundColor:["rgba(255, 99, 132, 1)","rgba(54, 162, 235, 1)","rgba(255, 206, 86, 1)","rgba(75, 192, 192, 1)","rgba(153, 102, 255, 1)","rgba(201, 203, 207, 1)","rgba(255, 159, 64, 1)","rgba(100, 149, 237, 1)","rgba(255, 105, 180, 1)","rgba(60, 179, 113, 1)","rgba(147, 112, 219, 1)","rgba(0, 191, 255, 1)","rgba(220, 20, 60, 1)","rgba(255, 140, 0, 1)","rgba(70, 130, 180, 1)","rgba(199, 21, 133, 1)"],borderWidth:1}]})),r={responsive:!0,plugins:{legend:{position:"top"},tooltip:{callbacks:{label:g=>Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",currencyDisplay:"symbol"}).format(g.raw)}}}},l=Re(()=>({labels:["Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec","Jan","Feb","Mar"],datasets:t.salesEngdatasets}));Ho(async()=>{localStorage.setItem("permissions",JSON.stringify(t.permissions))});const c=ui(t.organizationId),h=g=>{e.get(route("dashboard",{organization_id:g}),{preserveState:!0})},d=(g,m)=>{c.value=g,h(c.value)},u=g=>{const m=new Date(g),p={year:"numeric",month:"short",day:"numeric"};return m.toLocaleDateString("en-US",p)},f=g=>{let m=g.toFixed(2).toString(),[p,b]=m.split("."),x=p.substring(p.length-3),v=p.substring(0,p.length-3);return v!==""&&(x=","+x),`${v.replace(/\B(?=(\d{2})+(?!\d))/g,",")+x}.${b}`};return(g,m)=>(j(),$(ne,null,[ft(No(Wo),{title:"Dashboard"}),ft(zo,null,{header:se(()=>[sd]),default:se(()=>[_("div",nd,[_("div",od,[ad,_("div",rd,[_("div",ld,[ft(Bo,{options:i.organization,modelValue:c.value,"onUpdate:modelValue":m[0]||(m[0]=p=>c.value=p),onOnchange:d},null,8,["options","modelValue"])])])]),_("div",cd,[_("div",hd,[_("div",dd,[ud,_("div",fd,[_("p",gd,z(i.customerCount),1),pd])])]),_("div",md,[_("div",bd,[xd,_("div",_d,[_("p",yd,z(i.productCount),1),vd])])]),_("div",wd,[_("div",Md,[kd,_("div",Sd,[_("p",Pd,z(i.companyCount),1),Dd])])]),_("div",Cd,[_("div",Od,[Ad,_("div",Td,[_("p",Ld,z(i.quotationCount),1),Rd])])]),_("div",Id,[_("div",Ed,[Fd,_("div",zd,[_("p",Bd,z(i.ordersCount),1),Vd])])]),_("div",Hd,[_("div",Nd,[Wd,_("div",jd,[_("p",$d,z(i.taxInvoiceCount),1),Yd])])]),_("div",Ud,[_("div",Xd,[Kd,_("div",qd,[_("p",Gd,z(i.retailInvoiceCount),1),Zd])])]),_("div",Jd,[_("div",Qd,[tu,_("div",eu,[_("p",iu,z(i.purchaseCount),1),su])])]),_("div",nu,[_("div",ou,[au,_("div",ru,[_("p",lu,z(i.jobCardCount),1),cu])])])]),i.user.role_id==1?(j(),$("div",hu,[ft(id,{chartData:l.value},null,8,["chartData"])])):bt("",!0),i.user.role_id==1?(j(),$("div",du,[_("div",uu,[_("div",fu,[_("div",null,[gu,ft(zn,{chartData:s.value,chartOptions:o},null,8,["chartData"])]),_("div",null,[pu,ft(zn,{chartData:n.value,chartOptions:o},null,8,["chartData"])])])]),_("div",mu,[_("div",null,[bu,ft(td,{chartData:a.value,chartOptions:r},null,8,["chartData"])])])])):bt("",!0),_("div",xu,[_("div",_u,[yu,i.recentQuotations.length===0?(j(),$("div",vu,Mu)):bt("",!0),i.recentQuotations.length>0?(j(),$("div",ku,[(j(!0),$(ne,null,Ie(i.recentQuotations,p=>(j(),$("div",{key:p.id,class:"w-full bg-gray-50 border rounded-lg flex justify-between items-center px-4 py-2 mb-4 hover:bg-gray-100"},[ft(xi,{href:g.route("quotation.view",{id:p.id,source:"dashboard"})},{text:se(()=>{var b;return[_("div",null,[_("p",Su,z(((b=p==null?void 0:p.customers)==null?void 0:b.customer_name)??""),1),_("p",Pu,z(p.quotation_number)+" - "+z(u(p.date)),1),_("p",Du," ₹"+z(f(p.total_amount)),1)])]}),_:2},1032,["href"])]))),128))])):bt("",!0)]),_("div",Cu,[Ou,i.recentPurchaseOrder.length===0?(j(),$("div",Au,Lu)):bt("",!0),i.recentPurchaseOrder.length>0?(j(),$("div",Ru,[(j(!0),$(ne,null,Ie(i.recentPurchaseOrder,p=>(j(),$("div",{key:p.id,class:"w-full bg-gray-50 border rounded-lg flex justify-between items-center px-4 py-2 mb-4 hover:bg-gray-100"},[ft(xi,{href:g.route("purchaseinvoice.view",{id:p.id,source:"dashboard"})},{text:se(()=>{var b,x;return[_("div",null,[_("p",Iu,z(p?(x=(b=p.purchase_order)==null?void 0:b.company)==null?void 0:x.name:""),1),_("p",Eu,z(p.customer_invoice_no)+" - "+z(u(p.customer_invoice_date)),1),_("p",Fu," ₹"+z(f(p.total_amount)),1)])]}),_:2},1032,["href"])]))),128))])):bt("",!0)]),_("div",zu,[Bu,i.recentInvoices.length===0?(j(),$("div",Vu,Nu)):bt("",!0),i.recentInvoices.length>0?(j(),$("div",Wu,[(j(!0),$(ne,null,Ie(i.recentInvoices,p=>(j(),$("div",{key:p.id,class:"w-full bg-gray-50 border rounded-lg flex justify-between items-center px-4 py-2 mb-4 hover:bg-gray-100"},[ft(xi,{href:g.route("invoice.view",{id:p.id,source:"dashboard"})},{text:se(()=>{var b;return[_("div",null,[_("p",ju,z(((b=p==null?void 0:p.customers)==null?void 0:b.customer_name)??""),1),_("p",$u,z(p.invoice_no)+" - "+z(u(p.date)),1),_("p",Yu," ₹"+z(f(p.total_amount)),1)])]}),_:2},1032,["href"])]))),128))])):bt("",!0)])]),Uu,_("div",Xu,[_("div",Ku,[_("table",qu,[Gu,i.productsWithLowStock.data&&i.productsWithLowStock.data.length>0?(j(),$("tbody",Zu,[(j(!0),$(ne,null,Ie(i.productsWithLowStock.data,p=>(j(),$("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:p.id},[_("td",Ju,z(p.item_code)+" "+z(p.item_code?p.item_code+"  : ":"")+" "+z(p.name??""),1),_("td",Qu,z(p.company_name),1),_("td",tf,z(p.stock),1)]))),128))])):(j(),$("tbody",ef,nf))])]),i.productsWithLowStock.data&&i.productsWithLowStock.data.length>0?(j(),jo(Uo,{key:0,class:"mt-6",links:i.productsWithLowStock.links},null,8,["links"])):bt("",!0)])])]),_:1})],64))}},uf=Xo(of,[["__scopeId","data-v-a1a4edf0"]]);export{uf as default};
