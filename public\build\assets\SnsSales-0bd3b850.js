import{K as H,r as _,l as Z,o as n,c as p,a as d,u as f,w as V,F as k,Z as J,b as e,g as A,k as T,v as D,n as z,i as M,e as I,f as O,t as u}from"./app-03250c83.js";import{_ as W}from"./AdminLayout-a6b1643c.js";import{_ as ee}from"./CreateButton-65a913fd.js";/* empty css                                                              */import{_ as te}from"./Pagination-cf15a66c.js";import{_ as se}from"./SimpleDropdown-211221b3.js";import{_ as w}from"./InputLabel-28ecec2a.js";import{_ as oe}from"./SearchableDropdown-517e9849.js";import{_ as ae}from"./ArrowIcon-d90ea530.js";import{s as le}from"./sortAndSearch-18ed650c.js";const ne={class:"animate-top"},re={class:"flex justify-between items-center"},ie=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"SNS Sales Report")],-1),de={class:"flex mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},ce={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},me={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},pe=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),ue={class:"flex ml-6"},ge={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},_e={class:"flex justify-between mb-2"},he={class:"flex"},ve=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),fe={class:"inline-flex items-center space-x-4 justify-end w-full"},ye=["src"],xe={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center"},be={class:"sm:col-span-3"},we={class:"relative mt-2"},ke={class:"sm:col-span-3"},Se={class:"relative mt-2"},Ce={class:"sm:col-span-3"},Ne={class:"sm:col-span-3"},ze={class:"mt-8 overflow-x-auto sm:rounded-lg"},Me={class:"shadow sm:rounded-lg"},Oe={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Ee={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Ve={class:"border-b-2 grid grid-cols-12 gap-x-2"},Ae=["onClick"],Te={key:0,class:"divide-y divide-gray-300 bg-white"},De={class:"sm:col-span-2 px-3 py-3 text-sm text-gray-500"},Ie={class:"sm:col-span-4 px-3 py-3 text-sm text-gray-900"},$e={class:"sm:col-span-3 px-3 py-3 text-sm text-gray-500"},Le={class:"sm:col-span-2 px-1 py-3 text-sm text-gray-500"},Ue={class:"sm:col-span-1 px-3 py-3 text-sm text-gray-500"},Be={class:"flex items-center justify-start gap-4"},Re=["onClick"],je=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),qe=[je],Fe={key:0,class:"divide-y divide-gray-300 sm:col-span-12 product-details border mx-6 mb-4"},Pe=e("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-5 bg-gray-50"},[e("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"INVOICE NO"),e("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"DATE"),e("th",{scope:"col",class:"py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"CUSTOMER NAME"),e("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"QTY")],-1),Ye={class:"divide-y divide-gray-300 bg-white grid grid-cols-1"},Ge={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},Ke={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},Qe={class:"py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6"},Xe={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},He={key:1},Ze=e("tr",{class:"bg-white"},[e("td",{colspan:"8",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Je=[Ze],mt={__name:"SnsSales",props:["data","organization","search","company","organizationId","companyId"],setup(c){const h=c,{form:S,search:We,sort:$,fetchData:et,sortKey:L,sortDirection:U,updateParams:B}=le("sns-sales.report",{organization_id:h.organizationId,company_id:h.companyId,from_date:h.from_date,to_date:h.to_date});H().props.data.links.find(s=>s.active===!0);const a=_(h.organizationId),l=_(h.companyId),E=_("ALL COMPANY"),r=_(""),i=_(""),y=_("");Z([a,l,r,i],()=>{B({organization_id:a.value,conpany_id:l.value,from_date:r.value,to_date:i.value})});const R=[{field:"item_code",label:"PRODUCT CODE",sortable:!0,colSpan:"col-span-2"},{field:"name",label:"PRODUCT NAME",sortable:!0,colSpan:"col-span-4"},{field:"company.name",label:"COMPANY",sortable:!0,colSpan:"col-span-3"},{field:"min_qty",label:"SALES QTY",sortable:!0,colSpan:"col-span-2"},{field:"details",label:"SALES DETAILS",sortable:!1,colSpan:"col-span-1"}],x=(s,o,t,g,m)=>{y.value=s,S.get(route("sns-sales.report",{search:s,organization_id:o,company_id:t,from_date:g,to_date:m}),{preserveState:!0})},j=(s,o)=>{a.value=s,x(y.value,a.value,l.value,r.value,i.value)},q=(s,o)=>{l.value=s,E.value=o,x(y.value,a.value,l.value,r.value,i.value)},C=_([]),F=s=>{C.value[s]=!C.value[s]},P=s=>{const o=new Date(s),t={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",t)},Y=()=>{let s="";switch(a.value){case 1:s="MC";break;case 2:s="HC";break;case 3:s="NOX";break;default:s="All_Organizations";break}const o=E.value.replace(/\s+/g,"_"),t=`SNS_Salse_Report_${s}_${o}`,g={organization_id:a.value,company_id:l.value,from_date:r.value,to_date:i.value},N=`/export-sns-sales-report?${new URLSearchParams(g).toString()}`;fetch(N,{method:"GET"}).then(v=>{if(!v.ok)throw new Error("Network response was not ok");return v.blob()}).then(v=>{const X=window.URL.createObjectURL(new Blob([v])),b=document.createElement("a");b.href=X,b.setAttribute("download",t+".xlsx"),document.body.appendChild(b),b.click(),document.body.removeChild(b)}).catch(v=>{console.error("Error exporting data:",v)})},G=()=>{x(y.value,a.value,l.value,r.value,i.value)},K=()=>{x(y.value,a.value,l.value,r.value,i.value)},Q=s=>s&&s.length>0?s.reduce((o,t)=>o+t.qty,0):"-";return(s,o)=>(n(),p(k,null,[d(f(J),{title:" Sns Sales Report"}),d(W,null,{default:V(()=>[e("div",ne,[e("div",re,[ie,e("div",de,[e("div",ce,[e("div",me,[pe,e("input",{id:"search-field",onInput:o[0]||(o[0]=t=>x(t.target.value,a.value,l.value,r.value,i.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),e("div",ue,[d(ee,{href:s.route("reports")},{default:V(()=>[A(" Back ")]),_:1},8,["href"])])])]),e("div",ge,[e("div",_e,[e("div",he,[ve,d(w,{for:"customer_id",value:"Filters"})]),e("div",fe,[e("button",{onClick:Y},[e("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,ye)])])]),e("div",xe,[e("div",be,[d(w,{for:"customer_id",value:"Organization Name"}),e("div",we,[d(se,{options:c.organization,modelValue:a.value,"onUpdate:modelValue":o[1]||(o[1]=t=>a.value=t),onOnchange:j},null,8,["options","modelValue"])])]),e("div",ke,[d(w,{for:"customer_id",value:"Company Name"}),e("div",Se,[d(oe,{options:c.company,modelValue:l.value,"onUpdate:modelValue":o[2]||(o[2]=t=>l.value=t),onOnchange:q},null,8,["options","modelValue"])])]),e("div",Ce,[d(w,{for:"date",value:"From Date"}),T(e("input",{"onUpdate:modelValue":o[3]||(o[3]=t=>r.value=t),class:z(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":f(S).errors.from_date}]),type:"date",onChange:G},null,34),[[D,r.value]])]),e("div",Ne,[d(w,{for:"date",value:"To Date"}),T(e("input",{"onUpdate:modelValue":o[4]||(o[4]=t=>i.value=t),class:z(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":f(S).errors.to_date}]),type:"date",onChange:K},null,34),[[D,i.value]])])])]),e("div",ze,[e("div",Me,[e("table",Oe,[e("thead",Ee,[e("tr",Ve,[(n(),p(k,null,M(R,(t,g)=>e("th",{key:g,scope:"col",class:z(["px-4 py-4 text-sm font-semibold text-gray-900 cursor-pointer",t.colSpan]),onClick:m=>f($)(t.field,t.sortable)},[A(u(t.label)+" ",1),t.sortable?(n(),I(ae,{key:0,isSorted:f(L)===t.field,direction:f(U)},null,8,["isSorted","direction"])):O("",!0)],10,Ae)),64))])]),c.data.data&&c.data.data.length>0?(n(),p("tbody",Te,[(n(!0),p(k,null,M(c.data.data,(t,g)=>(n(),p("tr",{class:"odd:bg-white even:bg-gray-50 border-b grid grid-cols-1 gap-x-2 sm:grid-cols-12",key:t.id},[e("td",De,u(t.item_code??"-"),1),e("td",Ie,u(t.name??"-"),1),e("td",$e,u(t.company.name??"-"),1),e("td",Le,u(Q(t.invoice_details)??"-"),1),e("td",Ue,[e("div",Be,[e("button",{onClick:m=>F(g)},qe,8,Re)])]),C.value[g]&&t.invoice_details.length>0?(n(),p("div",Fe,[Pe,e("tbody",Ye,[(n(!0),p(k,null,M(t.invoice_details,(m,N)=>(n(),p("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-5",key:N},[e("td",Ge,u(m.invoice.invoice_no??"-"),1),e("td",Ke,u(P(m.invoice.date)),1),e("td",Qe,u(m.invoice.customers.customer_name??"-"),1),e("td",Xe,u(m.qty??"-"),1)]))),128))])])):O("",!0)]))),128))])):(n(),p("tbody",He,Je))])]),c.data.data&&c.data.data.length>0?(n(),I(te,{key:0,class:"mt-6",links:c.data.links},null,8,["links"])):O("",!0)])])]),_:1})],64))}};export{mt as default};
