<template>
    <div class="responsive-grid" :class="gridClasses">
        <slot />
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    cols: {
        type: [Number, Object],
        default: () => ({
            default: 1,
            sm: 2,
            md: 3,
            lg: 4,
            xl: 5
        })
    },
    gap: {
        type: String,
        default: 'normal',
        validator: (value) => ['none', 'small', 'normal', 'large'].includes(value)
    },
    responsive: {
        type: Boolean,
        default: true
    }
});

const gridClasses = computed(() => {
    const classes = ['grid'];
    
    if (typeof props.cols === 'number') {
        // Simple number of columns
        classes.push(`grid-cols-1 sm:grid-cols-2 lg:grid-cols-${props.cols}`);
    } else {
        // Responsive object
        const { default: def = 1, sm = 2, md = 3, lg = 4, xl = 5 } = props.cols;
        classes.push(`grid-cols-${def}`);
        if (sm) classes.push(`sm:grid-cols-${sm}`);
        if (md) classes.push(`md:grid-cols-${md}`);
        if (lg) classes.push(`lg:grid-cols-${lg}`);
        if (xl) classes.push(`xl:grid-cols-${xl}`);
    }
    
    // Gap classes
    switch (props.gap) {
        case 'none':
            classes.push('gap-0');
            break;
        case 'small':
            classes.push('gap-2 sm:gap-3');
            break;
        case 'large':
            classes.push('gap-6 sm:gap-8');
            break;
        default:
            classes.push('gap-4 sm:gap-6');
    }
    
    return classes.join(' ');
});
</script>

<style scoped>
.responsive-grid {
    @apply w-full;
}

/* Ensure grid items are properly sized on mobile */
@media (max-width: 640px) {
    .responsive-grid > * {
        @apply min-w-0; /* Prevent grid items from overflowing */
    }
}
</style>
