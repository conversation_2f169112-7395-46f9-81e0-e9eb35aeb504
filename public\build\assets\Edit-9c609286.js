import{r as x,j as q,l as O,m as ee,o as d,c as u,a as l,u as n,w as F,F as B,Z as te,b as a,t as y,f as p,d as oe,n as f,k as ae,v as se,i as Z,g as ne,T as le}from"./app-16701445.js";import{_ as ie,a as re}from"./AdminLayout-e15be38d.js";import{_ as v}from"./InputLabel-d69efee6.js";import{P as de}from"./PrimaryButton-eddb8b77.js";import{_ as k}from"./TextInput-764e3400.js";import{_ as ue}from"./TextArea-b68da786.js";import{_ as me}from"./RadioButton-b4275d4f.js";import{_ as U}from"./SearchableDropdown-c456ce8e.js";import{u as ce}from"./index-10107770.js";/* empty css                                                                          */import{_ as _e}from"./Checkbox-743761b5.js";import"./_plugin-vue_export-helper-c27b6911.js";const pe={class:"h-screen animate-top"},ve={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},ye={class:"sm:flex sm:items-center"},fe=a("div",{class:"sm:flex-auto"},[a("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Payment")],-1),ge={class:"flex items-center justify-between"},he={key:0,class:"text-base font-semibold leading-6 text-gray-900"},xe=["onSubmit"],be={class:"border-b border-gray-900/10 pb-12"},ke={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},we={class:"sm:col-span-3"},Ve={class:"relative mt-2"},Ne={class:"sm:col-span-3"},ze={class:"relative mt-2"},Ce={class:"sm:col-span-2 hidden"},Ae={class:"relative mt-2"},$e={key:0,class:"sm:col-span-3"},Fe={class:"relative mt-2"},Ue={key:1,class:"sm:col-span-3"},Pe={key:2,class:"sm:col-span-2"},Se={key:3,class:"sm:col-span-1"},Te={key:4,class:"sm:col-span-1"},De={key:5,class:"sm:col-span-1"},Oe={key:6,class:"sm:col-span-3"},Be={key:7,class:"sm:col-span-2"},Ee={class:"mt-4 flex justify-start"},Ie={class:"text-base font-semibold"},je={key:8,class:"sm:col-span-2"},Me={key:9,class:"sm:col-span-2"},Ye={key:10,class:"sm:col-span-2"},Le={class:"relative mt-2"},Re={key:11,class:"sm:col-span-3"},qe={class:"sm:col-span-6"},Ze={class:"overflow-x-auto divide-y divide-gray-300 w-full"},Ge=a("div",{class:"w-full"},[a("thead",{class:"w-full"},[a("tr",{class:""},[a("th",{scope:"col",class:""}),a("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),a("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),a("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),a("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),a("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1),He={style:{"overflow-y":"auto","max-height":"318px"}},Je={class:"divide-y divide-gray-300 bg-white"},Ke={class:"whitespace-nowrap px-2 text-sm text-gray-900"},Qe={class:"text-sm text-gray-900 leading-6 py-1.5"},We={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},Xe={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},et={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},tt={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},ot={key:0,class:"text-red-500 text-xs absolute"},at={class:"whitespace-nowrap px-2 text-sm text-gray-900"},st={class:"sm:col-span-2"},nt={class:"mt-4 flex justify-start"},lt={class:"text-base font-semibold"},it={key:0,class:"text-red-500 text-xs absolute"},rt={key:12,class:"sm:col-span-6"},dt={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},ut=a("thead",null,[a("tr",null,[a("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),a("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),a("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),a("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1),mt={class:"divide-y divide-gray-300 bg-white"},ct={class:"whitespace-nowrap py-3 text-sm text-gray-900"},_t={class:"whitespace-nowrap py-3 text-sm text-gray-900"},pt={class:"flex flex-col"},vt={class:"text-sm text-gray-900"},yt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},ft={class:"whitespace-nowrap py-3 text-sm text-gray-900"},gt={class:"flex mt-6 items-center justify-between"},ht={class:"ml-auto flex items-center justify-end gap-x-6"},xt=a("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),bt={key:0,class:"text-sm text-gray-600"},Tt={__name:"Edit",props:["payment","paymentType","bankinfo","organization","customers","invoices","credit"],setup(A){const i=A;x([]);const P=x([]),G=i.bankinfo.filter(s=>s.organization_id===i.payment.organization_id);P.value=G;const w=x(i.payment.payment_type),t=ce("post","/receipt",{id:i.payment.id,organization_id:i.payment.organization_id,customer_id:i.payment.customer_id,payment_type:i.payment.payment_type,date:i.payment.date,note:i.payment.note,amount:i.payment.amount,tds_amount:i.payment.tds_amount||0,discount_amount:i.payment.discount_amount||0,round_off:i.payment.round_off||0,check_number:i.payment.check_number,bank_name:i.payment.bank_name,org_bank_id:i.payment.org_bank_id,invoice:[],settled_amount:"",advance_amount:"",is_credit:"No",credit_data:[],total_unused_amount:""}),H=()=>{t.settled_amount=S.value,t.advance_amount=I.value,t.total_unused_amount=V.value,t.is_credit=c.value,t.invoice=h.value,t.credit_data=g.value,t.submit({preserveScroll:!0,onSuccess:()=>{}})},J=(s,o)=>{w.value=s,t.payment_type=s,t.errors.payment_type=null,o==="Cash"?t.note="Cash":t.note==="Cash"&&(t.note="")},z=x([]),g=x([]),V=x(""),E=(s,o)=>{const e=i.bankinfo.filter(_=>_.organization_id===s);P.value=e;const m=i.invoices.filter(_=>_.organization_id===s&&_.customer_id===t.customer_id);z.value=m;const r=i.credit.filter(_=>_.organization_id===s&&_.customer_id===t.customer_id);g.value=r,V.value=g.value.reduce((_,b)=>_+b.unused_amount,0),t.organization_id=s,t.errors.organization_id=null},K=(s,o)=>{const e=i.invoices.filter(r=>r.customer_id===s&&r.organization_id===t.organization_id);z.value=e;const m=i.credit.filter(r=>r.customer_id===s&&r.organization_id===t.organization_id);g.value=m,V.value=g.value.reduce((r,_)=>r+_.unused_amount,0),t.customer_id=s,t.errors.customer_id=null},Q=(s,o)=>{t.org_bank_id=s,t.errors.org_bank_id=null},S=q(()=>h.value.reduce((s,o)=>s+(o.check&&o.amount?parseFloat(o.amount):0),0)),I=q(()=>{const s=parseFloat(t.amount||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0),o=parseFloat(t.round_off||0),e=S.value;return s-e-o}),T=()=>{},C=s=>{let o=s.toFixed(2).toString(),[e,m]=o.split("."),r=e.substring(e.length-3),_=e.substring(0,e.length-3);return _!==""&&(r=","+r),`${_.replace(/\B(?=(\d{2})+(?!\d))/g,",")+r}.${m}`},j=s=>{const o=new Date(s),e={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",e)},c=x("No"),W=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],X=s=>{const o=c.value==="Yes"?parseFloat(V.value||0):parseFloat(t.amount||0)+parseFloat(t.round_off||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0);if(!h.value[s].check){h.value[s].amount=0;return}let e=o;h.value.forEach((_,b)=>{_.check&&b!==s&&(e-=parseFloat(_.amount||0))});const m=parseFloat(h.value[s].pending_amount||0),r=Math.min(m,e);h.value[s].amount=r.toFixed(2)},h=x([]),$=()=>{h.value=z.value.map(s=>{const o=i.payment.invoice_data.some(m=>m.id===s.id),e=o?i.payment.invoice_data.find(m=>m.id===s.id).amount:0;return{id:s.id,date:s.date,invoice_no:s.invoice_no,total_amount:parseFloat(s.total_amount||0).toFixed(2),pending_amount:parseFloat(s.original_pending_amount||s.pending_amount||0).toFixed(2),check:o,amount:o?e.toString():"0.00"}})};O(z,()=>{$()}),O(c,()=>{$()}),O(()=>t.amount,()=>{c.value==="No"&&$()});const N=s=>{t.errors[s]=null,t.errors.settled_amount=null};return ee(()=>{E(i.payment.organization_id);const s=i.invoices.filter(e=>e.organization_id===i.payment.organization_id&&e.customer_id===i.payment.customer_id);z.value=s,$();const o=i.credit.filter(e=>e.organization_id===i.payment.organization_id&&e.customer_id===i.payment.customer_id);g.value=o,V.value=g.value.reduce((e,m)=>e+m.unused_amount,0)}),(s,o)=>(d(),u(B,null,[l(n(te),{title:"Edit Receipt"}),l(ie,null,{default:F(()=>[a("div",pe,[a("div",ve,[a("div",ye,[fe,a("div",ge,[g.value.length>0?(d(),u("div",he," Credits Available: ₹"+y(C(V.value)),1)):p("",!0)])]),a("form",{onSubmit:oe(H,["prevent"]),class:""},[a("div",be,[a("div",ke,[a("div",we,[l(v,{for:"payment_type",value:"Organization"}),a("div",Ve,[l(U,{options:A.organization,modelValue:n(t).organization_id,"onUpdate:modelValue":o[0]||(o[0]=e=>n(t).organization_id=e),onOnchange:E,class:f({"error rounded-md":n(t).errors.organization_id})},null,8,["options","modelValue","class"])])]),a("div",Ne,[l(v,{for:"payment_type",value:"Customer"}),a("div",ze,[l(U,{options:A.customers,modelValue:n(t).customer_id,"onUpdate:modelValue":o[1]||(o[1]=e=>n(t).customer_id=e),onOnchange:K,class:f({"error rounded-md":n(t).errors.customer_id})},null,8,["options","modelValue","class"])])]),a("div",Ce,[l(v,{for:"role_id",value:"Payment Through Credit ?"}),a("div",Ae,[l(me,{modelValue:c.value,"onUpdate:modelValue":o[2]||(o[2]=e=>c.value=e),options:W},null,8,["modelValue"])])]),c.value=="No"?(d(),u("div",$e,[l(v,{for:"payment_type",value:"Payment Type"}),a("div",Fe,[l(U,{options:A.paymentType,modelValue:n(t).payment_type,"onUpdate:modelValue":o[3]||(o[3]=e=>n(t).payment_type=e),onOnchange:J,class:f({"error rounded-md":n(t).errors.payment_type})},null,8,["options","modelValue","class"])])])):p("",!0),c.value=="No"?(d(),u("div",Ue,[l(v,{for:"date",value:"Payment Date"}),ae(a("input",{"onUpdate:modelValue":o[4]||(o[4]=e=>n(t).date=e),class:f(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":n(t).errors.date}]),type:"date",onChange:o[5]||(o[5]=e=>N("date"))},null,34),[[se,n(t).date]])])):p("",!0),c.value=="No"?(d(),u("div",Pe)):p("",!0),c.value=="No"?(d(),u("div",Se,[l(v,{for:"tds_amount",value:"TDS Amount"}),l(k,{type:"text",onChange:o[6]||(o[6]=e=>N("tds_amount")),onInput:o[7]||(o[7]=e=>T()),modelValue:n(t).tds_amount,"onUpdate:modelValue":o[8]||(o[8]=e=>n(t).tds_amount=e),class:f({"error rounded-md":n(t).errors.tds_amount})},null,8,["modelValue","class"])])):p("",!0),c.value=="No"?(d(),u("div",Te,[l(v,{for:"discount_amount",value:"Discount Amount"}),l(k,{type:"text",onChange:o[9]||(o[9]=e=>N("discount_amount")),onInput:o[10]||(o[10]=e=>T()),modelValue:n(t).discount_amount,"onUpdate:modelValue":o[11]||(o[11]=e=>n(t).discount_amount=e),class:f({"error rounded-md":n(t).errors.discount_amount})},null,8,["modelValue","class"])])):p("",!0),c.value=="No"?(d(),u("div",De,[l(v,{for:"round_off",value:"Round Off"}),l(k,{type:"text",onChange:o[12]||(o[12]=e=>N("round_off")),modelValue:n(t).round_off,"onUpdate:modelValue":o[13]||(o[13]=e=>n(t).round_off=e),class:f({"error rounded-md":n(t).errors.round_off})},null,8,["modelValue","class"])])):p("",!0),c.value=="No"?(d(),u("div",Oe,[l(v,{for:"amount",value:"Amount"}),l(k,{id:"amount",type:"text",onChange:o[14]||(o[14]=e=>N("amount")),onInput:o[15]||(o[15]=e=>T()),modelValue:n(t).amount,"onUpdate:modelValue":o[16]||(o[16]=e=>n(t).amount=e),class:f({"error rounded-md":n(t).errors.amount})},null,8,["modelValue","class"])])):p("",!0),c.value=="No"?(d(),u("div",Be,[l(v,{for:"advance",value:"Advance(Ref) Amount"}),a("div",Ee,[a("p",Ie,y(C(I.value)),1)])])):p("",!0),w.value=="check"||w.value=="Cheque"&&c.value=="No"?(d(),u("div",je,[l(v,{for:"check_number",value:"Cheque Number"}),l(k,{id:"check_number",type:"text",modelValue:n(t).check_number,"onUpdate:modelValue":o[17]||(o[17]=e=>n(t).check_number=e),class:f({"error rounded-md":n(t).errors["data.check_number"]})},null,8,["modelValue","class"])])):p("",!0),w.value=="check"&&c.value=="No"?(d(),u("div",Me,[l(v,{for:"bank_name",value:"Bank Name"}),l(k,{id:"bank_name",type:"text",modelValue:n(t).bank_name,"onUpdate:modelValue":o[18]||(o[18]=e=>n(t).bank_name=e),class:f({"error rounded-md":n(t).errors["data.bank_name"]})},null,8,["modelValue","class"])])):p("",!0),w.value!="cash"&&c.value=="No"?(d(),u("div",Ye,[l(v,{for:"org_bank_id",value:"Our Bank"}),a("div",Le,[l(U,{options:P.value,modelValue:n(t).org_bank_id,"onUpdate:modelValue":o[19]||(o[19]=e=>n(t).org_bank_id=e),onOnchange:Q,class:f({"error rounded-md":n(t).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):p("",!0),w.value!="cash"&&c.value=="No"?(d(),u("div",Re)):p("",!0),a("div",qe,[a("table",Ze,[Ge,a("div",He,[a("tbody",Je,[(d(!0),u(B,null,Z(h.value,(e,m)=>(d(),u("tr",{key:m},[a("td",Ke,[a("div",Qe,[l(_e,{name:"check",checked:e.check,"onUpdate:checked":r=>e.check=r,onChange:r=>X(m)},null,8,["checked","onUpdate:checked","onChange"])])]),a("td",We,y(e.invoice_no),1),a("td",Xe,y(e.total_amount),1),a("td",et,y(e.pending_amount),1),a("td",tt,[l(k,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":r=>e.amount=r,onChange:r=>N("invoice."+m+".amount"),class:f({error:n(t).errors[`invoice.${m}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),n(t).errors[`invoice.${m}.amount`]?(d(),u("p",ot,y(n(t).errors[`invoice.${m}.amount`]),1)):p("",!0)]),a("td",at,y(j(e.date)),1)]))),128))])])])]),a("div",st,[l(v,{for:"note",value:"Total Settled Amount"}),a("div",nt,[a("p",lt,y(C(S.value)),1)]),n(t).errors.settled_amount?(d(),u("p",it,y(n(t).errors.settled_amount),1)):p("",!0)]),c.value=="No"?(d(),u("div",rt,[l(v,{for:"note",value:"Note"}),l(ue,{id:"note",type:"text",rows:2,modelValue:n(t).note,"onUpdate:modelValue":o[20]||(o[20]=e=>n(t).note=e)},null,8,["modelValue"])])):p("",!0)]),g.value.length>0&&c.value=="Yes"?(d(),u("table",dt,[ut,a("tbody",mt,[(d(!0),u(B,null,Z(g.value,(e,m)=>{var r,_,b,D,M,Y,L,R;return d(),u("tr",{key:m},[a("td",ct,y(j(e.date)),1),a("td",_t,[a("div",pt,[a("div",vt,y((_=(r=e.paymentreceive)==null?void 0:r.bank_info)!=null&&_.bank_name?(D=(b=e.paymentreceive)==null?void 0:b.bank_info)==null?void 0:D.bank_name:"Cash")+" - "+y((Y=(M=e.paymentreceive)==null?void 0:M.bank_info)!=null&&Y.account_number?(R=(L=e.paymentreceive)==null?void 0:L.bank_info)==null?void 0:R.account_number:""),1)])]),a("td",yt,y(C(e.amount)),1),a("td",ft,y(C(e.unused_amount)),1)])}),128))])])):p("",!0)]),a("div",gt,[a("div",ht,[l(re,{href:s.route("receipt.index")},{svg:F(()=>[xt]),_:1},8,["href"]),l(de,{disabled:n(t).processing},{default:F(()=>[ne("Update")]),_:1},8,["disabled"]),l(le,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:F(()=>[n(t).recentlySuccessful?(d(),u("p",bt,"Saved.")):p("",!0)]),_:1})])])],40,xe)])])]),_:1})],64))}};export{Tt as default};
