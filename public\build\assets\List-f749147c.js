import{_ as Re,b as He,a as ee}from"./AdminLayout-595ad5a7.js";import{_ as Ye}from"./CreateButton-91ea7c7b.js";import{_ as B}from"./SecondaryButton-d0c53c3f.js";import{D as We}from"./DangerButton-36669f8b.js";import{P as E}from"./PrimaryButton-46ac4375.js";import{M as G}from"./Modal-48c075e7.js";import{N as Ke,Q as Xe}from"./tagify.esm-8ca7f1b0.js";import{_ as Ze}from"./Pagination-5e2f223d.js";import{_ as re}from"./SimpleDropdown-f072c5ba.js";import{_ as F}from"./SearchableDropdown-9d1b12d3.js";import{_ as R}from"./SearchableDropdownNew-3cee6407.js";import{K as ce,r as h,l as Je,o as n,c as u,a as r,u as I,n as te,t as a,f as d,w as m,F as H,Z as De,b as e,g as x,i as se,e as Q,s as et,x as tt,O as st}from"./app-97275a91.js";import"./html2canvas.esm-6a1541b2.js";import{_ as w}from"./InputLabel-eb73087c.js";import{Q as ot}from"./vue-quill.snow-8b4d9386.js";import{_ as oe}from"./TextInput-11c46564.js";import{_ as lt}from"./ArrowIcon-572ff5c4.js";import{s as nt}from"./sortAndSearch-6f0bc414.js";import{_ as at}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const s=c=>(et("data-v-48fd1fd6"),c=c(),tt(),c),it={class:"animate-top"},ut={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6"},dt=s(()=>e("div",{class:"items-start"},[e("h1",{class:"text-xl sm:text-2xl font-semibold leading-7 text-gray-900"},"Quotation")],-1)),rt={class:"flex flex-col sm:flex-row gap-3 sm:gap-4"},ct={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},_t=s(()=>e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),vt={key:0},mt={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},ht={class:"flex mb-2"},pt=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),gt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},yt={class:"sm:col-span-4"},ft={class:"relative mt-2"},xt={class:"sm:col-span-4"},wt={class:"relative mt-2"},bt={key:0,class:"sm:col-span-4"},kt={class:"relative mt-2"},Ct={class:"sm:col-span-4"},St={class:"relative mt-2"},Tt={class:"sm:col-span-4"},jt={class:"relative mt-2"},zt={class:"sm:col-span-4"},Mt={class:"relative mt-2"},It={class:"mt-8 overflow-x-auto sm:rounded-lg"},Vt={class:"shadow sm:rounded-lg"},qt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Bt={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Pt={class:"border-b-2"},Nt=["onClick"],At={key:0},Ot={class:"px-4 py-2.5 min-w-44"},Et={scope:"row",class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},Gt={class:"px-4 py-2.5 min-w-52"},Qt={class:"px-4 py-2.5 min-w-32"},Ut={class:"px-4 py-2.5 min-w-32"},$t={class:"flex flex-1 items-center px-4 py-2.5"},Lt={class:"items-center px-4 py-2.5"},Ft={class:"flex items-center justify-start gap-4"},Rt=s(()=>e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)),Ht=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),Yt=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),Wt=["onClick"],Kt=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Xt=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)),Zt=[Kt,Xt],Jt=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),Dt=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Convert To Order ",-1)),es=["onClick"],ts=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M4 3h16v18H4V3zm6 1v4M10 8h8M6 12h12M6 16h8"})],-1)),ss=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Generate PI ",-1)),os=[ts,ss],ls=["onClick"],ns=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M6 18L18 6M6 6l12 12"})],-1)),as=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Reject Quotation ",-1)),is=[ns,as],us=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),ds=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," View Quotation ",-1)),rs=["onClick"],cs=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M6 2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm2 6h8m-4 4h4"})],-1)),_s=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Generate PDF ",-1)),vs=[cs,_s],ms=["onClick"],hs=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M3 8l7 5 7-5M3 8v8m14-8v8M3 8l7 5 7-5"})],-1)),ps=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"},"Send Email",-1)),gs=[hs,ps],ys={key:1},fs=s(()=>e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),xs=[fs],ws=s(()=>e("div",{class:"flex w-full justify-start bg-indigo-600 px-6 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"},[e("h2",{class:"text-2xl font-bold"},"Send Email")],-1)),bs={class:"p-6 overflow-y-auto max-h-screen"},ks={class:"space-y-4"},Cs={key:0,class:"text-red-500 text-sm"},Ss={class:"relative mt-2"},Ts={key:0,class:"text-red-500 text-sm"},js={class:"flex space-x-4"},zs={class:"w-1/2"},Ms={class:"relative mt-2"},Is={key:0,class:"text-red-500 text-sm"},Vs={class:"w-1/2"},qs={class:"relative mt-2"},Bs={key:0,class:"text-red-500 text-sm"},Ps={key:0,class:"text-red-500 text-sm"},Ns={class:"mb-20"},As={key:0,class:"text-red-500 text-sm"},Os={class:"px-10 py-4 bg-white flex justify-end absolute w-full right-0 bottom-0"},Es={class:"w-36"},Gs={key:0},Qs=s(()=>e("svg",{class:"animate-spin h-5 w-5 mr-2 text-white inline-block",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8v8H4z"})],-1)),Us={key:1},$s={class:"p-6"},Ls=s(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),Fs={class:"mt-6 flex justify-end"},Rs={class:"p-6"},Hs=s(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you want to convert this quotation in to order? ",-1)),Ys={class:"mt-6 flex justify-end space-x-3"},Ws={class:"w-32"},Ks={class:"p-6"},Xs=s(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you want to create proforma invoice for this quotation ? ",-1)),Zs={class:"mt-6 flex justify-end space-x-3"},Js={class:"w-32"},Ds={class:"p-6"},eo={id:"pdf-content"},to={class:"container1"},so={key:0,class:"header",style:{display:"flex","align-items":"center","justify-content":"space-between","margin-bottom":"10px"}},oo=["src"],lo=s(()=>e("p",null,[e("strong",{style:{"font-size":"20px"}},"Quotation")],-1)),no=s(()=>e("h1",null,[e("div",{style:{width:"120px"}})],-1)),ao={key:1,class:"header",style:{"align-items":"start","justify-content":"center","text-align":"center"}},io=["src"],uo=s(()=>e("div",{style:{"align-items":"center","justify-content":"space-between","margin-bottom":"10px"}},[e("p",{style:{"font-size":"20px"}},[e("strong",null,"Quotation")])],-1)),ro={style:{display:"flex","justify-content":"space-between"}},co={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"360px"}},_o=s(()=>e("p",{style:{"margin-bottom":"10px"}},[e("strong")],-1)),vo=s(()=>e("p",{style:{"margin-bottom":"4px"}},[e("strong")],-1)),mo={style:{display:"flex"}},ho=s(()=>e("p",{style:{width:"40px"}},[e("strong",null,"Phone")],-1)),po={style:{display:"flex"}},go=s(()=>e("p",{style:{width:"40px"}},[e("strong",null,"GST")],-1)),yo={class:"invoice-details",style:{"margin-bottom":"20px","justify-items":"start",width:"260px"}},fo={style:{display:"flex"}},xo=s(()=>e("p",{style:{width:"120px"}},[e("strong",null,"Quotation Number")],-1)),wo={style:{display:"flex"}},bo=s(()=>e("p",{style:{width:"120px"}},[e("strong",null,"Quotation Date")],-1)),ko={style:{display:"flex"}},Co=s(()=>e("p",{style:{width:"120px"}},[e("strong",null,"Contact Us")],-1)),So={style:{display:"flex"}},To=s(()=>e("p",{style:{width:"120px"}},[e("strong",null,"GST")],-1)),jo={style:{"overflow-x":"auto"}},zo=s(()=>e("th",null,"SN",-1)),Mo={key:0},Io=s(()=>e("th",null,"HSN",-1)),Vo={key:1},qo=s(()=>e("th",null,"DESCRIPTION",-1)),Bo=s(()=>e("th",null,"MRP",-1)),Po=s(()=>e("th",null,"PRICE (₹)",-1)),No=s(()=>e("th",null,"QTY",-1)),Ao=s(()=>e("th",null,"TOTAL PRICE (₹)",-1)),Oo={key:2},Eo={key:3},Go={key:4},Qo=s(()=>e("th",null,"GST (₹)",-1)),Uo=s(()=>e("th",null,"DIS.(₹)",-1)),$o=s(()=>e("th",null,"TOTAL AMOUNT",-1)),Lo={key:0},Fo={key:1,class:""},Ro=["src"],Ho={key:1},Yo=["innerHTML"],Wo={key:2},Ko={key:3},Xo={key:4},Zo={class:"",style:{"margin-bottom":"10px","justify-items":"start",width:"400"}},Jo=s(()=>e("p",null,[e("strong")],-1)),Do={style:{display:"flex","justify-content":"space-between"}},el={class:"quotationbank",style:{"margin-bottom":"20px","justify-items":"start",width:"400px"}},tl=s(()=>e("p",null,[e("strong",null,"TERMS & CONDITIONS")],-1)),sl={style:{display:"flex"}},ol=s(()=>e("p",null,[e("strong",null,"Validity")],-1)),ll={style:{display:"flex"}},nl=s(()=>e("p",null,[e("strong",null,"Delivery")],-1)),al={style:{display:"flex"}},il=s(()=>e("p",null,[e("strong",null,"Payment Terms")],-1)),ul={style:{display:"flex"}},dl={key:0,style:{display:"flex"}},rl=s(()=>e("p",null,[e("strong",null,"Warranty")],-1)),cl={class:"invoice-details",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},_l={style:{display:"flex"}},vl=s(()=>e("p",{style:{width:"105px"}},[e("strong",null,"Sub Total (₹)")],-1)),ml={style:{display:"flex"}},hl=s(()=>e("p",{style:{width:"105px"}},[e("strong",null,"Total Discount (₹)")],-1)),pl={key:0,style:{display:"flex"}},gl=s(()=>e("p",{style:{width:"105px"}},[e("strong",null,"Total IGST (₹):")],-1)),yl={key:1,style:{display:"flex"}},fl=s(()=>e("p",{style:{width:"105px"}},[e("strong",null,"Total CGST (₹):")],-1)),xl={key:2,style:{display:"flex"}},wl=s(()=>e("p",{style:{width:"105px"}},[e("strong",null,"Total SGST (₹):")],-1)),bl={style:{display:"flex"}},kl=s(()=>e("p",{style:{width:"105px"}},[e("strong",null,"Total Amount (₹)")],-1)),Cl={style:{display:"flex","justify-content":"space-between"}},Sl={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"400"}},Tl=s(()=>e("p",null,[e("strong",null,"OUR BANK DETAILS")],-1)),jl={key:0,style:{display:"flex"}},zl=s(()=>e("p",null,[e("strong",null,"Bank Name")],-1)),Ml={key:1,style:{display:"flex"}},Il=s(()=>e("p",null,[e("strong",null,"Branch Name")],-1)),Vl={key:2,style:{display:"flex"}},ql=s(()=>e("p",null,[e("strong",null,"Account No")],-1)),Bl={key:3,style:{display:"flex"}},Pl=s(()=>e("p",null,[e("strong",null,"IFSC Code")],-1)),Nl={key:4,style:{display:"flex"}},Al=s(()=>e("p",null,[e("strong",null,"Bank Name")],-1)),Ol={key:5,style:{display:"flex"}},El=s(()=>e("p",null,[e("strong",null,"Branch Name")],-1)),Gl={key:6,style:{display:"flex"}},Ql=s(()=>e("p",null,[e("strong",null,"Account No")],-1)),Ul={key:7,style:{display:"flex"}},$l=s(()=>e("p",null,[e("strong",null,"IFSC Code")],-1)),Ll={key:8,style:{display:"flex"}},Fl=s(()=>e("p",null,[e("strong",null,"Bank Name")],-1)),Rl={key:9,style:{display:"flex"}},Hl=s(()=>e("p",null,[e("strong",null,"Branch Name")],-1)),Yl={key:10,style:{display:"flex"}},Wl=s(()=>e("p",null,[e("strong",null,"Account No")],-1)),Kl={key:11,style:{display:"flex"}},Xl=s(()=>e("p",null,[e("strong",null,"IFSC Code")],-1)),Zl={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},Jl=s(()=>e("p",null,[e("strong",null,"FOR,")],-1)),Dl=["src"],en={id:"footer",style:{padding:"8px 0px"}},tn={class:"footer"},sn=s(()=>e("span",{class:"piller"},null,-1)),on={key:0},ln=s(()=>e("p",null,"+91 **********",-1)),nn={key:0},an={key:1},un={key:1},dn=s(()=>e("p",null,"+91 **********",-1)),rn=s(()=>e("p",null,"+91 **********",-1)),cn=[dn,rn],_n=s(()=>e("span",{class:"piller"},null,-1)),vn={key:2},mn=s(()=>e("p",null,"<EMAIL>",-1)),hn={key:0},pn={key:1},gn={key:2},yn={key:3},fn=s(()=>e("p",null,"<EMAIL>",-1)),xn=s(()=>e("p",null,"<EMAIL>",-1)),wn=[fn,xn],bn=s(()=>e("div",{style:{display:"flex","justify-content":"center","margin-bottom":"10px"}},[e("p",null,"We Make The Difference")],-1)),kn={class:"mt-6 px-4 flex justify-end"},Cn={class:"flex flex-col justify-end space-y-6"},Sn={class:"flex items-center space-x-2"},Tn={class:"flex justify-end"},jn={class:"w-36"},zn={class:"p-6"},Mn=s(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to reject this quotation? ",-1)),In={class:"mt-6 flex justify-end space-x-3"},Vn={class:"w-32"},qn={__name:"List",props:["data","financialYears","financialYear","user_id","emailTemplates","email","permissions","quotationbank","quotationHealthCareBankinfo","quotationNoxBank","organization","customers","salesuser","category","organizationId","customerId","salesUserId","categoryId","createdBy","productpath","pagetypes"],setup(c){const p=c,{form:P,search:Bn,sort:_e,fetchData:Pn,sortKey:ve,sortDirection:me,updateParams:he}=nt("quotation.index",{organization_id:p.organizationId,customer_id:p.customerId,sales_user_id:p.salesUserId,category:p.categoryId,created_by:p.createdBy,financial_year:p.financialYear}),Y=ce().props.filepath.view,pe=ce().props.productpath.view,o=h([]),W=h(!1),le=h(!1),K=h(!1),ne=h(null),N=h(null),ge=[{field:"quotation_number",label:"QUOTATION NUMBER",sortable:!0},{field:"customers.customer_name",label:"CUSTOMER NAME",sortable:!0},{field:"users.first_name",label:"SALES PERSON",sortable:!0},{field:"date",label:"DATE",sortable:!0},{field:"total_amount",label:"AMOUNT (₹)",sortable:!0},{field:"status",label:"STATUS",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],ye=i=>{ne.value=i,W.value=!0},fe=i=>{N.value=i,K.value=!0},A=()=>{W.value=!1},ae=()=>{le.value=!1},ie=()=>{K.value=!1},xe=()=>{P.delete(route("quotation.destroy",{id:ne.value}),{onSuccess:()=>A()})},we=()=>{P.get(route("quotation.convertorder",{id:N.value}),{onSuccess:()=>A()})},be=()=>{P.get(route("quotation.convertopi",{id:N.value}),{onSuccess:()=>A()})},b=h(p.organizationId),k=h(p.customerId),C=h(p.salesUserId),S=h(p.categoryId),T=h(p.createdBy),j=h(p.financialYear),V=h("");Je([b,k,C,S,T],()=>{he({organization_id:b.value,customer_id:k.value,sales_user_id:C.value,category:S.value,created_by:T.value})});const q=(i,l,t,g,f,M,O)=>{V.value=i,P.get(route("quotation.index",{search:i,organization_id:l,customer_id:t,sales_user_id:g,category:f,created_by:M,financial_year:O}),{preserveState:!0})},ke=(i,l)=>{b.value=i,q(V.value,b.value,k.value,C.value,S.value,T.value,j.value)},Ce=(i,l)=>{k.value=i,q(V.value,b.value,k.value,C.value,S.value,T.value,j.value)},Se=(i,l)=>{S.value=i,q(V.value,b.value,k.value,C.value,S.value,T.value,j.value)},Te=(i,l)=>{C.value=i,q(V.value,b.value,k.value,C.value,S.value,T.value,j.value)},je=(i,l)=>{T.value=i,q(V.value,b.value,k.value,C.value,S.value,T.value,j.value)},ze=(i,l)=>{j.value=i,q(V.value,b.value,k.value,C.value,S.value,T.value,j.value)},X=h(!1),Me=h("custom"),Ie=i=>{const l=p.data.data.find(t=>t.id===i);o.value=l,X.value=!0},ue=()=>{X.value=!1},Ve=i=>{switch(i){case"Pending":return"bg-blue-100";case"Accepted":return"bg-yellow-100";case"Completed":return"bg-green-100";default:return"bg-red-100"}},qe=i=>{switch(i){case"Pending":return"text-blue-600";case"Accepted":return"text-yellow-600";case"Completed":return"text-green-600";default:return"text-red-600"}},Z=h(!1),Be=i=>{N.value=i,Z.value=!0},J=()=>{Z.value=!1},Pe=()=>{P.get(route("quotation.reject",{id:N.value}),{onSuccess:()=>J()})},de=i=>{const l=new Date(i),t={year:"numeric",month:"short",day:"numeric"};return l.toLocaleDateString("en-US",t)},y=i=>{let l=i.toFixed(2).toString(),[t,g]=l.split("."),f=t.substring(t.length-3),M=t.substring(0,t.length-3);return M!==""&&(f=","+f),`${M.replace(/\B(?=(\d{2})+(?!\d))/g,",")+f}.${g}`},Ne=i=>i&&i.length>40?i.substring(0,40)+"...":i,U=h("portrait"),Ae=(i,l)=>{U.value=i},Oe=(i,l)=>{window.open(`/quotation/download/${i}/${l}`,"_blank")},$=h(!1),L=h(!1),z=h({show:!1,message:"",type:"success"}),v=h({to:"",from:"",template:"",pagetype:"",subject:"",content:""}),_=h({to:"",from:"",cc:"",template:"",subject:"",content:"",id:"",user_id:"",pagetype:"portrait"}),Ee=i=>{const l=p.data.data.find(t=>t.id===i);_.value={to:l?l.customers.email:"",cc:"",id:i,from:"",template:"",subject:"",content:"",user_id:p.user_id,pagetype:"portrait"},v.value={to:"",from:"",template:"",pagetype:"",subject:"",content:""},Array.isArray(p.email)&&p.email.length>0&&(_.value.from=p.email[0].id),L.value=!0,st(()=>{const t=document.getElementById("cc");if(t){const g=new Xe(t,{delimiters:",",pattern:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,placeholder:"Add CC emails..."});g.on("change",f=>{_.value.cc=g.value.map(M=>M.value).join(",")})}})},Ge=()=>{L.value=!1},Qe=(i,l)=>{const t=p.emailTemplates.find(g=>g.id===i);t&&(_.value.template=l,_.value.subject=t.email_subject,_.value.content=t.content,v.value.template="",v.value.subject="",v.value.content="")},Ue=(i,l)=>{_.value.from=i,v.value.from=""},$e=(i,l)=>{_.value.pagetype=i,v.value.pagetype=""},Le=()=>{let i=!0;if(v.value={to:"",from:"",template:"",pagetype:"",subject:"",content:""},(!_.value.to||_.value.to.trim()==="")&&(v.value.to="This field is required.",i=!1),_.value.from||(v.value.from="This field is required.",i=!1),_.value.template||(v.value.template="This field is required.",i=!1),_.value.pagetype||(v.value.pagetype="This field is required.",i=!1),(!_.value.subject||_.value.subject.trim()==="")&&(v.value.subject="This field is required.",i=!1),(!_.value.content||_.value.content.trim()==="")&&(v.value.content="This field is required.",i=!1),!i)return;$.value=!0;const l=new FormData;l.append("to",_.value.to),l.append("from",_.value.from),l.append("cc",_.value.cc),l.append("subject",_.value.subject),l.append("content",_.value.content),l.append("id",_.value.id),l.append("user_id",_.value.user_id),l.append("pagetype",_.value.pagetype),l.append("template",_.value.template),fetch(route("send-invoice-email"),{method:"POST",body:l,headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}}).then(async t=>{const g=await t.json();if(!t.ok)throw z.value={show:!0,message:g.error||"Failed to send email",type:"error"},new Error(g.error||"Failed to send email");z.value={show:!0,message:g.success||"Email sent successfully",type:"success"},L.value=!1,setTimeout(()=>{z.value.show=!1},3e3)}).catch(t=>{z.value={show:!0,message:t.message||"An unexpected error occurred",type:"error"}}).finally(()=>{$.value=!1,setTimeout(()=>{z.value.show=!1},5e3)})},Fe=i=>i?i.replace(/\n/g,"<br>"):"";return(i,l)=>(n(),u(H,null,[r(I(De),{title:"Quotation"}),z.value.show?(n(),u("div",{key:0,class:te(["notification",z.value.type]),onClick:l[0]||(l[0]=t=>z.value.show=!1)},a(z.value.message),3)):d("",!0),r(Re,null,{default:m(()=>[e("div",it,[e("div",ut,[dt,e("div",rt,[e("div",ct,[_t,e("input",{id:"search-field",onInput:l[1]||(l[1]=t=>q(t.target.value,b.value,k.value,C.value,S.value,T.value,j.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)]),c.permissions.canCreateQuotation?(n(),u("div",vt,[r(Ye,{href:i.route("quotation.create")},{default:m(()=>[x(" Add Quotation ")]),_:1},8,["href"])])):d("",!0)])]),e("div",mt,[e("div",ht,[pt,r(w,{for:"customer_id",value:"Filters"})]),e("div",gt,[e("div",yt,[r(w,{for:"customer_id",value:"Organization Name"}),e("div",ft,[r(re,{options:c.organization,modelValue:b.value,"onUpdate:modelValue":l[2]||(l[2]=t=>b.value=t),onOnchange:ke},null,8,["options","modelValue"])])]),e("div",xt,[r(w,{for:"customer_id",value:"Customer Name"}),e("div",wt,[r(R,{options:c.customers,modelValue:k.value,"onUpdate:modelValue":l[3]||(l[3]=t=>k.value=t),onOnchange:Ce},null,8,["options","modelValue"])])]),c.permissions.canCreateQuotation?(n(),u("div",bt,[r(w,{for:"customer_id",value:"Sales Person"}),e("div",kt,[r(R,{options:c.salesuser,modelValue:C.value,"onUpdate:modelValue":l[4]||(l[4]=t=>C.value=t),onOnchange:Te},null,8,["options","modelValue"])])])):d("",!0),e("div",Ct,[r(w,{for:"customer_id",value:"Category"}),e("div",St,[r(re,{options:c.category,modelValue:S.value,"onUpdate:modelValue":l[5]||(l[5]=t=>S.value=t),onOnchange:Se},null,8,["options","modelValue"])])]),e("div",Tt,[r(w,{for:"customer_id",value:"Created By"}),e("div",jt,[r(R,{options:c.salesuser,modelValue:T.value,"onUpdate:modelValue":l[6]||(l[6]=t=>T.value=t),onOnchange:je},null,8,["options","modelValue"])])]),e("div",zt,[r(w,{for:"financial_year",value:"Financial Year"}),e("div",Mt,[r(R,{options:c.financialYears,modelValue:j.value,"onUpdate:modelValue":l[7]||(l[7]=t=>j.value=t),onOnchange:ze},null,8,["options","modelValue"])])])])]),e("div",It,[e("div",Vt,[e("table",qt,[e("thead",Bt,[e("tr",Pt,[(n(),u(H,null,se(ge,(t,g)=>e("th",{key:g,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:f=>I(_e)(t.field,t.sortable)},[x(a(t.label)+" ",1),t.sortable?(n(),Q(lt,{key:0,isSorted:I(ve)===t.field,direction:I(me)},null,8,["isSorted","direction"])):d("",!0)],8,Nt)),64))])]),c.data.data&&c.data.data.length>0?(n(),u("tbody",At,[(n(!0),u(H,null,se(c.data.data,(t,g)=>(n(),u("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Ot,a(t.quotation_number),1),e("th",Et,a(Ne(t.customers.customer_name)??"-"),1),e("td",Gt,a(t.users.first_name)+" "+a(t.users.last_name),1),e("td",Qt,a(de(t.date)),1),e("td",Ut,a(y(t.total_amount)),1),e("td",$t,[e("div",{class:te(["flex rounded-full px-4 py-1",Ve(t.status)])},[e("span",{class:te(["text-sm font-semibold",qe(t.status)])},a(t.status),3)],2)]),e("td",Lt,[e("div",Ft,[r(He,{align:"right",width:"48"},{trigger:m(()=>[Rt]),content:m(()=>[t.status=="Pending"&&c.permissions.canEditQuotation?(n(),Q(ee,{key:0,href:i.route("quotation.edit",{id:t.id})},{svg:m(()=>[Ht]),text:m(()=>[Yt]),_:2},1032,["href"])):d("",!0),(t.status=="Pending"||t.status=="Rejected"||t.status=="Accepted")&&c.permissions.canDeleteQuotation?(n(),u("button",{key:1,type:"button",onClick:f=>ye(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Zt,8,Wt)):d("",!0),t.status=="Pending"&&c.permissions.canConvertQuotation?(n(),Q(ee,{key:2,href:i.route("quotation.order",{id:t.id}),type:"button",class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},{svg:m(()=>[Jt]),text:m(()=>[Dt]),_:2},1032,["href"])):d("",!0),t.status=="Pending"&&c.permissions.canConvertQuotation?(n(),u("button",{key:3,type:"button",onClick:f=>fe(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},os,8,es)):d("",!0),t.status=="Pending"&&t.status!="Completed"&&t.status!="Rejected"&&c.permissions.canConvertQuotation?(n(),u("button",{key:4,type:"button",onClick:f=>Be(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},is,8,ls)):d("",!0),c.permissions.canViewQuotation?(n(),Q(ee,{key:5,href:i.route("quotation.view",{id:t.id,source:"quotation.index"})},{svg:m(()=>[us]),text:m(()=>[ds]),_:2},1032,["href"])):d("",!0),e("button",{type:"button",onClick:f=>Ie(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},vs,8,rs),c.permissions.canCreateQuotation?(n(),u("button",{key:6,type:"button",onClick:f=>Ee(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},gs,8,ms)):d("",!0)]),_:2},1024)])])]))),128))])):(n(),u("tbody",ys,xs))])])]),c.data.data&&c.data.data.length>0?(n(),Q(Ze,{key:0,class:"mt-6",links:c.data.links},null,8,["links"])):d("",!0)]),r(Ke,{show:L.value},{default:m(()=>[ws,e("div",bs,[e("div",ks,[e("div",null,[r(w,{for:"to",value:"To:"}),r(oe,{id:"to",type:"email",modelValue:_.value.to,"onUpdate:modelValue":l[8]||(l[8]=t=>_.value.to=t),onInput:l[9]||(l[9]=t=>v.value.to="")},null,8,["modelValue"]),v.value.to?(n(),u("span",Cs,a(v.value.to),1)):d("",!0)]),e("div",null,[r(w,{for:"cc",value:"CC:"}),r(oe,{id:"cc",type:"text",placeholder:"Enter multiple emails",ref:"ccInput"},null,512)]),e("div",null,[r(w,{for:"from",value:"From:"}),e("div",Ss,[r(F,{options:c.email,modelValue:_.value.from,"onUpdate:modelValue":l[10]||(l[10]=t=>_.value.from=t),onOnchange:Ue},null,8,["options","modelValue"])]),v.value.from?(n(),u("span",Ts,a(v.value.from),1)):d("",!0)]),e("div",js,[e("div",zs,[r(w,{value:"Template:"}),e("div",Ms,[r(F,{options:c.emailTemplates,modelValue:_.value.template,"onUpdate:modelValue":l[11]||(l[11]=t=>_.value.template=t),onOnchange:Qe},null,8,["options","modelValue"])]),v.value.template?(n(),u("span",Is,a(v.value.template),1)):d("",!0)]),e("div",Vs,[r(w,{value:"PDF Page:"}),e("div",qs,[r(F,{options:c.pagetypes,modelValue:_.value.pagetype,"onUpdate:modelValue":l[12]||(l[12]=t=>_.value.pagetype=t),onOnchange:$e},null,8,["options","modelValue"])]),v.value.pagetype?(n(),u("span",Bs,a(v.value.pagetype),1)):d("",!0)])]),e("div",null,[r(w,{for:"subject",value:"Subject:"}),r(oe,{id:"subject",type:"text",modelValue:_.value.subject,"onUpdate:modelValue":l[13]||(l[13]=t=>_.value.subject=t),onInput:l[14]||(l[14]=t=>v.value.subject="")},null,8,["modelValue"]),v.value.subject?(n(),u("span",Ps,a(v.value.subject),1)):d("",!0)]),e("div",Ns,[r(w,{for:"content",value:"Content:"}),r(I(ot),{content:_.value.content,"onUpdate:content":[l[15]||(l[15]=t=>_.value.content=t),l[16]||(l[16]=t=>v.value.content="")],contentType:"html",theme:"snow",toolbar:"essential"},null,8,["content"]),v.value.content?(n(),u("span",As,a(v.value.content),1)):d("",!0)])])]),e("div",Os,[r(B,{onClick:Ge},{default:m(()=>[x("Cancel")]),_:1}),e("div",Es,[r(E,{class:"ml-3 w-20",onClick:Le,disabled:$.value},{default:m(()=>[$.value?(n(),u("span",Gs,[Qs,x(" Sending... ")])):(n(),u("span",Us,"Send Email"))]),_:1},8,["disabled"])])])]),_:1},8,["show"]),r(G,{show:W.value,onClose:A},{default:m(()=>[e("div",$s,[Ls,e("div",Fs,[r(B,{onClick:A},{default:m(()=>[x(" Cancel ")]),_:1}),r(We,{class:"ml-3",onClick:xe},{default:m(()=>[x(" Delete ")]),_:1})])])]),_:1},8,["show"]),r(G,{show:le.value,onClose:ae},{default:m(()=>[e("div",Rs,[Hs,e("div",Ys,[r(B,{onClick:ae},{default:m(()=>[x(" Cancel ")]),_:1}),e("div",Ws,[r(E,{onClick:we,type:"button"},{default:m(()=>[x("Approve")]),_:1})])])])]),_:1},8,["show"]),r(G,{show:K.value,onClose:ie},{default:m(()=>[e("div",Ks,[Xs,e("div",Zs,[r(B,{onClick:ie},{default:m(()=>[x(" Cancel ")]),_:1}),e("div",Js,[r(E,{onClick:be,type:"button"},{default:m(()=>[x("Approve")]),_:1})])])])]),_:1},8,["show"]),r(G,{show:X.value,onClose:ue,maxWidth:Me.value},{default:m(()=>[e("div",Ds,[e("div",eo,[e("div",to,[o.value.organization.id=="3"?(n(),u("div",so,[e("img",{class:"w-20 h-20",src:I(Y)+o.value.organization.logo,alt:"logo"},null,8,oo),lo,no])):d("",!0),o.value.organization.id=="1"||o.value.organization.id=="2"?(n(),u("div",ao,[e("img",{class:"w-full h-10",src:I(Y)+o.value.organization.logo,alt:"logo"},null,8,io),uo])):d("",!0),e("div",ro,[e("div",co,[_o,e("p",null,[e("strong",null,a(o.value.customers.customer_name),1)]),e("p",null,a(o.value.customers.person_name),1),e("p",null,a(o.value.customers.address),1),vo,e("div",mo,[ho,e("p",null,": "+a(o.value.customers.contact_no??"-"),1)]),e("div",po,[go,e("p",null,": "+a(o.value.customers.gst_no??"-"),1)])]),e("div",yo,[e("div",fo,[xo,e("p",null,": "+a(o.value.quotation_number),1)]),e("div",wo,[bo,e("p",null,": "+a(de(o.value.date)),1)]),e("div",ko,[Co,e("p",null,": "+a(o.value.organization.contact_no),1)]),e("div",So,[To,e("p",null,": "+a(o.value.organization.gst_no),1)])])]),e("div",jo,[e("table",null,[e("thead",null,[e("tr",null,[zo,o.value.organization.id!="3"?(n(),u("th",Mo,"MODEL")):d("",!0),Io,o.value.category=="Sales"&&o.value.organization.id!="3"?(n(),u("th",Vo,"IMAGE")):d("",!0),qo,Bo,Po,No,Ao,o.value.customers.gst_type=="IGST"?(n(),u("th",Oo,"IGST (%)")):d("",!0),o.value.customers.gst_type=="CGST/SGST"?(n(),u("th",Eo,"CGST (%)")):d("",!0),o.value.customers.gst_type=="CGST/SGST"?(n(),u("th",Go,"SGST (%)")):d("",!0),Qo,Uo,$o])]),e("tbody",null,[(n(!0),u(H,null,se(o.value.quotation_detail,(t,g)=>{var f,M,O,D;return n(),u("tr",{key:t.id},[e("td",null,a(g+1),1),o.value.organization.id!="3"?(n(),u("td",Lo,a(((f=t==null?void 0:t.product)==null?void 0:f.item_code)??"-"),1)):d("",!0),e("td",null,a(((M=t==null?void 0:t.product)==null?void 0:M.hsn_code)??"-"),1),o.value.category=="Sales"&&o.value.organization.id!="3"?(n(),u("td",Fo,[t.product&&t.product.image?(n(),u("img",{key:0,class:"mt-2",src:I(pe)+t.product.image,alt:"Product Photo"},null,8,Ro)):(n(),u("span",Ho,"No Image Available"))])):d("",!0),e("td",{innerHTML:Fe(t.description)},null,8,Yo),e("td",null,a((D=(O=t==null?void 0:t.product)==null?void 0:O.serial_numbers[0])!=null&&D.mrp?y(t.product.serial_numbers[0].mrp):"-"),1),e("td",null,a(y(t.price)),1),e("td",null,a(t.qty),1),e("td",null,a(y(t.total_price)),1),o.value.customers.gst_type=="IGST"?(n(),u("td",Wo,a(y(t.gst)??"-"),1)):d("",!0),o.value.customers.gst_type=="CGST/SGST"?(n(),u("td",Ko,a(y(t.gst/2)??"-"),1)):d("",!0),o.value.customers.gst_type=="CGST/SGST"?(n(),u("td",Xo,a(y(t.gst/2)??"-"),1)):d("",!0),e("td",null,a(y(t.total_gst_amount)),1),e("td",null,a(y(t.discount_amount)??"-"),1),e("td",null,a(y(t.total_amount)),1)])}),128))])])]),e("div",Zo,[Jo,e("p",null,a(o.value.note),1)]),e("div",Do,[e("div",el,[tl,e("div",sl,[ol,e("p",null,": "+a(o.value.validity),1)]),e("div",ll,[nl,e("p",null,": "+a(o.value.delivery),1)]),e("div",al,[il,e("p",null,": "+a(o.value.payment_terms),1)]),e("div",ul,[e("p",null,[x("Make PO in Name of "),e("strong",null,a(o.value.organization.name),1)])]),o.value.warranty!=null?(n(),u("div",dl,[rl,e("p",null,": "+a(o.value.warranty),1)])):d("",!0)]),e("div",cl,[e("div",_l,[vl,e("p",null,": "+a(y(o.value.sub_total)),1)]),e("div",ml,[hl,e("p",null,": "+a(y(o.value.total_discount)),1)]),o.value.customers.gst_type=="IGST"?(n(),u("div",pl,[gl,e("p",null,": "+a(y(o.value.igst)),1)])):d("",!0),o.value.customers.gst_type=="CGST/SGST"?(n(),u("div",yl,[fl,e("p",null,": "+a(y(o.value.cgst)),1)])):d("",!0),o.value.customers.gst_type=="CGST/SGST"?(n(),u("div",xl,[wl,e("p",null,": "+a(y(o.value.sgst)),1)])):d("",!0),e("div",bl,[kl,e("p",null,": "+a(y(o.value.total_amount)),1)])])]),e("div",Cl,[e("div",Sl,[Tl,o.value.organization.id=="1"?(n(),u("div",jl,[zl,e("p",null,": "+a(c.quotationbank.bank_name),1)])):d("",!0),o.value.organization.id=="1"?(n(),u("div",Ml,[Il,e("p",null,": "+a(c.quotationbank.branch_name),1)])):d("",!0),o.value.organization.id=="1"?(n(),u("div",Vl,[ql,e("p",null,": "+a(c.quotationbank.account_no),1)])):d("",!0),o.value.organization.id=="1"?(n(),u("div",Bl,[Pl,e("p",null,": "+a(c.quotationbank.ifsc_code),1)])):d("",!0),o.value.organization.id=="2"?(n(),u("div",Nl,[Al,e("p",null,": "+a(c.quotationHealthCareBankinfo.bank_name),1)])):d("",!0),o.value.organization.id=="2"?(n(),u("div",Ol,[El,e("p",null,": "+a(c.quotationHealthCareBankinfo.branch_name),1)])):d("",!0),o.value.organization.id=="2"?(n(),u("div",Gl,[Ql,e("p",null,": "+a(c.quotationHealthCareBankinfo.account_no),1)])):d("",!0),o.value.organization.id=="2"?(n(),u("div",Ul,[$l,e("p",null,": "+a(c.quotationHealthCareBankinfo.ifsc_code),1)])):d("",!0),o.value.organization.id=="3"?(n(),u("div",Ll,[Fl,e("p",null,": "+a(c.quotationNoxBank.bank_name),1)])):d("",!0),o.value.organization.id=="3"?(n(),u("div",Rl,[Hl,e("p",null,": "+a(c.quotationNoxBank.branch_name),1)])):d("",!0),o.value.organization.id=="3"?(n(),u("div",Yl,[Wl,e("p",null,": "+a(c.quotationNoxBank.account_no),1)])):d("",!0),o.value.organization.id=="3"?(n(),u("div",Kl,[Xl,e("p",null,": "+a(c.quotationNoxBank.ifsc_code),1)])):d("",!0)]),e("div",Zl,[Jl,e("p",null,[e("strong",null,a(o.value.organization.name),1)]),e("img",{class:"h-28",src:I(Y)+o.value.organization.signature,alt:"logo"},null,8,Dl)])])])]),e("div",en,[e("div",tn,[e("div",null,[e("p",null,a(o.value.organization.address_line_1),1),e("p",null,a(o.value.organization.address_line_2),1),e("p",null,a(o.value.organization.pincode)+" , "+a(o.value.organization.city),1)]),sn,o.value.organization.id=="1"||o.value.organization.id=="2"?(n(),u("div",on,[ln,o.value.category=="Sales"?(n(),u("p",nn,"+91 **********")):d("",!0),o.value.category=="Service"?(n(),u("p",an,"+91 **********")):d("",!0)])):d("",!0),o.value.organization.id=="3"?(n(),u("div",un,cn)):d("",!0),_n,o.value.organization.id=="1"||o.value.organization.id=="2"?(n(),u("div",vn,[mn,o.value.category=="Service"?(n(),u("p",hn,"<EMAIL>")):d("",!0),o.value.category=="Sales"?(n(),u("p",pn,"<EMAIL>")):d("",!0),o.value.organization.id=="1"?(n(),u("p",gn,"www.visionmedinst.com")):d("",!0)])):d("",!0),o.value.organization.id=="3"?(n(),u("div",yn,wn)):d("",!0)]),bn]),e("div",kn,[e("div",Cn,[e("div",Sn,[r(w,{for:"customer_id",value:"Page Type :"}),r(F,{options:c.pagetypes,modelValue:U.value,"onUpdate:modelValue":l[17]||(l[17]=t=>U.value=t),onOnchange:Ae},null,8,["options","modelValue"])]),e("div",Tn,[r(B,{onClick:ue},{default:m(()=>[x(" Cancel ")]),_:1}),e("div",jn,[r(E,{class:"ml-3 w-20",onClick:l[18]||(l[18]=t=>Oe(o.value.id,U.value))},{default:m(()=>[x("Generate Pdf ")]),_:1})])])])])])]),_:1},8,["show","maxWidth"]),r(G,{show:Z.value,onClose:J},{default:m(()=>[e("div",zn,[Mn,e("div",In,[r(B,{onClick:J},{default:m(()=>[x(" Cancel ")]),_:1}),e("div",Vn,[r(E,{onClick:Pe,type:"button"},{default:m(()=>[x("Reject")]),_:1})])])])]),_:1},8,["show"])]),_:1})],64))}},ta=at(qn,[["__scopeId","data-v-48fd1fd6"]]);export{ta as default};
