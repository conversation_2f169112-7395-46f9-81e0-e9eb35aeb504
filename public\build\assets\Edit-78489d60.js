import{K as I,r as g,l as K,o as _,c as y,a as o,u as s,w as c,F as L,Z,b as n,d as z,k as x,v as w,e as O,f as v,t as G,g as D,T as J}from"./app-b7a94f67.js";import{_ as Q,a as R}from"./AdminLayout-0f1fdf67.js";import{_ as p}from"./InputError-86b88c86.js";import{_ as i}from"./InputLabel-11b5d690.js";import{P as X}from"./PrimaryButton-4ffecd1c.js";import{_ as Y}from"./SearchableDropdown-711fb977.js";import{_ as ee}from"./FileUpload-aa40fe3a.js";import{_ as te}from"./FileViewer-d3655eec.js";import{D as ae}from"./DangerButton-a612a79a.js";import{M as N}from"./Modal-e44dcdf0.js";import{_ as F}from"./SecondaryButton-c893313c.js";import{_ as u}from"./TextInput-fea73171.js";import{u as se}from"./index-5a4eda7d.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const oe={class:"animate-top h-screen"},ne=n("div",{class:"sm:flex sm:items-center"},[n("div",{class:"sm:flex-auto"},[n("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Maintenance Contract")])],-1),le={class:"mt-8 bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},de=["onSubmit"],ie={class:"border-b border-gray-900/10 pb-12"},me={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},re={class:"sm:col-span-4"},pe={class:"sm:col-span-4"},ue={class:"sm:col-span-2"},_e={class:"sm:col-span-2"},ce={class:"sm:col-span-3"},ge={class:"sm:col-span-3"},ve={class:"sm:col-span-3"},fe={class:"relative mt-2"},ye={class:"sm:col-span-3"},he={class:"sm:col-span-5"},xe={class:"sm:col-span-2"},we={class:"sm:col-span-2"},be={class:"sm:col-span-3"},Ve={class:"sm:col-span-2"},De={class:"sm:col-span-4"},Me=n("div",{class:"sm:col-span-10"},null,-1),Ce={key:0,class:"sm:col-span-3"},ke={key:1,class:"sm:col-span-3"},Se={key:2,class:"sm:col-span-3"},$e={key:3,class:"sm:col-span-3"},Ue=n("br",null,null,-1),Te={key:0,class:"bg-white p-1 shadow sm:rounded-lg border"},Pe={class:"min-w-full divide-y divide-gray-300"},Ie=n("thead",{class:"bg-gray-50"},[n("tr",null,[n("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"UPLOADED DOCUMENT"),n("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1),Oe={class:"divide-y divide-gray-300 bg-white"},Ne={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},Fe={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},Be=n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[n("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),Ee=[Be],je={class:"flex mt-6 items-center justify-between"},Ae={class:"ml-auto flex items-center justify-end gap-x-6"},We=n("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),He={key:0,class:"text-sm text-gray-600"},qe={class:"p-6"},Ke=n("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this document? ",-1),Le={class:"mt-6 flex justify-end"},Ze={class:"p-6"},ze={class:"mt-6 px-4 flex justify-end"},mt={__name:"Edit",props:["data","maintenance_type","filepath"],setup(B){const E=I().props.filepath.view,l=I().props.data,d=g({pm_date_1:l.pm_date_1||"",pm_date_2:l.pm_date_2||"",pm_date_3:l.pm_date_3||"",pm_date_4:l.pm_date_4||""}),t=se("post","/maintenance-contract",{id:l.id,hospital_name:l.hospital_name,address:l.address,city:l.city,contact_no:l.contact_no,contract_start_date:l.contract_start_date,contract_end_date:l.contract_end_date,maintenance_type:l.maintenance_type,time_period:l.time_period,product_name:l.product_name,price:l.price,pm_date_1:"",pm_date_2:"",pm_date_3:"",pm_date_4:"",company_name:l.company_name,invoice_number:l.invoice_number,name:l.name}),b=g(l.pm_date_1?l.pm_date_2?l.pm_date_3?l.pm_date_4?4:3:2:1:0),h=g(b.value),S=g("");K(b,m=>{h.value=parseInt(m,10)||0,M()});const M=()=>{const m=h.value;if(!t.contract_start_date){d.value={pm_date_1:"",pm_date_2:"",pm_date_3:"",pm_date_4:""};return}const e=new Date(t.contract_start_date);if(d.value={pm_date_1:"",pm_date_2:"",pm_date_3:"",pm_date_4:""},m===2){const a=new Date(e),r=new Date(e);a.setMonth(a.getMonth()+6),r.setMonth(r.getMonth()+11),d.value={pm_date_1:a.toISOString().split("T")[0],pm_date_2:r.toISOString().split("T")[0]}}else if(m===3){const a=new Date(e),r=new Date(e),f=new Date(e);a.setMonth(a.getMonth()+4),r.setMonth(r.getMonth()+8),f.setMonth(f.getMonth()+12),d.value={pm_date_1:a.toISOString().split("T")[0],pm_date_2:r.toISOString().split("T")[0],pm_date_3:f.toISOString().split("T")[0]}}else if(m===4){const a=new Date(e),r=new Date(e),f=new Date(e),k=new Date(e);a.setMonth(a.getMonth()+3),r.setMonth(r.getMonth()+6),f.setMonth(f.getMonth()+9),k.setMonth(k.getMonth()+12),d.value={pm_date_1:a.toISOString().split("T")[0],pm_date_2:r.toISOString().split("T")[0],pm_date_3:f.toISOString().split("T")[0],pm_date_4:k.toISOString().split("T")[0]}}S.value=m<=0?"Please enter a valid number of PMs.":""},V=(m,e)=>{t[m]=e},j=(m,e,a)=>{t[m]=e},A=m=>{t.name=m},$=g(!1),U=()=>{$.value=!1},C=g(!1),T=g(null),W=g("custom"),H=m=>{T.value=m,C.value=!0},P=()=>{C.value=!1},q=()=>{t.pm_date_1=d.value.pm_date_1,t.pm_date_2=d.value.pm_date_2,t.pm_date_3=d.value.pm_date_3,t.pm_date_4=d.value.pm_date_4,t.submit({preserveScroll:!0,onSuccess:()=>t.reset()})};return(m,e)=>(_(),y(L,null,[o(s(Z),{title:"Maintenance Contract"}),o(Q,null,{default:c(()=>[n("div",oe,[ne,n("div",le,[n("form",{onSubmit:z(q,["prevent"]),class:""},[n("div",ie,[n("div",me,[n("div",re,[o(i,{for:"hospital_name",value:"Hospital Name"}),o(u,{id:"hospital_name",hospital_name:"text",modelValue:s(t).hospital_name,"onUpdate:modelValue":e[0]||(e[0]=a=>s(t).hospital_name=a),autocomplete:"hospital_name",onChange:e[1]||(e[1]=a=>s(t).validate("hospital_name"))},null,8,["modelValue"]),o(p,{class:"",message:s(t).errors.hospital_name},null,8,["message"])]),n("div",pe,[o(i,{for:"address",value:"Address"}),o(u,{id:"address",type:"text",modelValue:s(t).address,"onUpdate:modelValue":e[2]||(e[2]=a=>s(t).address=a),onChange:e[3]||(e[3]=a=>s(t).validate("address"))},null,8,["modelValue"]),o(p,{class:"",message:s(t).errors.address},null,8,["message"])]),n("div",ue,[o(i,{for:"contact_no",value:"Contact No"}),o(u,{id:"contact_no",contact_no:"text",modelValue:s(t).contact_no,"onUpdate:modelValue":e[4]||(e[4]=a=>s(t).contact_no=a),numeric:!0,autocomplete:"contact_no",onChange:e[5]||(e[5]=a=>s(t).validate("contact_no"))},null,8,["modelValue"]),o(p,{class:"",message:s(t).errors.contact_no},null,8,["message"])]),n("div",_e,[o(i,{for:"city",value:"City"}),o(u,{id:"city",city:"text",modelValue:s(t).city,"onUpdate:modelValue":e[6]||(e[6]=a=>s(t).city=a),autocomplete:"city",onChange:e[7]||(e[7]=a=>s(t).validate("city"))},null,8,["modelValue"]),o(p,{class:"",message:s(t).errors.city},null,8,["message"])]),n("div",ce,[o(i,{for:"contract_start_date",value:"Contract Start"}),x(n("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date",onChange:e[8]||(e[8]=a=>s(t).validate("contract_start_date")),"onUpdate:modelValue":e[9]||(e[9]=a=>s(t).contract_start_date=a)},null,544),[[w,s(t).contract_start_date]]),s(t).invalid("contract_start_date")?(_(),O(p,{key:0,class:"",message:s(t).errors.contract_start_date},null,8,["message"])):v("",!0)]),n("div",ge,[o(i,{for:"contract_end_date",value:"Contract End"}),x(n("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date",onChange:e[10]||(e[10]=a=>s(t).validate("contract_end_date")),"onUpdate:modelValue":e[11]||(e[11]=a=>s(t).contract_end_date=a)},null,544),[[w,s(t).contract_end_date]]),s(t).invalid("contract_end_date")?(_(),O(p,{key:0,class:"",message:s(t).errors.contract_end_date},null,8,["message"])):v("",!0)]),n("div",ve,[o(i,{for:"type",value:"Maintenance Type"}),n("div",fe,[o(Y,{options:B.maintenance_type,modelValue:s(t).maintenance_type,"onUpdate:modelValue":e[12]||(e[12]=a=>s(t).maintenance_type=a),onOnchange:e[13]||(e[13]=(a,r)=>j("maintenance_type",a,r)),onChange:e[14]||(e[14]=a=>s(t).validate("maintenance_type"))},null,8,["options","modelValue"])]),o(p,{class:"",message:s(t).errors.maintenance_type},null,8,["message"])]),n("div",ye,[o(i,{for:"time_period",value:"Time Period"}),o(u,{id:"time_period",time_period:"text",modelValue:s(t).time_period,"onUpdate:modelValue":e[15]||(e[15]=a=>s(t).time_period=a),autocomplete:"time_period",onChange:e[16]||(e[16]=a=>s(t).validate("time_period"))},null,8,["modelValue"]),o(p,{class:"",message:s(t).errors.time_period},null,8,["message"])]),n("div",he,[o(i,{for:"product_name",value:"Equipment Name"}),o(u,{id:"product_name",product_name:"text",modelValue:s(t).product_name,"onUpdate:modelValue":e[17]||(e[17]=a=>s(t).product_name=a),autocomplete:"product_name",onChange:e[18]||(e[18]=a=>s(t).validate("product_name"))},null,8,["modelValue"]),o(p,{class:"",message:s(t).errors.product_name},null,8,["message"])]),n("div",xe,[o(i,{for:"price",value:"Price"}),o(u,{id:"price",price:"text",modelValue:s(t).price,"onUpdate:modelValue":e[19]||(e[19]=a=>s(t).price=a),autocomplete:"price",onChange:e[20]||(e[20]=a=>s(t).validate("price"))},null,8,["modelValue"]),o(p,{class:"",message:s(t).errors.price},null,8,["message"])]),n("div",we,[o(i,{for:"company_name",value:"Company Name"}),o(u,{id:"company_name",company_name:"text",modelValue:s(t).company_name,"onUpdate:modelValue":e[21]||(e[21]=a=>s(t).company_name=a),autocomplete:"company_name",onChange:e[22]||(e[22]=a=>s(t).validate("company_name"))},null,8,["modelValue"]),o(p,{class:"",message:s(t).errors.company_name},null,8,["message"])]),n("div",be,[o(i,{for:"invoice_number",value:"Invoice Number"}),o(u,{id:"invoice_number",invoice_number:"text",modelValue:s(t).invoice_number,"onUpdate:modelValue":e[23]||(e[23]=a=>s(t).invoice_number=a),autocomplete:"invoice_number",onChange:e[24]||(e[24]=a=>s(t).validate("invoice_number"))},null,8,["modelValue"]),o(p,{class:"",message:s(t).errors.invoice_number},null,8,["message"])]),n("div",Ve,[o(i,{for:"inputField",value:"How Many PM ?"}),o(u,{id:"inputField",modelValue:b.value,"onUpdate:modelValue":e[25]||(e[25]=a=>b.value=a),onInput:M,onBlur:M},null,8,["modelValue"]),o(p,{message:S.value},null,8,["message"])]),n("div",De,[o(i,{for:"name",value:"Upload Document"}),o(ee,{label:"Upload Document",inputId:"name",inputName:"name",uploadedFiles:s(t).name,onFile:A},null,8,["uploadedFiles"])]),Me,h.value>=1?(_(),y("div",Ce,[o(i,{for:"pm_date_1",value:"PM Date 1"}),x(n("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date",id:"pm_date_1","onUpdate:modelValue":e[26]||(e[26]=a=>d.value.pm_date_1=a),onChange:e[27]||(e[27]=a=>V("pm_date_1",d.value.pm_date_1))},null,544),[[w,d.value.pm_date_1]])])):v("",!0),h.value>=2?(_(),y("div",ke,[o(i,{for:"pm_date_2",value:"PM Date 2"}),x(n("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date",id:"pm_date_2","onUpdate:modelValue":e[28]||(e[28]=a=>d.value.pm_date_2=a),onChange:e[29]||(e[29]=a=>V("pm_date_2",d.value.pm_date_2))},null,544),[[w,d.value.pm_date_2]])])):v("",!0),h.value>=3?(_(),y("div",Se,[o(i,{for:"pm_date_3",value:"PM Date 3"}),x(n("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date",id:"pm_date_3","onUpdate:modelValue":e[30]||(e[30]=a=>d.value.pm_date_3=a),onChange:e[31]||(e[31]=a=>V("pm_date_3",d.value.pm_date_3))},null,544),[[w,d.value.pm_date_3]])])):v("",!0),h.value>=4?(_(),y("div",$e,[o(i,{for:"pm_date_4",value:"PM Date 4"}),x(n("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date",id:"pm_date_4","onUpdate:modelValue":e[32]||(e[32]=a=>d.value.pm_date_4=a),onChange:e[33]||(e[33]=a=>V("pm_date_4",d.value.pm_date_4))},null,544),[[w,d.value.pm_date_4]])])):v("",!0)]),Ue,s(t).name?(_(),y("div",Te,[n("table",Pe,[Ie,n("tbody",Oe,[n("tr",null,[n("td",Ne,G(s(t).name),1),n("td",Fe,[n("button",{type:"button",onClick:e[34]||(e[34]=a=>H(s(t).name))},Ee)])])])])])):v("",!0)]),n("div",je,[n("div",Ae,[o(R,{href:m.route("maintenance-contract.index")},{svg:c(()=>[We]),_:1},8,["href"]),o(X,{disabled:s(t).processing},{default:c(()=>[D("Save")]),_:1},8,["disabled"]),o(J,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:c(()=>[s(t).recentlySuccessful?(_(),y("p",He,"Saved.")):v("",!0)]),_:1})])])],40,de)])]),o(N,{show:$.value,onClose:U},{default:c(()=>[n("div",qe,[Ke,n("div",Le,[o(F,{onClick:U},{default:c(()=>[D(" Cancel ")]),_:1}),o(ae,{class:"ml-3",onClick:m.deleteDocument},{default:c(()=>[D(" Delete ")]),_:1},8,["onClick"])])])]),_:1},8,["show"]),o(N,{show:C.value,onClose:P,maxWidth:W.value},{default:c(()=>[n("div",Ze,[o(te,{fileUrl:s(E)+T.value},null,8,["fileUrl"]),n("div",ze,[o(F,{onClick:P},{default:c(()=>[D(" Cancel ")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}};export{mt as default};
