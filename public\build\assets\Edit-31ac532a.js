import{_ as o}from"./AdminLayout-db62264f.js";import i from"./DeleteUserForm-2eb83a16.js";import m from"./UpdatePasswordForm-ba0a6990.js";import r from"./UpdateProfileInformationForm-98369d43.js";import{o as l,c,a as t,u as n,w as e,F as p,Z as d,b as s}from"./app-21e66fd5.js";import"./DangerButton-b1b34a7e.js";import"./_plugin-vue_export-helper-c27b6911.js";import"./InputError-01d93b90.js";import"./InputLabel-4a50badc.js";import"./Modal-7ff7f630.js";/* empty css                                                              */import"./SecondaryButton-0406f438.js";import"./TextInput-625f6add.js";import"./PrimaryButton-ed35dcb4.js";import"./TextArea-2c14c909.js";const _=s("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1),u={class:""},f={class:"max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6"},h={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},x={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},g={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},z={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,y)=>(l(),c(p,null,[t(n(d),{title:"Profile"}),t(o,null,{header:e(()=>[_]),default:e(()=>[s("div",u,[s("div",f,[s("div",h,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",g,[t(i,{class:"max-w-xl"})])])])]),_:1})],64))}};export{z as default};
