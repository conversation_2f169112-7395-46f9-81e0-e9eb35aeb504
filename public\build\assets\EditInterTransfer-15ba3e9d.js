import{o as i,c as k,a as o,u as a,w as p,F as T,Z as D,b as t,d as B,n as c,e as u,f as m,k as w,v as I,g as S,T as E,s as $,x as U}from"./app-16701445.js";import{_ as C,a as F}from"./AdminLayout-e15be38d.js";import{_}from"./InputError-11376965.js";import{_ as f}from"./InputLabel-d69efee6.js";import{P as N}from"./PrimaryButton-eddb8b77.js";import{_ as A}from"./TextInput-764e3400.js";import{_ as b}from"./SearchableDropdownNew-7be1679d.js";import{u as O}from"./index-********.js";import{_ as P}from"./_plugin-vue_export-helper-c27b6911.js";const v=d=>($("data-v-14d7e529"),d=d(),U(),d),j={class:"h-screen animate-top"},M={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},z=v(()=>t("div",{class:"sm:flex sm:items-center"},[t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Internal Bank Transaction")])],-1)),Z=["onSubmit"],q={class:"border-b border-gray-900/10 pb-12"},G={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},H={class:"sm:col-span-3"},J={class:"relative mt-2"},K={class:"sm:col-span-3"},L={class:"relative mt-2"},Q={class:"sm:col-span-3"},R={class:"sm:col-span-3"},W={class:"sm:col-span-3"},X={class:"relative mt-2"},Y={class:"flex mt-6 items-center justify-between"},ee={class:"ml-auto flex items-center justify-end gap-x-6"},ae=v(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),te={key:0,class:"text-sm text-gray-600"},se={__name:"EditInterTransfer",props:["bankinfo","accounttype","transferData"],setup(d){const r=d,e=O("post","/updateinternalbanktransfer",{id:r.transferData.id,related_id:r.transferData.related_id,from_bank:r.transferData.from_bank,from_bank_name:r.transferData.from_bank_name,to_bank:r.transferData.to_bank,to_bank_name:r.transferData.to_bank_name,account_type:r.transferData.account_type,date:r.transferData.date,amount:r.transferData.amount}),g=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()}),y=(l,s)=>{e.from_bank=l,e.from_bank_name=s,e.errors.from_bank=null},x=(l,s)=>{e.to_bank=l,e.to_bank_name=s,e.errors.to_bank=null},h=(l,s)=>{e.account_type=l,e.errors.account_type=null},V=l=>{e.errors[l]=null};return(l,s)=>(i(),k(T,null,[o(a(D),{title:"Edit Internal Bank Transaction"}),o(C,null,{default:p(()=>[t("div",j,[t("div",M,[z,t("form",{onSubmit:B(g,["prevent"]),class:""},[t("div",q,[t("div",G,[t("div",H,[o(f,{for:"from_bank",value:"From Bank"}),t("div",J,[o(b,{options:d.bankinfo,modelValue:a(e).from_bank,"onUpdate:modelValue":s[0]||(s[0]=n=>a(e).from_bank=n),onOnchange:y,class:c({"error rounded-md":a(e).errors.from_bank})},null,8,["options","modelValue","class"]),a(e).invalid("from_bank")?(i(),u(_,{key:0,class:"",message:a(e).errors.from_bank},null,8,["message"])):m("",!0)])]),t("div",K,[o(f,{for:"to_bank",value:"To Bank"}),t("div",L,[o(b,{options:d.bankinfo,modelValue:a(e).to_bank,"onUpdate:modelValue":s[1]||(s[1]=n=>a(e).to_bank=n),onOnchange:x,class:c({"error rounded-md":a(e).errors.to_bank})},null,8,["options","modelValue","class"]),a(e).invalid("to_bank")?(i(),u(_,{key:0,class:"",message:a(e).errors.to_bank},null,8,["message"])):m("",!0)])]),t("div",Q,[o(f,{for:"date",value:"Payment Date"}),w(t("input",{"onUpdate:modelValue":s[2]||(s[2]=n=>a(e).date=n),class:c(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":a(e).errors.date}]),type:"date"},null,2),[[I,a(e).date]]),a(e).invalid("date")?(i(),u(_,{key:0,class:"",message:a(e).errors.date},null,8,["message"])):m("",!0)]),t("div",R,[o(f,{for:"amount",value:"Amount"}),o(A,{id:"amount",type:"text",onChange:s[3]||(s[3]=n=>V("amount")),modelValue:a(e).amount,"onUpdate:modelValue":s[4]||(s[4]=n=>a(e).amount=n),class:c({"error rounded-md":a(e).errors.amount})},null,8,["modelValue","class"]),a(e).invalid("amount")?(i(),u(_,{key:0,class:"",message:a(e).errors.amount},null,8,["message"])):m("",!0)]),t("div",W,[o(f,{for:"account_type",value:"Account Type"}),t("div",X,[o(b,{options:d.accounttype,modelValue:a(e).account_type,"onUpdate:modelValue":s[5]||(s[5]=n=>a(e).account_type=n),onOnchange:h,class:c({"error rounded-md":a(e).errors.account_type})},null,8,["options","modelValue","class"]),a(e).invalid("account_type")?(i(),u(_,{key:0,class:"",message:a(e).errors.account_type},null,8,["message"])):m("",!0)])])])]),t("div",Y,[t("div",ee,[o(F,{href:l.route("banktransaction.index")},{svg:p(()=>[ae]),_:1},8,["href"]),o(N,{disabled:a(e).processing},{default:p(()=>[S("Update")]),_:1},8,["disabled"]),o(E,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:p(()=>[a(e).recentlySuccessful?(i(),k("p",te,"Saved.")):m("",!0)]),_:1})])])],40,Z)])])]),_:1})],64))}},_e=P(se,[["__scopeId","data-v-14d7e529"]]);export{_e as default};
