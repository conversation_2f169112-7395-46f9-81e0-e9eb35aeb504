import{o as d,c as f,a as r,u as s,w as _,F as k,Z as w,b as t,t as S,d as C,n as l,e as u,f as c,k as $,v as N,g as U,T}from"./app-97275a91.js";import{_ as B,a as P}from"./AdminLayout-595ad5a7.js";import{_ as p}from"./InputError-b3250228.js";import{_ as i}from"./InputLabel-eb73087c.js";import{P as R}from"./PrimaryButton-46ac4375.js";import{_ as g}from"./TextInput-11c46564.js";import{_ as y}from"./SearchableDropdown-9d1b12d3.js";import{_ as D}from"./MultipleFileUpload-368d3540.js";import{u as F}from"./index-05d29b1c.js";import"./_plugin-vue_export-helper-c27b6911.js";const E={class:"h-screen animate-top"},j={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},M={class:"sm:flex sm:items-center"},O=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create Report")],-1),z={class:"text-sm font-semibold text-gray-900"},A=["onSubmit"],I={class:"border-b border-gray-900/10 pb-12"},Z={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10"},q={class:"sm:col-span-3"},G={class:"sm:col-span-3"},H={class:"sm:col-span-3"},J={class:"sm:col-span-3"},K={class:"relative mt-2"},L={class:"sm:col-span-3"},Q={class:"relative mt-2"},W={class:"sm:col-span-3"},X={class:"sm:col-span-4"},Y={class:"w-full"},ee={class:"flex mt-6 items-center justify-between"},se={class:"ml-auto flex items-center justify-end gap-x-6"},te=t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),oe={key:0,class:"text-sm text-gray-600"},ve={__name:"Add",props:["reporttype","serviceperson","customer"],setup(m){const e=F("post","/service-reports",{customer_id:m.customer.id,product_code:"",product_name:"",date:"",serial_no:"",document:"",type:"",service_engineer_id:""}),x=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()}),h=(n,o)=>{e.type=n,e.errors.type=null},b=(n,o)=>{e.service_engineer_id=n,e.errors.service_engineer_id=null},V=n=>{e.document=n},v=n=>{e.errors[n]=null};return(n,o)=>(d(),f(k,null,[r(s(w),{title:"Create Report"}),r(B,null,{default:_(()=>[t("div",E,[t("div",j,[t("div",M,[O,t("p",z,S(m.customer.customer_name),1)]),t("form",{onSubmit:C(x,["prevent"]),class:""},[t("div",I,[t("div",Z,[t("div",q,[r(i,{for:"product_code",value:"Product Code"}),r(g,{id:"product_code",type:"text",onChange:o[0]||(o[0]=a=>v("data.product_code")),modelValue:s(e).product_code,"onUpdate:modelValue":o[1]||(o[1]=a=>s(e).product_code=a),class:l({"error rounded-md":s(e).errors["data.product_code"]})},null,8,["modelValue","class"])]),t("div",G,[r(i,{for:"product_name",value:"Product Name"}),r(g,{id:"product_name",type:"text",onChange:o[2]||(o[2]=a=>v("data.product_name")),modelValue:s(e).product_name,"onUpdate:modelValue":o[3]||(o[3]=a=>s(e).product_name=a),class:l({"error rounded-md":s(e).errors["data.product_name"]})},null,8,["modelValue","class"]),s(e).invalid("product_name")?(d(),u(p,{key:0,class:"",message:s(e).errors.product_name},null,8,["message"])):c("",!0)]),t("div",H,[r(i,{for:"serial_no",value:"Serial No"}),r(g,{id:"serial_no",type:"text",onChange:o[4]||(o[4]=a=>v("data.serial_no")),modelValue:s(e).serial_no,"onUpdate:modelValue":o[5]||(o[5]=a=>s(e).serial_no=a),class:l({"error rounded-md":s(e).errors["data.serial_no"]})},null,8,["modelValue","class"]),s(e).invalid("serial_no")?(d(),u(p,{key:0,class:"",message:s(e).errors.serial_no},null,8,["message"])):c("",!0)]),t("div",J,[r(i,{for:"type",value:"Report Type"}),t("div",K,[r(y,{options:m.reporttype,modelValue:s(e).type,"onUpdate:modelValue":o[6]||(o[6]=a=>s(e).type=a),onOnchange:h,class:l({"error rounded-md":s(e).errors.type})},null,8,["options","modelValue","class"]),s(e).invalid("type")?(d(),u(p,{key:0,class:"",message:s(e).errors.type},null,8,["message"])):c("",!0)])]),t("div",L,[r(i,{for:"service_engineer_id",value:"Service Engineer"}),t("div",Q,[r(y,{options:m.serviceperson,modelValue:s(e).service_engineer_id,"onUpdate:modelValue":o[7]||(o[7]=a=>s(e).service_engineer_id=a),onOnchange:b,class:l({"error rounded-md":s(e).errors.service_engineer_id})},null,8,["options","modelValue","class"]),s(e).invalid("service_engineer_id")?(d(),u(p,{key:0,class:"",message:s(e).errors.service_engineer_id},null,8,["message"])):c("",!0)])]),t("div",W,[r(i,{for:"date",value:"Date"}),$(t("input",{"onUpdate:modelValue":o[8]||(o[8]=a=>s(e).date=a),class:l(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":s(e).errors["data.date"]}]),type:"date"},null,2),[[N,s(e).date]]),s(e).invalid("date")?(d(),u(p,{key:0,class:"",message:s(e).errors.date},null,8,["message"])):c("",!0)]),t("div",X,[t("div",Y,[r(i,{for:"note",value:"Upload Report"}),r(D,{inputId:"document",inputName:"document",onFiles:V})])])])]),t("div",ee,[t("div",se,[r(P,{href:n.route("service-reports.show",{id:m.customer.id})},{svg:_(()=>[te]),_:1},8,["href"]),r(R,{disabled:s(e).processing},{default:_(()=>[U("Save")]),_:1},8,["disabled"]),r(T,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:_(()=>[s(e).recentlySuccessful?(d(),f("p",oe,"Saved.")):c("",!0)]),_:1})])])],40,A)])])]),_:1})],64))}};export{ve as default};
