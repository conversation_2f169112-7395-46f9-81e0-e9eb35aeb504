import{A as i,o as r,c as _,b as e,a as s,w as l,u as a,y as p,Z as d,s as u,x as h}from"./app-6cdaf2bc.js";import{_ as f}from"./_plugin-vue_export-helper-c27b6911.js";const o=t=>(u("data-v-83a23f40"),t=t(),h(),t),m={class:"min-h-screen loginview flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gray-100"},v={class:"container"},g=o(()=>e("h2",null,"Access Denied",-1)),x=o(()=>e("p",null,"You do not have permission to access this page/event. ",-1)),y=o(()=>e("p",null,"Please contact your administrator if you believe this is an error.",-1)),w=["href"],R={__name:"Registerv2",setup(t){return(n,A)=>{const c=i("ApplicationLogo");return r(),_("div",m,[e("div",null,[s(a(p),{href:"/"},{default:l(()=>[s(c,{class:"w-60 fill-current text-gray-500"})]),_:1})]),s(a(d),{title:"Register"}),e("div",v,[g,x,y,e("h4",null,[e("a",{href:n.route("login")},"Return to Login",8,w)])])])}}},B=f(R,[["__scopeId","data-v-83a23f40"]]);export{B as default};
