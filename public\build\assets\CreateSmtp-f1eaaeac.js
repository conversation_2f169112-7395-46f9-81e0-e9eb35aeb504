import{h as _,o as l,c as n,a,u as t,w as u,F as f,Z as y,b as r,d as x,t as d,f as i,g,T as V}from"./app-4c3f0163.js";import{_ as h,a as b}from"./AdminLayout-36b0d46a.js";/* empty css                                                              */import{_ as m}from"./InputLabel-d6414ecf.js";import{_ as p}from"./TextInput-e8957d69.js";import{P as w}from"./PrimaryButton-353715d1.js";import"./_plugin-vue_export-helper-c27b6911.js";const C={class:"animate-top"},S={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},$=r("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create SMTP",-1),k=["onSubmit"],U={class:"border-b border-gray-900/10 pb-12"},P={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},T={class:"sm:col-span-3"},E={key:0,class:"mt-1 text-sm text-red-500"},N={class:"sm:col-span-3"},B={key:0,class:"mt-1 text-sm text-red-500"},F={class:"sm:col-span-3"},M={key:0,class:"mt-1 text-sm text-red-500"},j={class:"sm:col-span-3"},D={key:0,class:"mt-1 text-sm text-red-500"},H={class:"sm:col-span-3"},Z={key:0,class:"mt-1 text-sm text-red-500"},q={class:"sm:col-span-3"},z={key:0,class:"mt-1 text-sm text-red-500"},A={class:"sm:col-span-3"},G={key:0,class:"mt-1 text-sm text-red-500"},I={class:"flex mt-6 items-center justify-between"},J={class:"ml-auto flex items-center justify-end gap-x-6"},K=r("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),L={key:0,class:"text-sm text-gray-600"},se={__name:"CreateSmtp",setup(O){const e=_({host:"",port:"",username:"",password:"",email:"",encryption:"",name:""}),v=()=>{console.log("Submitting:",e),e.post(route("mail-configs.store"),{preserveScroll:!0,onSuccess:()=>{console.log("Form submitted successfully"),e.reset()},onError:c=>console.log("Errors:",c)})};return(c,s)=>(l(),n(f,null,[a(t(y),{title:"SMTP"}),a(h,null,{default:u(()=>[r("div",C,[r("div",S,[$,r("form",{onSubmit:x(v,["prevent"]),class:""},[r("div",U,[r("div",P,[r("div",T,[a(m,{for:"host",value:"Host"}),a(p,{id:"host",type:"text",modelValue:t(e).host,"onUpdate:modelValue":s[0]||(s[0]=o=>t(e).host=o),autocomplete:"host",onChange:s[1]||(s[1]=o=>t(e).validate("host"))},null,8,["modelValue"]),t(e).errors.host?(l(),n("p",E,d(t(e).errors.host),1)):i("",!0)]),r("div",N,[a(m,{for:"port",value:"Port"}),a(p,{id:"port",type:"text",modelValue:t(e).port,"onUpdate:modelValue":s[2]||(s[2]=o=>t(e).port=o),autocomplete:"port",onChange:s[3]||(s[3]=o=>t(e).validate("port"))},null,8,["modelValue"]),t(e).errors.port?(l(),n("p",B,d(t(e).errors.port),1)):i("",!0)]),r("div",F,[a(m,{for:"username",value:"Username"}),a(p,{id:"username",type:"text",modelValue:t(e).username,"onUpdate:modelValue":s[4]||(s[4]=o=>t(e).username=o),autocomplete:"username",onChange:s[5]||(s[5]=o=>t(e).validate("username"))},null,8,["modelValue"]),t(e).errors.username?(l(),n("p",M,d(t(e).errors.username),1)):i("",!0)]),r("div",j,[a(m,{for:"password",value:"Password"}),a(p,{id:"password",type:"text",modelValue:t(e).password,"onUpdate:modelValue":s[6]||(s[6]=o=>t(e).password=o),autocomplete:"password",onChange:s[7]||(s[7]=o=>t(e).validate("password"))},null,8,["modelValue"]),t(e).errors.password?(l(),n("p",D,d(t(e).errors.password),1)):i("",!0)]),r("div",H,[a(m,{for:"email",value:"Email"}),a(p,{id:"email",type:"text",modelValue:t(e).email,"onUpdate:modelValue":s[8]||(s[8]=o=>t(e).email=o),autocomplete:"email",onChange:s[9]||(s[9]=o=>t(e).validate("email"))},null,8,["modelValue"]),t(e).errors.email?(l(),n("p",Z,d(t(e).errors.email),1)):i("",!0)]),r("div",q,[a(m,{for:"encryption",value:"Encryption"}),a(p,{id:"encryption",type:"text",modelValue:t(e).encryption,"onUpdate:modelValue":s[10]||(s[10]=o=>t(e).encryption=o),autocomplete:"encryption",onChange:s[11]||(s[11]=o=>t(e).validate("encryption"))},null,8,["modelValue"]),t(e).errors.encryption?(l(),n("p",z,d(t(e).errors.encryption),1)):i("",!0)]),r("div",A,[a(m,{for:"name",value:"Name"}),a(p,{id:"name",type:"text",modelValue:t(e).name,"onUpdate:modelValue":s[12]||(s[12]=o=>t(e).name=o),autocomplete:"name",onChange:s[13]||(s[13]=o=>t(e).validate("name"))},null,8,["modelValue"]),t(e).errors.name?(l(),n("p",G,d(t(e).errors.name),1)):i("",!0)])])]),r("div",I,[r("div",J,[a(b,{href:c.route("mail-configs.index")},{svg:u(()=>[K]),_:1},8,["href"]),a(w,{disabled:t(e).processing},{default:u(()=>[g("Save")]),_:1},8,["disabled"]),a(V,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:u(()=>[t(e).recentlySuccessful?(l(),n("p",L,"Saved.")):i("",!0)]),_:1})])])],40,k)])])]),_:1})],64))}};export{se as default};
