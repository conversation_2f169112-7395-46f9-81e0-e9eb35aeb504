import{r as O,l as S,m as A,o as c,c as d,a,u as n,w as b,F as P,Z as E,b as e,d as q,k as w,P as k,g as T,f as y,n as V,v as M,i as B,t as _}from"./app-b320a640.js";import{_ as D,a as j}from"./AdminLayout-aac65a75.js";import{_ as U}from"./InputError-257b182b.js";import{_ as p}from"./InputLabel-946d937b.js";import{P as G}from"./PrimaryButton-cb5bb104.js";import{_ as g}from"./TextInput-cb7ba6f7.js";import{_ as z}from"./SearchableDropdown-4997ffb6.js";import{u as L}from"./index-c4301439.js";import"./_plugin-vue_export-helper-c27b6911.js";const Q={class:"animate-top"},R={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},Z=e("div",{class:"sm:flex sm:items-center"},[e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Convert Challan to Invoice")])],-1),H=["onSubmit"],J={class:"border-b border-gray-900/10 pb-12"},K={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},W={class:"sm:col-span-3"},X={class:"mt-2"},Y={class:"sm:col-span-3"},ee={class:"mt-2"},te={class:"sm:col-span-6"},se={class:"mt-2 flex space-x-4"},oe={class:"flex items-center"},ie=e("label",{for:"invoice_type_new",class:"ml-2 block text-sm font-medium leading-6 text-gray-900 cursor-pointer"},"Create New Invoice",-1),ae={class:"flex items-center"},ne=["disabled"],le={for:"invoice_type_existing",class:"ml-2 block text-sm font-medium leading-6 text-gray-900 cursor-pointer"},re={key:0,class:"text-gray-500"},ce={key:0,class:"sm:col-span-4"},de={class:"relative mt-2"},_e={key:1,class:"sm:col-span-3"},me={class:"mt-2"},ue={key:2,class:"sm:col-span-3"},pe={class:"mt-2"},ve={key:3,class:"sm:col-span-3"},ge={class:"mt-2"},xe={key:4,class:"sm:col-span-3"},ye={class:"mt-2"},he={class:"sm:col-span-6"},fe={class:"overflow-x-auto"},be={class:"min-w-full divide-y divide-gray-300"},we=e("thead",null,[e("tr",null,[e("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Product"),e("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"Quantity"),e("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"Price (₹)"),e("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)"),e("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"GST (%)"),e("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"GST Amount (₹)"),e("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)")])],-1),Ve={class:"divide-y divide-gray-200"},Ie={class:"whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900"},Ce={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-500"},Oe={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-500"},Pe={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-500"},ke={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-500"},Te={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-500"},Ue={class:"whitespace-nowrap px-3 py-4 text-sm text-gray-500"},$e=e("td",{colspan:"3",class:"py-4 pl-4 pr-3 text-right text-sm font-semibold text-gray-900"},"Total:",-1),Fe={class:"whitespace-nowrap px-3 py-4 text-sm font-semibold text-gray-900"},Ne=e("td",null,null,-1),Se={class:"whitespace-nowrap px-3 py-4 text-sm font-semibold text-gray-900"},Ae={class:"whitespace-nowrap px-3 py-4 text-sm font-semibold text-gray-900"},Ee={class:"flex items-center justify-end mt-6 w-full"},qe={class:"flex items-center justify-end gap-x-6 w-1/2"},Me=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1),Ke={__name:"ConvertToInvoice",props:{purchaseOrder:Object,existingInvoices:Array},setup(m){const v=m,l=O("new"),h=O(null),s=L("post",route("purchaseinvoice.save-convert-to-invoice"),{id:v.purchaseOrder.id,purchase_order_id:v.purchaseOrder.purchase_order_id,invoice_type:l.value,existing_invoice_id:null,customer_invoice_no:"",customer_invoice_date:"",total_price:v.purchaseOrder.total_price,total_gst_amount:v.purchaseOrder.total_gst_amount,total_amount:v.purchaseOrder.total_amount,products:v.purchaseOrder.purchase_order_receive_details.map(o=>({id:o.id,product_id:o.product_id,purchase_order_detail_id:o.purchase_order_detail_id,qty:o.receive_qty,price:o.serial_numbers[0].purchase_price,total_price:0,gst:o.purchase_order_detail.gst,total_gst_amount:0,total_amount:0,product_name:o.product.name}))});S(l,o=>{s.invoice_type=o,o==="existing"&&h.value?console.log("newValue",o):o==="new"&&(s.existing_invoice_id=null,s.customer_invoice_no="",s.customer_invoice_date="")}),A(()=>{s.products.forEach(o=>{f(o)})});const $=(o,i)=>{s.existing_invoice_id=o,s.errors.existing_invoice_id=null;const t=v.existingInvoices.find(u=>u.id===o);s.customer_invoice_no=t.customer_invoice_no,s.customer_invoice_date=t.customer_invoice_date},f=o=>{o.total_price=parseFloat(o.price)*parseFloat(o.qty),o.total_gst_amount=o.total_price*parseFloat(o.gst)/100,o.total_amount=o.total_price+o.total_gst_amount,F()},F=()=>{let o=0,i=0,t=0;s.products.forEach(u=>{o+=parseFloat(u.total_price),i+=parseFloat(u.total_gst_amount),t+=parseFloat(u.total_amount)}),s.total_price=o,s.total_gst_amount=i,s.total_amount=t},N=()=>{if(l.value==="new"){if(!s.customer_invoice_no){s.errors.customer_invoice_no="Invoice number is required";return}if(!s.customer_invoice_date){s.errors.customer_invoice_date="Invoice date is required";return}}else if(l.value==="existing"&&!s.existing_invoice_id){s.errors.existing_invoice_id="Please select an existing invoice";return}s.submit({preserveScroll:!0,onSuccess:()=>{}})},x=o=>{let i=o.toFixed(2).toString(),[t,u]=i.split("."),r=t.substring(t.length-3),C=t.substring(0,t.length-3);return C!==""&&(r=","+r),`${C.replace(/\B(?=(\d{2})+(?!\d))/g,",")+r}.${u}`},I=o=>{s.errors[o]=null};return(o,i)=>(c(),d(P,null,[a(n(E),{title:"Convert Challan to Invoice"}),a(D,null,{default:b(()=>[e("div",Q,[e("div",R,[Z,e("form",{onSubmit:q(N,["prevent"]),class:"mt-6"},[e("div",J,[e("div",K,[e("div",W,[a(p,{for:"po_number",value:"PO Number"}),e("div",X,[a(g,{id:"po_number",type:"text",modelValue:m.purchaseOrder.purchase_order.po_number,"onUpdate:modelValue":i[0]||(i[0]=t=>m.purchaseOrder.purchase_order.po_number=t),disabled:"",class:"bg-gray-100"},null,8,["modelValue"])])]),e("div",Y,[a(p,{for:"company",value:"Company"}),e("div",ee,[a(g,{id:"company",type:"text",modelValue:m.purchaseOrder.purchase_order.company.name,"onUpdate:modelValue":i[1]||(i[1]=t=>m.purchaseOrder.purchase_order.company.name=t),disabled:"",class:"bg-gray-100"},null,8,["modelValue"])])]),e("div",te,[a(p,{value:"Invoice Type"}),e("div",se,[e("div",oe,[w(e("input",{id:"invoice_type_new",type:"radio",value:"new","onUpdate:modelValue":i[2]||(i[2]=t=>l.value=t),class:"h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600"},null,512),[[k,l.value]]),ie]),e("div",ae,[w(e("input",{id:"invoice_type_existing",type:"radio",value:"existing","onUpdate:modelValue":i[3]||(i[3]=t=>l.value=t),class:"h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-600",disabled:m.existingInvoices.length===0},null,8,ne),[[k,l.value]]),e("label",le,[T(" Merge with Existing Invoice "),m.existingInvoices.length===0?(c(),d("span",re,"(No existing invoices available)")):y("",!0)])])])]),l.value==="existing"?(c(),d("div",ce,[a(p,{for:"existing_invoice",value:"Select Existing Invoice"}),e("div",de,[a(z,{options:m.existingInvoices,modelValue:h.value,"onUpdate:modelValue":i[4]||(i[4]=t=>h.value=t),onOnchange:$,class:V({"error rounded-md":n(s).errors.existing_invoice_id})},null,8,["options","modelValue","class"])])])):y("",!0),l.value==="new"?(c(),d("div",_e,[a(p,{for:"customer_invoice_no",value:"Invoice Number"}),e("div",me,[a(g,{id:"customer_invoice_no",type:"text",modelValue:n(s).customer_invoice_no,"onUpdate:modelValue":i[5]||(i[5]=t=>n(s).customer_invoice_no=t),onChange:i[6]||(i[6]=t=>I("customer_invoice_no")),class:V({"error rounded-md":n(s).errors.customer_invoice_no})},null,8,["modelValue","class"]),a(U,{message:n(s).errors.customer_invoice_no},null,8,["message"])])])):y("",!0),l.value==="new"?(c(),d("div",ue,[a(p,{for:"customer_invoice_date",value:"Invoice Date"}),e("div",pe,[w(e("input",{id:"customer_invoice_date",type:"date","onUpdate:modelValue":i[7]||(i[7]=t=>n(s).customer_invoice_date=t),onChange:i[8]||(i[8]=t=>I("customer_invoice_date")),class:V({"error rounded-md":n(s).errors.customer_invoice_date,"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6":!0})},null,34),[[M,n(s).customer_invoice_date]]),a(U,{message:n(s).errors.customer_invoice_date},null,8,["message"])])])):y("",!0),l.value==="existing"?(c(),d("div",ve,[a(p,{for:"customer_invoice_no_display",value:"Invoice Number"}),e("div",ge,[a(g,{id:"customer_invoice_no_display",type:"text",modelValue:n(s).customer_invoice_no,"onUpdate:modelValue":i[9]||(i[9]=t=>n(s).customer_invoice_no=t),disabled:"",class:"bg-gray-100"},null,8,["modelValue"])])])):y("",!0),l.value==="existing"?(c(),d("div",xe,[a(p,{for:"customer_invoice_date_display",value:"Invoice Date"}),e("div",ye,[a(g,{id:"customer_invoice_date_display",type:"date",modelValue:n(s).customer_invoice_date,"onUpdate:modelValue":i[10]||(i[10]=t=>n(s).customer_invoice_date=t),disabled:"",class:"bg-gray-100"},null,8,["modelValue"])])])):y("",!0),e("div",he,[e("div",fe,[e("table",be,[we,e("tbody",Ve,[(c(!0),d(P,null,B(n(s).products,(t,u)=>(c(),d("tr",{key:t.id},[e("td",Ie,_(t.product_name),1),e("td",Ce,_(t.qty),1),e("td",Oe,[a(g,{type:"number",step:"0.01",modelValue:t.price,"onUpdate:modelValue":r=>t.price=r,onChange:r=>f(t),class:"w-24"},null,8,["modelValue","onUpdate:modelValue","onChange"])]),e("td",Pe,_(x(t.total_price)),1),e("td",ke,[a(g,{type:"number",step:"0.01",modelValue:t.gst,"onUpdate:modelValue":r=>t.gst=r,onChange:r=>f(t),class:"w-16"},null,8,["modelValue","onUpdate:modelValue","onChange"])]),e("td",Te,_(x(t.total_gst_amount)),1),e("td",Ue,_(x(t.total_amount)),1)]))),128))]),e("tfoot",null,[e("tr",null,[$e,e("td",Fe,_(x(n(s).total_price)),1),Ne,e("td",Se,_(x(n(s).total_gst_amount)),1),e("td",Ae,_(x(n(s).total_amount)),1)])])])])])])]),e("div",Ee,[e("div",qe,[a(j,{href:o.route("purchaseinvoice.index")},{svg:b(()=>[Me]),_:1},8,["href"]),a(G,{disabled:n(s).processing},{default:b(()=>[T(_(l.value==="new"?"Create New Invoice":"Merge with Existing Invoice"),1)]),_:1},8,["disabled"])])])],40,H)])])]),_:1})],64))}};export{Ke as default};
