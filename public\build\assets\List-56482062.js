import{_ as Le,b as qe,a as q}from"./AdminLayout-0f1fdf67.js";import{_ as ee}from"./CreateButton-fedd28a2.js";import{_ as T}from"./SecondaryButton-c893313c.js";import{P as N}from"./PrimaryButton-4ffecd1c.js";import{D as Ue}from"./DangerButton-a612a79a.js";import{M as I}from"./Modal-e44dcdf0.js";import{_ as Ee}from"./Pagination-50283e81.js";import{_ as ue}from"./SimpleDropdown-366207fb.js";import{_ as Re}from"./SearchableDropdown-711fb977.js";import{_ as he}from"./SearchableDropdownNew-6e56f54c.js";import{j as te,k as We,D as He,o as i,c as r,K as Ye,r as g,l as Qe,a as c,u as z,w as u,F as P,Z as Je,b as e,g as h,f as _,i as $,e as V,t as l,n as _e,s as Ke,x as Ze}from"./app-b7a94f67.js";import"./html2canvas.esm-18903d57.js";import{_ as A}from"./InputLabel-11b5d690.js";import{_ as Xe}from"./ArrowIcon-dce9e610.js";import{s as De}from"./sortAndSearch-77279369.js";import{_ as et}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const tt=["value"],me={__name:"MultipleCheckbox",props:{checked:{type:Array,default:()=>[]},value:{default:null}},emits:["update:checked"],setup(d,{emit:v}){const y=d,U=te({get(){return Array.isArray(y.checked)?y.checked.includes(y.value):!1},set(F){const S=Array.isArray(y.checked)?[...y.checked]:[];if(F)S.includes(y.value)||S.push(y.value);else{const j=S.indexOf(y.value);j>-1&&S.splice(j,1)}v("update:checked",S)}});return(F,S)=>We((i(),r("input",{type:"checkbox",value:d.value,"onUpdate:modelValue":S[0]||(S[0]=j=>U.value=j),class:"cursor-pointer rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"},null,8,tt)),[[He,U.value]])}};const s=d=>(Ke("data-v-29309b6d"),d=d(),Ze(),d),st={class:"animate-top"},lt={class:"flex justify-between items-center"},ot=s(()=>e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Challan")],-1)),nt={class:"flex justify-end"},at={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},it={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},ct=s(()=>e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),rt={key:0,class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},dt={class:"flex justify-end"},ut={key:1,class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},ht={class:"flex justify-end"},_t={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},mt={class:"flex mb-2"},vt=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),gt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},ft={class:"sm:col-span-4"},yt={class:"relative mt-2"},pt={class:"sm:col-span-4"},xt={class:"relative mt-2"},wt={key:0,class:"sm:col-span-4"},bt={class:"relative mt-2"},Ct={class:"sm:col-span-4"},kt={class:"relative mt-2"},St={class:"mt-8 overflow-x-auto sm:rounded-lg"},Mt={class:"shadow sm:rounded-lg"},zt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},jt={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Tt={class:"border-b-2"},It={scope:"col",class:"px-4"},Pt=["checked"],Vt=["onClick"],At={key:0},Bt={class:""},Gt={key:0,class:"pl-3"},Ot={class:"px-4 py-2.5 min-w-44"},Nt={class:"px-4 py-2.5"},$t={scope:"row",class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},Ft={class:"px-4 py-2.5 min-w-52"},Lt={class:"px-4 py-2.5 min-w-32"},qt={class:"px-4 py-2.5 min-w-32"},Ut={class:"flex flex-1 items-center px-4 py-2.5"},Et={class:"items-center px-4 py-2.5"},Rt={class:"flex items-center justify-start gap-4"},Wt=s(()=>e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)),Ht=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),Yt=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),Qt=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),Jt=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),Kt=["onClick"],Zt=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Xt=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)),Dt=[Zt,Xt],es=["onClick"],ts=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5 13l4 4L19 7"})],-1)),ss=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Convert To Foc ",-1)),ls=[ts,ss],os=["onClick"],ns=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M6 18L18 6M6 6l12 12"})],-1)),as=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Close Challan ",-1)),is=[ns,as],cs=["onClick"],rs=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),ds=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Return Product ",-1)),us=[rs,ds],hs=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-width":"1.5","stroke-linejoin":"round",d:"M3 7.5v9a1.5 1.5 0 001.5 1.5h15a1.5 1.5 0 001.5-1.5v-9H3z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6 6V3.75A1.5 1.5 0 017.5 2h9A1.5 1.5 0 0118 3.75V6m-15 0h18"})],-1)),_s=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Generate Invoice ",-1)),ms=["onClick"],vs=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M6 2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm2 6h8m-4 4h4"})],-1)),gs=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Demo Close ",-1)),fs=[vs,gs],ys=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),ps=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," View Challan ",-1)),xs=["onClick"],ws=s(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M6 2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm2 6h8m-4 4h4"})],-1)),bs=s(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Generate PDF ",-1)),Cs=[ws,bs],ks={key:1},Ss=s(()=>e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),Ms=[Ss],zs={class:"p-6"},js=s(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),Ts={class:"mt-6 flex justify-end"},Is={class:"p-6"},Ps=s(()=>e("div",{class:"header",style:{display:"flex","align-items":"center","justify-content":"space-between"}},[e("h1",null,[e("strong",null,"Following quantity are unsold")])],-1)),Vs={class:"mt-4",id:"pdf-content"},As=s(()=>e("thead",null,[e("tr",null,[e("th",null,"SN"),e("th",null,"Batch"),e("th",null,"Product Code"),e("th",null,"Product Name"),e("th",null,"HSN"),e("th",null,"Qty Received")])],-1)),Bs=s(()=>e("div",{class:"total",style:{"margin-top":"20px","text-align":"right"}},null,-1)),Gs=s(()=>e("div",{class:"header",style:{display:"flex","align-items":"center","justify-content":"space-between"}},[e("h1",null,[e("strong",null,"Do You Want to Close this Challan ?")])],-1)),Os={class:"mt-6 px-4 flex justify-end"},Ns={class:"w-36"},$s={class:"p-6"},Fs=s(()=>e("div",{class:"header",style:{display:"flex","align-items":"center","justify-content":"space-between"}},[e("h1",null,[e("strong",null,"Following quantity are in this challan")])],-1)),Ls={class:"mt-4",id:"pdf-content"},qs=s(()=>e("thead",null,[e("tr",null,[e("th",null,"SN"),e("th",null,"Batch"),e("th",null,"Product Code"),e("th",null,"Product Name"),e("th",null,"HSN"),e("th",null,"Qty Received")])],-1)),Us={class:""},Es={class:"p-1"},Rs=s(()=>e("div",{class:"total",style:{"margin-top":"20px","text-align":"right"}},null,-1)),Ws=s(()=>e("div",{class:"header",style:{display:"flex","align-items":"center","justify-content":"space-between"}},[e("h1",null,[e("strong",null,"Do You Want to Return this products ?")])],-1)),Hs={class:"mt-6 px-4 flex justify-end"},Ys={class:"w-36"},Qs={class:"p-6"},Js=s(()=>e("div",{class:"header",style:{display:"flex","align-items":"center","justify-content":"space-between"}},[e("h1",null,[e("strong",null,"Do You Want to Convert this Challan to FOC ?")])],-1)),Ks={class:"mt-6 px-4 flex justify-end"},Zs={class:"w-36"},Xs={class:"p-6"},Ds=s(()=>e("div",{class:"header",style:{display:"flex","align-items":"center","justify-content":"space-between"}},[e("h1",null,[e("strong",null,"Do You Want to Close This Demo ?")])],-1)),el={class:"mt-6 px-4 flex justify-end"},tl={class:"w-36"},sl={class:"p-6"},ll={class:"container1 p-2",id:"pdf-content"},ol={key:0,class:"header",style:{display:"flex","align-items":"center","justify-content":"space-between","margin-bottom":"10px"}},nl=["src"],al=s(()=>e("p",null,[e("strong",{style:{"font-size":"20px"}},"Challan")],-1)),il={key:1,class:"header",style:{"align-items":"start","justify-content":"center","text-align":"center"}},cl=["src"],rl=s(()=>e("div",{style:{"align-items":"center","justify-content":"space-between","margin-bottom":"10px"}},[e("p",{style:{"font-size":"20px"}},[e("strong",null,"Challan")])],-1)),dl={style:{display:"flex","justify-content":"space-between"}},ul={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},hl={style:{"font-size":"14px","margin-top":"10px"}},_l=s(()=>e("strong",null,"Phone",-1)),ml=s(()=>e("strong",null,"Email",-1)),vl=s(()=>e("strong",null,"GST",-1)),gl={class:"invoice-details",style:{"margin-bottom":"20px","justify-items":"start",width:"320px"}},fl=s(()=>e("strong",null,"Challan Number:",-1)),yl=s(()=>e("strong",null,"Challan Date:",-1)),pl=s(()=>e("strong",null,"Phone",-1)),xl=s(()=>e("strong",null,"Email",-1)),wl=s(()=>e("strong",null,"GST",-1)),bl=s(()=>e("th",null,"SN",-1)),Cl=s(()=>e("th",null,"Code",-1)),kl=s(()=>e("th",null,"Product Name",-1)),Sl=s(()=>e("th",null,"HSN",-1)),Ml=s(()=>e("th",null,"Qty",-1)),zl=s(()=>e("th",null,"Batch",-1)),jl=s(()=>e("th",null,"Exp",-1)),Tl=s(()=>e("th",null,"Mrp",-1)),Il=s(()=>e("th",null,"Rate",-1)),Pl=s(()=>e("th",null,"Discount",-1)),Vl={key:0},Al={key:1},Bl={key:2},Gl=s(()=>e("th",null,"Amount",-1)),Ol={style:{display:"flex"}},Nl={key:0},$l={key:1},Fl={key:2},Ll={style:{display:"flex","justify-content":"space-between"}},ql={style:{"margin-bottom":"20px",width:"260px"}},Ul={class:"invoice-details",style:{"margin-bottom":"20px",width:"300px"}},El=s(()=>e("strong",null,"Sub Total (₹):",-1)),Rl=s(()=>e("strong",null,"Total Discount Amount (₹):",-1)),Wl={key:0},Hl=s(()=>e("strong",null,"Total IGST (₹):",-1)),Yl={key:1},Ql=s(()=>e("strong",null,"Total CGST (₹):",-1)),Jl={key:2},Kl=s(()=>e("strong",null,"Total SGST (₹):",-1)),Zl=s(()=>e("strong",null,"Total Amount (₹):",-1)),Xl={style:{display:"flex","justify-content":"space-between"}},Dl=s(()=>e("div",{class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"400"}},null,-1)),eo={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},to=s(()=>e("p",null,[e("strong",null,"FOR,")],-1)),so=["src"],lo={class:"mt-6 px-4 flex justify-end"},oo={class:"flex flex-col justify-end space-y-6"},no={class:"flex items-center space-x-2"},ao={class:"mt-6 px-4 flex justify-end"},io={class:"w-36"},co=s(()=>e("div",{class:"flex gap-2 items-center justify-start"},[e("div",{class:""})],-1)),ro={class:"flex gap-3 items-center justify-end"},uo=s(()=>e("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M1 13L13 1M1 1L13 13",stroke:"#9CA3AF","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)),ho=[uo],_o={__name:"List",props:["data","permissions","organization","customers","organizationId","customerId","salesuser","salesUserId","category","categoryId","pagetypes"],setup(d){const v=d,{form:y,search:U,sort:F,fetchData:S,sortKey:j,sortDirection:ve,updateParams:ge}=De("challan.index",{organization_id:v.organizationId,customer_id:v.customerId,sales_user_id:v.salesUserId,category:v.categoryId}),E=Ye().props.filepath.view,a=g([]),se=g([]),R=g(!1),x=g(null),fe=[{field:"challan_number",label:"CHALLAN NUMBER",sortable:!0},{field:"category",label:"TYPE",sortable:!0},{field:"customers.customer_name",label:"CUSTOMER NAME",sortable:!0},{field:"users.first_name",label:"SALES PERSON",sortable:!0},{field:"date",label:"DATE",sortable:!0},{field:"total_amount",label:"AMOUNT (₹)",sortable:!0},{field:"status",label:"STATUS",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],ye=o=>{x.value=o,R.value=!0},B=()=>{R.value=!1},pe=o=>{y.delete(route("challan.destroy",{id:x.value}),{onSuccess:()=>B()})},w=g(v.organizationId),b=g(v.customerId),C=g(v.salesUserId),k=g(v.categoryId),G=g("");Qe([w,b,C,k],()=>{ge({organization_id:w.value,customer_id:b.value,sales_user_id:C.value,category:k.value})});const O=(o,n,t,m,f)=>{G.value=o,y.get(route("challan.index",{search:o,organization_id:n,customer_id:t,sales_user_id:m,category:f}),{preserveState:!0})},xe=(o,n)=>{w.value=o,O(G.value,w.value,b.value,C.value,k.value)},we=(o,n)=>{b.value=o,O(G.value,w.value,b.value,C.value,k.value)},be=(o,n)=>{k.value=o,O(G.value,w.value,b.value,C.value,k.value)},Ce=(o,n)=>{C.value=o,O(G.value,w.value,b.value,C.value,k.value)},W=g(!1),H=g(!1),Y=g(!1),Q=g(!1),J=g(!1),K=g("custom"),ke=o=>{const n=v.data.data.find(t=>t.id===o);a.value=n,W.value=!0},le=()=>{W.value=!1},Se=o=>{Q.value=!0,x.value=o},oe=()=>{Q.value=!1},Me=o=>{y.get(route("challan.foc",{id:x.value}),{onSuccess:()=>B()})},ze=o=>{J.value=!0,x.value=o},ne=()=>{J.value=!1},je=o=>{y.get(route("challan.democlose",{id:x.value}),{onSuccess:()=>B()})},Te=o=>{const n=v.data.data.find(t=>t.id===o);if(n){const t=n.challan_detail.filter(m=>m.qty-m.invoiced_qty>0&&m.qty-m.return_qty>0);se.value=t}H.value=!0,x.value=o},Z=()=>{H.value=!1},Ie=()=>{y.get(route("challan.close",{id:x.value}),{onSuccess:()=>Z()})},ae=g([]),Pe=o=>{const n=v.data.data.find(t=>t.id===o);if(n){const t=n.challan_detail.filter(m=>m.qty-m.invoiced_qty>0&&m.qty-m.return_qty>0);ae.value=t}Y.value=!0,x.value=o},X=()=>{Y.value=!1},D=g([]),M=g([]),Ve=()=>{y.get(route("challan.return",{id:x.value,detail:JSON.stringify(D.value)}),{onSuccess:()=>X()})},Ae=o=>{switch(o){case"Open":return"bg-blue-100";case"In-process":return"bg-yellow-100";case"Close":return"bg-green-100";case"Foc":return"bg-orange-100";case"Demo Close":return"bg-pink-100";default:return"bg-red-100"}},Be=o=>{switch(o){case"Open":return"text-blue-600";case"In-process":return"text-yellow-600";case"Close":return"text-green-600";case"Foc":return"text-orange-600";case"Demo Close":return"text-pink-600";default:return"text-red-600"}},L=g("portrait"),Ge=(o,n)=>{L.value=o},Oe=(o,n)=>{window.open(`/challan/download/${o}/${n}`,"_blank")},ie=o=>{const n=new Date(o),t={year:"numeric",month:"short",day:"numeric"};return n.toLocaleDateString("en-US",t)},p=o=>{let n=o.toFixed(2).toString(),[t,m]=n.split("."),f=t.substring(t.length-3),de=t.substring(0,t.length-3);return de!==""&&(f=","+f),`${de.replace(/\B(?=(\d{2})+(?!\d))/g,",")+f}.${m}`},Ne=te(()=>M.value.length===v.data.data.length),ce=o=>{o.target.checked?M.value=v.data.data.map(n=>n.id):M.value=[]},$e=o=>{console.log(o)},Fe=o=>{M.value=[]},re=te(()=>{if(v.data.data.length===0)return!1;const o=v.data.data[0].customers.id,n=v.data.data[0].organization_id;return v.data.data.every(t=>t.customers.id===o&&t.organization_id===n)});return(o,n)=>(i(),r(P,null,[c(z(Je),{title:"Challan"}),c(Le,null,{default:u(()=>[e("div",st,[e("div",lt,[ot,e("div",nt,[e("div",at,[e("div",it,[ct,e("input",{id:"search-field",onInput:n[0]||(n[0]=t=>O(t.target.value,w.value,b.value,C.value,k.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),d.permissions.canCreateChallan?(i(),r("div",rt,[e("div",dt,[c(ee,{href:o.route("challan.create")},{default:u(()=>[h(" Add Challan ")]),_:1},8,["href"])])])):_("",!0),d.permissions.canCreateChallan?(i(),r("div",ut,[e("div",ht,[c(ee,{href:o.route("challantransfer")},{default:u(()=>[h(" Transfer Challan ")]),_:1},8,["href"])])])):_("",!0)])]),e("div",_t,[e("div",mt,[vt,c(A,{for:"customer_id",value:"Filters"})]),e("div",gt,[e("div",ft,[c(A,{for:"customer_id",value:"Organization Name"}),e("div",yt,[c(ue,{options:d.organization,modelValue:w.value,"onUpdate:modelValue":n[1]||(n[1]=t=>w.value=t),onOnchange:xe},null,8,["options","modelValue"])])]),e("div",pt,[c(A,{for:"customer_id",value:"Customer Name"}),e("div",xt,[c(he,{options:d.customers,modelValue:b.value,"onUpdate:modelValue":n[2]||(n[2]=t=>b.value=t),onOnchange:we},null,8,["options","modelValue"])])]),d.permissions.canCreateChallan?(i(),r("div",wt,[c(A,{for:"customer_id",value:"Sales Person"}),e("div",bt,[c(he,{options:d.salesuser,modelValue:C.value,"onUpdate:modelValue":n[3]||(n[3]=t=>C.value=t),onOnchange:Ce},null,8,["options","modelValue"])])])):_("",!0),e("div",Ct,[c(A,{for:"customer_id",value:"Category"}),e("div",kt,[c(ue,{options:d.category,modelValue:k.value,"onUpdate:modelValue":n[4]||(n[4]=t=>k.value=t),onOnchange:be},null,8,["options","modelValue"])])])])]),e("div",St,[e("div",Mt,[e("table",zt,[e("thead",jt,[e("tr",Tt,[e("th",It,[re.value?(i(),r("input",{key:0,type:"checkbox",class:"rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500",checked:Ne.value,onChange:ce},null,40,Pt)):_("",!0)]),(i(),r(P,null,$(fe,(t,m)=>e("th",{key:m,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:f=>z(F)(t.field,t.sortable)},[h(l(t.label)+" ",1),t.sortable?(i(),V(Xe,{key:0,isSorted:z(j)===t.field,direction:z(ve)},null,8,["isSorted","direction"])):_("",!0)],8,Vt)),64))])]),d.data.data&&d.data.data.length>0?(i(),r("tbody",At,[(i(!0),r(P,null,$(d.data.data,(t,m)=>(i(),r("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Bt,[t.status!="Close"&&d.permissions.canInvoiceChallan&&t.status!="Cancelled"&&t.status!="Foc"&&t.category!="Demo"&&re.value?(i(),r("div",Gt,[c(me,{checked:M.value,"onUpdate:checked":n[5]||(n[5]=f=>M.value=f),value:t.id,onChange:$e},null,8,["checked","value"])])):_("",!0)]),e("td",Ot,l(t.challan_number),1),e("td",Nt,l(t.category),1),e("th",$t,l(t.customers.customer_name),1),e("td",Ft,l(t.users.first_name)+" "+l(t.users.last_name),1),e("td",Lt,l(ie(t.date)),1),e("td",qt,l(p(t.total_amount)),1),e("td",Ut,[e("div",{class:_e(["flex rounded-full px-4 py-1",Ae(t.status)])},[e("span",{class:_e(["text-sm font-semibold whitespace-nowrap",Be(t.status)])},l(t.status),3)],2)]),e("td",Et,[e("div",Rt,[c(qe,{align:"right",width:"48"},{trigger:u(()=>[Wt]),content:u(()=>[t.status=="Open"&&d.permissions.canEditChallan&&t.customers.organization_id==null?(i(),V(q,{key:0,href:o.route("challan.edit",{id:t.id})},{svg:u(()=>[Ht]),text:u(()=>[Yt]),_:2},1032,["href"])):_("",!0),t.status=="Open"&&d.permissions.canEditChallan&&t.customers.organization_id!=null?(i(),V(q,{key:1,href:o.route("challantransfer.edit",{id:t.id})},{svg:u(()=>[Qt]),text:u(()=>[Jt]),_:2},1032,["href"])):_("",!0),t.status=="Open"&&d.permissions.canDeleteChallan&&t.customers.organization_id==null?(i(),r("button",{key:2,type:"button",onClick:f=>ye(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Dt,8,Kt)):_("",!0),t.status=="Open"&&d.permissions.canFocChallan?(i(),r("button",{key:3,type:"button",onClick:f=>Se(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},ls,8,es)):_("",!0),t.status!="Close"&&t.status!="Demo Close"&&d.permissions.canCloseChallan&&t.status!="Cancelled"&&t.status!="Foc"&&t.customers.organization_id==null?(i(),r("button",{key:4,type:"button",onClick:f=>Te(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},is,8,os)):_("",!0),t.status!="Close"&&t.status!="Demo Close"&&d.permissions.canCloseChallan&&t.status!="Cancelled"&&t.status!="Foc"&&t.customers.organization_id==null?(i(),r("button",{key:5,type:"button",onClick:f=>Pe(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},us,8,cs)):_("",!0),t.status!="Close"&&d.permissions.canInvoiceChallan&&t.status!="Cancelled"&&t.status!="Foc"&&t.category!="Demo"&&t.customers.organization_id==null?(i(),V(q,{key:6,href:o.route("challan.invoice",{id:t.id})},{svg:u(()=>[hs]),text:u(()=>[_s]),_:2},1032,["href"])):_("",!0),t.status=="Open"&&t.category=="Demo"&&d.permissions.canCloseChallan&&t.status!="Cancelled"&&t.status!="Foc"&&t.customers.organization_id==null?(i(),r("button",{key:7,onClick:f=>ze(t.id),type:"button",class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},fs,8,ms)):_("",!0),d.permissions.canViewChallan?(i(),V(q,{key:8,href:o.route("challan.view",{id:t.id})},{svg:u(()=>[ys]),text:u(()=>[ps]),_:2},1032,["href"])):_("",!0),e("button",{type:"button",onClick:f=>ke(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Cs,8,xs)]),_:2},1024)])])]))),128))])):(i(),r("tbody",ks,Ms))])])]),d.data.data&&d.data.data.length>0?(i(),V(Ee,{key:0,class:"mt-6",links:d.data.links},null,8,["links"])):_("",!0)]),c(I,{show:R.value,onClose:B},{default:u(()=>[e("div",zs,[js,e("div",Ts,[c(T,{onClick:B},{default:u(()=>[h(" Cancel ")]),_:1}),c(Ue,{class:"ml-3",onClick:pe},{default:u(()=>[h(" Delete ")]),_:1})])])]),_:1},8,["show"]),c(I,{show:H.value,onClose:Z,maxWidth:K.value},{default:u(()=>[e("div",Is,[Ps,e("div",Vs,[e("table",null,[As,e("tbody",null,[(i(!0),r(P,null,$(se.value,(t,m)=>(i(),r("tr",{key:t.id},[e("td",null,l(m+1),1),e("td",null,l(t.viewserialnumbers.unique_id??"-"),1),e("td",null,l(t.viewserialnumbers.product.item_code??"-"),1),e("td",null,l(t.viewserialnumbers.product.name??"-"),1),e("td",null,l(t.viewserialnumbers.product.hsn_code??"-"),1),e("td",null,l(t.qty-t.invoiced_qty-t.return_qty),1)]))),128))])]),Bs]),Gs,e("div",Os,[c(T,{onClick:Z},{default:u(()=>[h(" Cancel ")]),_:1}),e("div",Ns,[c(N,{class:"ml-3 w-20",onClick:Ie},{default:u(()=>[h(" Close Challan ")]),_:1})])])])]),_:1},8,["show","maxWidth"]),c(I,{show:Y.value,onClose:X,maxWidth:K.value},{default:u(()=>[e("div",$s,[Fs,e("div",Ls,[e("table",null,[qs,e("tbody",null,[(i(!0),r(P,null,$(ae.value,(t,m)=>(i(),r("tr",{key:t.id},[e("td",Us,[e("div",Es,[c(me,{checked:D.value,"onUpdate:checked":n[6]||(n[6]=f=>D.value=f),value:t.id},null,8,["checked","value"])])]),e("td",null,l(t.viewserialnumbers.unique_id??"-"),1),e("td",null,l(t.viewserialnumbers.product.item_code??"-"),1),e("td",null,l(t.viewserialnumbers.product.name??"-"),1),e("td",null,l(t.viewserialnumbers.product.hsn_code??"-"),1),e("td",null,l(t.qty-t.invoiced_qty-t.return_qty),1)]))),128))])]),Rs]),Ws,e("div",Hs,[c(T,{onClick:X},{default:u(()=>[h(" Cancel ")]),_:1}),e("div",Ys,[c(N,{class:"ml-3 w-20",onClick:Ve},{default:u(()=>[h(" Confirm ")]),_:1})])])])]),_:1},8,["show","maxWidth"]),c(I,{show:Q.value,onClose:oe},{default:u(()=>[e("div",Qs,[Js,e("div",Ks,[c(T,{onClick:oe},{default:u(()=>[h(" Cancel ")]),_:1}),e("div",Zs,[c(N,{class:"ml-3",onClick:Me},{default:u(()=>[h(" FOC ")]),_:1})])])])]),_:1},8,["show"]),c(I,{show:J.value,onClose:ne},{default:u(()=>[e("div",Xs,[Ds,e("div",el,[c(T,{onClick:ne},{default:u(()=>[h(" Cancel ")]),_:1}),e("div",tl,[c(N,{class:"ml-3",onClick:je},{default:u(()=>[h(" Demo Close ")]),_:1})])])])]),_:1},8,["show"]),c(I,{show:W.value,onClose:le,maxWidth:K.value},{default:u(()=>[e("div",sl,[e("div",ll,[a.value.organization.id=="3"?(i(),r("div",ol,[e("img",{class:"w-20 h-20",src:z(E)+a.value.organization.logo,alt:"logo"},null,8,nl),al])):_("",!0),a.value.organization.id=="1"||a.value.organization.id=="2"?(i(),r("div",il,[e("img",{class:"w-full h-10",src:z(E)+a.value.organization.logo,alt:"logo"},null,8,cl),rl])):_("",!0),e("div",dl,[e("div",ul,[e("p",null,[e("strong",hl,l(a.value.organization.name),1)]),e("p",null,l(a.value.organization.address_line_1),1),e("p",null,l(a.value.organization.address_line_2),1),e("p",null,l(a.value.organization.pincode)+" , "+l(a.value.organization.city),1),e("p",null,[_l,h(": "+l(a.value.organization.contact_no),1)]),e("p",null,[ml,h(": "+l(a.value.organization.email),1)]),e("p",null,[vl,h(": "+l(a.value.organization.gst_no),1)])]),e("div",gl,[e("div",null,[fl,h(" "+l(a.value.challan_number),1)]),e("div",null,[yl,h(" "+l(ie(a.value.date)),1)]),e("p",null,[e("strong",null,l(a.value.customers.customer_name),1)]),e("p",null,l(a.value.customers.address),1),e("p",null,[pl,h(": "+l(a.value.customers.contact_no),1)]),e("p",null,[xl,h(": "+l(a.value.customers.email),1)]),e("p",null,[wl,h(": "+l(a.value.customers.gst_no),1)])])]),e("table",null,[e("thead",null,[e("tr",null,[bl,Cl,kl,Sl,Ml,zl,jl,Tl,Il,Pl,a.value.customers.gst_type=="IGST"?(i(),r("th",Vl,"IGST")):_("",!0),a.value.customers.gst_type=="CGST/SGST"?(i(),r("th",Al,"CGST")):_("",!0),a.value.customers.gst_type=="CGST/SGST"?(i(),r("th",Bl,"SGST")):_("",!0),Gl])]),e("tbody",null,[(i(!0),r(P,null,$(a.value.challan_detail,(t,m)=>(i(),r("tr",{key:t.id},[e("td",null,l(m+1),1),e("td",null,l(t.viewserialnumbers.product.item_code),1),e("td",null,[h(l(t.viewserialnumbers.product.name),1),e("span",Ol,l(t.description),1)]),e("td",null,l(t.viewserialnumbers.product.hsn_code),1),e("td",null,l(t.qty),1),e("td",null,l(t.viewserialnumbers.batch??"-"),1),e("td",null,l(t.viewserialnumbers.expiry_date??"-"),1),e("td",null,l(t.viewserialnumbers.mrp?p(t.viewserialnumbers.mrp):"-"),1),e("td",null,l(p(t.price)??"-"),1),e("td",null,l(p(t.discount)??"-"),1),a.value.customers.gst_type=="IGST"?(i(),r("td",Nl,l(p(t.gst)??"-"),1)):_("",!0),a.value.customers.gst_type=="CGST/SGST"?(i(),r("td",$l,l(p(t.gst/2)??"-"),1)):_("",!0),a.value.customers.gst_type=="CGST/SGST"?(i(),r("td",Fl,l(p(t.gst/2)??"-"),1)):_("",!0),e("td",null,l(p(t.total_price)??"-"),1)]))),128))])]),e("div",Ll,[e("div",ql,[e("p",null,l(a.value.note),1)]),e("div",Ul,[e("div",null,[El,h(" "+l(p(a.value.sub_total)),1)]),e("div",null,[Rl,h(" "+l(p(a.value.total_discount)),1)]),a.value.customers.gst_type=="IGST"?(i(),r("div",Wl,[Hl,h(" "+l(p(a.value.igst)),1)])):_("",!0),a.value.customers.gst_type=="CGST/SGST"?(i(),r("div",Yl,[Ql,h(" "+l(p(a.value.cgst)),1)])):_("",!0),a.value.customers.gst_type=="CGST/SGST"?(i(),r("div",Jl,[Kl,h(" "+l(p(a.value.sgst)),1)])):_("",!0),e("div",null,[Zl,h(" "+l(p(a.value.total_amount)),1)])])]),e("div",Xl,[Dl,e("div",eo,[to,e("p",null,[e("strong",null,l(a.value.organization.name),1)]),e("img",{class:"h-28",src:z(E)+a.value.organization.signature,alt:"logo"},null,8,so)])])]),e("div",lo,[e("div",oo,[e("div",no,[c(A,{for:"customer_id",value:"Page Type :"}),c(Re,{options:d.pagetypes,modelValue:L.value,"onUpdate:modelValue":n[7]||(n[7]=t=>L.value=t),onOnchange:Ge},null,8,["options","modelValue"])])])]),e("div",ao,[c(T,{onClick:le},{default:u(()=>[h(" Cancel ")]),_:1}),e("div",io,[c(N,{class:"ml-3 w-20",onClick:n[8]||(n[8]=t=>Oe(a.value.id,L.value))},{default:u(()=>[h(" Generate Pdf ")]),_:1})])])])]),_:1},8,["show","maxWidth"]),M.value.length>0?(i(),r("div",{key:0,style:{transition:"height 0.2s"},id:"action",class:"relative overflow-visible bottomaction flex items-center justify-between px-8 bg-white shadow border-gray-300 h-16",onChange:ce},[co,e("div",ro,[c(ee,{href:o.route("challan.combine-invoice",{detail:JSON.stringify(M.value)})},{default:u(()=>[h(" Generate Combine Invoice ")]),_:1},8,["href"])]),e("button",{type:"button",title:"Cancel",onClick:Fe},ho)],32)):_("",!0)]),_:1})],64))}},Ao=et(_o,[["__scopeId","data-v-29309b6d"]]);export{Ao as default};
