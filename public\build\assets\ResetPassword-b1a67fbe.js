import{h as c,o as g,e as w,w as m,a as o,u as e,Z as f,b as r,g as _,n as y,d as x}from"./app-ce7743ab.js";import{G as b}from"./GuestLayout-63b4e9c4.js";import{_ as l}from"./InputError-473f1c1e.js";import{_ as i}from"./InputLabel-3aa35471.js";import{P as h}from"./PrimaryButton-6ff8a943.js";import{_ as d}from"./TextInput-65921831.js";import"./_plugin-vue_export-helper-c27b6911.js";const V=r("h2",{class:"text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"},"Reset Password",-1),k=["onSubmit"],v={class:"mt-2"},P={class:"mt-4"},q={class:"flex items-center justify-end mt-4"},T={__name:"ResetPassword",props:{email:{type:String,required:!0},token:{type:String,required:!0}},setup(p){const n=p,s=c({token:n.token,email:n.email,password:"",password_confirmation:""}),u=()=>{s.post(route("password.store"),{onFinish:()=>s.reset("password","password_confirmation")})};return(B,a)=>(g(),w(b,null,{default:m(()=>[o(e(f),{title:"Reset Password"}),V,r("form",{onSubmit:x(u,["prevent"])},[r("div",null,[o(i,{for:"email",value:"Email"}),o(d,{id:"email",type:"email",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).email,"onUpdate:modelValue":a[0]||(a[0]=t=>e(s).email=t),required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),o(l,{class:"mt-2",message:e(s).errors.email},null,8,["message"])]),r("div",v,[o(i,{for:"password",value:"Password"}),o(d,{id:"password",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).password,"onUpdate:modelValue":a[1]||(a[1]=t=>e(s).password=t),required:"",autocomplete:"new-password"},null,8,["modelValue"]),o(l,{class:"mt-2",message:e(s).errors.password},null,8,["message"])]),r("div",P,[o(i,{for:"password_confirmation",value:"Confirm Password"}),o(d,{id:"password_confirmation",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).password_confirmation,"onUpdate:modelValue":a[2]||(a[2]=t=>e(s).password_confirmation=t),required:"",autocomplete:"new-password"},null,8,["modelValue"]),o(l,{class:"mt-2",message:e(s).errors.password_confirmation},null,8,["message"])]),r("div",q,[o(h,{class:y({"opacity-25":e(s).processing}),disabled:e(s).processing},{default:m(()=>[_(" Reset Password ")]),_:1},8,["class","disabled"])])],40,k)]),_:1}))}};export{T as default};
