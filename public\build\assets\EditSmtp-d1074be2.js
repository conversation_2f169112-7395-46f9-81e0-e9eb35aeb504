import{h as f,o as n,c as l,a,u as t,w as c,F as x,Z as g,b as r,d as V,t as d,f as i,g as h,T as b}from"./app-b7a94f67.js";import{_ as w,a as $}from"./AdminLayout-0f1fdf67.js";/* empty css                                                              */import{_ as p}from"./InputLabel-11b5d690.js";import{_ as u}from"./TextInput-fea73171.js";import{P as k}from"./PrimaryButton-4ffecd1c.js";import"./_plugin-vue_export-helper-c27b6911.js";const C={class:"animate-top"},S={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},U=r("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit SMTP",-1),P=["onSubmit"],T={class:"border-b border-gray-900/10 pb-12"},E={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},N={class:"sm:col-span-3"},B={key:0,class:"mt-1 text-sm text-red-500"},j={class:"sm:col-span-3"},M={key:0,class:"mt-1 text-sm text-red-500"},F={class:"sm:col-span-3"},D={key:0,class:"mt-1 text-sm text-red-500"},H={class:"sm:col-span-3"},O={key:0,class:"mt-1 text-sm text-red-500"},Z={class:"sm:col-span-3"},q={key:0,class:"mt-1 text-sm text-red-500"},z={class:"sm:col-span-3"},A={key:0,class:"mt-1 text-sm text-red-500"},G={class:"sm:col-span-3"},I={key:0,class:"mt-1 text-sm text-red-500"},J={class:"flex mt-6 items-center justify-between"},K={class:"ml-auto flex items-center justify-end gap-x-6"},L=r("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Q={key:0,class:"text-sm text-gray-600"},oe={__name:"EditSmtp",props:{smtp:Object},setup(v){const m=v,e=f({host:m.smtp.host||"",port:m.smtp.port||"",username:m.smtp.username||"",password:m.smtp.password||"",email:m.smtp.email||"",encryption:m.smtp.encryption||"",name:m.smtp.name||""}),_=()=>{e.put(`/mail-configs/${m.smtp.id}`,{onSuccess:()=>{e.reset()}})};return(y,s)=>(n(),l(x,null,[a(t(g),{title:"SMTP"}),a(w,null,{default:c(()=>[r("div",C,[r("div",S,[U,r("form",{onSubmit:V(_,["prevent"]),class:""},[r("div",T,[r("div",E,[r("div",N,[a(p,{for:"host",value:"Host"}),a(u,{id:"host",type:"text",modelValue:t(e).host,"onUpdate:modelValue":s[0]||(s[0]=o=>t(e).host=o),autocomplete:"host",onChange:s[1]||(s[1]=o=>t(e).validate("host"))},null,8,["modelValue"]),t(e).errors.host?(n(),l("p",B,d(t(e).errors.host),1)):i("",!0)]),r("div",j,[a(p,{for:"port",value:"Port"}),a(u,{id:"port",type:"text",modelValue:t(e).port,"onUpdate:modelValue":s[2]||(s[2]=o=>t(e).port=o),autocomplete:"port",onChange:s[3]||(s[3]=o=>t(e).validate("port"))},null,8,["modelValue"]),t(e).errors.port?(n(),l("p",M,d(t(e).errors.port),1)):i("",!0)]),r("div",F,[a(p,{for:"username",value:"Username"}),a(u,{id:"username",type:"text",modelValue:t(e).username,"onUpdate:modelValue":s[4]||(s[4]=o=>t(e).username=o),autocomplete:"username",onChange:s[5]||(s[5]=o=>t(e).validate("username"))},null,8,["modelValue"]),t(e).errors.username?(n(),l("p",D,d(t(e).errors.username),1)):i("",!0)]),r("div",H,[a(p,{for:"password",value:"Password"}),a(u,{id:"password",type:"text",modelValue:t(e).password,"onUpdate:modelValue":s[6]||(s[6]=o=>t(e).password=o),autocomplete:"password",onChange:s[7]||(s[7]=o=>t(e).validate("password"))},null,8,["modelValue"]),t(e).errors.password?(n(),l("p",O,d(t(e).errors.password),1)):i("",!0)]),r("div",Z,[a(p,{for:"email",value:"Email"}),a(u,{id:"email",type:"text",modelValue:t(e).email,"onUpdate:modelValue":s[8]||(s[8]=o=>t(e).email=o),autocomplete:"email",onChange:s[9]||(s[9]=o=>t(e).validate("email"))},null,8,["modelValue"]),t(e).errors.email?(n(),l("p",q,d(t(e).errors.email),1)):i("",!0)]),r("div",z,[a(p,{for:"encryption",value:"Encryption"}),a(u,{id:"encryption",type:"text",modelValue:t(e).encryption,"onUpdate:modelValue":s[10]||(s[10]=o=>t(e).encryption=o),autocomplete:"encryption",onChange:s[11]||(s[11]=o=>t(e).validate("encryption"))},null,8,["modelValue"]),t(e).errors.encryption?(n(),l("p",A,d(t(e).errors.encryption),1)):i("",!0)]),r("div",G,[a(p,{for:"name",value:"Name"}),a(u,{id:"name",type:"text",modelValue:t(e).name,"onUpdate:modelValue":s[12]||(s[12]=o=>t(e).name=o),autocomplete:"name",onChange:s[13]||(s[13]=o=>t(e).validate("name"))},null,8,["modelValue"]),t(e).errors.name?(n(),l("p",I,d(t(e).errors.name),1)):i("",!0)])])]),r("div",J,[r("div",K,[a($,{href:y.route("mail-configs.index")},{svg:c(()=>[L]),_:1},8,["href"]),a(k,{disabled:t(e).processing},{default:c(()=>[h("Save")]),_:1},8,["disabled"]),a(b,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:c(()=>[t(e).recentlySuccessful?(n(),l("p",Q,"Saved.")):i("",!0)]),_:1})])])],40,P)])])]),_:1})],64))}};export{oe as default};
