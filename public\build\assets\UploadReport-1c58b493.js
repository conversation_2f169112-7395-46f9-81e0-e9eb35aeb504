import{o as d,c as g,a as o,u as t,w as l,F as w,Z as R,b as s,t as V,d as S,n as p,e as u,f as c,k,v as U,g as T,T as $}from"./app-ce7743ab.js";import{_ as B,a as N}from"./AdminLayout-6af2fc6a.js";import{_}from"./InputError-473f1c1e.js";import{_ as m}from"./InputLabel-3aa35471.js";import{P as C}from"./PrimaryButton-6ff8a943.js";import{_ as f}from"./SearchableDropdown-6fd7fbbe.js";import{_ as D}from"./MultipleFileUpload-7a5f3a52.js";import{u as F}from"./index-588ba5dc.js";import"./_plugin-vue_export-helper-c27b6911.js";const P={class:"h-screen animate-top"},j={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},E={class:"sm:flex sm:items-center"},M=s("div",{class:"sm:flex-auto"},[s("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Upload Report")],-1),O={class:"text-sm font-semibold text-gray-900"},z=["onSubmit"],I={class:"border-b border-gray-900/10 pb-12"},Z={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10"},q={class:"sm:col-span-3"},A={class:"relative mt-2"},G={class:"sm:col-span-3"},H={class:"relative mt-2"},J={class:"sm:col-span-3"},K={class:"sm:col-span-4"},L={class:"w-full"},Q={class:"flex mt-6 items-center justify-between"},W={class:"ml-auto flex items-center justify-end gap-x-6"},X=s("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),Y={key:0,class:"text-sm text-gray-600"},le={__name:"UploadReport",props:["reporttype","serviceperson","serviceReport"],setup(a){const v=a,e=F("post","/save-upload-service-report",{service_report_id:v.serviceReport.id,customer_id:v.serviceReport.customers.id,date:"",document:"",type:"",service_engineer_id:""}),y=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()}),h=(i,r)=>{e.type=i,e.errors.type=null},x=(i,r)=>{e.service_engineer_id=i,e.errors.service_engineer_id=null},b=i=>{e.document=i};return(i,r)=>(d(),g(w,null,[o(t(R),{title:"Upload Report"}),o(B,null,{default:l(()=>[s("div",P,[s("div",j,[s("div",E,[M,s("p",O,V(a.serviceReport.customers.customer_name),1)]),s("form",{onSubmit:S(y,["prevent"]),class:""},[s("div",I,[s("div",Z,[s("div",q,[o(m,{for:"type",value:"Report Type"}),s("div",A,[o(f,{options:a.reporttype,modelValue:t(e).type,"onUpdate:modelValue":r[0]||(r[0]=n=>t(e).type=n),onOnchange:h,class:p({"error rounded-md":t(e).errors.type})},null,8,["options","modelValue","class"]),t(e).invalid("type")?(d(),u(_,{key:0,class:"",message:t(e).errors.type},null,8,["message"])):c("",!0)])]),s("div",G,[o(m,{for:"service_engineer_id",value:"Service Engineer"}),s("div",H,[o(f,{options:a.serviceperson,modelValue:t(e).service_engineer_id,"onUpdate:modelValue":r[1]||(r[1]=n=>t(e).service_engineer_id=n),onOnchange:x,class:p({"error rounded-md":t(e).errors.service_engineer_id})},null,8,["options","modelValue","class"]),t(e).invalid("service_engineer_id")?(d(),u(_,{key:0,class:"",message:t(e).errors.service_engineer_id},null,8,["message"])):c("",!0)])]),s("div",J,[o(m,{for:"date",value:"Date"}),k(s("input",{"onUpdate:modelValue":r[2]||(r[2]=n=>t(e).date=n),class:p(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":t(e).errors["data.date"]}]),type:"date"},null,2),[[U,t(e).date]]),t(e).invalid("date")?(d(),u(_,{key:0,class:"",message:t(e).errors.date},null,8,["message"])):c("",!0)]),s("div",K,[s("div",L,[o(m,{for:"note",value:"Upload Report"}),o(D,{inputId:"document",inputName:"document",onFiles:b})])])])]),s("div",Q,[s("div",W,[o(N,{href:i.route("service-reports.show",{id:a.serviceReport.customers.id})},{svg:l(()=>[X]),_:1},8,["href"]),o(C,{disabled:t(e).processing},{default:l(()=>[T("Save")]),_:1},8,["disabled"]),o($,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:l(()=>[t(e).recentlySuccessful?(d(),g("p",Y,"Saved.")):c("",!0)]),_:1})])])],40,z)])])]),_:1})],64))}};export{le as default};
