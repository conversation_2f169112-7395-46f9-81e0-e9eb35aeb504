import{K as c,h as u,o as d,c as l,a as t,u as s,w as n,F as p,Z as _,b as e,d as f,g,T as v,f as y}from"./app-4c3f0163.js";import{_ as b,a as x}from"./AdminLayout-36b0d46a.js";import{_ as h}from"./InputError-64c2d172.js";import{_ as V}from"./InputLabel-d6414ecf.js";import{P as $}from"./PrimaryButton-353715d1.js";import{_ as w}from"./TextInput-e8957d69.js";import"./_plugin-vue_export-helper-c27b6911.js";const N={class:"animate-top bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},T=e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Account",-1),B={class:"border-b border-gray-900/10 pb-12"},C={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},S={class:"sm:col-span-6"},k={class:"flex mt-6 items-center justify-between"},A={class:"ml-auto flex items-center justify-end gap-x-6"},E=e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),j={key:0,class:"text-sm text-gray-600"},z={__name:"Edit",props:["data"],setup(F){const r=c().props.data,a=u({id:r.id,name:r.name});return(m,o)=>(d(),l(p,null,[t(s(_),{title:"Account Types"}),t(b,null,{default:n(()=>[e("div",N,[T,e("form",{onSubmit:o[2]||(o[2]=f(i=>s(a).patch(m.route("account-type.update")),["prevent"]))},[e("div",B,[e("div",C,[e("div",S,[t(V,{for:"name",value:"Account Name"}),t(w,{id:"name",type:"text",modelValue:s(a).name,"onUpdate:modelValue":o[0]||(o[0]=i=>s(a).name=i),autocomplete:"name",onChange:o[1]||(o[1]=i=>s(a).validate("name"))},null,8,["modelValue"]),t(h,{class:"",message:s(a).errors.name},null,8,["message"])])])]),e("div",k,[e("div",A,[t(x,{href:m.route("account-type.index")},{svg:n(()=>[E]),_:1},8,["href"]),t($,{disabled:s(a).processing},{default:n(()=>[g("Save")]),_:1},8,["disabled"]),t(v,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:n(()=>[s(a).recentlySuccessful?(d(),l("p",j,"Saved.")):y("",!0)]),_:1})])])],32)])]),_:1})],64))}};export{z as default};
