import{r as T,o as u,c as p,a as r,u as o,w as _,F as h,Z as U,b as s,t as F,d as R,n as l,k as B,v as I,i as P,e as A,f,g as D,T as j,s as E,x as O}from"./app-4ea19997.js";import{_ as M,a as z}from"./AdminLayout-5eccc000.js";import{_ as L}from"./InputError-5063c06f.js";import{_ as i}from"./InputLabel-3a43d7c9.js";import{P as Z}from"./PrimaryButton-15e730cb.js";import{_ as g}from"./TextInput-2009383d.js";import{_ as y}from"./SearchableDropdown-d15cf33a.js";import{_ as q}from"./MultipleFileUpload-c28047d5.js";import{u as G}from"./index-f02fbcd1.js";import{_ as H}from"./_plugin-vue_export-helper-c27b6911.js";const x=d=>(E("data-v-17762362"),d=d(),O(),d),J={class:"h-screen animate-top"},K={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},Q={class:"sm:flex sm:items-center"},W=x(()=>s("div",{class:"sm:flex-auto"},[s("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create Report")],-1)),X={class:"text-sm font-semibold text-gray-900"},Y=["onSubmit"],ee={class:"border-b border-gray-900/10 pb-12"},se={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10"},oe={class:"sm:col-span-3"},te={class:"sm:col-span-3"},re={class:"sm:col-span-3"},ne={class:"relative mt-2"},ae={class:"sm:col-span-3"},de={class:"relative mt-2"},ie={class:"sm:col-span-3"},le={class:"relative mt-2"},ce={class:"sm:col-span-3"},me={class:"sm:col-span-3"},ue={key:0},pe=["onClick"],_e={class:"sm:col-span-4"},ve={class:"w-full"},fe={class:"flex mt-6 items-center justify-between"},ge={class:"ml-auto flex items-center justify-end gap-x-6"},ye=x(()=>s("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),be={key:0,class:"text-sm text-gray-600"},he={__name:"TempAdd",props:["reporttype","serviceperson","customer","company"],setup(d){const e=G("post","/service-reports",{customer_id:d.customer.id,company_id:"",product_code:"",product_name:"",date:"",serial_no:[],document:"",type:"",service_engineer_id:""}),c=T([""]),V=()=>{e.serial_no=c.value,e.submit({preserveScroll:!0,onSuccess:()=>e.reset()})},w=(a,t)=>{e.type=a,e.errors.type=null},S=(a,t)=>{e.company_id=a,e.errors.company_id=null},C=(a,t)=>{e.service_engineer_id=a,e.errors.service_engineer_id=null},k=a=>{e.document=a},v=a=>{e.errors.fieldName=null},N=()=>{c.value.push("")},$=a=>{c.value.splice(a,1)};return(a,t)=>(u(),p(h,null,[r(o(U),{title:"Create Report"}),r(M,null,{default:_(()=>[s("div",J,[s("div",K,[s("div",Q,[W,s("p",X,F(d.customer.customer_name),1)]),s("form",{onSubmit:R(V,["prevent"]),class:""},[s("div",ee,[s("div",se,[s("div",oe,[r(i,{for:"product_code",value:"Product Code"}),r(g,{id:"product_code",type:"text",onChange:t[0]||(t[0]=n=>v("product_code")),modelValue:o(e).product_code,"onUpdate:modelValue":t[1]||(t[1]=n=>o(e).product_code=n),class:l({"error rounded-md":o(e).errors.product_code})},null,8,["modelValue","class"])]),s("div",te,[r(i,{for:"product_name",value:"Product Name"}),r(g,{id:"product_name",type:"text",onChange:t[2]||(t[2]=n=>v("product_name")),modelValue:o(e).product_name,"onUpdate:modelValue":t[3]||(t[3]=n=>o(e).product_name=n),class:l({"error rounded-md":o(e).errors.product_name})},null,8,["modelValue","class"])]),s("div",re,[r(i,{for:"company_id",value:"Company Name"}),s("div",ne,[r(y,{options:d.company,modelValue:o(e).company_id,"onUpdate:modelValue":t[4]||(t[4]=n=>o(e).company_id=n),onOnchange:S,class:l({"error rounded-md":o(e).errors.company_id})},null,8,["options","modelValue","class"])])]),s("div",ae,[r(i,{for:"type",value:"Report Type"}),s("div",de,[r(y,{options:d.reporttype,modelValue:o(e).type,"onUpdate:modelValue":t[5]||(t[5]=n=>o(e).type=n),onOnchange:w,class:l({"error rounded-md":o(e).errors.type})},null,8,["options","modelValue","class"])])]),s("div",ie,[r(i,{for:"service_engineer_id",value:"Service Engineer"}),s("div",le,[r(y,{options:d.serviceperson,modelValue:o(e).service_engineer_id,"onUpdate:modelValue":t[6]||(t[6]=n=>o(e).service_engineer_id=n),onOnchange:C,class:l({"error rounded-md":o(e).errors.service_engineer_id})},null,8,["options","modelValue","class"])])]),s("div",ce,[r(i,{for:"date",value:"Date"}),B(s("input",{"onUpdate:modelValue":t[7]||(t[7]=n=>o(e).date=n),class:l(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":o(e).errors.date}]),type:"date",onChange:t[8]||(t[8]=n=>v("date"))},null,34),[[I,o(e).date]])]),s("div",me,[r(i,{for:"serial_no",value:"Serial No"}),(u(!0),p(h,null,P(c.value,(n,m)=>(u(),p("div",{key:m,class:"flex items-center gap-x-2 mb-2"},[r(g,{id:`serial_no_${m}`,type:"text",modelValue:c.value[m],"onUpdate:modelValue":b=>c.value[m]=b,class:l({"error rounded-md":o(e).errors.serial_no})},null,8,["id","modelValue","onUpdate:modelValue","class"]),m!=0?(u(),p("div",ue,[s("button",{type:"button",onClick:b=>$(m),class:"inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150 ml-3"}," Remove ",8,pe)])):f("",!0)]))),128)),s("div",{class:"w-32"},[s("button",{type:"button",onClick:N,class:"flex w-full justify-center rounded-md bg-indigo-600 px-4 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"}," Add Serial No ")])]),s("div",_e,[s("div",ve,[r(i,{for:"note",value:"Upload Report"}),r(q,{inputId:"document",inputName:"document",onFiles:k}),o(e).invalid("document")?(u(),A(L,{key:0,message:o(e).errors.document},null,8,["message"])):f("",!0)])])])]),s("div",fe,[s("div",ge,[r(z,{href:a.route("service-reports.show",{id:d.customer.id})},{svg:_(()=>[ye]),_:1},8,["href"]),r(Z,{disabled:o(e).processing},{default:_(()=>[D("Save")]),_:1},8,["disabled"]),r(j,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:_(()=>[o(e).recentlySuccessful?(u(),p("p",be,"Saved.")):f("",!0)]),_:1})])])],40,Y)])])]),_:1})],64))}},Re=H(he,[["__scopeId","data-v-17762362"]]);export{Re as default};
