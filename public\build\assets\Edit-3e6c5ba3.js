import{K as tt,r as b,C as bt,j as D,o as u,c as m,a as i,u as a,w as x,F as z,Z as Vt,b as t,t as v,k as Ct,v as St,d as It,n as _,f as y,i as et,g as k,e as Ft,s as kt,x as Pt}from"./app-b7a94f67.js";import{_ as $t,a as Tt}from"./AdminLayout-0f1fdf67.js";import{_ as Dt}from"./InputError-86b88c86.js";import{_ as V}from"./InputLabel-11b5d690.js";import{P as st}from"./PrimaryButton-4ffecd1c.js";import{_ as C}from"./TextInput-fea73171.js";import{_ as B}from"./TextArea-500c5ac8.js";import{_ as U}from"./SearchableDropdown-711fb977.js";import{D as ot}from"./DangerButton-a612a79a.js";import{_ as O}from"./SecondaryButton-c893313c.js";import{M as E}from"./Modal-e44dcdf0.js";import{_ as Ut}from"./FileViewer-d3655eec.js";import{_ as Gt}from"./MultipleFileUpload-e62c96d8.js";import{u as Mt}from"./index-5a4eda7d.js";import{_ as jt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const c=f=>(kt("data-v-d5de077c"),f=f(),Pt(),f),At={class:"animate-top"},Nt={class:"sm:flex sm:items-center"},qt=c(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Proforma Invoice")],-1)),zt={class:"w-auto"},Bt={class:"flex space-x-2 items-center"},Ot=c(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6"},"PI Number:",-1)),Et={class:"text-sm font-semibold text-gray-900 leading-6"},Lt={class:"flex space-x-2 items-center"},Wt=c(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Date :",-1)),Rt=["onSubmit"],Ht={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Kt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Qt={class:"sm:col-span-4"},Zt={class:"relative mt-2"},Jt={class:"sm:col-span-4"},Xt={class:"relative mt-2"},Yt={class:"sm:col-span-4"},te={class:"relative mt-2"},ee={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},se={class:"overflow-x-auto w-full"},oe={class:"overflow-x-auto divide-y divide-gray-300 w-full",style:{"margin-bottom":"160px"}},le=c(()=>t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Model",-1)),ae=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),ne=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),ie=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),re=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),de=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),ce={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ue={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},me={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},pe={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},_e={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ge=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),ve=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),ye=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),he={class:"divide-y divide-gray-300 bg-white"},xe={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-80"},fe={class:"relative mt-2"},we={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-96"},be={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ve={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ce={key:0,class:"text-red-500 text-xs absolute"},Se={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Ie={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Fe={class:"text-sm text-gray-900 leading-6 mt-2 py-1.5"},ke={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Pe={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},$e={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Te={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},De={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ue={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ge={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-36"},Me={class:"flex space-x-2"},je={class:"text-sm text-gray-900 leading-6 py-1.5"},Ae=["onClick"],Ne=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),qe=[Ne],ze={class:"flex items-center justify-between"},Be={class:"ml-auto flex items-center justify-end gap-x-6"},Oe={key:0,class:"mt-6 bg-white p-1 shadow sm:rounded-lg border"},Ee={class:"min-w-full divide-y divide-gray-300"},Le=c(()=>t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"UPLOADED DOCUMENT "),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"REMOVE")])],-1)),We={class:"divide-y divide-gray-300 bg-white"},Re={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},He={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},Ke=["onClick"],Qe=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Ze=[Qe],Je=["onClick"],Xe=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),Ye=[Xe],ts=["onClick"],es=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),ss=[es],os={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},ls={class:"items-center justify-between"},as={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},ns={class:"inline-flex items-center justify-end w-full space-x-3"},is=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),rs={class:"text-base font-semibold text-gray-900 w-20"},ds={class:"inline-flex items-center justify-end w-full space-x-3"},cs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),us={class:"w-40"},ms={class:"inline-flex items-center justify-end w-full space-x-3"},ps=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),_s={class:"text-base font-semibold text-gray-900 w-w-32"},gs={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},vs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),ys={class:"text-base font-semibold text-gray-900 w-w-32"},hs={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},xs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),fs={class:"text-base font-semibold text-gray-900 w-w-32"},ws={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},bs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),Vs={class:"text-base font-semibold text-gray-900 w-w-32"},Cs={class:"inline-flex items-center justify-end w-full space-x-3"},Ss=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),Is={class:"text-base font-semibold text-gray-900 w-w-32"},Fs={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},ks={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Ps={class:"sm:col-span-10"},$s={class:"flex space-x-4"},Ts={class:"w-full"},Ds=c(()=>t("div",{class:"w-full"},null,-1)),Us={class:"w-full"},Gs={class:"relative mt-2"},Ms={class:"sm:col-span-10"},js={class:"flex space-x-4"},As={class:"w-full"},Ns={class:"w-full"},qs={class:"w-full"},zs={class:"sm:col-span-10"},Bs={class:"flex space-x-4"},Os={class:"w-full"},Es={class:"w-full"},Ls={class:"flex mt-6 items-center justify-between"},Ws={class:"ml-auto flex items-center justify-end gap-x-6"},Rs=c(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),Hs={class:"p-6"},Ks=c(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this product? ",-1)),Qs={class:"mt-6 flex justify-end"},Zs={class:"p-6"},Js=c(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this document? ",-1)),Xs={class:"mt-6 flex justify-end"},Ys={class:"p-6"},to={class:"mt-6 px-4 flex justify-end"},eo={__name:"Edit",props:["customers","salesuser","products","filepath","category","organization"],setup(f){const lt=f,L=tt().props.filepath.view,d=tt().props.data[0],s=Mt("post","/proforma-invoice",{note:d.note,date:d.date,selectedProductItem:[],customer_id:d.customer_id,sales_user_id:d.sales_user_id,organization_id:d.organization_id,category:d.category,total_amount:"",order_number:d.order_number,pi_id:d.id,document:d.documents,cgst:d.cgst,sgst:d.sgst,igst:d.igst,total_gst:d.total_gst,sub_total:d.sub_total,total_discount:d.total_discount,overall_discount:d.overall_discount,validity:d.validity,delivery:d.delivery,payment_terms:d.payment_terms,warranty:d.warranty}),at=()=>{s.sub_total=H.value,s.total_discount=K.value,s.cgst=g.value=="CGST/SGST"?F.value/2:"0",s.sgst=g.value=="CGST/SGST"?F.value/2:"0",s.igst=g.value=="IGST"?F.value:"0",s.total_gst=F.value,s.total_amount=R.value,s.selectedProductItem=p.value,s.submit({preserveScroll:!0,onSuccess:()=>s.reset()})},p=b([{product_id:"",description:"",qty:"",price:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",discount:"",discount_amount:"",total_amount:"",pi_details_id:""}]);bt(()=>{p.value=d.proforma_invoice_details.map(o=>{var l,e;return{product_id:o.product_id,pi_details_id:o.id,description:o.description,qty:o.qty,price:parseFloat(o.price).toFixed(2),total_price:parseFloat(o.total_price).toFixed(2),mrp:(e=(l=o.product)==null?void 0:l.serial_numbers[0])!=null&&e.mrp?parseFloat(o.product.serial_numbers[0].mrp).toFixed(2):"-",gst:parseFloat(o.gst).toFixed(2),sgst:parseFloat(o.gst/2).toFixed(2),gst_amount:parseFloat(o.gst_amount).toFixed(2),total_gst_amount:parseFloat(o.total_gst_amount).toFixed(2),total_amount:parseFloat(o.total_amount).toFixed(2),discount:parseFloat(o.discount).toFixed(2),discount_amount:parseFloat(o.discount_amount).toFixed(2)??"0"}})});const g=b(d.customers.gst_type),nt=(o,l,e,r)=>{var h;const n=lt.products.find(S=>S.id===o);n&&(p.value[e].product_id=n.id,p.value[e].price=parseFloat(n.price).toFixed(2),p.value[e].description=n.description,p.value[e].mrp=(h=n==null?void 0:n.serial_numbers[0])!=null&&h.mrp?parseFloat(n.serial_numbers[0].mrp).toFixed(2):"-",p.value[e].gst=parseFloat(n.gst).toFixed(2),p.value[e].sgst=parseFloat(n.gst/2).toFixed(2),p.value[e].discount="0.00",s.errors[`selectedProductItem.${e}.product_id`]=null,s.errors[`selectedProductItem.${e}.price`]=null,I(r))},it=()=>{p.value.push({product_id:"",description:"",qty:"",price:"",gst:"",total_price:"",gst_amount:"",total_gst_amount:"",discount:"",discount_amount:"",total_amount:"",pi_details_id:""})},G=b(!1),W=b(null),rt=b(null),M=()=>{G.value=!1},dt=()=>{s.get(route("removeproduct",{id:W.value,model:"ProformaInvoiceDetails"}),{onSuccess:()=>{M(),p.value.splice(index,1)}})},ct=(o,l)=>{l!==void 0&&l!=""?(W.value=l,rt.value=o,G.value=!0):p.value.splice(o,1)},ut=(o,l)=>{const e=parseFloat(o.price),r=parseFloat(o.discount)||0,n=g.value=="IGST"?parseFloat(o.gst):parseFloat(o.sgst*2),h=parseFloat(o.qty);let S=0,$=0;r>0?S=e*h:S=e*h*(1+n/100);const T=S*(r/100)||0,X=e*1*(n/100),q=(e*h-T)*(n/100);r>0?$=S-T+q:$=S-T;const Y=e*h;return o.total_price=isNaN(Y)?"":parseFloat(Y).toFixed(2),o.gst_amount=isNaN(X)?"":parseFloat(X).toFixed(2),o.total_gst_amount=isNaN(q)?"":parseFloat(q).toFixed(2),o.discount_amount=isNaN(T)?"":parseFloat(T).toFixed(2),isNaN($)?"":parseFloat($).toFixed(2)},I=(o,l)=>{o.total_amount=ut(o)},R=D(()=>{const o=Math.round(p.value.reduce((e,r)=>e+(r.total_amount?parseFloat(r.total_amount):0),0)),l=s.overall_discount?parseFloat(s.overall_discount):0;return o-l}),F=D(()=>p.value.reduce((o,l)=>o+(l.total_gst_amount?parseFloat(l.total_gst_amount):0),0)),H=D(()=>p.value.reduce((o,l)=>o+(l.total_price?parseFloat(l.total_price):0),0)),K=D(()=>{const o=p.value.reduce((e,r)=>e+(r.discount_amount?parseFloat(r.discount_amount):0),0),l=s.overall_discount?parseFloat(s.overall_discount):0;return o+l}),w=o=>{s.errors[o]=null};D(()=>{const o=new Date(d.date),l={year:"numeric",month:"long",day:"numeric"};return o.toLocaleDateString("en-US",l)});const mt=o=>{s.document=o},j=b(!1),Q=b(null),pt=o=>{Q.value=o,j.value=!0},_t=()=>{s.get(route("removedocument",{id:Q.value,name:"piDocument"}),{onSuccess:()=>{A()}})},A=()=>{j.value=!1},gt=(o,l)=>{s.sales_user_id=o},N=b(!1),Z=b(null),vt=b("custom"),yt=o=>{Z.value=o,N.value=!0},J=()=>{N.value=!1},ht=o=>{const l=window.location.origin+L+o,e=document.createElement("a");e.href=l,e.setAttribute("download",o),document.body.appendChild(e),e.click(),document.body.removeChild(e)},P=o=>{let l=o.toFixed(2).toString(),[e,r]=l.split("."),n=e.substring(e.length-3),h=e.substring(0,e.length-3);return h!==""&&(n=","+n),`${h.replace(/\B(?=(\d{2})+(?!\d))/g,",")+n}.${r}`},xt=(o,l)=>{s.customer_id=o,s.errors.customer_id=null},ft=(o,l)=>{s.category=o,s.errors.category=null},wt=(o,l)=>{s.organization_id=o,s.errors.organization_id=null};return(o,l)=>(u(),m(z,null,[i(a(Vt),{title:"Proforma Invoice"}),i($t,null,{default:x(()=>[t("div",At,[t("div",Nt,[qt,t("div",zt,[t("div",Bt,[Ot,t("span",Et,v(a(d).order_number),1)]),t("div",Lt,[Wt,Ct(t("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":l[0]||(l[0]=e=>a(s).date=e),onChange:l[1]||(l[1]=e=>a(s).validate("date"))},null,544),[[St,a(s).date]])])])]),t("form",{onSubmit:It(at,["prevent"]),class:""},[t("div",Ht,[t("div",Kt,[t("div",Qt,[i(V,{for:"company_name",value:"Organization"}),t("div",Zt,[i(U,{options:f.organization,modelValue:a(s).organization_id,"onUpdate:modelValue":l[2]||(l[2]=e=>a(s).organization_id=e),onOnchange:wt,class:_({"error rounded-md":a(s).errors.organization_id}),editMode:"editMode"},null,8,["options","modelValue","class"])])]),t("div",Jt,[i(V,{for:"customer_id",value:"Customer Name"}),t("div",Xt,[i(U,{options:f.customers,modelValue:a(s).customer_id,"onUpdate:modelValue":l[3]||(l[3]=e=>a(s).customer_id=e),onOnchange:xt,class:_({"error rounded-md":a(s).errors.customer_id})},null,8,["options","modelValue","class"])])]),t("div",Yt,[i(V,{for:"company_name",value:"Category"}),t("div",te,[i(U,{options:f.category,modelValue:a(s).category,"onUpdate:modelValue":l[4]||(l[4]=e=>a(s).category=e),onOnchange:ft,class:_({"error rounded-md":a(s).errors.category})},null,8,["options","modelValue","class"])])])])]),t("div",ee,[t("div",se,[t("table",oe,[t("thead",null,[t("tr",null,[le,ae,ne,ie,re,de,g.value=="IGST"?(u(),m("th",ce,"IGST (%)")):y("",!0),g.value=="IGST"?(u(),m("th",ue,"IGST (₹)")):y("",!0),g.value=="CGST/SGST"?(u(),m("th",me,"CGST (%)")):y("",!0),g.value=="CGST/SGST"?(u(),m("th",pe,"SGST (%)")):y("",!0),g.value=="CGST/SGST"?(u(),m("th",_e,"Total GST (₹)")):y("",!0),ge,ve,ye])]),t("tbody",he,[(u(!0),m(z,null,et(p.value,(e,r)=>(u(),m("tr",{key:r},[t("td",xe,[t("div",fe,[i(U,{options:f.products,modelValue:e.product_id,"onUpdate:modelValue":n=>e.product_id=n,onOnchange:(n,h)=>nt(n,h,r,e),onChange:l[5]||(l[5]=n=>a(s).validate("product_id")),class:_({"error rounded-md":a(s).errors[`selectedProductItem.${r}.product_id`]})},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class"])])]),t("td",we,[i(B,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":n=>e.description=n,autocomplete:"description",rows:2,onChange:n=>e.validate("description"),class:_({"error rounded-md":a(s).errors[`selectedProductItem.${r}.description`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])]),t("td",be,v(e.mrp??"-"),1),t("td",Ve,[i(C,{id:"qty",type:"text",numeric:!0,modelValue:e.qty,"onUpdate:modelValue":n=>e.qty=n,autocomplete:"qty",onInput:n=>I(e,r),onChange:n=>w("selectedProductItem."+r+".qty"),class:_({error:a(s).errors[`selectedProductItem.${r}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"]),a(s).errors[`selectedProductItem.${r}.qty1`]?(u(),m("p",Ce,v(a(s).errors[`selectedProductItem.${r}.qty1`]),1)):y("",!0)]),t("td",Se,[i(C,{id:"price",type:"text",modelValue:e.price,"onUpdate:modelValue":n=>e.price=n,autocomplete:"price",onInput:n=>I(e,r),onChange:n=>w("selectedProductItem."+r+".price"),class:_({error:a(s).errors[`selectedProductItem.${r}.price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Ie,[t("div",Fe,v(e.total_price),1)]),g.value=="IGST"?(u(),m("td",ke,[i(C,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":n=>e.gst=n,onInput:n=>I(e,r),onChange:n=>w("selectedProductItem."+r+".gst"),class:_({error:a(s).errors[`selectedProductItem.${r}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):y("",!0),g.value=="CGST/SGST"?(u(),m("td",Pe,[i(C,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":n=>e.sgst=n,onInput:n=>I(e,r),onChange:n=>w("selectedProductItem."+r+".gst"),class:_({error:a(s).errors[`selectedProductItem.${r}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):y("",!0),g.value=="CGST/SGST"?(u(),m("td",$e,[i(C,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":n=>e.sgst=n,onInput:n=>I(e,r),onChange:n=>w("selectedProductItem."+r+".gst"),class:_({error:a(s).errors[`selectedProductItem.${r}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):y("",!0),t("td",Te,v(e.total_gst_amount),1),t("td",De,[i(C,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":n=>e.discount=n,onInput:n=>I(e,r),onChange:n=>w("selectedProductItem."+r+".discount"),class:_({error:a(s).errors[`selectedProductItem.${r}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Ue,v(e.discount_amount),1),t("td",Ge,[t("div",Me,[t("div",je,v(e.total_amount),1),t("button",{type:"button",class:"mt-1 flex",onClick:n=>ct(r,e.pi_details_id)},qe,8,Ae)])])]))),128))])])]),t("div",ze,[t("div",Be,[i(st,{onClick:it,type:"button"},{default:x(()=>[k("Add Product")]),_:1})])])]),a(d).documents&&a(d).documents.length>0?(u(),m("div",Oe,[t("table",Ee,[Le,t("tbody",We,[(u(!0),m(z,null,et(a(d).documents,(e,r)=>(u(),m("tr",{key:a(d).id,class:""},[t("td",Re,v(e.orignal_name),1),t("td",He,[t("button",{type:"button",onClick:n=>pt(e.id)},Ze,8,Ke),t("button",{type:"button",onClick:n=>yt(e.name)},Ye,8,Je),t("button",{type:"button",onClick:n=>ht(e.name)},ss,8,ts)])]))),128))])])])):y("",!0),t("div",os,[t("div",ls,[t("div",as,[t("div",ns,[is,t("p",rs,v(P(H.value)),1)]),t("div",ds,[cs,t("div",us,[i(C,{id:"overall_discount",type:"text",modelValue:a(s).overall_discount,"onUpdate:modelValue":l[6]||(l[6]=e=>a(s).overall_discount=e)},null,8,["modelValue"])])]),t("div",ms,[ps,t("p",_s,v(P(K.value)),1)]),g.value=="IGST"?(u(),m("div",gs,[vs,t("p",ys,v(P(F.value)),1)])):y("",!0),g.value=="CGST/SGST"?(u(),m("div",hs,[xs,t("p",fs,v(P(F.value/2)),1)])):y("",!0),g.value=="CGST/SGST"?(u(),m("div",ws,[bs,t("p",Vs,v(P(F.value/2)),1)])):y("",!0),t("div",Cs,[Ss,t("p",Is,v(P(R.value)),1)])])])]),t("div",Fs,[t("div",ks,[t("div",Ps,[t("div",$s,[t("div",Ts,[i(V,{for:"note",value:"Upload Documents"}),i(Gt,{inputId:"document",inputName:"document",onFiles:mt})]),Ds,t("div",Us,[i(V,{for:"company_name",value:"Sales Person"}),t("div",Gs,[i(U,{options:f.salesuser,modelValue:a(s).sales_user_id,"onUpdate:modelValue":l[7]||(l[7]=e=>a(s).sales_user_id=e),onOnchange:gt,class:_({"error rounded-md":a(s).errors.sales_user_id})},null,8,["options","modelValue","class"])])])])]),t("div",Ms,[t("div",js,[t("div",As,[i(V,{for:"Validity",value:"Validity"}),i(C,{id:"price",type:"text",modelValue:a(s).validity,"onUpdate:modelValue":l[8]||(l[8]=e=>a(s).validity=e),onChange:l[9]||(l[9]=e=>w(a(s).errors.validity)),class:_({"error rounded-md":a(s).errors.validity})},null,8,["modelValue","class"])]),t("div",Ns,[i(V,{for:"delivery",value:"Delivery"}),i(C,{id:"price",type:"text",modelValue:a(s).delivery,"onUpdate:modelValue":l[10]||(l[10]=e=>a(s).delivery=e),onChange:l[11]||(l[11]=e=>w(a(s).errors.delivery)),class:_({"error rounded-md":a(s).errors.delivery})},null,8,["modelValue","class"])]),t("div",qs,[i(V,{for:"warranty",value:"Warranty"}),i(C,{id:"price",type:"text",modelValue:a(s).warranty,"onUpdate:modelValue":l[12]||(l[12]=e=>a(s).warranty=e),onChange:l[13]||(l[13]=e=>w(a(s).errors.warranty)),class:_({"error rounded-md":a(s).errors.warranty})},null,8,["modelValue","class"])])])]),t("div",zs,[t("div",Bs,[t("div",Os,[i(V,{for:"payment_terms",value:"Payment terms"}),i(B,{id:"price",type:"text",rows:4,modelValue:a(s).payment_terms,"onUpdate:modelValue":l[14]||(l[14]=e=>a(s).payment_terms=e),onChange:l[15]||(l[15]=e=>w(a(s).errors.payment_terms)),class:_({"error rounded-md":a(s).errors.payment_terms})},null,8,["modelValue","class"])]),t("div",Es,[i(V,{for:"note",value:"Note"}),i(B,{id:"note",type:"text",rows:4,modelValue:a(s).note,"onUpdate:modelValue":l[16]||(l[16]=e=>a(s).note=e),autocomplete:"note",onChange:l[17]||(l[17]=e=>a(s).validate("note"))},null,8,["modelValue"]),a(s).invalid("note")?(u(),Ft(Dt,{key:0,class:"",message:a(s).errors.note},null,8,["message"])):y("",!0)])])])])]),t("div",Ls,[t("div",Ws,[i(Tt,{href:o.route("proforma-invoice.index")},{svg:x(()=>[Rs]),_:1},8,["href"]),i(st,{disabled:a(s).processing},{default:x(()=>[k("Submit")]),_:1},8,["disabled"])])])],40,Rt)]),i(E,{show:G.value,onClose:M},{default:x(()=>[t("div",Hs,[Ks,t("div",Qs,[i(O,{onClick:M},{default:x(()=>[k(" Cancel")]),_:1}),i(ot,{class:"ml-3",onClick:dt},{default:x(()=>[k(" Delete ")]),_:1})])])]),_:1},8,["show"]),i(E,{show:j.value,onClose:A},{default:x(()=>[t("div",Zs,[Js,t("div",Xs,[i(O,{onClick:A},{default:x(()=>[k(" Cancel")]),_:1}),i(ot,{class:"ml-3",onClick:_t},{default:x(()=>[k(" Delete ")]),_:1})])])]),_:1},8,["show"]),i(E,{show:N.value,onClose:J,maxWidth:vt.value},{default:x(()=>[t("div",Ys,[i(Ut,{fileUrl:a(L)+Z.value},null,8,["fileUrl"]),t("div",to,[i(O,{onClick:J},{default:x(()=>[k(" Cancel")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},xo=jt(eo,[["__scopeId","data-v-d5de077c"]]);export{xo as default};
