<?php

namespace App\Traits;
use App\Models\Setting;
use Carbon\Carbon;

trait CommonTrait {

    public function getYear(){
        $currentMonth = date('n');
        $currentYear = date('Y');
        if ($currentMonth >= 4) {
            $startYear = $currentYear % 100;
            $endYear = ($currentYear + 1) % 100;
        } else {
            $startYear = ($currentYear - 1) % 100;
            $endYear = $currentYear % 100;
        }
        return '/' . $startYear . '-' . $endYear;
    }

    private function sanitizeFilename($filename) {
        // Replace spaces with underscores
        $sanitizedFilename = str_replace(' ', '_', $filename);
        // Remove invalid characters
        $sanitizedFilename = preg_replace('/[<>:"\/\\\\|?*]+/', '', $sanitizedFilename);
        return $sanitizedFilename;
    }

    private function generatePONumber()
    {
        $settings = Setting::where('type', 'po_number')->get();
        $poNumbersByOrganization = [];
        foreach ($settings as $setting) {
            $paddedNumber = str_pad($setting->number, 5, '0', STR_PAD_LEFT);
            $poNumber = $setting->value . $paddedNumber . $this->getYear();
            $poNumbersByOrganization[$setting->organization_id] = $poNumber;
        }
        return $poNumbersByOrganization;
    }

    private function updatePONumber($po_number, $organization_id)
    {
        $challanNumber = Setting::where(['type' => 'po_number', 'organization_id' => $organization_id])->firstOrFail();
        $new_number = (int)substr($po_number, strlen($challanNumber->value)) + 1;
        $challanNumber->update(['number' => $new_number]);
    }

    private function generatePOReceiveNumber()
    {
        $settings = Setting::where('type', 'po_receive_number')->get();
        $poNumbersByOrganization = [];
        foreach ($settings as $setting) {
            $paddedNumber = str_pad($setting->number, 5, '0', STR_PAD_LEFT);
            $poNumber = $setting->value . $paddedNumber . $this->getYear();
            $poNumbersByOrganization[$setting->organization_id] = $poNumber;
        }
        return $poNumbersByOrganization;
    }

    private function updatePOReceiveNumber($po_number, $organization_id)
    {
        $poNumber = Setting::where(['type' => 'po_receive_number', 'organization_id' => $organization_id])->firstOrFail();
        $new_number = (int)substr($po_number, strlen($poNumber->value)) + 1;
        $poNumber->update(['number' => $new_number]);
    }

    private function generateCHLNumber()
    {
        $challanNumber = Setting::where('type', 'challan_number')->get();
        $challanNumberByOrganization = [];
        foreach ($challanNumber as $setting) {
            $paddedNumber = str_pad($setting->number, 5, '0', STR_PAD_LEFT);
            $poNumber = $setting->value . $paddedNumber . $this->getYear();
            $challanNumberByOrganization[$setting->organization_id] = $poNumber;
        }
        return $challanNumberByOrganization;
    }

    private function updateCHLNumber($challan_number , $organization_id)
    {
        $challanNumber = Setting::where(['type' => 'challan_number', 'organization_id' => $organization_id])->firstOrFail();
        $new_number = (int)substr($challan_number, strlen($challanNumber->value)) + 1;
        $challanNumber->update(['number' => $new_number]);
    }

    private function generateDemoCHLNumber()
    {
        $challanNumber = Setting::where('type', 'demo_challan_number')->get();
        $challanNumberByOrganization = [];
        foreach ($challanNumber as $setting) {
            $paddedNumber = str_pad($setting->number, 5, '0', STR_PAD_LEFT);
            $poNumber = $setting->value . $paddedNumber . $this->getYear();
            $challanNumberByOrganization[$setting->organization_id] = $poNumber;
        }
        return $challanNumberByOrganization;
    }

    private function updateDemoCHLNumber($demo_challan_number , $organization_id)
    {
        $challanNumber = Setting::where(['type' => 'demo_challan_number', 'organization_id' => $organization_id])->firstOrFail();
        $new_number = (int)substr($demo_challan_number, strlen($challanNumber->value)) + 1;
        $challanNumber->update(['number' => $new_number]);
    }

    private function generateInvoiceNo()
    {
        $settings = Setting::where('type', 'invoice_number')->get();
        $invoiceNumberByOrganization = [];
        foreach ($settings as $setting) {
            $paddedNumber = str_pad($setting->number, 5, '0', STR_PAD_LEFT);
            $poNumber = $setting->value . $paddedNumber . $this->getYear();
            $invoiceNumberByOrganization[$setting->organization_id] = $poNumber;
        }
        return $invoiceNumberByOrganization;
    }

    private function updateInvoiceNo($invoice_number,  $organization_id)
    {
        $invoiceNumber = Setting::where(['type' => 'invoice_number', 'organization_id' => $organization_id])->firstOrFail();
        $new_number = (int)substr($invoice_number, strlen($invoiceNumber->value)) + 1;
        $invoiceNumber->update(['number' => $new_number]);
    }

    private function generateRetailInvoiceNo()
    {
        $settings = Setting::where('type', 'retail_invoice_number')->get();
        $invoiceNumberByOrganization = [];
        foreach ($settings as $setting) {
            $paddedNumber = str_pad($setting->number, 5, '0', STR_PAD_LEFT);
            $poNumber = $setting->value . $paddedNumber . $this->getYear();
            $invoiceNumberByOrganization[$setting->organization_id] = $poNumber;
        }
        return $invoiceNumberByOrganization;
    }

    private function updateRetailInvoiceNo($retail_invoice_number, $organization_id)
    {
        $invoiceNumber = Setting::where(['type' => 'retail_invoice_number', 'organization_id' => $organization_id])->firstOrFail();
        $new_number = (int)substr($retail_invoice_number, strlen($invoiceNumber->value)) + 1;
        $invoiceNumber->update(['number' => $new_number]);
    }

    private function generateORNumber()
    {
        $orderNumber = Setting::where('type', 'order_number')->get();
        $orderNumberByOrganization = [];
        foreach ($orderNumber as $setting) {
            $paddedNumber = str_pad($setting->number, 5, '0', STR_PAD_LEFT);
            $poNumber = $setting->value . $paddedNumber . $this->getYear();
            $orderNumberByOrganization[$setting->organization_id] = $poNumber;
        }
        return $orderNumberByOrganization;
    }

    private function updateORNumber($order_number, $organization_id)
    {
        $orderNumber = Setting::where(['type' => 'order_number', 'organization_id' => $organization_id])->firstOrFail();
        $new_number = (int)substr($order_number, strlen($orderNumber->value)) + 1;
        $orderNumber->update(['number' => $new_number]);
    }


    private function generatePINumber()
    {
        $piNumber = Setting::where('type', 'pi_number')->get();
        $piNumberByOrganization = [];
        foreach ($piNumber as $setting) {
            $paddedNumber = str_pad($setting->number, 5, '0', STR_PAD_LEFT);
            $piNumber = $setting->value . $paddedNumber . $this->getYear();
            $piNumberByOrganization[$setting->organization_id] = $piNumber;
        }
        return $piNumberByOrganization;
    }

    private function updatePINumber($pi_number, $organization_id)
    {
        $piNumber = Setting::where(['type' => 'pi_number', 'organization_id' => $organization_id])->firstOrFail();
        $new_number = (int)substr($pi_number, strlen($piNumber->value)) + 1;
        $piNumber->update(['number' => $new_number]);
    }

    private function generateORDNumber()
    {
        $orderDeliverNumber = Setting::where('type', 'order_deliver_number')->first();
        $paddedNumber = str_pad($orderDeliverNumber->number, 5, '0', STR_PAD_LEFT);
        return $orderDeliverNumber->value . $paddedNumber .$this->getYear();
    }

    private function updateORDNumber($order_number)
    {
        $orderDeliverNumber = Setting::where('type', 'order_deliver_number')->first();
        $number = (int)substr($order_number, strlen($orderDeliverNumber->value));
        $new_number = $number + 1;
        $updatePoNumber = Setting::where('type', 'order_deliver_number')->update(['number' => $new_number]);
    }

    private function generateQUTNumber()
    {
        $quotationNumber = Setting::where('type', 'quotation_number')->get();
        $quotationNumberByOrganization = [];
        foreach ($quotationNumber as $setting) {
            $paddedNumber = str_pad($setting->number, 5, '0', STR_PAD_LEFT);
            $poNumber = $setting->value . $paddedNumber . $this->getYear();
            $quotationNumberByOrganization[$setting->organization_id] = $poNumber;
        }
        return $quotationNumberByOrganization;
    }

    private function updateQUTNumber($quotation_number, $organization_id)
    {
        $quotationNumber = Setting::where(['type' => 'quotation_number', 'organization_id' => $organization_id])->firstOrFail();
        $new_number = (int)substr($quotation_number, strlen($quotationNumber->value)) + 1;
        $quotationNumber->update(['number' => $new_number]);
    }

    private function generateJobNumber()
    {
        $settings = Setting::where('type', 'job_card_number')->get();
        $paddedNumber = str_pad($settings[0]->number, 5, '0', STR_PAD_LEFT);
        $jobNumber = $settings[0]->value . $paddedNumber . $this->getYear();
        return $jobNumber;
    }

    private function updateJobNumber($job_card_number)
    {
        $jobNumber = Setting::where(['type' => 'job_card_number', 'organization_id' => 1])->firstOrFail();
        $new_number = (int)substr($job_card_number, strlen($jobNumber->value)) + 1;
        $jobNumber->update(['number' => $new_number]);
    }

    private function generateCreditNo()
    {
        $settings = Setting::where('type', 'credit_note_no')->get();
        $invoiceNumberByOrganization = [];
        foreach ($settings as $setting) {
            $paddedNumber = str_pad($setting->number, 5, '0', STR_PAD_LEFT);
            $poNumber = $setting->value . $paddedNumber . $this->getYear();
            $invoiceNumberByOrganization[$setting->organization_id] = $poNumber;
        }
        return $invoiceNumberByOrganization;
    }

    private function updateCreditNo($credit_note_no,  $organization_id)
    {
        $invoiceNumber = Setting::where(['type' => 'credit_note_no', 'organization_id' => $organization_id])->firstOrFail();
        $new_number = (int)substr($credit_note_no, strlen($invoiceNumber->value)) + 1;
        $invoiceNumber->update(['number' => $new_number]);
    }

    private function generateDebitNo()
    {
        $settings = Setting::where('type', 'debit_note_no')->get();
        $invoiceNumberByOrganization = [];
        foreach ($settings as $setting) {
            $paddedNumber = str_pad($setting->number, 5, '0', STR_PAD_LEFT);
            $poNumber = $setting->value . $paddedNumber . $this->getYear();
            $invoiceNumberByOrganization[$setting->organization_id] = $poNumber;
        }
        return $invoiceNumberByOrganization;
    }

    private function updateDebitNo($debit_note_no,  $organization_id)
    {
        $invoiceNumber = Setting::where(['type' => 'debit_note_no', 'organization_id' => $organization_id])->firstOrFail();
        $new_number = (int)substr($debit_note_no, strlen($invoiceNumber->value)) + 1;
        $invoiceNumber->update(['number' => $new_number]);
    }

    protected function getFinancialYears()
    {
        $currentYear = date('Y');
        $startYear = 2025; // or any start year you want
        $financialYears = [];
        for ($i = $currentYear + 1; $i >= $startYear; $i--) {
            $start = Carbon::createFromDate($i - 1, 4, 1)->format('d-m-Y');
            $end = Carbon::createFromDate($i, 3, 31)->format('d-m-Y');
            $id = ($i - 1) . '-' . $i; // e.g., 2024-2025
            $name = $start . ' to ' . $end;
            $financialYears[] = ['id' => $name, 'name' => $id];
        }
        return $financialYears;
    }

}
