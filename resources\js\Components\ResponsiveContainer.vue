<template>
    <div class="responsive-container">
        <!-- Mobile Header (only visible on mobile) -->
        <div v-if="showMobileHeader" class="lg:hidden mb-4 sm:mb-6">
            <div class="bg-white rounded-lg shadow-sm border p-4">
                <h1 class="text-lg sm:text-xl font-semibold text-gray-900 truncate">
                    {{ title }}
                </h1>
                <p v-if="subtitle" class="text-sm text-gray-600 mt-1">
                    {{ subtitle }}
                </p>
            </div>
        </div>

        <!-- Main Content -->
        <div class="responsive-content">
            <slot />
        </div>
    </div>
</template>

<script setup>
defineProps({
    title: {
        type: String,
        default: ''
    },
    subtitle: {
        type: String,
        default: ''
    },
    showMobileHeader: {
        type: Boolean,
        default: true
    }
});
</script>

<style scoped>
.responsive-container {
    @apply w-full;
}

.responsive-content {
    @apply w-full;
}

/* Mobile optimizations */
@media (max-width: 640px) {
    .responsive-container {
        @apply px-1;
    }
}

@media (min-width: 641px) and (max-width: 1024px) {
    .responsive-container {
        @apply px-2;
    }
}
</style>
