import{_ as F,b as R,a as K}from"./AdminLayout-dc64724f.js";import{_ as Y}from"./CreateButton-1a5625b4.js";import{_ as q}from"./SecondaryButton-9a822eb1.js";import{D as H}from"./DangerButton-c7881a4e.js";import{M as Z}from"./Modal-b05cc76d.js";import{_ as G}from"./Pagination-cae8b9a9.js";import{_ as J}from"./SearchableDropdownNew-bd12e1c2.js";import{_ as Q}from"./SimpleDropdown-0e0a895b.js";import{_ as k}from"./InputLabel-5e6ac969.js";import{r as u,l as W,o,c as i,a,u as g,w as c,F as x,Z as X,b as e,g as h,i as C,e as z,f as v,t as r,n as D}from"./app-6a429cee.js";import{_ as ee}from"./ArrowIcon-2f445522.js";import{s as te}from"./sortAndSearch-de435c62.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const se={class:"animate-top"},oe={class:"flex justify-between items-center"},ne=e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Receive Payments")],-1),le={class:"flex justify-end"},ae=e("div",{class:"flex space-x-6 mt-4 sm:mt-0 w-64"},[e("div",{class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},[e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})]),e("input",{id:"search-field",class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"})])],-1),ie={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},re={class:"flex justify-end"},de={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},ce={class:"flex mb-2"},me=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),_e={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},ue={class:"sm:col-span-4"},he={class:"relative mt-2"},fe={class:"sm:col-span-4"},pe={class:"relative mt-2"},ge={class:"mt-8 overflow-x-auto sm:rounded-lg"},xe={class:"shadow sm:rounded-lg"},ve={class:"w-full text-sm text-left rtl:text-right text-gray-500"},ye={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},be={class:"border-b-2"},we=["onClick"],ke={key:0},Ce={class:"px-4 py-2.5 min-w-32"},ze={class:"px-4 py-2.5 min-w-52 text-sm flex flex-col font-medium text-gray-900"},Me={class:"tooltiptext text-xs"},Ne={class:"px-4 py-2.5 min-w-36"},Se={class:"px-4 py-2.5 min-w-52"},Ie={class:"px-4 py-2.5 min-w-44"},$e={key:0,class:"space-y-1"},Ve={class:"text-sm"},Ae={key:1},Be={class:"px-4 py-2.5 min-w-32"},Oe={class:"items-center px-4 py-2.5"},Le={class:"flex items-center justify-start gap-4"},Pe=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),je=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),Ee=e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),Te=["onClick"],Ue=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),Fe=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),Re=[Ue,Fe],Ke={key:1},Ye=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),qe=[Ye],He={class:"p-6"},Ze=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this transaction? ",-1),Ge={class:"mt-6 flex justify-end"},ut={__name:"List",props:["data","organization","customers","organizationId","customerId"],setup(d){const f=d,{form:M,search:Je,sort:$,fetchData:Qe,sortKey:V,sortDirection:A,updateParams:B}=te("receipt.index",{organization_id:f.organizationId,customer_id:f.customerId}),y=u(!1),N=u(null),O=[{field:"date",label:"DATE",sortable:!0},{field:"customers.customer_name",label:"CUSTOMER NAME",sortable:!0},{field:"payment_type",label:"PAYMENT TYPE",sortable:!0},{field:"bank_info.bank_name",label:"BANK",sortable:!0},{field:"invoice.invoice_no",label:"INVOICE NO",sortable:!1},{field:"amount",label:"AMOUNT (₹)",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],L=s=>{N.value=s,y.value=!0},b=()=>{y.value=!1},P=()=>{M.delete(route("receipt.destroy",{id:N.value}),{onSuccess:()=>b()})},j=s=>{const n=new Date(s),t={year:"numeric",month:"short",day:"numeric"};return n.toLocaleDateString("en-US",t)},E=s=>{let n=s.toFixed(2).toString(),[t,p]=n.split("."),l=t.substring(t.length-3),I=t.substring(0,t.length-3);return I!==""&&(l=","+l),`${I.replace(/\B(?=(\d{2})+(?!\d))/g,",")+l}.${p}`},m=u(f.organizationId),_=u(f.customerId),w=u("");W([m,_],()=>{B({organization_id:m.value,company_id:_.value})});const S=(s,n,t)=>{w.value=s,M.get(route("receipt.index",{search:s,organization_id:n,customer_id:t}),{preserveState:!0})},T=(s,n)=>{m.value=s,S(w.value,m.value,_.value)},U=(s,n)=>{_.value=s,S(w.value,m.value,_.value)};return(s,n)=>(o(),i(x,null,[a(g(X),{title:"Receipt"}),a(F,null,{default:c(()=>[e("div",se,[e("div",oe,[ne,e("div",le,[ae,e("div",ie,[e("div",re,[a(Y,{href:s.route("receipt.create")},{default:c(()=>[h(" Receive payment ")]),_:1},8,["href"])])])])]),e("div",de,[e("div",ce,[me,a(k,{for:"customer_id",value:"Filters"})]),e("div",_e,[e("div",ue,[a(k,{for:"customer_id",value:"Organization Name"}),e("div",he,[a(Q,{options:d.organization,modelValue:m.value,"onUpdate:modelValue":n[0]||(n[0]=t=>m.value=t),onOnchange:T},null,8,["options","modelValue"])])]),e("div",fe,[a(k,{for:"customer_id",value:"Customer Name"}),e("div",pe,[a(J,{options:d.customers,modelValue:_.value,"onUpdate:modelValue":n[1]||(n[1]=t=>_.value=t),onOnchange:U},null,8,["options","modelValue"])])])])]),e("div",ge,[e("div",xe,[e("table",ve,[e("thead",ye,[e("tr",be,[(o(),i(x,null,C(O,(t,p)=>e("th",{key:p,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:l=>g($)(t.field,t.sortable)},[h(r(t.label)+" ",1),t.sortable?(o(),z(ee,{key:0,isSorted:g(V)===t.field,direction:g(A)},null,8,["isSorted","direction"])):v("",!0)],8,we)),64))])]),d.data.data&&d.data.data.length>0?(o(),i("tbody",ke,[(o(!0),i(x,null,C(d.data.data,(t,p)=>(o(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Ce,r(j(t.date)??"-"),1),e("td",ze,[h(r(t.customers.customer_name??"-")+" ",1),e("span",Me,r(t.customers.city??"-"),1)]),e("td",Ne,r(t.payment_type=="check"?"Cheque":t.payment_type??"-"),1),e("td",Se,r(t!=null&&t.bank_info?(t==null?void 0:t.bank_info.bank_name)+"-"+(t==null?void 0:t.bank_info.account_number):"-"),1),e("td",Ie,[t!=null&&t.invoice_data&&t.invoice_data.length>0?(o(),i("div",$e,[(o(!0),i(x,null,C(t.invoice_data,l=>(o(),i("div",{key:l.id,class:"flex items-center gap-2"},[e("span",{class:D([l.invoice_type?l.invoice_type==="sales"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800":"bg-green-100 text-green-800","inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium"])},r(l.invoice_type?l.invoice_type==="sales"?"S":"P":"S"),3),e("span",Ve,r(l.invoice_no),1)]))),128))])):(o(),i("span",Ae,r(t.invoice_no||"-"),1))]),e("td",Be,r(E(t.amount)??"-"),1),e("td",Oe,[e("div",Le,[a(R,{align:"right",width:"48"},{trigger:c(()=>[Pe]),content:c(()=>[t.invoice_data.length!=0?(o(),z(K,{key:0,href:s.route("receipt.edit",t.id)},{svg:c(()=>[je]),text:c(()=>[Ee]),_:2},1032,["href"])):v("",!0),t.invoice_id==null?(o(),i("button",{key:1,type:"button",onClick:l=>L(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Re,8,Te)):v("",!0)]),_:2},1024)])])]))),128))])):(o(),i("tbody",Ke,qe))])])]),d.data.data&&d.data.data.length>0?(o(),z(G,{key:0,class:"mt-6",links:d.data.links},null,8,["links"])):v("",!0)]),a(Z,{show:y.value,onClose:b},{default:c(()=>[e("div",He,[Ze,e("div",Ge,[a(q,{onClick:b},{default:c(()=>[h(" Cancel ")]),_:1}),a(H,{class:"ml-3",onClick:P},{default:c(()=>[h(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{ut as default};
