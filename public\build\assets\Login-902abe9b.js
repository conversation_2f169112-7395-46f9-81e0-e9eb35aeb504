import{r as g,h,m as x,o as p,c as b,e as w,w as i,a as o,u as t,Z as v,b as a,y as c,t as k,f as S,g as d,n as V,d as L,s as B,x as $}from"./app-ce7743ab.js";import{_ as I}from"./Checkbox-540f8602.js";import{G as N}from"./GuestLayout-63b4e9c4.js";import{_ as f}from"./InputError-473f1c1e.js";import{_}from"./InputLabel-3aa35471.js";import{P as U}from"./PrimaryButton-6ff8a943.js";import{_ as y}from"./TextInput-65921831.js";import{_ as P}from"./_plugin-vue_export-helper-c27b6911.js";const R=["src"],j={__name:"ApplicationLogo",setup(l){const e=g("/uploads/companyprofile/defaultimg.png"),m=g("/uploads/companyprofile/visionlogo.png");h({});const n=s=>{e.value=s};return x(async()=>{try{const s=await fetch("/api/logo");if(s.ok){const r=await s.json();r.logoUrl?n("/uploads/companyprofile/"+r.logoUrl):n("/uploads/companyprofile/defaultimg.png")}}catch(s){console.error("Error fetching logo:",s)}}),(s,r)=>(p(),b("img",{src:m.value,alt:"LOGO"},null,8,R))}};const u=l=>(B("data-v-ae0ea7fb"),l=l(),$(),l),q={class:"flex justify-center items-center"},C=u(()=>a("h2",{class:"text-center text-2xl font-semibold leading-9 tracking-tight text-gray-900 mt-2"},"Sign in to your account",-1)),E={key:0,class:"mb-4 font-medium text-sm text-green-600"},G=["onSubmit"],F={class:""},M={class:"mt-2"},O={class:"mt-2"},T={class:"mb-4 flex justify-between items-center"},z={class:"flex items-center"},A=u(()=>a("span",{class:"ml-2 text-center text-sm text-gray-500"},"Remember me",-1)),D={class:"mt-2 flex items-center space-x-1"},Z=u(()=>a("span",{class:"text-sm text-gray-700"},"Not have an account ?",-1)),H={__name:"Login",props:{canResetPassword:{type:Boolean},status:{type:String},canRegister:{type:Boolean}},setup(l){const e=h({email:"",password:"",remember:!1}),m=()=>{e.post(route("login"),{onFinish:()=>e.reset("password")})};return x(async()=>{localStorage.removeItem("permissions")}),(n,s)=>(p(),w(N,null,{default:i(()=>[o(t(v),{title:"Log in"}),a("div",q,[o(t(c),{href:"/",class:""},{default:i(()=>[o(j,{class:"w-60 fill-current text-gray-500"})]),_:1})]),C,l.status?(p(),b("div",E,k(l.status),1)):S("",!0),a("form",{onSubmit:L(m,["prevent"])},[a("div",F,[o(_,{for:"email",value:"Email"}),o(y,{id:"email",type:"email",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:t(e).email,"onUpdate:modelValue":s[0]||(s[0]=r=>t(e).email=r),required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),o(f,{class:"mt-2",message:t(e).errors.email},null,8,["message"])]),a("div",M,[o(_,{for:"password",value:"Password"}),o(y,{id:"password",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:t(e).password,"onUpdate:modelValue":s[1]||(s[1]=r=>t(e).password=r),required:"",autocomplete:"current-password"},null,8,["modelValue"]),o(f,{class:"mt-2",message:t(e).errors.password},null,8,["message"])]),a("div",O,[a("div",T,[a("label",z,[o(I,{name:"remember",checked:t(e).remember,"onUpdate:checked":s[2]||(s[2]=r=>t(e).remember=r)},null,8,["checked"]),A]),o(t(c),{href:n.route("password.request"),class:"font-semibold text-indigo-600 text-sm hover:text-indigo-500"},{default:i(()=>[d(" Forgot your password? ")]),_:1},8,["href"])])]),o(U,{class:V(["",{"opacity-25":t(e).processing}]),disabled:t(e).processing},{default:i(()=>[d(" Log in ")]),_:1},8,["class","disabled"]),a("div",D,[Z,o(t(c),{href:n.route("register"),class:"font-semibold text-indigo-600 text-sm hover:text-indigo-500"},{default:i(()=>[d("Register ")]),_:1},8,["href"])])],40,G)]),_:1}))}},te=P(H,[["__scopeId","data-v-ae0ea7fb"]]);export{te as default};
