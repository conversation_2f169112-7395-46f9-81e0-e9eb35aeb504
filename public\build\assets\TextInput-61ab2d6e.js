import{r as i,m,o as p,c as d}from"./app-6cdaf2bc.js";const f=["value"],v={__name:"TextInput",props:{modelValue:{required:!0},numeric:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(o,{expose:a,emit:l}){const r=o,e=i(null),s=t=>t.replace(/\D/g,""),c=t=>{const u=t.target.value,n=r.numeric?s(u):u;n==""&&(e.value.value=""),l("update:modelValue",n)};return m(()=>{e.value.hasAttribute("autofocus")&&e.value.focus()}),a({focus:()=>e.value.focus()}),(t,u)=>(p(),d("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",value:o.modelValue,onInput:c,autocomplete:"off",ref_key:"input",ref:e},null,40,f))}};export{v as _};
