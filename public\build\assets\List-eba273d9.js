import{_ as F,b as Y,a as K}from"./AdminLayout-dc64724f.js";import{_ as q}from"./CreateButton-1a5625b4.js";import{_ as H}from"./SecondaryButton-9a822eb1.js";import{D as Z}from"./DangerButton-c7881a4e.js";import{M as G}from"./Modal-b05cc76d.js";import{_ as J}from"./Pagination-cae8b9a9.js";import{_ as Q}from"./SearchableDropdownNew-bd12e1c2.js";import{_ as R}from"./SimpleDropdown-0e0a895b.js";import{_ as w}from"./InputLabel-5e6ac969.js";import{r as p,l as W,o as s,c as i,a as l,u as f,w as r,F as g,Z as X,b as e,g as y,i as k,e as C,f as z,t as c,n as D}from"./app-6a429cee.js";import{_ as ee}from"./ArrowIcon-2f445522.js";import{s as te}from"./sortAndSearch-de435c62.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const oe={class:"animate-top"},se={class:"flex justify-between items-center"},ne=e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Paid Payments")],-1),ae={class:"flex justify-end"},le=e("div",{class:"flex space-x-6 mt-4 sm:mt-0 w-64"},[e("div",{class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},[e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})]),e("input",{id:"search-field",class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"})])],-1),ie={class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},de={class:"flex justify-end"},re={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},ce={class:"flex mb-2"},me=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),_e={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},pe={class:"sm:col-span-4"},he={class:"relative mt-2"},ue={class:"sm:col-span-4"},fe={class:"relative mt-2"},ge={class:"mt-8 overflow-x-auto sm:rounded-lg"},ye={class:"shadow sm:rounded-lg"},ve={class:"w-full text-sm text-left rtl:text-right text-gray-500"},xe={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},be={class:"border-b-2"},we=["onClick"],ke={key:0},Ce={class:"px-4 py-2.5 min-w-32"},ze={scope:"row",class:"px-4 py-2.5 font-medium text-gray-900 min-w-52"},Me={class:"px-4 py-2.5 min-w-36"},Ne={class:"px-4 py-2.5 min-w-52"},Ie={class:"px-4 py-2.5 min-w-40"},$e={key:0,class:"space-y-1"},Ae={class:"text-sm"},Pe={key:1},Se={class:"px-4 py-2.5 min-w-32"},Ve={class:"items-center px-4 py-2.5"},Be={class:"flex items-center justify-start gap-4"},Oe=e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Le=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),je=e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),Ee=["onClick"],Te=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),Ue=e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),Fe=[Te,Ue],Ye={key:1},Ke=e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),qe=[Ke],He={class:"p-6"},Ze=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this transaction? ",-1),Ge={class:"mt-6 flex justify-end"},_t={__name:"List",props:["data","organization","companies","organizationId","companyId"],setup(d){const h=d,{form:M,search:Je,sort:A,fetchData:Qe,sortKey:P,sortDirection:S,updateParams:V}=te("payment.index",{organization_id:h.organizationId,company_id:h.companyId}),v=p(!1),N=p(null),B=[{field:"date",label:"DATE",sortable:!0},{field:"company.name",label:"COMPANY NAME",sortable:!0},{field:"payment_type",label:"PAYMENT TYPE",sortable:!0},{field:"bank_info.bank_name",label:"BANK",sortable:!0},{field:"invoice_no",label:"INVOICE NO",sortable:!1},{field:"amount",label:"AMOUNT (₹)",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],O=o=>{N.value=o,v.value=!0},x=()=>{v.value=!1},L=()=>{M.delete(route("payment.destroy",{id:N.value}),{onSuccess:()=>x()})},j=o=>{const n=new Date(o),t={year:"numeric",month:"short",day:"numeric"};return n.toLocaleDateString("en-US",t)},E=o=>{let n=o.toFixed(2).toString(),[t,u]=n.split("."),a=t.substring(t.length-3),$=t.substring(0,t.length-3);return $!==""&&(a=","+a),`${$.replace(/\B(?=(\d{2})+(?!\d))/g,",")+a}.${u}`},m=p(h.organizationId),_=p(h.companyId),b=p("");W([m,_],()=>{V({organization_id:m.value,company_id:_.value})});const I=(o,n,t)=>{b.value=o,M.get(route("payment.index",{search:o,organization_id:n,company_id:t}),{preserveState:!0})},T=(o,n)=>{m.value=o,I(b.value,m.value,_.value)},U=(o,n)=>{_.value=o,I(b.value,m.value,_.value)};return(o,n)=>(s(),i(g,null,[l(f(X),{title:"Payment"}),l(F,null,{default:r(()=>[e("div",oe,[e("div",se,[ne,e("div",ae,[le,e("div",ie,[e("div",de,[l(q,{href:o.route("payment.create")},{default:r(()=>[y(" Make payment ")]),_:1},8,["href"])])])])]),e("div",re,[e("div",ce,[me,l(w,{for:"company_id",value:"Filters"})]),e("div",_e,[e("div",pe,[l(w,{for:"company_id",value:"Organization Name"}),e("div",he,[l(R,{options:d.organization,modelValue:m.value,"onUpdate:modelValue":n[0]||(n[0]=t=>m.value=t),onOnchange:T},null,8,["options","modelValue"])])]),e("div",ue,[l(w,{for:"company_id",value:"Company Name"}),e("div",fe,[l(Q,{options:d.companies,modelValue:_.value,"onUpdate:modelValue":n[1]||(n[1]=t=>_.value=t),onOnchange:U},null,8,["options","modelValue"])])])])]),e("div",ge,[e("div",ye,[e("table",ve,[e("thead",xe,[e("tr",be,[(s(),i(g,null,k(B,(t,u)=>e("th",{key:u,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:a=>f(A)(t.field,t.sortable)},[y(c(t.label)+" ",1),t.sortable?(s(),C(ee,{key:0,isSorted:f(P)===t.field,direction:f(S)},null,8,["isSorted","direction"])):z("",!0)],8,we)),64))])]),d.data.data&&d.data.data.length>0?(s(),i("tbody",ke,[(s(!0),i(g,null,k(d.data.data,(t,u)=>(s(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Ce,c(j(t.date)??"-"),1),e("td",ze,c(t.company.name??"-"),1),e("td",Me,c(t.payment_type=="check"?"Cheque":t.payment_type??"-"),1),e("td",Ne,c(t!=null&&t.bank_info?(t==null?void 0:t.bank_info.bank_name)+"-"+(t==null?void 0:t.bank_info.account_number):"-"),1),e("td",Ie,[t!=null&&t.invoice_data&&t.invoice_data.length>0?(s(),i("div",$e,[(s(!0),i(g,null,k(t.invoice_data,a=>(s(),i("div",{key:a.id,class:"flex items-center gap-2"},[e("span",{class:D([a.invoice_type?a.invoice_type==="purchase"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800":"bg-blue-100 text-blue-800","inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium"])},c(a.invoice_type?a.invoice_type==="sales"?"P":"S":"P"),3),e("span",Ae,c(a.invoice_no),1)]))),128))])):(s(),i("span",Pe,c(t.invoice_no||"-"),1))]),e("td",Se,c(E(t.amount)??"-"),1),e("td",Ve,[e("div",Be,[l(Y,{align:"right",width:"48"},{trigger:r(()=>[Oe]),content:r(()=>[t.invoice_data.length!=0?(s(),C(K,{key:0,href:o.route("payment.edit",t.id)},{svg:r(()=>[Le]),text:r(()=>[je]),_:2},1032,["href"])):z("",!0),e("button",{type:"button",onClick:a=>O(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Fe,8,Ee)]),_:2},1024)])])]))),128))])):(s(),i("tbody",Ye,qe))])])]),d.data.data&&d.data.data.length>0?(s(),C(J,{key:0,class:"mt-6",links:d.data.links},null,8,["links"])):z("",!0)]),l(G,{show:v.value,onClose:x},{default:r(()=>[e("div",He,[Ze,e("div",Ge,[l(H,{onClick:x},{default:r(()=>[y(" Cancel ")]),_:1}),l(Z,{class:"ml-3",onClick:L},{default:r(()=>[y(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{_t as default};
