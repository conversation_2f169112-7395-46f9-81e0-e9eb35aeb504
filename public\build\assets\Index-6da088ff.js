import{h as f,r as h,j as w,o as r,c as n,a as s,u as x,w as o,F as v,Z as g,b as e,k as p,v as k,f as a}from"./app-21e66fd5.js";import{_}from"./AdminLayout-db62264f.js";import{_ as l}from"./CustomButton-e4e15462.js";const b={class:"animate-top"},y={class:"sm:flex sm:items-center"},C=e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Settings")],-1),M={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},z={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},B=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),V={class:"border-gray-900 mt-10",style:{height:"500px"}},T={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},A={key:0,class:"sm:col-span-2"},H=e("svg",{class:"w-12 h-12 fill-current text-blue-600 inline-block",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M4 3h16a1 1 0 0 1 1 1v17h-6v-5h-6v5H3V4a1 1 0 0 1 1-1zm2 2v14h4v-5h4v5h4V5H6zm2 3h2v2H8V8zm6 0h2v2h-2V8zM8 12h2v2H8v-2zm6 0h2v2h-2v-2z"})],-1),P=e("span",{class:"font-semibold text-lg ml-4"},"Organization",-1),S={class:"sm:col-span-2"},E=e("svg",{class:"w-12 h-12 fill-current text-blue-600 inline-block",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M12 2l7 4v6c0 5-3 9-7 10-4-1-7-5-7-10V6l7-4z"}),e("circle",{cx:"12",cy:"10",r:"3",fill:"white"}),e("path",{d:"M9 16c1-2 5-2 6 0",stroke:"white","stroke-width":"2",fill:"none"})],-1),L=e("span",{class:"font-semibold text-lg ml-4"},"Roles & Permissions",-1),N={key:1,class:"sm:col-span-2"},j=e("svg",{class:"w-12 h-12 fill-current text-blue-600 inline-block",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M6 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6H6zm12 18H6V4h7v5h5v11z"}),e("path",{d:"M8 10h8v2H8v-2zm0 4h6v2H8v-2z"}),e("rect",{x:"14",y:"15",width:"5",height:"4",rx:"1",stroke:"currentColor","stroke-width":"2",fill:"none"}),e("text",{x:"15.5",y:"18.5","font-size":"3",fill:"currentColor","text-anchor":"middle"},"A")],-1),J=e("span",{class:"font-semibold text-lg ml-4"},"Manage Prefix",-1),O={key:2,class:"sm:col-span-2"},F=e("svg",{class:"w-12 h-12 fill-current text-blue-600 inline-block",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M3 10l9-7 9 7",stroke:"currentColor","stroke-width":"2",fill:"none"}),e("path",{d:"M4 10v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V10",stroke:"currentColor","stroke-width":"2",fill:"none"}),e("rect",{x:"10",y:"14",width:"4",height:"6",stroke:"currentColor","stroke-width":"2",fill:"none"})],-1),R=e("span",{class:"font-semibold text-lg ml-4"},"Banks",-1),$={key:3,class:"sm:col-span-2"},D=e("svg",{class:"w-12 h-12 fill-current text-blue-600 inline-block",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"2",fill:"none"}),e("circle",{cx:"12",cy:"10",r:"3",stroke:"currentColor","stroke-width":"2",fill:"none"}),e("path",{d:"M15 16c0-1.5-3-1.5-3-1.5s-3 0-3 1.5",stroke:"currentColor","stroke-width":"2",fill:"none"})],-1),I=e("span",{class:"font-semibold text-lg ml-4"},"Account Type",-1),Q={key:4,class:"sm:col-span-2"},U=e("svg",{class:"w-12 h-12 fill-current text-blue-600 inline-block",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("polyline",{points:"9 2 11 4 15 0",fill:"none",stroke:"currentColor","stroke-width":"2"}),e("rect",{x:"6",y:"7",width:"12",height:"14",rx:"2",ry:"2",stroke:"currentColor","stroke-width":"2",fill:"none"}),e("circle",{cx:"9",cy:"6.5",r:"1",fill:"currentColor"}),e("circle",{cx:"12",cy:"6.5",r:"1",fill:"currentColor"}),e("circle",{cx:"15",cy:"6.5",r:"1",fill:"currentColor"}),e("line",{x1:"8",y1:"11",x2:"16",y2:"11",stroke:"currentColor","stroke-width":"2"}),e("line",{x1:"8",y1:"15",x2:"16",y2:"15",stroke:"currentColor","stroke-width":"2"}),e("line",{x1:"8",y1:"19",x2:"16",y2:"19",stroke:"currentColor","stroke-width":"1"})],-1),Z=e("span",{class:"font-semibold text-lg ml-4"},"Jobcard Checklist",-1),q={key:5,class:"sm:col-span-2"},G=e("svg",{class:"w-12 h-12 fill-current text-blue-600 inline-block",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M2 4h20v16H2z",fill:"none",stroke:"currentColor","stroke-width":"2"}),e("path",{d:"M2 4l10 9L22 4",fill:"none",stroke:"currentColor","stroke-width":"2"})],-1),K=e("span",{class:"font-semibold text-lg ml-4"},"SMTP",-1),W={key:6,class:"sm:col-span-2"},X=e("svg",{class:"w-12 h-12 fill-current text-blue-600 inline-block",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M6 2h8l6 6v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z",stroke:"currentColor","stroke-width":"2",fill:"none"}),e("path",{d:"M14 2v6h6",stroke:"currentColor","stroke-width":"2",fill:"none"}),e("path",{d:"M4 14l8 6 8-6",stroke:"currentColor","stroke-width":"2",fill:"none"}),e("path",{d:"M4 14v6h16v-6",stroke:"currentColor","stroke-width":"2",fill:"none"})],-1),Y=e("span",{class:"font-semibold text-lg ml-4"},"Email Template",-1),ee={key:7,class:"sm:col-span-2"},te=e("svg",{class:"w-12 h-12 fill-current text-blue-600 inline-block",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M2 10V4a2 2 0 0 1 2-2h6l10 10a2 2 0 0 1 0 3l-6 6a2 2 0 0 1-3 0L2 12V10z",stroke:"currentColor","stroke-width":"2",fill:"none"}),e("circle",{cx:"7",cy:"7",r:"2",fill:"currentColor"}),e("path",{d:"M15 12l 5M18 9l4",stroke:"currentColor","stroke-width":"2",fill:"none"})],-1),se=e("span",{class:"font-semibold text-lg ml-4"},"Email Tags",-1),oe={class:"sm:col-span-2"},le=e("svg",{class:"w-12 h-12 fill-current text-blue-600 inline-block",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M4 4h16v16H4z",stroke:"currentColor","stroke-width":"2",fill:"none"}),e("text",{x:"12",y:"16","font-size":"10",fill:"currentColor","text-anchor":"middle","font-weight":"bold"},"123")],-1),re=e("span",{class:"font-semibold text-lg ml-4"},"Number Setting",-1),ce={__name:"Index",props:["permissions"],setup(i){f({});const c=h(""),u=h([{name:"Organization",route:"organization.index"},{name:"Roles & Permissions",route:"roles.index"},{name:"Manage Prefix",route:"manage-prefix"},{name:"Banks",route:"bankinfo.index"},{name:"Account Type",route:"account-type.index"},{name:"Jobcard CheckList",route:"jobcard-checklist.index"},{name:"SMTP",route:"mail-configs.index"},{name:"Email Template",route:"emailtemplates.index"},{name:"Email Tags",route:"email-tag.index"},{name:"Number Setting",route:"number-setting"}]);return w(()=>u.value.filter(t=>t.name.toLowerCase().includes(c.value.toLowerCase()))),(t,d)=>(r(),n(v,null,[s(x(g),{title:"Settings"}),s(_,null,{default:o(()=>[e("div",b,[e("div",y,[C,e("div",M,[e("div",z,[B,p(e("input",{id:"search-field","onUpdate:modelValue":d[0]||(d[0]=m=>c.value=m),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,512),[[k,c.value]])])])]),e("div",V,[e("div",T,[i.permissions.canOrganizationAdd?(r(),n("div",A,[s(l,{href:t.route("organization.index")},{default:o(()=>[H,P]),_:1},8,["href"])])):a("",!0),e("div",S,[s(l,{href:t.route("roles.index")},{default:o(()=>[E,L]),_:1},8,["href"])]),i.permissions.canPrefixAdd?(r(),n("div",N,[s(l,{href:t.route("manage-prefix")},{default:o(()=>[j,J]),_:1},8,["href"])])):a("",!0),i.permissions.canBanksAdd?(r(),n("div",O,[s(l,{href:t.route("bankinfo.index")},{default:o(()=>[F,R]),_:1},8,["href"])])):a("",!0),i.permissions.canAccountTypeAdd?(r(),n("div",$,[s(l,{href:t.route("account-type.index")},{default:o(()=>[D,I]),_:1},8,["href"])])):a("",!0),i.permissions.canJobCardChecklistAdd?(r(),n("div",Q,[s(l,{href:t.route("jobcard-checklist.index")},{default:o(()=>[U,Z]),_:1},8,["href"])])):a("",!0),i.permissions.canSMPTAdd?(r(),n("div",q,[s(l,{href:t.route("mail-configs.index")},{default:o(()=>[G,K]),_:1},8,["href"])])):a("",!0),i.permissions.canEmailTemplateAdd?(r(),n("div",W,[s(l,{href:t.route("emailtemplates.index")},{default:o(()=>[X,Y]),_:1},8,["href"])])):a("",!0),i.permissions.canEmailTagsAdd?(r(),n("div",ee,[s(l,{href:t.route("email-tag.index")},{default:o(()=>[te,se]),_:1},8,["href"])])):a("",!0),e("div",oe,[s(l,{href:t.route("number-setting")},{default:o(()=>[le,re]),_:1},8,["href"])])])])])]),_:1})],64))}};export{ce as default};
