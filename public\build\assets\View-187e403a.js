import{_ as g}from"./AdminLayout-0f1fdf67.js";import{_ as p}from"./CreateButton-fedd28a2.js";import{o,c as a,a as d,u as b,w as r,F as h,Z as w,b as t,t as e,g as v,f as c,i as S}from"./app-b7a94f67.js";const T={class:"animate-top"},G={class:"sm:flex sm:items-center"},k=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Credit Note Detail")],-1),C={class:"flex items-center space-x-4"},j={class:"text-sm font-semibold text-gray-900"},N={class:"flex justify-end w-20"},D={key:0,class:"mt-6 bg-white rounded-lg shadow-sm border"},I={class:"p-4 sm:p-6"},A={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},B=t("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Debit Note Information",-1),E={class:"space-y-3"},V={class:"inline-flex items-center justify-start w-full space-x-2"},L=t("p",{class:"text-sm text-gray-700 w-40"},"Invoice Number:",-1),O={class:"text-sm font-medium text-gray-900"},R={class:"inline-flex items-center justify-start w-full space-x-2"},P=t("p",{class:"text-sm text-gray-700 w-40"},"Debit Note Number:",-1),F={class:"text-sm font-medium text-gray-900"},U={class:"inline-flex items-center justify-start w-full space-x-2"},$=t("p",{class:"text-sm text-gray-700 w-40"},"Debit Note Date:",-1),q={class:"text-sm font-medium text-gray-900"},z={class:"inline-flex items-center justify-start w-full space-x-2"},H=t("p",{class:"text-sm text-gray-700 w-40"},"Credit Note Number:",-1),M={class:"text-sm font-medium text-gray-900"},Q={class:"inline-flex items-center justify-start w-full space-x-2"},X=t("p",{class:"text-sm text-gray-700 w-40"},"Reason:",-1),Y={class:"text-sm font-medium text-gray-900"},Z=t("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Company Information",-1),J={class:"space-y-3"},K={class:"inline-flex items-center justify-start w-full space-x-2"},W=t("p",{class:"text-sm text-gray-700 w-28"},"Company:",-1),tt={class:"text-sm font-medium"},st={class:"inline-flex items-center justify-start w-full space-x-2"},et=t("p",{class:"text-sm text-gray-700 w-28"},"GST No:",-1),ot={class:"text-sm font-medium"},at={class:"inline-flex items-center justify-start w-full space-x-2"},nt=t("p",{class:"text-sm text-gray-700 w-28"},"Email:",-1),ct={class:"text-sm font-medium"},it={class:"inline-flex items-center justify-start w-full space-x-2"},dt=t("p",{class:"text-sm text-gray-700 w-28"},"Contact No:",-1),lt={class:"text-sm font-medium"},mt={class:"mb-8"},_t=t("h2",{class:"text-lg font-semibold text-gray-900 mb-4"},"Product Details",-1),xt={class:"overflow-x-auto sm:rounded-lg"},yt={class:"shadow sm:rounded-lg"},rt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},ht={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},ut={class:"border-b-2"},ft=t("th",{class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"SR No",-1),gt=t("th",{class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"itme code",-1),pt=t("th",{class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"name",-1),bt=t("th",{class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"BATCH",-1),wt=t("th",{class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"EXP DATE",-1),vt=t("th",{class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"QTY",-1),St=t("th",{class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"RATE",-1),Tt=t("th",{class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"TOTAL PRICE",-1),Gt={key:0,class:"px-4 py-4 text-sm font-semi bold text-gray-900"},kt={key:1,class:"px-4 py-4 text-sm font-semi bold text-gray-900"},Ct={key:2,class:"px-4 py-4 text-sm font-semi bold text-gray-900"},jt={key:3,class:"px-4 py-4 text-sm font-semi bold text-gray-900"},Nt={key:4,class:"px-4 py-4 text-sm font-semi bold text-gray-900"},Dt=t("th",{class:"px-4 py-4 text-sm font-semi bold text-gray-900"},"TOTAL AMOUNT",-1),It={class:"px-4 py-2.5 min-w-20"},At={class:"px-4 py-2.5 font-medium text-gray-900 min-w-20"},Bt={class:"px-4 py-2.5 font-medium text-gray-900 min-w-40"},Et={class:"px-4 py-2.5 min-w-28"},Vt={class:"px-4 py-2.5 min-w-28"},Lt={class:"px-4 py-2.5"},Ot={class:"px-4 py-2.5 min-w-28"},Rt={class:"px-4 py-2.5 min-w-32"},Pt={key:0,class:"px-4 py-2.5 min-w-24"},Ft={key:1,class:"px-4 py-2.5 min-w-24"},Ut={key:2,class:"px-4 py-2.5 min-w-24"},$t={key:3,class:"px-4 py-2.5 min-w-24"},qt={key:4,class:"px-4 py-2.5 min-w-32"},zt={class:"px-4 py-2.5 min-w-36"},Ht={class:"border-t pt-6"},Mt={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Qt=t("div",{class:"space-y-3"},null,-1),Xt=t("div",{class:"space-y-3"},null,-1),Yt={class:"space-y-3"},Zt={class:"flex justify-between"},Jt=t("span",{class:"text-sm text-gray-700"},"Sub Total:",-1),Kt={class:"font-medium text-sm"},Wt={key:0,class:"flex justify-between"},ts=t("span",{class:"text-sm text-gray-700"},"CGST:",-1),ss={class:"font-medium text-sm"},es={key:1,class:"flex justify-between"},os=t("span",{class:"text-sm text-gray-700"},"SGST:",-1),as={class:"font-medium text-sm"},ns={key:2,class:"flex justify-between"},cs=t("span",{class:"text-sm text-gray-700"},"IGST:",-1),is={class:"font-medium text-sm"},ds={class:"flex justify-between"},ls=t("span",{class:"text-sm text-gray-700"},"Discount Before Tax:",-1),ms={class:"font-medium text-sm"},_s={class:"flex justify-between"},xs=t("span",{class:"text-sm text-gray-700"},"Overall Discount:",-1),ys={class:"font-medium text-sm"},rs={class:"flex justify-between"},hs=t("span",{class:"text-sm text-gray-700"},"Total GST:",-1),us={class:"font-medium text-sm"},fs={class:"flex justify-between border-t pt-3"},gs=t("span",{class:"text-lg font-semibold text-gray-900"},"Total Amount:",-1),ps={class:"text-lg font-bold text-gray-900"},bs={key:1,class:"bg-white rounded-lg shadow-sm border p-8 text-center"},ws=t("p",{class:"text-gray-500"},"No debit note data found.",-1),vs=[ws],ks={__name:"View",props:{data:Array},setup(s){const u=i=>{const l=new Date(i),n={year:"numeric",month:"short",day:"numeric"};return l.toLocaleDateString("en-US",n)};return(i,l)=>(o(),a(h,null,[d(b(w),{title:"Debit Note"}),d(g,null,{default:r(()=>[t("div",T,[t("div",G,[k,t("div",C,[t("div",null,[t("p",j,e(s.data[0].organization.name),1)]),t("div",N,[d(p,{href:i.route("debitnote.index")},{default:r(()=>[v(" Back ")]),_:1},8,["href"])])])]),s.data&&s.data.length>0?(o(),a("div",D,[t("div",I,[t("div",A,[t("div",null,[B,t("div",E,[t("div",V,[L,t("p",O,e(s.data[0].purchase_invoice.customer_invoice_no??"-"),1)]),t("div",R,[P,t("p",F,e(s.data[0].debit_note_no??"-"),1)]),t("div",U,[$,t("p",q,e(u(s.data[0].date)??"-"),1)]),t("div",z,[H,t("p",M,e(s.data[0].credit_note_number??"-"),1)]),t("div",Q,[X,t("p",Y,e(s.data[0].reason??"-"),1)])])]),t("div",null,[Z,t("div",J,[t("div",K,[W,t("p",tt,e(s.data[0].company.name??"-"),1)]),t("div",st,[et,t("p",ot,e(s.data[0].company.gst_no??"-"),1)]),t("div",at,[nt,t("p",ct,e(s.data[0].company.email??"-"),1)]),t("div",it,[dt,t("p",lt,e(s.data[0].company.contact_no??"-"),1)])])])]),t("div",mt,[_t,t("div",xt,[t("div",yt,[t("table",rt,[t("thead",ht,[t("tr",ut,[ft,gt,pt,bt,wt,vt,St,Tt,s.data[0].company.gst_type=="IGST"?(o(),a("th",Gt,"IGST (%)")):c("",!0),s.data[0].company.gst_type=="IGST"?(o(),a("th",kt,"IGST (₹)")):c("",!0),s.data[0].company.gst_type=="CGST/SGST"?(o(),a("th",Ct,"CGST (%)")):c("",!0),s.data[0].company.gst_type=="CGST/SGST"?(o(),a("th",jt,"SGST (%)")):c("",!0),s.data[0].company.gst_type=="CGST/SGST"?(o(),a("th",Nt,"Total GST (₹)")):c("",!0),Dt])]),t("tbody",null,[(o(!0),a(h,null,S(s.data[0].debit_note_details,(n,f)=>{var m,_,x,y;return o(),a("tr",{key:n.id,class:"odd:bg-white even:bg-gray-50 border-b"},[t("td",It,e(f+1),1),t("td",At,e(((m=n.product)==null?void 0:m.item_code)||"-"),1),t("td",Bt,e(((_=n.product)==null?void 0:_.name)||"-"),1),t("td",Et,e(((x=n.serial_numbers)==null?void 0:x.batch)||"-"),1),t("td",Vt,e(((y=n.serial_numbers)==null?void 0:y.expiry_date)||"-"),1),t("td",Lt,e(n.qty||0),1),t("td",Ot,e(n.price||0),1),t("td",Rt,e(n.total_price||0),1),s.data[0].company.gst_type=="IGST"?(o(),a("td",Pt,e(n.gst),1)):c("",!0),s.data[0].company.gst_type=="IGST"?(o(),a("td",Ft,e(n.gst_amount),1)):c("",!0),s.data[0].company.gst_type=="CGST/SGST"?(o(),a("td",Ut,e(n.gst/2),1)):c("",!0),s.data[0].company.gst_type=="CGST/SGST"?(o(),a("td",$t,e(n.gst/2),1)):c("",!0),s.data[0].company.gst_type=="CGST/SGST"?(o(),a("td",qt,e(n.total_gst_amount),1)):c("",!0),t("td",zt,e(n.total_amount||0),1)])}),128))])])])])]),t("div",Ht,[t("div",Mt,[Qt,Xt,t("div",Yt,[t("div",Zt,[Jt,t("span",Kt,"₹"+e(s.data[0].sub_total||0),1)]),s.data[0].company.gst_type==="CGST/SGST"?(o(),a("div",Wt,[ts,t("span",ss,"₹"+e(s.data[0].cgst||0),1)])):c("",!0),s.data[0].company.gst_type==="CGST/SGST"?(o(),a("div",es,[os,t("span",as,"₹"+e(s.data[0].sgst||0),1)])):c("",!0),s.data[0].company.gst_type==="IGST"?(o(),a("div",ns,[cs,t("span",is,"₹"+e(s.data[0].igst||0),1)])):c("",!0),t("div",ds,[ls,t("span",ms,"₹"+e(s.data[0].discount_before_tax||0),1)]),t("div",_s,[xs,t("span",ys,"₹"+e(s.data[0].overall_discount||0),1)]),t("div",rs,[hs,t("span",us,"₹"+e(s.data[0].total_gst||0),1)]),t("div",fs,[gs,t("span",ps,"₹"+e(s.data[0].total_amount||0),1)])])])])])])):(o(),a("div",bs,vs))])]),_:1})],64))}};export{ks as default};
