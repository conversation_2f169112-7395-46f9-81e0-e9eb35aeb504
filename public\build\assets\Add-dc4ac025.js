import{o as u,c as p,a,u as s,w as m,F as f,Z as V,b as l,d as y,g as x,T as b,f as C}from"./app-6cdaf2bc.js";import{_ as $,a as U}from"./AdminLayout-b73e8538.js";import{_ as i}from"./InputError-5c3c98c2.js";import{_ as n}from"./InputLabel-38b98ddd.js";import{P as k}from"./PrimaryButton-b7e37df1.js";import{_ as d}from"./TextInput-61ab2d6e.js";import{_ as N}from"./TextArea-8bab3e6c.js";import{_}from"./FileUpload-9e81567b.js";import{u as L}from"./index-839fed2d.js";import"./_plugin-vue_export-helper-c27b6911.js";const S={class:"animate-top bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},w=l("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Organization Information",-1),F=["onSubmit"],z={class:"border-b border-gray-900/10 pb-4"},A={class:"mt-5 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},B={class:"sm:col-span-4"},P={class:"sm:col-span-2"},T={class:"sm:col-span-1"},I={class:"sm:col-span-1"},O={class:"sm:col-span-2"},h={class:"sm:col-span-2"},j={class:"sm:col-span-3"},E={class:"sm:col-span-3"},D={class:"sm:col-span-2"},G={class:"sm:col-span-2"},M={class:"sm:col-span-2"},R={class:"sm:col-span-2"},Z={class:"sm:col-span-2"},q={class:"sm:col-span-6"},H={class:"flex mt-6 items-center justify-between"},J={class:"ml-auto flex items-center justify-end gap-x-6"},K=l("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1),Q={key:0,class:"text-sm text-gray-600"},de={__name:"Add",setup(W){const e=L("post","/organization",{name:"",address_line_1:"",address_line_2:"",pincode:"",city:"",state:"",gst_no:"",pan_no:"",drug_licence_no:"",email:"",contact_no:"",remarks:"",logo:"/uploads/companyprofile/defaultimg.png",signature:"/uploads/companyprofile/defaultimg.png"}),g=()=>e.submit({preserveScroll:!0,resetOnSuccess:!1}),c=r=>{e.logo=r},v=r=>{e.signature=r};return(r,o)=>(u(),p(f,null,[a(s(V),{title:"Organization"}),a($,null,{default:m(()=>[l("div",S,[w,l("form",{onSubmit:y(g,["prevent"]),class:""},[l("div",z,[l("div",A,[l("div",B,[a(n,{for:"name",value:"Name"}),a(d,{id:"name",type:"text",modelValue:s(e).name,"onUpdate:modelValue":o[0]||(o[0]=t=>s(e).name=t),autocomplete:"name",onChange:o[1]||(o[1]=t=>s(e).validate("name"))},null,8,["modelValue"]),a(i,{class:"",message:s(e).errors.name},null,8,["message"])]),l("div",P,[a(n,{for:"email",value:"Email"}),a(d,{id:"email",type:"text",modelValue:s(e).email,"onUpdate:modelValue":o[2]||(o[2]=t=>s(e).email=t),autocomplete:"email",onChange:o[3]||(o[3]=t=>s(e).validate("email"))},null,8,["modelValue"]),a(i,{class:"",message:s(e).errors.email},null,8,["message"])]),l("div",T,[a(n,{for:"gst_no",value:"GST Number"}),a(d,{id:"gst_no",type:"text",modelValue:s(e).gst_no,"onUpdate:modelValue":o[4]||(o[4]=t=>s(e).gst_no=t),maxLength:"15",autocomplete:"gst_no",onChange:o[5]||(o[5]=t=>s(e).validate("gst_no"))},null,8,["modelValue"]),a(i,{class:"",message:s(e).errors.gst_no},null,8,["message"])]),l("div",I,[a(n,{for:"pan_no",value:"PAN Number"}),a(d,{id:"pan_no",modelValue:s(e).pan_no,"onUpdate:modelValue":o[6]||(o[6]=t=>s(e).pan_no=t),maxLength:"10",autocomplete:"pan_no",onChange:o[7]||(o[7]=t=>s(e).validate("pan_no"))},null,8,["modelValue"]),a(i,{class:"",message:s(e).errors.pan_no},null,8,["message"])]),l("div",O,[a(n,{for:"drug_licence_no",value:"DrugLicence Number"}),a(d,{id:"drug_licence_no",type:"text",modelValue:s(e).drug_licence_no,"onUpdate:modelValue":o[8]||(o[8]=t=>s(e).drug_licence_no=t),autocomplete:"drug_licence_no",onChange:o[9]||(o[9]=t=>s(e).validate("drug_licence_no"))},null,8,["modelValue"]),a(i,{class:"",message:s(e).errors.drug_licence_no},null,8,["message"])]),l("div",h,[a(n,{for:"contact_no",value:"Contact Number"}),a(d,{id:"contact_no",type:"text",modelValue:s(e).contact_no,"onUpdate:modelValue":o[10]||(o[10]=t=>s(e).contact_no=t),numeric:!0,maxLength:"10",autocomplete:"contact_no",onChange:o[11]||(o[11]=t=>s(e).validate("contact_no"))},null,8,["modelValue"]),a(i,{class:"",message:s(e).errors.contact_no},null,8,["message"])]),l("div",j,[a(n,{for:"address_line_1",value:"Address Line 1"}),a(d,{id:"address_line_1",type:"text",modelValue:s(e).address_line_1,"onUpdate:modelValue":o[12]||(o[12]=t=>s(e).address_line_1=t),autocomplete:"address_line_1",onChange:o[13]||(o[13]=t=>s(e).validate("address_line_1"))},null,8,["modelValue"]),a(i,{class:"",message:s(e).errors.address_line_1},null,8,["message"])]),l("div",E,[a(n,{for:"address_line_2",value:"Address Line 2"}),a(d,{id:"address_line_2",type:"text",modelValue:s(e).address_line_2,"onUpdate:modelValue":o[14]||(o[14]=t=>s(e).address_line_2=t),autocomplete:"address_line_2"},null,8,["modelValue"]),a(i,{class:"",message:s(e).errors.address_line_2},null,8,["message"])]),l("div",D,[a(n,{for:"city",value:"City"}),a(d,{id:"city",type:"text",modelValue:s(e).city,"onUpdate:modelValue":o[15]||(o[15]=t=>s(e).city=t),autocomplete:"city",onChange:o[16]||(o[16]=t=>s(e).validate("city"))},null,8,["modelValue"]),a(i,{class:"",message:s(e).errors.city},null,8,["message"])]),l("div",G,[a(n,{for:"state",value:"State"}),a(d,{id:"state",type:"text",modelValue:s(e).state,"onUpdate:modelValue":o[17]||(o[17]=t=>s(e).state=t),autocomplete:"state",onChange:o[18]||(o[18]=t=>s(e).validate("state"))},null,8,["modelValue"]),a(i,{class:"",message:s(e).errors.state},null,8,["message"])]),l("div",M,[a(n,{for:"pincode",value:"Pincode"}),a(d,{id:"pincode",type:"text",numeric:!0,maxLength:"6",modelValue:s(e).pincode,"onUpdate:modelValue":o[19]||(o[19]=t=>s(e).pincode=t),autocomplete:"pincode",onChange:o[20]||(o[20]=t=>s(e).validate("pincode"))},null,8,["modelValue"]),a(i,{class:"",message:s(e).errors.pincode},null,8,["message"])]),l("div",R,[a(n,{for:"logo",value:"Company Logo"}),a(_,{label:"Company Logo",inputId:"logo",inputName:"logo",fileUrl:s(e).logo,onFile:c},null,8,["fileUrl"])]),l("div",Z,[a(n,{for:"signature",value:"Company signature"}),a(_,{label:"Company signature",inputId:"signature",inputName:"signature",fileUrl:s(e).signature,onFile:v},null,8,["fileUrl"])]),l("div",q,[a(n,{for:"remarks",value:"Remarks"}),a(N,{id:"remarks",type:"text",rows:3,modelValue:s(e).remarks,"onUpdate:modelValue":o[21]||(o[21]=t=>s(e).remarks=t),autocomplete:"remarks",onChange:o[22]||(o[22]=t=>s(e).validate("remarks"))},null,8,["modelValue"]),a(i,{class:"",message:s(e).errors.remarks},null,8,["message"])])])]),l("div",H,[l("div",J,[a(U,{href:r.route("organization.index")},{svg:m(()=>[K]),_:1},8,["href"]),a(k,{disabled:s(e).processing},{default:m(()=>[x("Save")]),_:1},8,["disabled"]),a(b,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:m(()=>[s(e).recentlySuccessful?(u(),p("p",Q,"Saved.")):C("",!0)]),_:1})])])],40,F)])]),_:1})],64))}};export{de as default};
