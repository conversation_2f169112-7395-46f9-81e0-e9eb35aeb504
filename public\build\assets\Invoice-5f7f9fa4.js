import{r as w,K as B,m as xt,j as D,l as ht,o as u,c as m,a as c,u as o,w as b,F as E,Z as yt,b as t,t as _,k as gt,v as ft,d as vt,f as p,i as Y,n as V,e as wt,g as G,s as bt,x as Vt}from"./app-97275a91.js";import{_ as kt,a as St}from"./AdminLayout-595ad5a7.js";import{_ as Ct}from"./InputError-b3250228.js";import{_ as f}from"./InputLabel-eb73087c.js";import{P as It}from"./PrimaryButton-46ac4375.js";import{_ as h}from"./TextInput-11c46564.js";import{_ as Tt}from"./TextArea-5e21e606.js";import{D as Pt}from"./DangerButton-36669f8b.js";import{_ as Dt}from"./SearchableDropdown-9d1b12d3.js";import{_ as tt}from"./SecondaryButton-d0c53c3f.js";import{M as et}from"./Modal-48c075e7.js";import{_ as Ft}from"./FileViewer-01b17a23.js";import{_ as $t}from"./MultipleFileUpload-368d3540.js";import{_ as st}from"./Checkbox-c09a6665.js";import{u as Gt}from"./index-05d29b1c.js";import{_ as Ut}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const d=I=>(bt("data-v-bc53da61"),I=I(),Vt(),I),Nt={class:"animate-top"},qt={class:"sm:flex sm:items-center"},jt=d(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Generate Invoice")],-1)),Mt={class:"w-auto"},At={class:"flex space-x-2 items-center"},Bt={class:"text-sm font-semibold text-gray-900"},Et={class:"flex space-x-2 items-center"},Ot=["onSubmit"],zt={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Lt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Rt={class:"sm:col-span-6"},Wt={class:"inline-flex items-center justify-start w-full space-x-2"},Ht=d(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-32"},"Customer Name:",-1)),Qt={class:"text-sm text-gray-700 leading-6"},Kt={class:"inline-flex items-center justify-start w-full space-x-2"},Zt=d(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Email:",-1)),Jt={class:"text-sm leading-6 text-gray-700"},Xt={class:"inline-flex items-center justify-start w-full space-x-2"},Yt=d(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Contact No:",-1)),te={class:"text-sm leading-6 text-gray-700"},ee={class:"sm:col-span-6"},se={class:"inline-flex items-center justify-start w-full space-x-2"},oe=d(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-32"},"Invoice No:",-1)),le={key:0,class:"text-sm text-gray-700 leading-6"},ae={key:1,class:"text-sm text-gray-700 leading-6"},ne={class:"inline-flex items-center justify-start w-full space-x-2"},ce=d(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-32"},"Challan Number:",-1)),ie={class:"text-sm text-gray-700 leading-6"},re={class:"inline-flex items-center justify-start w-full space-x-2"},de=d(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-32"},"Date :",-1)),_e={class:"text-sm text-gray-700 leading-6"},ue={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border overflow-x-auto w-full"},me={class:"overflow-x-auto divide-y divide-gray-300 w-full",style:{width:"110%"}},pe={scope:"col",class:""},xe=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Code",-1)),he={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ye=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Product Name",-1)),ge=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"HSN",-1)),fe=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Batch",-1)),ve=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),we=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),be=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Sell Price (₹)",-1)),Ve={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ke={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Se={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Ce={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Ie={key:5,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Te=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),Pe=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),De=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),Fe=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),$e={class:"divide-y divide-gray-300 bg-white"},Ge={class:"whitespace-nowrap py-3 text-sm text-gray-900"},Ue={class:"text-sm text-gray-900 leading-6 py-1.5"},Ne={key:0,class:"text-red-500 text-xs absolute"},qe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},je={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-20"},Me={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-48"},Ae={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Be={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ee={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},Oe={key:0,class:"text-red-500 text-xs absolute"},ze={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Le={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},Re={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},We={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},He={key:3,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Qe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ke={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ze={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Je={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-48"},Xe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},Ye={key:0,class:"mt-6 bg-white p-1 shadow sm:rounded-lg border"},ts={class:"min-w-full divide-y divide-gray-300"},es=d(()=>t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"UPLOADED DOCUMENT"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"REMOVE")])],-1)),ss={class:"divide-y divide-gray-300 bg-white"},os={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},ls={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},as=["onClick"],ns=d(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),cs=[ns],is=["onClick"],rs=d(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),ds=[rs],_s=["onClick"],us=d(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),ms=[us],ps={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},xs={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},hs={class:"sm:col-span-3 space-y-4"},ys={class:"flex space-x-4"},gs={class:"w-full"},fs={class:"w-full"},vs={class:"relative mt-2"},ws={class:"flex space-x-4"},bs={class:"w-full"},Vs={class:"w-full"},ks={class:"w-full"},Ss={class:"flex space-x-4"},Cs={class:"w-full"},Is={class:"w-full"},Ts={class:"w-full"},Ps={class:"flex space-x-4"},Ds={class:"w-full"},Fs={class:"w-full"},$s={class:"sm:col-span-3"},Gs={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Us={class:"inline-flex items-center justify-end w-full space-x-3"},Ns=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),qs={class:"text-base font-semibold text-gray-900 w-20"},js={class:"inline-flex items-center justify-end w-full space-x-3"},Ms=d(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Discount On Sub Total(₹):",-1)),As={class:"w-40"},Bs={class:"inline-flex items-center justify-end w-full space-x-3"},Es=d(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),Os={class:"w-40"},zs={class:"inline-flex items-center justify-end w-full space-x-3"},Ls=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),Rs={class:"text-base font-semibold text-gray-900 w-20"},Ws={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},Hs=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),Qs={class:"text-base font-semibold text-gray-900 w-20"},Ks={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},Zs=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),Js={class:"text-base font-semibold text-gray-900 w-20"},Xs={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},Ys=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),to={class:"text-base font-semibold text-gray-900 w-20"},eo={class:"inline-flex items-center justify-end w-full space-x-3"},so=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),oo={class:"text-base font-semibold text-gray-900 w-20"},lo={class:"flex mt-6 items-center justify-between"},ao={class:"ml-auto flex items-center justify-end gap-x-6"},no=d(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),co={class:"p-6"},io=d(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this document? ",-1)),ro={class:"mt-6 flex justify-end"},_o={class:"p-6"},uo={class:"mt-6 px-4 flex justify-end"},mo={__name:"Invoice",props:["invoice_no","salesuser","retail_invoice_no","filepath"],setup(I){const O=I,U=w(),N=w(),z=B().props.filepath.view,r=B().props.data[0];U.value=O.invoice_no[r.organization.id],N.value=O.retail_invoice_no[r.organization.id];const l=Gt("post","/saveinvoice",{category:r.category,note:"",date:new Date().toISOString().slice(0,10),selectedProductItem:[],customer_id:r.customer_id,organization_id:r.organization.id,total_amount:"",invoice_no:"",invoice_type:"",challan_number:r.challan_number,challan_id:r.id,sales_user_id:r.sales_user_id,document:"",cgst:"",sgst:"",igst:"",total_gst:"",sub_total:"",total_discount:"",discount_before_tax:"",dispatch:"",transport:"",patient_name:"",customer_po_date:"",customer_po_number:"",eway_bill:"",due_days:"",cr_dr_note:"",overall_discount:"",challans:B().props.data}),ot=()=>{l.sub_total=R.value,l.cgst=r.customers.gst_type=="CGST/SGST"?S.value/2:"0",l.sgst=r.customers.gst_type=="CGST/SGST"?S.value/2:"0",l.igst=r.customers.gst_type=="IGST"?S.value:"0",l.total_gst=S.value,l.total_amount=L.value,l.total_discount=W.value,l.invoice_no=r.customers.customer_type=="Tax"?U.value:N.value,l.invoice_type=r.customers.customer_type,l.selectedProductItem=y.value,l.submit({preserveScroll:!0,onSuccess:()=>l.reset()})},y=w([{product_id:"",item_code:"",product_name:"",hsn_code:"",qty:"",challan_qty:"",return_qty:"",invoiced_qty:"",price:"",sell_price:"",discount:"",discount_amount:"",discount_before_tax_product:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",challan_detail_id:"",check:!1,description:""}]);xt(()=>{y.value=r.challan_detail.map(s=>({product_id:s.viewserialnumbers.product.id,serial_number_id:s.viewserialnumbers.id,serial_no:s.viewserialnumbers.serial_no,batch:s.viewserialnumbers.unique_id,item_code:s.viewserialnumbers.product.item_code,product_name:s.viewserialnumbers.product.name,challan_detail_id:s.id,qty:s.qty-s.invoiced_qty,challan_qty:s.qty,return_qty:s.return_qty,invoiced_qty:s.invoiced_qty,discount:"0.00",description:"",hsn_code:s.viewserialnumbers.product.hsn_code,price:parseFloat(s.viewserialnumbers.purchase_price).toFixed(2),total_price:parseFloat(s.viewserialnumbers.purchase_price).toFixed(2),gst:parseFloat(s.viewserialnumbers.product.gst).toFixed(2),sgst:parseFloat(s.viewserialnumbers.product.gst/2).toFixed(2),gst_amount:"",total_gst_amount:"",total_amount:"",sell_price:"",check:!1}))});const lt=(s,a)=>{const e=parseFloat(s.sell_price),i=parseFloat(s.discount_before_tax_product)||0,n=parseFloat(s.discount)||0,x=r.customers.gst_type=="IGST"?s.gst:parseFloat(s.sgst*2),v=parseFloat(s.qty);let g=0,$=0;n>0||i>0?g=e*v:g=e*v*(1+x/100);const P=g*(n/100)||0,J=e*1*(x/100),A=(e*v-P-i)*(x/100);n>0||i>0?$=g-P-i+A:$=g-P;const X=e*v;return s.total_price=isNaN(X)?"":parseFloat(X).toFixed(2),s.gst_amount=isNaN(J)?"":parseFloat(J).toFixed(2),s.total_gst_amount=isNaN(A)?"":parseFloat(A).toFixed(2),s.discount_amount=isNaN(P)?"":parseFloat(P).toFixed(2),s.gst=x,isNaN($)?"":parseFloat($).toFixed(2)},k=(s,a)=>{Z(),s.total_amount=lt(s)},at=(s,a)=>{l.sales_user_id=s},L=D(()=>{const s=Math.round(y.value.reduce((e,i)=>e+(i.check&&i.total_amount?parseFloat(i.total_amount):0),0)),a=l.overall_discount?parseFloat(l.overall_discount):0;return s-a}),S=D(()=>y.value.reduce((s,a)=>s+(a.check&&a.total_gst_amount?parseFloat(a.total_gst_amount):0),0)),R=D(()=>y.value.reduce((s,a)=>s+(a.check&&a.sell_price?parseFloat(a.sell_price*a.qty):0),0)),W=D(()=>{const s=y.value.reduce((i,n)=>i+(n.check&&n.discount_amount?parseFloat(n.discount_amount):0),0),a=l.overall_discount?parseFloat(l.overall_discount):0,e=l.discount_before_tax?parseFloat(l.discount_before_tax):0;return s+a+e}),C=s=>{l.errors[s]=null},nt=s=>{const a=new Date(s),e={year:"numeric",month:"short",day:"numeric"};return a.toLocaleDateString("en-US",e)},ct=s=>{l.document=s},q=w(!1),H=w(null),it=s=>{H.value=s,q.value=!0},rt=()=>{l.get(route("removedocument",{id:H.value,name:"challanDocument"}),{onSuccess:()=>{j()}})},j=()=>{q.value=!1},M=w(!1),Q=w(null),dt=w("custom"),_t=s=>{Q.value=s,M.value=!0},K=()=>{M.value=!1},ut=s=>{const a=window.location.origin+z+s,e=document.createElement("a");e.href=a,e.setAttribute("download",s),document.body.appendChild(e),e.click(),document.body.removeChild(e)},T=s=>{const[a,e]=s.toFixed(2).toString().split(".");return a.replace(/\B(?=(\d{3})+(?!\d))/g,",")+(e?"."+e:"")},mt=D(()=>y.value.length>0&&y.value.every(s=>s.check)),pt=s=>{y.value.map(a=>{a.check=s})},F=(s,a)=>{const e=a.length,i=a.reduce((v,g)=>v+(g.total_price?parseFloat(g.total_price):0),0),n=y.value.reduce((v,g)=>v+(g.total_price?parseFloat(g.total_price):0),0),x=s*i/n/e;a.forEach(v=>{v.discount_before_tax_product=x})},Z=()=>{const s=parseFloat(l.discount_before_tax)||0,a=y.value.filter(x=>x.gst==5&&x.total_price>0),e=y.value.filter(x=>x.gst==12&&x.total_price>0),i=y.value.filter(x=>x.gst==18&&x.total_price>0),n=y.value.filter(x=>x.gst==28&&x.total_price>0);F(s,a),F(s,e),F(s,i),F(s,n)};return ht(()=>l.discount_before_tax,s=>{Z(),y.value.forEach(a=>{k(a)})}),(s,a)=>(u(),m(E,null,[c(o(yt),{title:"Challan"}),c(kt,null,{default:b(()=>[t("div",Nt,[t("div",qt,[jt,t("div",Mt,[t("div",At,[t("p",Bt,_(o(r).organization.name),1),t("div",Et,[gt(t("input",{class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":a[0]||(a[0]=e=>o(l).date=e),onChange:a[1]||(a[1]=e=>o(l).validate("date"))},null,544),[[ft,o(l).date]])])])])]),t("form",{onSubmit:vt(ot,["prevent"]),class:""},[t("div",zt,[t("div",Lt,[t("div",Rt,[t("div",Wt,[Ht,t("span",Qt,_(o(r).customers.customer_name??"-"),1)]),t("div",Kt,[Zt,t("p",Jt,_(o(r).customers.email??"-"),1)]),t("div",Xt,[Yt,t("p",te,_(o(r).customers.contact_no??"-"),1)])]),t("div",ee,[t("div",se,[oe,o(r).customers.customer_type=="Retail"?(u(),m("span",le,_(N.value),1)):p("",!0),o(r).customers.customer_type=="Tax"?(u(),m("span",ae,_(U.value),1)):p("",!0)]),t("div",ne,[ce,t("span",ie,_(o(r).challan_number),1)]),t("div",re,[de,t("span",_e,_(nt(o(r).date)),1)])])])]),t("div",ue,[t("table",me,[t("thead",null,[t("tr",null,[t("th",pe,[c(st,{checked:mt.value,"onUpdate:checked":pt},null,8,["checked"])]),xe,o(r).category=="Service"?(u(),m("th",he,"Part No")):p("",!0),ye,ge,fe,ve,we,be,o(r).customers.gst_type=="IGST"?(u(),m("th",Ve,"IGST(%)")):p("",!0),o(r).customers.gst_type=="IGST"?(u(),m("th",ke,"IGST (₹)")):p("",!0),o(r).customers.gst_type=="CGST/SGST"?(u(),m("th",Se,"CGST(%)")):p("",!0),o(r).customers.gst_type=="CGST/SGST"?(u(),m("th",Ce,"SGST(%)")):p("",!0),o(r).customers.gst_type=="CGST/SGST"?(u(),m("th",Ie,"Total GST (₹)")):p("",!0),Te,Pe,De,Fe])]),t("tbody",$e,[(u(!0),m(E,null,Y(y.value,(e,i)=>(u(),m("tr",{key:i},[t("td",Ge,[t("div",Ue,[c(st,{name:"check",checked:e.check,"onUpdate:checked":n=>e.check=n},null,8,["checked","onUpdate:checked"])]),o(l).errors[`selectedProductItem.${i}.check`]?(u(),m("p",Ne,_(o(l).errors[`selectedProductItem.${i}.check`]),1)):p("",!0)]),t("td",qe,_(e.item_code??"-"),1),o(r).category=="Service"?(u(),m("td",je,_(e.item_code??"-"),1)):p("",!0),t("td",Me,_(e.product_name??"-"),1),t("td",Ae,_(e.hsn_code??"-"),1),t("td",Be,_(e.batch??"-"),1),t("td",Ee,[c(h,{id:"qty",type:"text",modelValue:e.qty,"onUpdate:modelValue":n=>e.qty=n,onInput:n=>k(e,i),onChange:n=>C("selectedProductItem."+i+".qty"),class:V({error:o(l).errors[`selectedProductItem.${i}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"]),o(l).errors[`selectedProductItem.${i}.qty`]?(u(),m("p",Oe,_(o(l).errors[`selectedProductItem.${i}.qty`]),1)):p("",!0)]),t("td",ze,_(e.price??"-"),1),t("td",Le,[c(h,{id:"sell_price",type:"text",modelValue:e.sell_price,"onUpdate:modelValue":n=>e.sell_price=n,onInput:n=>k(e,i),onChange:n=>C("selectedProductItem."+i+".sell_price"),class:V({error:o(l).errors[`selectedProductItem.${i}.sell_price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),o(r).customers.gst_type=="IGST"?(u(),m("td",Re,[c(h,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":n=>e.gst=n,onInput:n=>k(e,i),onChange:n=>C("selectedProductItem."+i+".gst"),class:V({error:o(l).errors[`selectedProductItem.${i}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):p("",!0),o(r).customers.gst_type=="CGST/SGST"?(u(),m("td",We,[c(h,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":n=>e.sgst=n,onInput:n=>k(e,i),onChange:n=>C("selectedProductItem."+i+".gst"),class:V({error:o(l).errors[`selectedProductItem.${i}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):p("",!0),o(r).customers.gst_type=="CGST/SGST"?(u(),m("td",He,[c(h,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":n=>e.sgst=n,onInput:n=>k(e,i),onChange:n=>C("selectedProductItem."+i+".gst"),class:V({error:o(l).errors[`selectedProductItem.${i}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):p("",!0),t("td",Qe,_(e.total_gst_amount),1),t("td",Ke,[c(h,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":n=>e.discount=n,onInput:n=>k(e,i),onChange:n=>C("selectedProductItem."+i+".discount"),class:V({error:o(l).errors[`selectedProductItem.${i}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Ze,_(e.discount_amount??"-"),1),t("td",Je,_(e.total_amount??"-"),1),t("td",Xe,[c(h,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":n=>e.description=n,onChange:n=>C("selectedProductItem."+i+".description"),class:V({error:o(l).errors[`selectedProductItem.${i}.description`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])])]))),128))])])]),o(r).documents&&o(r).documents.length>0?(u(),m("div",Ye,[t("table",ts,[es,t("tbody",ss,[(u(!0),m(E,null,Y(o(r).documents,(e,i)=>(u(),m("tr",{key:o(r).id,class:""},[t("td",os,_(e.orignal_name),1),t("td",ls,[t("button",{type:"button",onClick:n=>it(e.id)},cs,8,as),t("button",{type:"button",onClick:n=>_t(e.name)},ds,8,is),t("button",{type:"button",onClick:n=>ut(e.name)},ms,8,_s)])]))),128))])])])):p("",!0),t("div",ps,[t("div",xs,[t("div",hs,[t("div",ys,[t("div",gs,[c(f,{for:"note",value:"Upload Documents"}),c($t,{inputId:"document",inputName:"document",onFiles:ct})]),t("div",fs,[c(f,{for:"company_name",value:"Sales Person"}),t("div",vs,[c(Dt,{options:I.salesuser,modelValue:o(l).sales_user_id,"onUpdate:modelValue":a[2]||(a[2]=e=>o(l).sales_user_id=e),onOnchange:at,class:V({"error rounded-md":o(l).errors.sales_user_id})},null,8,["options","modelValue","class"])])])]),t("div",ws,[t("div",bs,[c(f,{for:"company_name",value:"Transport"}),c(h,{id:"gst",type:"text",modelValue:o(l).dispatch,"onUpdate:modelValue":a[3]||(a[3]=e=>o(l).dispatch=e)},null,8,["modelValue"])]),t("div",Vs,[c(f,{for:"company_name",value:"Dispatch"}),c(h,{id:"transport",type:"text",modelValue:o(l).transport,"onUpdate:modelValue":a[4]||(a[4]=e=>o(l).transport=e)},null,8,["modelValue"])]),t("div",ks,[c(f,{for:"eway_bill",value:"Eway Bill"}),c(h,{id:"eway_bill",type:"text",modelValue:o(l).eway_bill,"onUpdate:modelValue":a[5]||(a[5]=e=>o(l).eway_bill=e)},null,8,["modelValue"])])]),t("div",Ss,[t("div",Cs,[c(f,{for:"company_name",value:"PO Number"}),c(h,{id:"gst",type:"text",modelValue:o(l).customer_po_number,"onUpdate:modelValue":a[6]||(a[6]=e=>o(l).customer_po_number=e)},null,8,["modelValue"])]),t("div",Is,[c(f,{for:"company_name",value:"PO Date"}),c(h,{id:"customer_po_date",type:"date",modelValue:o(l).customer_po_date,"onUpdate:modelValue":a[7]||(a[7]=e=>o(l).customer_po_date=e)},null,8,["modelValue"])]),t("div",Ts,[c(f,{for:"due_days",value:"Due Days"}),c(h,{id:"due_days",type:"text",modelValue:o(l).due_days,"onUpdate:modelValue":a[8]||(a[8]=e=>o(l).due_days=e)},null,8,["modelValue"])])]),t("div",Ps,[t("div",Ds,[c(f,{for:"patient_name",value:"Patient Name"}),c(h,{id:"patient_name",type:"text",modelValue:o(l).patient_name,"onUpdate:modelValue":a[9]||(a[9]=e=>o(l).patient_name=e)},null,8,["modelValue"])]),t("div",Fs,[c(f,{for:"cr_dr_note",value:"CR DR Note"}),c(h,{id:"cr_dr_note",type:"text",modelValue:o(l).cr_dr_note,"onUpdate:modelValue":a[10]||(a[10]=e=>o(l).cr_dr_note=e)},null,8,["modelValue"])])]),t("div",null,[c(f,{for:"note",value:"Note"}),c(Tt,{id:"note",type:"text",modelValue:o(l).note,"onUpdate:modelValue":a[11]||(a[11]=e=>o(l).note=e),onChange:a[12]||(a[12]=e=>o(l).validate("note"))},null,8,["modelValue"]),o(l).invalid("note")?(u(),wt(Ct,{key:0,class:"",message:o(l).errors.note},null,8,["message"])):p("",!0)])]),t("div",$s,[t("div",Gs,[t("div",Us,[Ns,t("p",qs,_(T(R.value)),1)]),t("div",js,[Ms,t("div",As,[c(h,{id:"discount_before_tax",type:"text",modelValue:o(l).discount_before_tax,"onUpdate:modelValue":a[13]||(a[13]=e=>o(l).discount_before_tax=e)},null,8,["modelValue"])])]),t("div",Bs,[Es,t("div",Os,[c(h,{id:"overall_discount",type:"text",modelValue:o(l).overall_discount,"onUpdate:modelValue":a[14]||(a[14]=e=>o(l).overall_discount=e)},null,8,["modelValue"])])]),t("div",zs,[Ls,t("p",Rs,_(T(W.value)),1)]),o(r).customers.gst_type=="IGST"?(u(),m("div",Ws,[Hs,t("p",Qs,_(T(S.value)),1)])):p("",!0),o(r).customers.gst_type=="CGST/SGST"?(u(),m("div",Ks,[Zs,t("p",Js,_(T(S.value/2)),1)])):p("",!0),o(r).customers.gst_type=="CGST/SGST"?(u(),m("div",Xs,[Ys,t("p",to,_(T(S.value/2)),1)])):p("",!0),t("div",eo,[so,t("p",oo,_(T(L.value)),1)])])])])]),t("div",lo,[t("div",ao,[c(St,{href:s.route("challan.index")},{svg:b(()=>[no]),_:1},8,["href"]),c(It,{disabled:o(l).processing},{default:b(()=>[G("Submit")]),_:1},8,["disabled"])])])],40,Ot)]),c(et,{show:q.value,onClose:j},{default:b(()=>[t("div",co,[io,t("div",ro,[c(tt,{onClick:j},{default:b(()=>[G(" Cancel ")]),_:1}),c(Pt,{class:"ml-3",onClick:rt},{default:b(()=>[G(" Delete ")]),_:1})])])]),_:1},8,["show"]),c(et,{show:M.value,onClose:K,maxWidth:dt.value},{default:b(()=>[t("div",_o,[c(Ft,{fileUrl:o(z)+Q.value},null,8,["fileUrl"]),t("div",uo,[c(tt,{onClick:K},{default:b(()=>[G(" Cancel ")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},Fo=Ut(mo,[["__scopeId","data-v-bc53da61"]]);export{Fo as default};
