<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Organization;
use App\Models\Customer;
use App\Models\CompanyCredit;
use App\Models\SerialNumbers;
use App\Models\CompanyCreditDetails;
use App\Models\PaymentPaid;
use App\Models\BankInfo;
use App\Models\CustomerTransaction;
use App\Models\User;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderDetail;
use App\Models\PurchaseTransaction;
use App\Models\PurchaseOrderReceives;
use App\Models\BankTransaction;
use App\Models\Company;
use App\Models\Product;
use App\Models\DebitNote;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Http\Requests\PoReceiveRequest;
use App\Http\Requests\paymentRequest;
use App\Http\Requests\DebitNoteRequest;
use App\Models\PurchaseOrderReceiveDetails;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use App\Traits\CommonTrait;
use App\Traits\QueryTrait;
use Config;

class PurchaseInvoiceController extends Controller
{
    use CommonTrait;

    use QueryTrait;

    public function __construct()
    {
        $this->middleware('permission:List PurchaseInvoice')->only(['index']);
        $this->middleware('permission:Edit PurchaseInvoice')->only(['edit', 'store']);
        $this->middleware('permission:View PurchaseInvoice')->only('view');
    }

    public function index(Request $request)
    {
        $search = $request->input('search');
        $organizationId = $request->input('organization_id');
        $companyId = $request->input('company_id');
        $typeId = $request->input('type');

        $query = PurchaseOrderReceives::with('purchaseOrder.company', 'purchaseOrder.organization', 'purchaseOrder.credit.paymentpaid.bankinfo', 'purchaseOrderReceiveDetails')
        ->when($companyId, function ($query) use ($companyId) {
            $query->whereHas('purchaseOrder', function ($query) use ($companyId) {
                $query->where('company_id', $companyId);
            });
        })
        ->when($typeId, function ($query) use ($typeId) {
                $query->where('type', $typeId);
        })
        ->when($organizationId, function ($query) use ($organizationId) {
            $query->whereHas('purchaseOrder', function ($query) use ($organizationId) {
                $query->where('organization_id', $organizationId);
            });
        });
        if (!empty($search)) {
            $query->where(function ($query) use ($search) {
                $query->whereHas('purchaseOrder', function ($subquery) use ($search) {
                    $subquery->where('po_number', 'like', "%$search%");
                })
                ->orWhere('customer_invoice_no', 'like', "%$search%")
                ->orWhere('customer_invoice_date', 'like', "%$search%")
                ->orWhere('status', 'like', "%$search%");
            });
        }

        $searchableFields = ['customer_invoice_no', 'purchaseOrder.po_number', 'purchaseOrder.company.name', 'customer_invoice_date', 'total_amount', 'paid_amount', 'status'];
        $this->searchAndSort($query, $request, $searchableFields);
        $data = $query->orderBy('customer_invoice_date', 'desc')->paginate(20);
        $filepath = Config::get('constants.uploadFilePath.companyDocument');
        $organization  = Organization::select('id', 'name')->get();
        $companies = Company::select('name', 'id')->orderByRaw('name')->get();
        $types = Config::get('constants.purchaseTypeList');
        $allOrganization = ['id' => null, 'name' => 'ALL ORGANIZATION'];
        $organization->prepend($allOrganization);
        $allCompany = ['id' => null, 'name' => 'ALL COMPANY'];
        $companies->prepend($allCompany);
        $paymentType = Config::get('constants.paymentTypes');
        $bankinfo  = BankInfo::select('id', DB::raw("CONCAT(bank_name, ' - ', account_number) AS name"), 'organization_id')->get();
        $data->withQueryString()->links();

        $permissions = [
            'canEditPurchaseInvoice'      => auth()->user()->can('Edit PurchaseInvoice'),
            'canViewPurchaseInvoice'      => auth()->user()->can(abilities: 'View PurchaseInvoice'),
        ];

        return Inertia::render('PurchaseInvoice/List', compact('data', 'permissions', 'types', 'filepath', 'organization', 'companies', 'paymentType', 'bankinfo', 'organizationId', 'companyId', 'typeId'));
    }

    public function edit(string $id)
    {
        $purchase_order_receives = PurchaseOrderReceives::with('purchaseOrderReceiveDetails.serialNumbers',  'purchaseOrderReceiveDetails.product', 'purchaseOrderReceiveDetails.purchaseOrderDetail')->where('id', $id)->get()->toArray();
        $data = PurchaseOrder::where('id', $purchase_order_receives[0]['purchase_order_id'])->with('purchaseOrderDetailForReceive.product', 'company', 'documents', 'organization')->get()->toArray();
        $salesuser = User::where(['status' => '1'])->select(DB::raw("CONCAT(first_name, ' ', last_name) AS name"),'id')->orderByRaw('first_name')->get();
        $po_receive_number = '1';
        return Inertia::render('PurchaseInvoice/Edit', compact('data', 'purchase_order_receives', 'salesuser'));
    }

    public function view(Request $request, $id)
    {
       $data = PurchaseOrderReceives::where('id', $id)->with('purchaseOrderReceiveDetails.serialNumbers',  'purchaseOrderReceiveDetails.product',  'purchaseOrderReceiveDetails.purchaseOrderDetail', 'purchaseOrder.company', 'purchaseOrder.organization', 'users')->get()->toArray();
       return Inertia::render('PurchaseInvoice/View', compact('data'));
    }

    public function store(PoReceiveRequest $request)
    {
        $data = $request->all();
        DB::beginTransaction();
        try {
            $userId = Auth::id();
            $total_price = array_sum(array_column($data['receivedProduct'], 'total_price'));
            $total_gst_amount = array_sum(array_column($data['receivedProduct'], 'total_gst_amount'));
            $total_amount = array_sum(array_column($data['receivedProduct'], 'total_amount'));

            $purchaseOrderReceives = PurchaseOrderReceives::find($data['purchase_order_receive_id']);

            if($data['type'] == 'invoice'){
                $purchaseTransaction = PurchaseTransaction::where(['entity_type' => 'purchase_invoice', 'entity_id' => $data['purchase_order_receive_id']])->first();
                $purchaseTransaction->update(['amount' => $data['total_amount'] + $purchaseTransaction->amount]);
            }

            $purchaseOrderReceives = PurchaseOrderReceives::where('id', $data['purchase_order_receive_id'])->update([
                'updated_by'        => $data['created_by'],
                'pending_amount'    => $purchaseOrderReceives->pending_amount + $total_amount,
                'total_amount'      => $purchaseOrderReceives->total_amount + $total_amount,
                'total_gst_amount'  => $purchaseOrderReceives->total_gst_amount + $total_gst_amount,
                'total_price'       => $purchaseOrderReceives->total_price + $total_price,
                'customer_invoice_no'  => $data['customer_invoice_no'],
                'customer_invoice_date'=> $data['customer_invoice_date']
            ]);

            foreach ($data['receivedProduct'] as $receivedProduct) {
                if ($receivedProduct['receive_qty'] > 0) {
                $receivedProduct['purchase_order_receive_id'] = $data['purchase_order_receive_id'];
                $receivedProduct['updated_by'] = $receivedProduct['created_by'] = $data['created_by'];
                $purchaseOrderReceiveDetail = PurchaseOrderReceiveDetails::create($receivedProduct);
                    if($receivedProduct['total_batch'] != 0){ //to do check if null
                        foreach ($receivedProduct['productDetails'] as $productDetails) {
                            $productDetails['organization_id']  = $receivedProduct['organization_id'];
                            $productDetails['product_id']       = $receivedProduct['product_id'];
                            $productDetails['mrp']              = $receivedProduct['mrp'];
                            $productDetails['purchase_price']   = $receivedProduct['purchase_price'];
                            $productDetails['purchase_order_receive_detail_id'] = $purchaseOrderReceiveDetail->id;
                            $productDetails['receive_qty']      = $productDetails['qty'];
                            $productDetails['sell_qty']         = 0;
                            $productDetails['created_by']       = $userId;
                            $productDetails['updated_by']       = $userId;
                            $productDetails['unique_id']        = $productDetails['batch'];
                            $createProduct =  SerialNumbers::create($productDetails);
                            Product::addProductLog($createProduct, $productDetails, $receivedProduct['product_id'], $receivedProduct['organization_id'], 'received');
                        }
                    } else {
                        $productDetails['organization_id']  = $receivedProduct['organization_id'];
                        $productDetails['product_id']       = $receivedProduct['product_id'];
                        $productDetails['mrp']              = $receivedProduct['mrp'];
                        $productDetails['purchase_price']   = $receivedProduct['purchase_price'];
                        $productDetails['purchase_order_receive_detail_id'] = $purchaseOrderReceiveDetail->id;
                        $productDetails['receive_qty']      = $receivedProduct['receive_qty'];
                        $productDetails['sell_qty']         = 0;
                        $productDetails['created_by']       = $userId;
                        $productDetails['updated_by']       = $userId;
                        $productDetails['unique_id']        = "(₹) " .$receivedProduct['purchase_price'];
                        $createProduct = SerialNumbers::create($productDetails);
                        Product::addProductLog($createProduct, $productDetails, $receivedProduct['product_id'], $receivedProduct['organization_id'], 'received');
                    }
                    $purchaseOrderDetail = PurchaseOrderDetail::find($receivedProduct['purchase_order_detail_id']);
                    $purchaseOrderDetail->update([
                        'receive_qty' => $purchaseOrderDetail->receive_qty + $receivedProduct['receive_qty']
                    ]);
                }
            }

            $purchaseOrderDetails = PurchaseOrderDetail::where('purchase_order_id', $data['purchase_order_id']);
            $totalQty = $purchaseOrderDetails->sum('qty');
            $totalReceiveQty = $purchaseOrderDetails->sum('receive_qty');
            $status = PurchaseOrder::STATUS_OPEN;
            if ($totalReceiveQty == $totalQty) {
                $status = PurchaseOrder::STATUS_COMPLETE;
            } elseif ($totalReceiveQty > 0) {
                $status = PurchaseOrder::STATUS_PARTIALLY;
            }
            PurchaseOrder::where('id', $data['purchase_order_id'])->update(['status' => $status]);
            DB::commit();
            return Redirect::to("/purchaseinvoice")->with('success','Product Received Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to("/purchaseinvoice")->with('error', $e->getMessage());
        }
    }

    public function makePayment(Request $request)
    {
        $data = $request->all();
        $requestData = $request->all();
        DB::beginTransaction();
        try {
            $data = $requestData['form'];
            if($requestData['is_credit'] == 'Yes'){
                foreach($requestData['credit_data'] as $credit){
                    if(isset($credit['amount_to_credit'])){
                        $InvoiceDetail = PurchaseOrderReceives::find($data['purchase_order_receive_id']);

                        $updateData['paid_amount'] = $InvoiceDetail->paid_amount + $credit['amount_to_credit'];
                        $updateData['pending_amount'] = $InvoiceDetail->pending_amount - $credit['amount_to_credit'];
                        if ($updateData['pending_amount'] <= 0) {
                            $updateData['status'] = 'Paid';
                        } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                            $updateData['status'] = 'Partially Paid';
                        } else {
                            $updateData['status'] = 'Unpaid';
                        }
                        $InvoiceDetail->update($updateData);
                        $credit['purchase_order_receive_id'] = $data['purchase_order_receive_id'];
                        $credit['company_credit_id'] = $credit['id'];
                        $credit['amount'] = $credit['amount_to_credit'];
                        $credit['date'] = date('Y-m-d');
                        $credit['created_by'] = $credit['updated_by'] = Auth::user()->id;
                        $creditDetails = CompanyCreditDetails::create($credit);
                        if($creditDetails){
                            $creditInfo = CompanyCredit::with('paymentpaid')->where('id', $credit['id'])->first();
                            $data['created_by'] = $data['updated_by'] = Auth::user()->id;
                            $data['entity_id'] = $creditDetails->id;
                            $data['entity_type'] = 'credit_details';
                            $data['organization_id'] = $data['organization_id'];
                            $data['company_id'] = $data['company_id'];
                            $data['payment_type'] = 'dr';
                            $data['amount'] = $credit['amount_to_credit'];
                            $data['date'] = date('Y-m-d');
                            if($creditInfo->paymentpaid->payment_type == 'check'){
                                $data['note'] = 'Cheque No:' .$creditInfo->paymentpaid->check_number .' '.'Invoice No:' .$data['invoice_no'];
                            } else if($creditInfo->paymentpaid->payment_type == 'NEFT'){
                                $data['note'] = 'NEFT'. ' ' .'Invoice No:' .$data['invoice_no'];
                            } else if($creditInfo->paymentpaid->payment_type == 'cash'){
                                $data['note'] = $data['note'].' '.'Invoice No:' .$data['invoice_no'];
                            }
                            $creditInfo->update(['unused_amount' => $creditInfo->unused_amount -  $credit['amount_to_credit']]);
                            $customerTransaction = PurchaseTransaction::create($data);
                            DB::commit();
                            return Redirect::to('/invoice')->with('success','Payment Received Successfully');
                        }
                    }
                }
            } else {
                $data['created_by'] = $data['updated_by'] = Auth::user()->id;
                $paidPayment = PaymentPaid::create($data);
                if($paidPayment){
                    $InvoiceDetail = PurchaseOrderReceives::find($data['purchase_order_receive_id']);
                    $updateData['paid_amount'] = $InvoiceDetail->paid_amount + $data['amount'];
                    $updateData['pending_amount'] = $InvoiceDetail->pending_amount - $data['amount'];
                    if ($updateData['pending_amount'] <= 0) {
                        $updateData['status'] = 'Paid';
                    } elseif ($updateData['paid_amount'] > 0 && $updateData['pending_amount'] > 0) {
                        $updateData['status'] = 'Partially Paid';
                    } else {
                        $updateData['status'] = 'Unpaid';
                    }
                    $InvoiceDetail->update($updateData);
                }

                $data['entity_id'] = $paidPayment->id;
                $data['entity_type'] = 'payment_paid';
                if($data['payment_type'] == 'check'){
                    $data['payment_type'] = 'dr';
                    $data['note'] = 'Cheque No:' .$data['check_number'] .' '.'Invoice No:' .$data['invoice_no'];
                    $bankTransaction = BankTransaction::create($data);
                } else if($data['payment_type'] == 'NEFT'){
                    $data['payment_type'] = 'dr';
                    $data['note'] = 'NEFT'. ' ' .'Invoice No:' .$data['invoice_no'];
                    $bankTransaction = BankTransaction::create($data);
                } else if($data['payment_type'] == 'cash'){
                    $data['note'] = $data['note'].' '.'Invoice No:' .$data['invoice_no'];
                }
                PurchaseTransaction::create($data);
                DB::commit();
                return Redirect::to('/purchaseinvoice')->with('success','Payment Paid Successfully');
            }

        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::to('/purchaseinvoice')->with('error', $e->getMessage());
        }
    }

    public function updatePendingAmount(Request $request){
        DB::beginTransaction();
        try {
            $paymentReceives = PurchaseOrderReceives::get();
            foreach($paymentReceives as $paymentReceive){
                $updateData['pending_amount'] = $paymentReceive->total_amount;
                $paymentReceive->update($updateData);

            }
            DB::commit();
            return Redirect::back()->with('success','Transaction Completed Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $deleteInvoice = PurchaseOrderReceives::where('id', $id);
            $deleteInvoice->delete();
            DB::commit();
            return Redirect::back()->with('success', 'Purchase invoice Removed Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function convertToInvoice($id)
    {
        $purchaseOrder = PurchaseOrderReceives::where('id', $id)->with('purchaseOrderReceiveDetails.serialNumbers',  'purchaseOrderReceiveDetails.product',  'purchaseOrderReceiveDetails.purchaseOrderDetail', 'purchaseOrder.company', 'purchaseOrder.organization', 'users')->first();

        $existingInvoices = PurchaseOrderReceives::with('purchaseOrder')
            ->whereHas('purchaseOrder', function($query) use ($purchaseOrder) {
                $query->where('company_id', $purchaseOrder->purchaseOrder->company_id)
                      ->where('organization_id', $purchaseOrder->purchaseOrder->organization_id)
                      ->where('type', 'invoice');
            })
            ->where('status', '!=', 'Paid')
            ->select('id', DB::raw("CONCAT(customer_invoice_no, ' - ',  DATE_FORMAT(customer_invoice_date, '%d %b %Y'), '  ',  'Total Amount : (₹) ', total_amount) AS name"), 'total_amount', 'customer_invoice_no', 'customer_invoice_date')
            ->orderBy('customer_invoice_date', 'desc')
            ->get();

        return Inertia::render('PurchaseInvoice/ConvertToInvoice', [
            'purchaseOrder' => $purchaseOrder,
            'existingInvoices' => $existingInvoices,
        ]);
    }

    public function saveConvertToInvoice(Request $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            $purchaseOrder = PurchaseOrder::findOrFail($data['purchase_order_id']);
            $purchaseOrder->update(['type' => 'invoice', 'note' => 'invoice generated from purchase challan']);

            if ($data['invoice_type'] === 'new') {
                $poReceive = PurchaseOrderReceives::find($data['id']);
                $data['type'] = 'invoice';
                $poReceive->update($data);

                foreach ($data['products'] as $product) {
                    $serialNumber =  SerialNumbers::where('purchase_order_receive_detail_id', $product['id']);
                    $serialNumber->update(['purchase_price' => $product['price']]);
                }

                $transactionData = [
                    'entity_id' => $poReceive->id,
                    'entity_type' => 'purchase_invoice',
                    'created_by' => auth()->id(),
                    'updated_by' => auth()->id(),
                    'date' => $data['customer_invoice_date'],
                    'amount' => $data['total_amount'],
                    'payment_type' => 'cr',
                    'company_id' => $purchaseOrder->company_id,
                    'organization_id' => $purchaseOrder->organization_id,
                    'note' => 'Bill Number: ' . $data['customer_invoice_no'] . ' ' . 'Date: ' . $data['customer_invoice_date']
                ];
                PurchaseTransaction::create($transactionData);
                $successMessage = 'Invoice Generated successfully from challan';

            } else {

                $existingInvoiceId = $data['existing_invoice_id'];
                $existingInvoice = PurchaseOrderReceives::findOrFail($existingInvoiceId);

                $existingPurchaseOrderId = $existingInvoice->purchase_order_id;
                $newTotalPrice = $existingInvoice->total_price + $data['total_price'];
                $newTotalGstAmount = $existingInvoice->total_gst_amount + $data['total_gst_amount'];
                $newTotalAmount = $existingInvoice->total_amount + $data['total_amount'];
                $newPendingAmount = $existingInvoice->pending_amount + $data['total_amount'];
                $existingInvoice->total_price = $newTotalPrice;
                $existingInvoice->total_gst_amount = $newTotalGstAmount;
                $existingInvoice->total_amount = $newTotalAmount;
                $existingInvoice->pending_amount = $newPendingAmount;
                $existingInvoice->updated_by = auth()->id();
                $existingInvoice->save();

                // Create PurchaseOrderReceiveDetails records for each product and add to existing invoice
                foreach ($data['products'] as $product) {

                    $serialNumber =  SerialNumbers::where('purchase_order_receive_detail_id', $product['id']);
                    $serialNumber->update(['purchase_price' => $product['price']]);

                    $poReceiveDetail = PurchaseOrderReceiveDetails::find($product['id']);
                    $poReceiveDetail->old_record = $poReceiveDetail->toJson();
                    $poReceiveDetail->purchase_order_receive_id = $existingInvoiceId;
                    $poReceiveDetail->updated_by = auth()->id();
                    $poReceiveDetail->save();

                    $purchaseOrderDetail = PurchaseOrderDetail::find($product['purchase_order_detail_id']);
                    $purchaseOrderDetail->old_record = $purchaseOrderDetail->toJson();
                    $purchaseOrderDetail->purchase_order_id = $existingPurchaseOrderId;
                    $purchaseOrderDetail->updated_by = auth()->id();
                    $purchaseOrderDetail->save();
                }

                $purchaseOrderReceives = PurchaseOrderReceives::where('id', $data['id']);
                $purchaseOrderReceives->delete();

                $purchaseOrder = PurchaseOrder::find($data['purchase_order_id']);

                if ($purchaseOrder && $purchaseOrder->purchaseOrderReceives()->count() === 0) {
                    $purchaseOrder->delete();
                }

                $existingTransaction = PurchaseTransaction::where('entity_id', $existingInvoice->id)
                    ->where('entity_type', 'purchase_invoice')
                    ->first();

                if ($existingTransaction) {
                    $existingTransaction->amount = $newTotalAmount;
                    $existingTransaction->updated_by = auth()->id();
                    $existingTransaction->save();
                }
                $successMessage = 'Challan merged with existing invoice successfully';
            }
            DB::commit();
            return Redirect::to('/purchaseinvoice')->with('success', $successMessage);
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

    public function debitNoteAdd(string $id)
    {
        $debit_no = $this->generateDebitNo();
        $data = PurchaseOrderReceives::where('id', $id)
            ->with([
                'purchaseOrderReceiveDetails.serialNumbers' => function($query) {
                    $query->whereRaw('(receive_qty - sell_qty) > 0');
                },
                'purchaseOrderReceiveDetails.purchaseOrderDetail',
                'purchaseOrder.company',
                'purchaseOrder.organization',
                'purchaseOrderReceiveDetails.product'
            ])
            ->get()->toArray();

        // Get all available serial numbers for this purchase invoice
        $serialNumbers = SerialNumbers::whereHas('purchaseOrderReceiveDetails', function($query) use ($id) {
                $query->where('purchase_order_receive_id', $id);
            })
            ->whereRaw('(receive_qty - sell_qty) > 0')
            ->with(['product', 'purchaseOrderReceiveDetails.purchaseOrderDetail'])
            ->get()
            ->toArray();

        return Inertia::render('PurchaseInvoice/DebitNoteAdd', compact('data', 'debit_no', 'serialNumbers'));
    }

    public function debitNoteSave(DebitNoteRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = $request->all();
            dd($data);
            $data['created_by'] = $data['updated_by'] = Auth::user()->id;
            $data['overall_discount'] = $data['overall_discount'] ?? 0;
            $data['discount_before_tax'] = $data['discount_before_tax'] ?? 0;
            $data['pending_amount'] = $data['total_amount'];

            $debitNumbers = $this->generateDebitNo();
            $data['debit_note_no'] = $debitNumbers[$data['organization_id']];

            while (DebitNote::where('debit_note_no', $data['debit_note_no'])->exists()) {
                $this->updateDebitNo($data['debit_note_no'], $data['organization_id']);
                $debitNumbers = $this->generateDebitNo();
                $data['debit_note_no'] = $debitNumbers[$data['organization_id']];
            }

            $this->updateDebitNo($data['debit_note_no'], $data['organization_id']);

            $debitNote = DebitNote::create($data);

            foreach ($data['selectedProductItem'] as $product) {
                // Update serial number to mark it as part of debit note
                $serialNumber = SerialNumbers::find($product['serial_number_id']);
                if ($serialNumber) {
                    // Reduce the available quantity by the debit quantity
                    $serialNumber->sell_qty += $product['qty'];
                    $serialNumber->save();
                }

                // Update purchase order receive details
                // $purchaseDetail = PurchaseOrderReceiveDetails::find($product['purchase_detail_id']);
                // $purchaseDetail->update(['debit_note_id' => $debitNote->id, 'is_receive' => 'yes']);
            }

            // Update purchase invoice pending amount
            // $purchaseInvoice = PurchaseOrderReceives::find($data['purchase_invoice_id']);
            // $purchaseInvoice->pending_amount -= $data['total_amount'];
            // $purchaseInvoice->save();

            // Create purchase transaction
            $transactionData = [
                'organization_id' => $data['organization_id'],
                'company_id' => $data['company_id'],
                'entity_type' => 'debit_note',
                'entity_id' => $debitNote->id,
                'amount' => $data['total_amount'],
                'type' => 'dr',
                'date' => $data['date'],
                'note' => 'Debit Note: ' . $data['credit_note_number'],
                'created_by' => Auth::user()->id,
                'updated_by' => Auth::user()->id,
            ];
            PurchaseTransaction::create($transactionData);

            DB::commit();
            return Redirect::to('/purchase-invoice')->with('success', 'Debit Note Created Successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return Redirect::back()->with('error', $e->getMessage());
        }
    }

}
