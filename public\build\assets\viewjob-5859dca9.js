import{r as p,K as C,o as a,c as i,a as d,u as n,w as h,F as x,Z as M,b as t,d as N,g as k,t as s,f as r,i as y,e as D}from"./app-4c3f0163.js";import{_ as O}from"./AdminLayout-36b0d46a.js";import{_ as P}from"./CreateButton-fed8c592.js";import{_ as U}from"./InputLabel-d6414ecf.js";import{C as B}from"./CheckboxWithLabel-7d6650cb.js";import{M as S}from"./Modal-61735c0a.js";import{_ as T}from"./FileViewer-e6911454.js";import{_ as $}from"./SecondaryButton-d521cdbf.js";/* empty css                                                                          */import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const q={class:"animate-top h-screen"},A={class:"sm:flex sm:items-center"},E=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Jobcard Detail")],-1),V={class:"flex items-center space-x-4"},W=t("div",null,null,-1),J={class:"flex justify-end w-20"},I={class:"mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6"},L={class:"inline-flex items-start space-x-6 justify-start w-full"},F={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},H={class:"inline-flex items-center justify-start w-full space-x-2"},K=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Hospital Name:",-1),Q={class:"text-sm leading-6 text-gray-700"},R={class:"inline-flex items-center justify-start w-full space-x-2"},Z=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Contact No:",-1),z={class:"text-sm leading-6 text-gray-700"},G={class:"inline-flex items-center justify-start w-full space-x-2"},X=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Address:",-1),Y={class:"text-sm leading-6 text-gray-700"},tt={class:"inline-flex flex-col space-y-1 items-start justify-start w-full"},et={class:"inline-flex items-center justify-start w-full space-x-2"},st=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Jobcard Number:",-1),ot={class:"text-sm leading-6 text-gray-700"},at={class:"inline-flex items-center justify-start w-full space-x-2"},it=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Date:",-1),lt={class:"text-sm leading-6 text-gray-700"},ct={class:"inline-flex items-center justify-start w-full space-x-2"},dt=t("p",{class:"text-sm font-semibold text-gray-900"},"Engineer Name:",-1),nt={class:"text-sm leading-6 text-gray-700"},rt={class:"inline-flex items-center justify-start w-full space-x-2"},_t=t("p",{class:"text-sm font-semibold text-gray-900 w-28"},"Job Status:",-1),mt={class:"text-sm leading-6 text-gray-700"},ht={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border overflow-x-auto"},xt={class:"overflow-x-auto divide-y divide-gray-300 w-full"},ut=t("thead",null,[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Equipment"),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Model"),t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Serial No")])],-1),ft={class:"divide-y divide-gray-300 bg-white"},pt={class:"whitespace-nowrap pr-4 py-3 text-sm text-gray-900"},yt={class:"whitespace-nowrap px-4 py-3 text-sm text-gray-900"},gt={class:"whitespace-nowrap px-4 py-3 text-sm text-gray-900"},wt={class:"mt-6 space-y-1"},vt={class:"inline-flex items-center justify-start w-full space-x-2"},bt=t("p",{class:"text-sm font-semibold text-gray-900"},"Problem Description:",-1),kt={class:"text-sm leading-6 text-gray-700"},jt={class:"inline-flex items-center justify-start w-full space-x-2"},Ct=t("p",{class:"text-sm font-semibold text-gray-900"},"Parts Required:",-1),Mt={class:"text-sm leading-6 text-gray-700"},Nt={class:"flex flex-col lg:flex-row lg:space-x-8 space-y-4 lg:space-y-0"},Dt={class:"flex-shrink-0 lg:w-1/3"},Ot=t("p",{class:"text-sm font-semibold text-gray-900 mb-2"},"Warranty Status:",-1),Pt={class:"space-y-2"},Ut={class:"flex items-center space-x-2"},Bt={class:"flex items-center space-x-1"},St={class:"w-4 h-4 rounded-full border-2 border-gray-300 flex items-center justify-center"},Tt={key:0,class:"w-2 h-2 rounded-full bg-indigo-600"},$t=t("span",{class:"text-sm text-gray-700"},"Warranty",-1),qt={class:"flex items-center space-x-2"},At={class:"flex items-center space-x-1"},Et={class:"w-4 h-4 rounded-full border-2 border-gray-300 flex items-center justify-center"},Vt={key:0,class:"w-2 h-2 rounded-full bg-indigo-600"},Wt=t("span",{class:"text-sm text-gray-700"},"Out of Warranty",-1),Jt={class:"flex items-center space-x-2"},It={class:"flex items-center space-x-1"},Lt={class:"w-4 h-4 rounded-full border-2 border-gray-300 flex items-center justify-center"},Ft={key:0,class:"w-2 h-2 rounded-full bg-indigo-600"},Ht=t("span",{class:"text-sm text-gray-700"},"AMC",-1),Kt={class:"flex items-center space-x-2"},Qt={class:"flex items-center space-x-1"},Rt={class:"w-4 h-4 rounded-full border-2 border-gray-300 flex items-center justify-center"},Zt={key:0,class:"w-2 h-2 rounded-full bg-indigo-600"},zt=t("span",{class:"text-sm text-gray-700"},"CMC",-1),Gt={class:"flex-1"},Xt={class:"grid sm:grid-cols-6 relative mt-2"},Yt={class:"inline-flex items-center justify-start w-full space-x-2"},te=t("p",{class:"text-sm font-semibold text-gray-900"},"Close Note:",-1),ee={class:"text-sm leading-6 text-gray-700"},se={class:"inline-flex items-center justify-start w-full space-x-2"},oe=t("p",{class:"text-sm font-semibold text-gray-900"},"Close Date:",-1),ae={class:"text-sm leading-6 text-gray-700"},ie={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-4"},le={key:0,class:"bg-white p-1 shadow sm:rounded-lg border"},ce={class:"min-w-full divide-y divide-gray-300"},de=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"QUOTATION DOCUMENT"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1),ne={class:"divide-y divide-gray-300 bg-white"},re={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},_e={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},me=["onClick"],he=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),xe=[he],ue={key:1,class:"bg-white p-1 shadow sm:rounded-lg border"},fe={class:"min-w-full divide-y divide-gray-300"},pe=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"PO DOCUMENT"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1),ye={class:"divide-y divide-gray-300 bg-white"},ge={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},we={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},ve=["onClick"],be=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),ke=[be],je={class:"p-6"},Ce={class:"mt-6 px-4 flex justify-end"},Ae={__name:"viewjob",props:["data","checklist","filePath"],setup(e){const g=p([]),c=C().props.data[0];g.value=c.job_card_checks.map(l=>l.job_card_checklist_id);const w=l=>{const _=new Date(l),m={year:"numeric",month:"short",day:"numeric"};return _.toLocaleDateString("en-US",m)},u=p(!1),f=p(""),v=l=>{f.value=l,u.value=!0},b=()=>{u.value=!1,f.value=""};return(l,_)=>(a(),i(x,null,[d(n(M),{title:"Jobcard"}),d(O,null,{default:h(()=>{var m;return[t("div",q,[t("form",{onSubmit:_[0]||(_[0]=N((...o)=>l.submit&&l.submit(...o),["prevent"])),class:""},[t("div",A,[E,t("div",V,[W,t("div",J,[d(P,{href:l.route("jobcard.index")},{default:h(()=>[k(" Back ")]),_:1},8,["href"])])])]),t("div",I,[t("div",L,[t("div",F,[t("div",H,[K,t("p",Q,s(e.data[0].hospital_name??"-"),1)]),t("div",R,[Z,t("p",z,s(e.data[0].contact_no??"-"),1)]),t("div",G,[X,t("p",Y,s(e.data[0].address??"-"),1)])]),t("div",tt,[t("div",et,[st,t("p",ot,s(e.data[0].job_card_number??"-"),1)]),t("div",at,[it,t("p",lt,s(w(e.data[0].date)??"-"),1)]),t("div",ct,[dt,t("p",nt,s(e.data[0].users.first_name??"-")+" "+s(e.data[0].users.last_name??"-"),1)]),t("div",rt,[_t,t("p",mt,s(e.data[0].job_status??"-"),1)])])])]),t("div",ht,[t("table",xt,[ut,t("tbody",ft,[t("tr",null,[t("td",pt,s(e.data[0].product_name??"-"),1),t("td",yt,s(e.data[0].product_code??"-"),1),t("td",gt,s(e.data[0].serial_no??"-"),1)])])]),t("div",wt,[t("div",vt,[bt,t("p",kt,s(e.data[0].problem_description??"-"),1)]),t("div",jt,[Ct,t("p",Mt,s(e.data[0].parts_required??"-"),1)]),t("div",Nt,[t("div",Dt,[Ot,t("div",Pt,[t("div",Ut,[t("div",Bt,[t("div",St,[e.data[0].warranty_status==="warranty"?(a(),i("div",Tt)):r("",!0)]),$t])]),t("div",qt,[t("div",At,[t("div",Et,[e.data[0].warranty_status==="out_of_warranty"?(a(),i("div",Vt)):r("",!0)]),Wt])]),t("div",Jt,[t("div",It,[t("div",Lt,[e.data[0].warranty_status==="amc"?(a(),i("div",Ft)):r("",!0)]),Ht])]),t("div",Kt,[t("div",Qt,[t("div",Rt,[e.data[0].warranty_status==="cmc"?(a(),i("div",Zt)):r("",!0)]),zt])])])]),t("div",Gt,[d(U,{for:"engineer_id",value:"Checklist :"}),t("div",Xt,[(a(!0),i(x,null,y(e.checklist,o=>(a(),D(B,{key:o.id,checked:g.value,value:o.id,label:o.type,"onUpdate:checked":l.updateChecked},null,8,["checked","value","label","onUpdate:checked"]))),128))])])]),t("div",Yt,[te,t("p",ee,s(e.data[0].close_note??"-"),1)]),t("div",se,[oe,t("p",ae,s((m=e.data[0])!=null&&m.close_date?w(e.data[0].close_date):"-"),1)])]),t("div",ie,[n(c).quotation_documents&&n(c).quotation_documents.length>0?(a(),i("div",le,[t("table",ce,[de,t("tbody",ne,[(a(!0),i(x,null,y(n(c).quotation_documents,o=>(a(),i("tr",{key:o.id},[t("td",re,s(o.orignal_name),1),t("td",_e,[t("button",{type:"button",onClick:j=>v(o.name)},xe,8,me)])]))),128))])])])):r("",!0),n(c).po_documents&&n(c).po_documents.length>0?(a(),i("div",ue,[t("table",fe,[pe,t("tbody",ye,[(a(!0),i(x,null,y(n(c).po_documents,o=>(a(),i("tr",{key:o.id},[t("td",ge,s(o.orignal_name),1),t("td",we,[t("button",{type:"button",onClick:j=>v(o.name)},ke,8,ve)])]))),128))])])])):r("",!0)])])],32)]),d(S,{show:u.value,onClose:b,maxWidth:"xl"},{default:h(()=>{var o;return[t("div",je,[d(T,{fileUrl:((o=e.filePath)==null?void 0:o.view)+f.value},null,8,["fileUrl"]),t("div",Ce,[d($,{onClick:b},{default:h(()=>[k("Close")]),_:1})])])]}),_:1},8,["show"])]}),_:1})],64))}};export{Ae as default};
