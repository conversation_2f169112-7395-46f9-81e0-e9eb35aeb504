import{_ as D,b as F,a as R}from"./AdminLayout-db62264f.js";import{_ as H}from"./CreateButton-ac594764.js";import{_ as K}from"./SecondaryButton-0406f438.js";import{D as Z}from"./DangerButton-b1b34a7e.js";import{M as q}from"./Modal-7ff7f630.js";import{_ as G}from"./Pagination-26400838.js";import{_ as J}from"./SimpleDropdown-8f3922e2.js";import{_ as Q}from"./SearchableDropdownNew-a0aeed9d.js";import{r as _,l as W,o as d,c as u,a,u as g,w as n,F as y,Z as X,b as t,g as p,i as N,e as z,f as B,t as c}from"./app-21e66fd5.js";import"./html2canvas.esm-28fad9d1.js";import{_ as k}from"./InputLabel-4a50badc.js";import{_ as Y}from"./ArrowIcon-20ccf905.js";import{s as tt}from"./sortAndSearch-68be7e73.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const et={class:"animate-top"},st={class:"flex justify-between items-center"},ot=t("div",{class:"items-start"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Credit Notes")],-1),at={class:"flex justify-end"},lt={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},nt={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},it=t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),rt={class:"flex mt-4 sm:mt-0 sm:flex-none"},dt={class:"flex ml-6"},ct={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},ut={class:"flex justify-between mb-2"},mt={class:"flex"},_t=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),ht={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},ft={class:"sm:col-span-4"},gt={class:"relative mt-2"},pt={class:"sm:col-span-4"},vt={class:"relative mt-2"},xt={class:"mt-8 overflow-x-auto sm:rounded-lg"},bt={class:"shadow sm:rounded-lg"},wt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},yt={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},kt={class:"border-b-2"},Ct=["onClick"],Mt={key:0},It={class:"px-4 py-2.5 min-w-40"},Nt={class:"px-4 py-2.5 min-w-44"},zt={class:"px-4 py-2.5 min-w-36"},Bt={scope:"row",class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},Vt={class:"px-4 py-2.5 min-w-32"},$t={class:"px-4 py-2.5 min-w-32"},St={class:"items-center px-4 py-2.5"},Ot={class:"flex items-center justify-start gap-4"},At=t("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Et=["onClick"],Tt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),Ut=t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),jt=[Tt,Ut],Lt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),Pt=t("span",{class:"text-sm text-gray-700 leading-5"}," View CreditNote ",-1),Dt={key:1},Ft=t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Rt=[Ft],Ht={class:"p-6"},Kt=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this credit note? ",-1),Zt={class:"mt-6 flex justify-end"},me={__name:"List",props:["data","permissions","organization","customers","organizationId","customerId"],setup(l){const h=l,{form:C,search:qt,sort:V,fetchData:Gt,sortKey:$,sortDirection:S,updateParams:O}=tt("creditnote.index",{organization_id:h.organizationId,customer_id:h.customerId}),v=_(!1),M=_(null),A=[{field:"credit_note_no",label:"CREDIT NUMBER",sortable:!0},{field:"debit_note_number",label:"DEBIT NOTE NUMBER",sortable:!0},{field:"invoice.invoice_no",label:"INVOICE NO",sortable:!0},{field:"customers.customer_name",label:"CUSTOMER NAME",sortable:!0},{field:"date",label:"DATE",sortable:!0},{field:"total_amount",label:"AMOUNT (₹)",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],E=s=>{M.value=s,v.value=!0},x=()=>{v.value=!1},T=()=>{C.delete(route("creditnote.destroy",{id:M.value}),{onSuccess:()=>x()})},i=_(h.organizationId),r=_(h.customerId),b=_("");W([i,r],()=>{O({organization_id:i.value,customer_id:r.value})});const w=(s,o,e)=>{b.value=s,C.get(route("creditnote.index",{search:s,organization_id:o,customer_id:e}),{preserveState:!0})},U=(s,o)=>{i.value=s,w(b.value,i.value,r.value)},j=(s,o)=>{r.value=s,w(b.value,i.value,r.value)},L=s=>{const o=new Date(s),e={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",e)},P=s=>{let o=s.toFixed(2).toString(),[e,f]=o.split("."),m=e.substring(e.length-3),I=e.substring(0,e.length-3);return I!==""&&(m=","+m),`${I.replace(/\B(?=(\d{2})+(?!\d))/g,",")+m}.${f}`};return(s,o)=>(d(),u(y,null,[a(g(X),{title:"Credit Note"}),a(D,null,{default:n(()=>[t("div",et,[t("div",st,[ot,t("div",at,[t("div",lt,[t("div",nt,[it,t("input",{id:"search-field",onInput:o[0]||(o[0]=e=>w(e.target.value,i.value,r.value,s.salesUserId,s.categoryId,s.createdBy,s.statusId)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),t("div",rt,[t("div",dt,[a(H,{href:s.route("reports")},{default:n(()=>[p(" Back ")]),_:1},8,["href"])])])])]),t("div",ct,[t("div",ut,[t("div",mt,[_t,a(k,{for:"customer_id",value:"Filters"})])]),t("div",ht,[t("div",ft,[a(k,{for:"customer_id",value:"Organization Name"}),t("div",gt,[a(J,{options:l.organization,modelValue:i.value,"onUpdate:modelValue":o[1]||(o[1]=e=>i.value=e),onOnchange:U},null,8,["options","modelValue"])])]),t("div",pt,[a(k,{for:"customer_id",value:"Customer Name"}),t("div",vt,[a(Q,{options:l.customers,modelValue:r.value,"onUpdate:modelValue":o[2]||(o[2]=e=>r.value=e),onOnchange:j},null,8,["options","modelValue"])])])])]),t("div",xt,[t("div",bt,[t("table",wt,[t("thead",yt,[t("tr",kt,[(d(),u(y,null,N(A,(e,f)=>t("th",{key:f,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:m=>g(V)(e.field,e.sortable)},[p(c(e.label)+" ",1),e.sortable?(d(),z(Y,{key:0,isSorted:g($)===e.field,direction:g(S)},null,8,["isSorted","direction"])):B("",!0)],8,Ct)),64))])]),l.data.data&&l.data.data.length>0?(d(),u("tbody",Mt,[(d(!0),u(y,null,N(l.data.data,(e,f)=>(d(),u("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",It,c(e.credit_note_no),1),t("td",Nt,c(e.debit_note_number),1),t("td",zt,c(e.invoice.invoice_no??"-"),1),t("th",Bt,c(e.customers.customer_name??"-")+" - "+c(e.customers.city??"-"),1),t("td",Vt,c(L(e.date)),1),t("td",$t,c(P(e.total_amount)),1),t("td",St,[t("div",Ot,[a(F,{align:"right",width:"48"},{trigger:n(()=>[At]),content:n(()=>[t("button",{type:"button",onClick:m=>E(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},jt,8,Et),a(R,{href:s.route("creditnote.show",{id:e.id})},{svg:n(()=>[Lt]),text:n(()=>[Pt]),_:2},1032,["href"])]),_:2},1024)])])]))),128))])):(d(),u("tbody",Dt,Rt))])])]),l.data.data&&l.data.data.length>0?(d(),z(G,{key:0,class:"mt-6",links:l.data.links},null,8,["links"])):B("",!0)]),a(q,{show:v.value,onClose:x},{default:n(()=>[t("div",Ht,[Kt,t("div",Zt,[a(K,{onClick:x},{default:n(()=>[p(" Cancel ")]),_:1}),a(Z,{class:"ml-3",onClick:T},{default:n(()=>[p(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{me as default};
