import{r as P,j as k,l as _t,o as m,c as p,a as r,u as i,w as A,F as X,Z as mt,b as t,t as _,f as g,k as pt,v as gt,d as vt,n as f,i as xt,g as Y,e as yt,s as ft,x as ht,q as bt}from"./app-16701445.js";import{_ as wt,a as Vt}from"./AdminLayout-e15be38d.js";import{_ as Z}from"./InputError-11376965.js";import{_ as y}from"./InputLabel-d69efee6.js";import{P as J}from"./PrimaryButton-eddb8b77.js";import{_ as x}from"./TextInput-764e3400.js";import{_ as St}from"./TextArea-b68da786.js";import{_ as G}from"./SearchableDropdown-c456ce8e.js";import{_ as It}from"./MultipleFileUpload-09565aaf.js";import{u as Tt}from"./index-10107770.js";import{_ as Pt}from"./_plugin-vue_export-helper-c27b6911.js";const c=S=>(ft("data-v-a3f02d1b"),S=S(),ht(),S),Ct={class:"animate-top"},Ft={class:"sm:flex sm:items-center"},Gt=c(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create Invoice")],-1)),$t={class:"w-auto"},Ut={class:"flex space-x-2 items-center"},kt=c(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Invoice Number:",-1)),Nt={key:0,class:"text-sm font-semibold text-gray-900 leading-6"},Dt={key:1,class:"text-sm font-semibold text-gray-900 leading-6"},At={class:"flex space-x-2 items-center"},Ot=c(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-28"},"Date :",-1)),zt=["onSubmit"],qt={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},jt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Bt={class:"sm:col-span-4"},Et={class:"relative mt-2"},Rt={class:"sm:col-span-4"},Lt={class:"relative mt-2"},Mt={class:"sm:col-span-4"},Ht={class:"relative mt-2"},Qt={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border w-full"},Xt={class:"overflow-x-auto w-full"},Yt={class:"overflow-x-auto divide-y divide-gray-300",style:{"margin-bottom":"160px"}},Zt=c(()=>t("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Product Name",-1)),Jt=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Batch",-1)),Kt=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"HSN",-1)),Wt=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"EXP",-1)),te=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),ee=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"MRP (₹)",-1)),se=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),oe=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),ae=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Sell Price (₹)",-1)),le=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Price (₹)",-1)),ne={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ie={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},re={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},de={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ce={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ue=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),_e=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),me=c(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),pe={class:"divide-y divide-gray-300 bg-white"},ge={class:"whitespace-nowrap pr-3 py-3 text-sm text-gray-900 min-w-96"},ve={class:"relative mt-2"},xe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-60"},ye={class:"relative mt-2"},fe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},he={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},be={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},we={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ve={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Se={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ie={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Te={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-32"},Pe={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ce={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Fe={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ge={key:3,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},$e={key:4,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ue={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},ke={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ne={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 flex space-x-2 min-w-48"},De={class:"px-3 py-3 text-sm text-gray-900"},Ae=["onClick"],Oe=c(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),ze=[Oe],qe={class:"flex items-center justify-between"},je={key:0,class:""},Be=c(()=>t("span",{class:"text-lg font-semibold text-gray-900 leading-6"},"PREVIOUS INVOICE",-1)),Ee=[Be],Re={class:"ml-auto flex items-center justify-end gap-x-6"},Le={key:0,class:"flex justify-between"},Me={class:"flex space-x-2 items-center"},He=c(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"PRODUCT:",-1)),Qe={class:"text-sm font-semibold text-gray-700 leading-6"},Xe={class:"flex space-x-2 items-center"},Ye=c(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"HSN Code:",-1)),Ze={class:"text-sm font-semibold text-gray-700 leading-6"},Je={class:"flex space-x-2 items-center"},Ke=c(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"QTY:",-1)),We={class:"text-sm font-semibold text-gray-700 leading-6"},ts={class:"flex space-x-2 items-center"},es=c(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"PRICE (₹):",-1)),ss={class:"text-sm font-semibold text-gray-700 leading-6"},os={class:"flex space-x-2 items-center"},as=c(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"GST (%):",-1)),ls={class:"text-sm font-semibold text-gray-700 leading-6"},ns={class:"flex space-x-2 items-center"},is=c(()=>t("span",{class:"text-sm font-semibold text-gray-700 leading-6"},"TOTAL AMOUNT (₹):",-1)),rs={class:"text-sm font-semibold text-gray-700 leading-6"},ds=c(()=>t("div",null,null,-1)),cs={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},us={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},_s={class:"sm:col-span-3 space-y-4"},ms={class:"flex space-x-4"},ps={class:"w-full"},gs={class:"w-full"},vs={class:"relative mt-2"},xs={class:"flex space-x-4"},ys={class:"w-full"},fs={class:"w-full"},hs={class:"w-full"},bs={class:"flex space-x-4"},ws={class:"w-full"},Vs={class:"w-full"},Ss={class:"w-full"},Is={class:"flex space-x-4"},Ts={class:"w-full"},Ps={class:"w-full"},Cs={class:"sm:col-span-3"},Fs={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Gs={class:"inline-flex items-center justify-end w-full space-x-3"},$s=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),Us={class:"text-base font-semibold text-gray-900 w-32"},ks={class:"inline-flex items-center justify-end w-full space-x-3"},Ns=c(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Discount On Sub Total(₹):",-1)),Ds={class:"w-40"},As={class:"inline-flex items-center justify-end w-full space-x-3"},Os=c(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),zs={class:"w-40"},qs={class:"inline-flex items-center justify-end w-full space-x-3"},js=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),Bs={class:"text-base font-semibold text-gray-900 w-32"},Es={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},Rs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),Ls={class:"text-base font-semibold text-gray-900 w-32"},Ms={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},Hs=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),Qs={class:"text-base font-semibold text-gray-900 w-32"},Xs={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},Ys=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),Zs={class:"text-base font-semibold text-gray-900 w-32"},Js={class:"inline-flex items-center justify-end w-full space-x-3"},Ks=c(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),Ws={class:"text-base font-semibold text-gray-900 w-32"},to={class:"flex mt-6 items-center justify-between"},eo={class:"ml-auto flex items-center justify-end gap-x-6"},so=c(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),oo={__name:"Add",props:["customers","invoice_no","retail_invoice_no","serialno","products","organization","category","salesuser","invoice_details"],setup(S){const V=S,O=P(),z=P(),d=P([{serial_number_id:"",product_id:"",product_name:"",item_code:"",hsn_code:"",price:"",sell_price:"",discount:"",discount_amount:"",discount_before_tax_product:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",qty:"",description:"",expiry_date:"",mrp:""}]),b=P({product_name:"",hsn_code:"",qty:"",price:"",gst:"",total_amount:""}),v=P(null),$=P(null),K=(l,o)=>{s.customer_id=l,s.errors.customer_id=null;const e=V.customers.find(a=>a.id===l);e&&(v.value=e.gst_type,$.value=e.customer_type)},W=P([]),tt=async(l,o,e)=>{s.errors[`selectedProductItem.${e}.product_id`]=null;const a=V.serialno.filter(n=>n.product_id===l&&n.organization_id===s.organization_id);W.value=a,d.value[e].product_id=l;try{const n=await bt.post("/api/invoices/previous",{customer_id:s.customer_id,organization_id:s.organization_id,product_id:l});n.data.success?b.value=n.data.data:b.value={product_name:"",hsn_code:"",qty:"",price:"",gst:"",total_amount:""}}catch(n){console.error("Error fetching previous invoice:",n),b.value={product_name:"",hsn_code:"",qty:"",price:"",gst:"",total_amount:""}}},et=(l,o,e)=>{const a=V.serialno.find(n=>n.id===l);a&&(d.value[e].qty="",d.value[e].product_id=a.product.id,d.value[e].serial_number_id=a.id,d.value[e].item_code=a.product.item_code,d.value[e].expiry_date=a.expiry_date,d.value[e].mrp=a.mrp?parseFloat(a.mrp).toFixed(2):"-",d.value[e].product_name=a.product.name,d.value[e].hsn_code=a.product.hsn_code,d.value[e].discount="0.00",d.value[e].price=parseFloat(a.purchase_price).toFixed(2),d.value[e].total_price=parseFloat(a.purchase_price).toFixed(2),d.value[e].gst=parseFloat(a.product.gst).toFixed(2),d.value[e].sgst=parseFloat(a.product.gst/2).toFixed(2),d.value[e].gst_amount="",d.value[e].total_gst_amount="",d.value[e].total_amount="",d.value[e].description="",s.errors[`selectedProductItem.${e}.serial_number_id`]=null)},st=(l,o)=>{const e=parseFloat(l.sell_price),a=parseFloat(l.discount_before_tax_product)||0,n=parseFloat(l.discount)||0,u=v.value=="IGST"?l.gst:parseFloat(l.sgst*2),w=parseFloat(l.qty);let h=0,D=0;n>0||a>0?h=e*w:h=e*w*(1+u/100);const U=h*(n/100)||0,H=e*1*(u/100),j=(e*w-U-a)*(u/100);n>0||a>0?D=h-U-a+j:D=h-U;const Q=e*w;return l.total_price=isNaN(Q)?"":parseFloat(Q).toFixed(2),l.gst_amount=isNaN(H)?"":parseFloat(H).toFixed(2),l.total_gst_amount=isNaN(j)?"":parseFloat(j).toFixed(2),l.discount_amount=isNaN(U)?"":parseFloat(U).toFixed(2),l.gst=u,isNaN(D)?"":parseFloat(D).toFixed(2)},T=(l,o)=>{M(),l.total_amount=st(l)},B=k(()=>{const l=Math.round(d.value.reduce((e,a)=>e+(a.total_amount?parseFloat(a.total_amount):0),0)),o=s.overall_discount?parseFloat(s.overall_discount):0;return l-o}),C=k(()=>d.value.reduce((l,o)=>l+(o.total_gst_amount?parseFloat(o.total_gst_amount):0),0)),E=k(()=>d.value.reduce((l,o)=>l+(o.total_price?parseFloat(o.total_price):0),0)),R=k(()=>{const l=d.value.reduce((a,n)=>a+(n.discount_amount?parseFloat(n.discount_amount):0),0),o=s.overall_discount?parseFloat(s.overall_discount):0,e=s.discount_before_tax?parseFloat(s.discount_before_tax):0;return l+o+e}),s=Tt("post","/invoice",{note:"",date:new Date().toISOString().slice(0,10),sales_user_id:"",invoice_type:"",selectedProductItem:[],customer_id:"",category:"",invoice_no:"",document:"",cgst:"",sgst:"",igst:"",total_gst:"",sub_total:"",total_amount:"",total_discount:"",organization_id:"",dispatch:"",transport:"",patient_name:"",customer_po_date:"",customer_po_number:"",eway_bill:"",due_days:"",cr_dr_note:"",overall_discount:"",discount_before_tax:""}),L=()=>{d.value=[{serial_number_id:"",product_id:"",product_name:"",item_code:"",hsn_code:"",price:"",sell_price:"",discount:"",discount_amount:"",discount_before_tax_product:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",qty:"",description:"",expiry_date:"",mrp:""}]},q=P([]),ot=(l,o)=>{L(),V.products.filter(a=>a.category===l);const e=V.products.filter(a=>a.sales_products.some(u=>u.organization_id===s.organization_id));q.value=e,s.category=l,s.errors.category=null},at=(l,o)=>{s.sales_user_id=l,s.errors.sales_user_id=null},lt=(l,o)=>{L(),V.products.filter(a=>a.category===s.category);const e=V.products.filter(a=>a.sales_products.some(u=>u.organization_id===l));q.value=e,s.organization_id=l,s.errors.organization_id=null,O.value=V.invoice_no[l],z.value=V.retail_invoice_no[l]},nt=()=>{for(let l=0;l<d.value.length;l++){const o=d.value[l];if(o.qty>o.available_stock){alert("Total qty exceeds available stock.");return}}s.sub_total=E.value,s.cgst=v.value=="CGST/SGST"?C.value/2:"0",s.sgst=v.value=="CGST/SGST"?C.value/2:"0",s.igst=v.value=="IGST"?C.value:"0",s.total_gst=C.value,s.total_amount=B.value,s.total_discount=R.value,s.invoice_no=$.value=="Tax"?O.value:z.value,s.invoice_type=$.value,s.selectedProductItem=d.value,s.submit({preserveScroll:!0,onSuccess:()=>s.reset()})},it=()=>{d.value.push({serial_number_id:"",product_name:"",hsn_code:""})},rt=l=>{d.value.splice(l,1)};k(()=>{const l=new Date,o={year:"numeric",month:"long",day:"numeric"};return l.toLocaleDateString("en-US",o)});const dt=l=>{s.document=l},I=l=>{if(l==null||isNaN(l))return"0.00";let o=Number(l).toFixed(2),[e,a]=o.split("."),n=e.substring(e.length-3),u=e.substring(0,e.length-3);return u!==""&&(n=","+n),`${u.replace(/\B(?=(\d{2})+(?!\d))/g,",")+n}.${a}`},ct=(l,o,e)=>V.serialno.filter(n=>n.product_id===l&&n.organization_id===s.organization_id),ut=l=>l,N=(l,o)=>{const e=o.length,a=o.reduce((w,h)=>w+(h.total_price?parseFloat(h.total_price):0),0),n=d.value.reduce((w,h)=>w+(h.total_price?parseFloat(h.total_price):0),0),u=l*a/n/e;o.forEach(w=>{w.discount_before_tax_product=u})},M=()=>{const l=parseFloat(s.discount_before_tax)||0,o=d.value.filter(u=>u.gst==5&&u.total_price>0),e=d.value.filter(u=>u.gst==12&&u.total_price>0),a=d.value.filter(u=>u.gst==18&&u.total_price>0),n=d.value.filter(u=>u.gst==28&&u.total_price>0);N(l,o),N(l,e),N(l,a),N(l,n)};_t(()=>s.discount_before_tax,l=>{M(),d.value.forEach(o=>{T(o)})});const F=l=>{s.errors[l]&&delete s.errors[l]};return(l,o)=>(m(),p(X,null,[r(i(mt),{title:"Invoice"}),r(wt,null,{default:A(()=>[t("div",Ct,[t("div",Ft,[Gt,t("div",null,[t("div",$t,[t("div",Ut,[kt,$.value=="Retail"?(m(),p("span",Nt,_(z.value),1)):g("",!0),$.value=="Tax"?(m(),p("span",Dt,_(O.value),1)):g("",!0)]),t("div",At,[Ot,pt(t("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":o[0]||(o[0]=e=>i(s).date=e),onChange:o[1]||(o[1]=e=>i(s).validate("date"))},null,544),[[gt,i(s).date]])])])])]),t("form",{onSubmit:vt(nt,["prevent"]),class:""},[t("div",qt,[t("div",jt,[t("div",Bt,[r(y,{for:"company_name",value:"Organization"}),t("div",Et,[r(G,{options:S.organization,modelValue:i(s).organization_id,"onUpdate:modelValue":o[2]||(o[2]=e=>i(s).organization_id=e),onOnchange:lt,class:f({"error rounded-md":i(s).errors.organization_id})},null,8,["options","modelValue","class"])])]),t("div",Rt,[r(y,{for:"customer_id",value:"Customer Name"}),t("div",Lt,[r(G,{options:S.customers,modelValue:i(s).customer_id,"onUpdate:modelValue":o[3]||(o[3]=e=>i(s).customer_id=e),onOnchange:K,class:f({"error rounded-md":i(s).errors.customer_id})},null,8,["options","modelValue","class"])])]),t("div",Mt,[r(y,{for:"company_name",value:"Category"}),t("div",Ht,[r(G,{options:S.category,modelValue:i(s).category,"onUpdate:modelValue":o[4]||(o[4]=e=>i(s).category=e),onOnchange:ot,class:f({"error rounded-md":i(s).errors.category})},null,8,["options","modelValue","class"])])])])]),t("div",Qt,[t("div",Xt,[t("table",Yt,[t("thead",null,[t("tr",null,[Zt,Jt,Kt,Wt,te,ee,se,oe,ae,le,v.value=="IGST"?(m(),p("th",ne,"IGST (%)")):g("",!0),v.value=="IGST"?(m(),p("th",ie,"IGST (₹)")):g("",!0),v.value=="CGST/SGST"?(m(),p("th",re,"CGST (%)")):g("",!0),v.value=="CGST/SGST"?(m(),p("th",de,"SGST (%)")):g("",!0),v.value=="CGST/SGST"?(m(),p("th",ce,"Total GST (₹)")):g("",!0),ue,_e,me])]),t("tbody",pe,[(m(!0),p(X,null,xt(d.value,(e,a)=>(m(),p("tr",{key:a},[t("td",ge,[t("div",ve,[r(G,{options:ut(q.value),modelValue:e.product_id,"onUpdate:modelValue":n=>e.product_id=n,onOnchange:(n,u)=>tt(n,u,a),onChange:o[5]||(o[5]=n=>i(s).validate("product_id")),class:f({"error rounded-md":i(s).errors[`selectedProductItem.${a}.product_id`]})},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class"])])]),t("td",xe,[t("div",ye,[r(G,{options:ct(e.product_id),modelValue:e.serial_number_id,"onUpdate:modelValue":n=>e.serial_number_id=n,onOnchange:(n,u)=>et(n,u,a),onChange:o[6]||(o[6]=n=>i(s).validate("selectedProductItem.${index}.serial_number_id")),class:f({"error rounded-md":i(s).errors[`selectedProductItem.${a}.serial_number_id`]})},null,8,["options","modelValue","onUpdate:modelValue","onOnchange","class"])])]),t("td",fe,_(e.hsn_code),1),t("td",he,_(e.expiry_date??"-"),1),t("td",be,[r(x,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":n=>e.description=n,onInput:n=>T(e,a),onChange:n=>F("selectedProductItem."+a+".description"),class:f({error:i(s).errors[`selectedProductItem.${a}.description`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",we,_(e.mrp??"-"),1),t("td",Ve,_(e.price),1),t("td",Se,[r(x,{id:"qty",type:"text",onInput:n=>T(e,a),modelValue:d.value[a].qty,"onUpdate:modelValue":n=>d.value[a].qty=n,class:f({error:i(s).errors[`selectedProductItem.${a}.qty`]})},null,8,["onInput","modelValue","onUpdate:modelValue","class"]),r(Z,{message:i(s).errors[`selectedProductItem.${a}.qty`]},null,8,["message"])]),t("td",Ie,[r(x,{id:"sell_price",type:"text",modelValue:e.sell_price,"onUpdate:modelValue":n=>e.sell_price=n,onInput:n=>T(e,a),onChange:n=>F("selectedProductItem."+a+".sell_price"),class:f({error:i(s).errors[`selectedProductItem.${a}.sell_price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Te,_(e.total_price),1),v.value=="IGST"?(m(),p("td",Pe,[r(x,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":n=>e.gst=n,onInput:n=>T(e,a),onChange:n=>F("selectedProductItem."+a+".gst"),class:f({error:i(s).errors[`selectedProductItem.${a}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):g("",!0),v.value=="CGST/SGST"?(m(),p("td",Ce,[r(x,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":n=>e.sgst=n,onInput:n=>T(e,a),onChange:n=>F("selectedProductItem."+a+".gst"),class:f({error:i(s).errors[`selectedProductItem.${a}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):g("",!0),v.value=="CGST/SGST"?(m(),p("td",Fe,[r(x,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":n=>e.sgst=n,onInput:n=>T(e,a),onChange:n=>F("selectedProductItem."+a+".gst"),class:f({error:i(s).errors[`selectedProductItem.${a}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):g("",!0),v.value=="IGST"?(m(),p("td",Ge,_(e.total_gst_amount),1)):g("",!0),v.value=="CGST/SGST"?(m(),p("td",$e,_(e.total_gst_amount),1)):g("",!0),t("td",Ue,[r(x,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":n=>e.discount=n,onInput:n=>T(e,a),onChange:n=>F("selectedProductItem."+a+".discount"),class:f({error:i(s).errors[`selectedProductItem.${a}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",ke,_(e.discount_amount),1),t("td",Ne,[t("div",De,_(e.total_amount),1),a!=0?(m(),p("button",{key:0,type:"button",class:"mt-1",onClick:n=>rt(a)},ze,8,Ae)):g("",!0)])]))),128))])])]),t("div",qe,[b.value.product_name?(m(),p("div",je,Ee)):g("",!0),t("div",Re,[r(J,{onClick:it,type:"button"},{default:A(()=>[Y("Add Product")]),_:1})])]),b.value.product_name?(m(),p("div",Le,[t("div",null,[t("div",Me,[He,t("span",Qe,_(b.value.product_name),1)]),t("div",Xe,[Ye,t("span",Ze,_(b.value.hsn_code),1)]),t("div",Je,[Ke,t("span",We,_(b.value.qty),1)])]),t("div",null,[t("div",ts,[es,t("span",ss,_(I(b.value.price)),1)]),t("div",os,[as,t("span",ls,_(I(b.value.gst)),1)]),t("div",ns,[is,t("span",rs,_(I(b.value.total_amount)),1)])]),ds])):g("",!0)]),t("div",cs,[t("div",us,[t("div",_s,[t("div",ms,[t("div",ps,[r(y,{for:"note",value:"Upload Documents"}),r(It,{inputId:"document",inputName:"document",onFiles:dt})]),t("div",gs,[r(y,{for:"sales_user_id",value:"Sales Person"}),t("div",vs,[r(G,{options:S.salesuser,modelValue:i(s).sales_user_id,"onUpdate:modelValue":o[7]||(o[7]=e=>i(s).sales_user_id=e),onOnchange:at,class:f({"error rounded-md":i(s).errors.sales_user_id})},null,8,["options","modelValue","class"])])])]),t("div",xs,[t("div",ys,[r(y,{for:"company_name",value:"Transport"}),r(x,{id:"gst",type:"text",modelValue:i(s).dispatch,"onUpdate:modelValue":o[8]||(o[8]=e=>i(s).dispatch=e)},null,8,["modelValue"])]),t("div",fs,[r(y,{for:"company_name",value:"Dispatch"}),r(x,{id:"transport",type:"text",modelValue:i(s).transport,"onUpdate:modelValue":o[9]||(o[9]=e=>i(s).transport=e)},null,8,["modelValue"])]),t("div",hs,[r(y,{for:"eway_bill",value:"Eway Bill"}),r(x,{id:"eway_bill",type:"text",modelValue:i(s).eway_bill,"onUpdate:modelValue":o[10]||(o[10]=e=>i(s).eway_bill=e)},null,8,["modelValue"])])]),t("div",bs,[t("div",ws,[r(y,{for:"company_name",value:"PO Number"}),r(x,{id:"gst",type:"text",modelValue:i(s).customer_po_number,"onUpdate:modelValue":o[11]||(o[11]=e=>i(s).customer_po_number=e)},null,8,["modelValue"])]),t("div",Vs,[r(y,{for:"company_name",value:"PO Date"}),r(x,{id:"customer_po_date",type:"date",modelValue:i(s).customer_po_date,"onUpdate:modelValue":o[12]||(o[12]=e=>i(s).customer_po_date=e)},null,8,["modelValue"])]),t("div",Ss,[r(y,{for:"due_days",value:"Due Days"}),r(x,{id:"due_days",type:"text",modelValue:i(s).due_days,"onUpdate:modelValue":o[13]||(o[13]=e=>i(s).due_days=e)},null,8,["modelValue"])])]),t("div",Is,[t("div",Ts,[r(y,{for:"patient_name",value:"Patient Name"}),r(x,{id:"patient_name",type:"text",modelValue:i(s).patient_name,"onUpdate:modelValue":o[14]||(o[14]=e=>i(s).patient_name=e)},null,8,["modelValue"])]),t("div",Ps,[r(y,{for:"cr_dr_note",value:"CR DR Note"}),r(x,{id:"cr_dr_note",type:"text",modelValue:i(s).cr_dr_note,"onUpdate:modelValue":o[15]||(o[15]=e=>i(s).cr_dr_note=e)},null,8,["modelValue"])])]),t("div",null,[r(y,{for:"note",value:"Note"}),r(St,{id:"note",type:"text",modelValue:i(s).note,"onUpdate:modelValue":o[16]||(o[16]=e=>i(s).note=e),onChange:o[17]||(o[17]=e=>i(s).validate("note"))},null,8,["modelValue"]),i(s).invalid("note")?(m(),yt(Z,{key:0,class:"",message:i(s).errors.note},null,8,["message"])):g("",!0)])]),t("div",Cs,[t("div",Fs,[t("div",Gs,[$s,t("p",Us,_(I(E.value)),1)]),t("div",ks,[Ns,t("div",Ds,[r(x,{id:"discount_before_tax",type:"text",modelValue:i(s).discount_before_tax,"onUpdate:modelValue":o[18]||(o[18]=e=>i(s).discount_before_tax=e)},null,8,["modelValue"])])]),t("div",As,[Os,t("div",zs,[r(x,{id:"overall_discount",type:"text",modelValue:i(s).overall_discount,"onUpdate:modelValue":o[19]||(o[19]=e=>i(s).overall_discount=e)},null,8,["modelValue"])])]),t("div",qs,[js,t("p",Bs,_(I(R.value)),1)]),v.value=="IGST"?(m(),p("div",Es,[Rs,t("p",Ls,_(I(C.value)),1)])):g("",!0),v.value=="CGST/SGST"?(m(),p("div",Ms,[Hs,t("p",Qs,_(I(C.value/2)),1)])):g("",!0),v.value=="CGST/SGST"?(m(),p("div",Xs,[Ys,t("p",Zs,_(I(C.value/2)),1)])):g("",!0),t("div",Js,[Ks,t("p",Ws,_(I(B.value)),1)])])])])]),t("div",to,[t("div",eo,[r(Vt,{href:l.route("invoice.index")},{svg:A(()=>[so]),_:1},8,["href"]),r(J,{disabled:i(s).processing},{default:A(()=>[Y("Submit")]),_:1},8,["disabled"])])])],40,zt)])]),_:1})],64))}},vo=Pt(oo,[["__scopeId","data-v-a3f02d1b"]]);export{vo as default};
