import{r as w,K as z,m as pt,j as P,l as ht,o as u,c as _,a as i,u as o,w as b,F,Z as xt,b as t,t as m,k as yt,v as gt,d as ft,f as h,i as U,e as vt,g as N,n as I,s as wt,x as bt}from"./app-97275a91.js";import{_ as kt,a as Vt}from"./AdminLayout-595ad5a7.js";import{_ as St}from"./InputError-b3250228.js";import{_ as v}from"./InputLabel-eb73087c.js";import{P as Ct}from"./PrimaryButton-46ac4375.js";import{_ as x}from"./TextInput-11c46564.js";import{_ as It}from"./TextArea-5e21e606.js";import{D as Dt}from"./DangerButton-36669f8b.js";import{_ as tt}from"./SecondaryButton-d0c53c3f.js";import{M as et}from"./Modal-48c075e7.js";import{_ as Tt}from"./FileViewer-01b17a23.js";import{_ as Pt}from"./MultipleFileUpload-368d3540.js";import{_ as st}from"./Checkbox-c09a6665.js";import{u as Ft}from"./index-05d29b1c.js";import{_ as Gt}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const d=k=>(wt("data-v-e976bfe3"),k=k(),bt(),k),$t={class:"animate-top"},Ut={class:"sm:flex sm:items-center"},Nt=d(()=>t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Generate Invoice")],-1)),jt={class:"w-auto"},qt={class:"flex space-x-2 items-center"},Mt={class:"text-sm font-semibold text-gray-900"},At={class:"flex space-x-2 items-center"},Bt=["onSubmit"],Et={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Ot={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},zt={class:"sm:col-span-6"},Lt={class:"inline-flex items-center justify-start w-full space-x-2"},Rt=d(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-32"},"Customer Name:",-1)),Wt={class:"text-sm text-gray-700 leading-6"},Ht={class:"inline-flex items-center justify-start w-full space-x-2"},Qt=d(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Email:",-1)),Kt={class:"text-sm leading-6 text-gray-700"},Zt={class:"inline-flex items-center justify-start w-full space-x-2"},Jt=d(()=>t("p",{class:"text-sm font-semibold text-gray-900 w-32"},"Contact No:",-1)),Xt={class:"text-sm leading-6 text-gray-700"},Yt={class:"sm:col-span-6"},te={class:"inline-flex items-center justify-start w-full space-x-2"},ee=d(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-32"},"Invoice No:",-1)),se={key:0,class:"text-sm text-gray-700 leading-6"},oe={key:1,class:"text-sm text-gray-700 leading-6"},le={class:"inline-flex items-center justify-start w-full space-x-2"},ae=d(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-32"},"Challan Number:",-1)),ne={class:"text-sm text-gray-700 leading-6"},ce={class:"inline-flex items-center justify-start w-full space-x-2"},ie=d(()=>t("span",{class:"text-sm font-semibold text-gray-900 leading-6 w-32"},"Challan Date :",-1)),re={class:"text-sm text-gray-700 leading-6"},de={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border overflow-x-auto w-full"},ue={class:"overflow-x-auto divide-y divide-gray-300 w-full",style:{width:"110%"}},_e={scope:"col",class:""},me=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Code",-1)),pe={key:0,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},he=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Product Name",-1)),xe=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"HSN",-1)),ye=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Batch",-1)),ge=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Qty",-1)),fe=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Price (₹)",-1)),ve=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Sell Price (₹)",-1)),we={key:1,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},be={key:2,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},ke={key:3,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Ve={key:4,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Se={key:5,scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},Ce=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (%)",-1)),Ie=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Discount (₹)",-1)),De=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Total Amount (₹)",-1)),Te=d(()=>t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"Description",-1)),Pe={class:"divide-y divide-gray-300 bg-white"},Fe={class:"whitespace-nowrap py-3 text-sm text-gray-900"},Ge={class:"text-sm text-gray-900 leading-6"},$e={key:0,class:"text-red-500 text-xs absolute"},Ue={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ne={key:0,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-20"},je={class:"whitespace-normal px-3 py-3 text-sm text-gray-900 min-w-48"},qe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Me={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Ae={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},Be={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Ee={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},Oe={key:1,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},ze={key:2,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Le={key:3,class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-24"},Re={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},We={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},He={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-28"},Qe={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-48"},Ke={class:"whitespace-nowrap px-3 py-3 text-sm text-gray-900 min-w-40"},Ze={key:0,class:"mt-6 bg-white p-1 shadow sm:rounded-lg border"},Je={class:"min-w-full divide-y divide-gray-300"},Xe=d(()=>t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"},"UPLOADED DOCUMENT"),t("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"REMOVE")])],-1)),Ye={class:"divide-y divide-gray-300 bg-white"},ts={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},es={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},ss=["onClick"],os=d(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),ls=[os],as=["onClick"],ns=d(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),cs=[ns],is=["onClick"],rs=d(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),ds=[rs],us={class:"mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},_s={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},ms={class:"sm:col-span-3 space-y-4"},ps={class:"flex space-x-4"},hs={class:"w-full"},xs={class:"flex space-x-4"},ys={class:"w-full"},gs={class:"w-full"},fs={class:"w-full"},vs={class:"flex space-x-4"},ws={class:"w-full"},bs={class:"w-full"},ks={class:"w-full"},Vs={class:"flex space-x-4"},Ss={class:"w-full"},Cs={class:"w-full"},Is={class:"sm:col-span-3"},Ds={class:"inline-flex flex-col space-y-1 items-center justify-end w-full"},Ts={class:"inline-flex items-center justify-end w-full space-x-3"},Ps=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Sub Total (₹):",-1)),Fs={class:"text-base font-semibold text-gray-900 w-20"},Gs={class:"inline-flex items-center justify-end w-full space-x-3"},$s=d(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Discount On Sub Total(₹):",-1)),Us={class:"w-40"},Ns={class:"inline-flex items-center justify-end w-full space-x-3"},js=d(()=>t("p",{class:"text-sm font-semibold text-gray-700 mt-2"},"Overall Discount (₹):",-1)),qs={class:"w-40"},Ms={class:"inline-flex items-center justify-end w-full space-x-3"},As=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Discount Amount (₹):",-1)),Bs={class:"text-base font-semibold text-gray-900 w-20"},Es={key:0,class:"inline-flex items-center justify-end w-full space-x-3"},Os=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total IGST (₹):",-1)),zs={class:"text-base font-semibold text-gray-900 w-20"},Ls={key:1,class:"inline-flex items-center justify-end w-full space-x-3"},Rs=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total CGST (₹):",-1)),Ws={class:"text-base font-semibold text-gray-900 w-20"},Hs={key:2,class:"inline-flex items-center justify-end w-full space-x-3"},Qs=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total SGST (₹):",-1)),Ks={class:"text-base font-semibold text-gray-900 w-20"},Zs={class:"inline-flex items-center justify-end w-full space-x-3"},Js=d(()=>t("p",{class:"text-sm font-semibold text-gray-700"},"Total Amount (₹):",-1)),Xs={class:"text-base font-semibold text-gray-900 w-20"},Ys={class:"flex mt-6 items-center justify-between"},to={class:"ml-auto flex items-center justify-end gap-x-6"},eo=d(()=>t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)),so={class:"p-6"},oo=d(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this document? ",-1)),lo={class:"mt-6 flex justify-end"},ao={class:"p-6"},no={class:"mt-6 px-4 flex justify-end"},co={__name:"Combine-invoice",props:["invoice_no","retail_invoice_no","filepath","challanData"],setup(k){const j=k,q=w(),M=w(),L=z().props.filepath.view,r=z().props.data[0];q.value=j.invoice_no[r.challan.organization.id],M.value=j.retail_invoice_no[r.challan.organization.id];const l=Ft("post","/saveinvoice",{category:r.challan.category,note:"",date:new Date().toISOString().slice(0,10),selectedProductItem:[],customer_id:r.challan.customer_id,organization_id:r.challan.organization.id,total_amount:"",invoice_no:"",invoice_type:"",challan_number:r.challan.challan_number,challan_id:r.challan_id,sales_user_id:r.challan.sales_user_id,document:"",cgst:"",sgst:"",igst:"",total_gst:"",sub_total:"",total_discount:"",discount_before_tax:"",dispatch:"",transport:"",patient_name:"",customer_po_date:"",customer_po_number:"",eway_bill:"",due_days:"",cr_dr_note:"",overall_discount:"",challans:j.challanData}),ot=()=>{l.sub_total=W.value,l.cgst=r.challan.customers.gst_type=="CGST/SGST"?S.value/2:"0",l.sgst=r.challan.customers.gst_type=="CGST/SGST"?S.value/2:"0",l.igst=r.challan.customers.gst_type=="IGST"?S.value:"0",l.total_gst=S.value,l.total_amount=R.value,l.total_discount=H.value,l.invoice_no=r.challan.customers.customer_type=="Tax"?q.value:M.value,l.invoice_type=r.challan.customers.customer_type,l.selectedProductItem=y.value,l.submit({preserveScroll:!0,onSuccess:()=>l.reset()})},y=w([{product_id:"",item_code:"",product_name:"",hsn_code:"",qty:"",challan_qty:"",return_qty:"",invoiced_qty:"",price:"",sell_price:"",discount:"",discount_amount:"",discount_before_tax_product:"",gst:"",sgst:"",total_price:"",gst_amount:"",total_gst_amount:"",total_amount:"",challan_detail_id:"",check:!1,description:""}]);pt(()=>{y.value=z().props.data.map(s=>({product_id:s.viewserialnumbers.product.id,serial_number_id:s.viewserialnumbers.id,serial_no:s.viewserialnumbers.serial_no,batch:s.viewserialnumbers.unique_id,item_code:s.viewserialnumbers.product.item_code,product_name:s.viewserialnumbers.product.name,challan_detail_id:s.id,qty:s.qty-s.invoiced_qty,challan_qty:s.qty,return_qty:s.return_qty,invoiced_qty:s.invoiced_qty,discount:"0.00",description:"",hsn_code:s.viewserialnumbers.product.hsn_code,price:parseFloat(s.viewserialnumbers.purchase_price).toFixed(2),total_price:parseFloat(s.viewserialnumbers.purchase_price).toFixed(2),gst:parseFloat(s.viewserialnumbers.product.gst).toFixed(2),sgst:parseFloat(s.viewserialnumbers.product.gst/2).toFixed(2),gst_amount:"",total_gst_amount:"",total_amount:"",sell_price:"",check:!1}))});const lt=(s,a)=>{const e=parseFloat(s.sell_price),n=parseFloat(s.discount_before_tax_product)||0,c=parseFloat(s.discount)||0,p=r.challan.customers.gst_type=="IGST"?s.gst:parseFloat(s.sgst*2),f=parseFloat(s.qty);let g=0,$=0;c>0||n>0?g=e*f:g=e*f*(1+p/100);const T=g*(c/100)||0,X=e*1*(p/100),O=(e*f-T-n)*(p/100);c>0||n>0?$=g-T-n+O:$=g-T;const Y=e*f;return s.total_price=isNaN(Y)?"":parseFloat(Y).toFixed(2),s.gst_amount=isNaN(X)?"":parseFloat(X).toFixed(2),s.total_gst_amount=isNaN(O)?"":parseFloat(O).toFixed(2),s.discount_amount=isNaN(T)?"":parseFloat(T).toFixed(2),s.gst=p,isNaN($)?"":parseFloat($).toFixed(2)},V=(s,a)=>{J(),s.total_amount=lt(s)},R=P(()=>{const s=Math.round(y.value.reduce((e,n)=>e+(n.check&&n.total_amount?parseFloat(n.total_amount):0),0)),a=l.overall_discount?parseFloat(l.overall_discount):0;return s-a}),S=P(()=>y.value.reduce((s,a)=>s+(a.check&&a.total_gst_amount?parseFloat(a.total_gst_amount):0),0)),W=P(()=>y.value.reduce((s,a)=>s+(a.check&&a.sell_price?parseFloat(a.sell_price*a.qty):0),0)),H=P(()=>{const s=y.value.reduce((n,c)=>n+(c.check&&c.discount_amount?parseFloat(c.discount_amount):0),0),a=l.overall_discount?parseFloat(l.overall_discount):0,e=l.discount_before_tax?parseFloat(l.discount_before_tax):0;return s+a+e}),C=s=>{l.errors[s]=null},at=s=>{const a=new Date(s),e={year:"numeric",month:"short",day:"numeric"};return a.toLocaleDateString("en-US",e)},nt=s=>{l.document=s},A=w(!1),Q=w(null),ct=s=>{Q.value=s,A.value=!0},it=()=>{l.get(route("removedocument",{id:Q.value,name:"challanDocument"}),{onSuccess:()=>{B()}})},B=()=>{A.value=!1},E=w(!1),K=w(null),rt=w("custom"),dt=s=>{K.value=s,E.value=!0},Z=()=>{E.value=!1},ut=s=>{const a=window.location.origin+L+s,e=document.createElement("a");e.href=a,e.setAttribute("download",s),document.body.appendChild(e),e.click(),document.body.removeChild(e)},D=s=>{const[a,e]=s.toFixed(2).toString().split(".");return a.replace(/\B(?=(\d{3})+(?!\d))/g,",")+(e?"."+e:"")},_t=P(()=>y.value.length>0&&y.value.every(s=>s.check)),mt=s=>{y.value.map(a=>{a.check=s})},G=(s,a)=>{const e=a.length,n=a.reduce((f,g)=>f+(g.total_price?parseFloat(g.total_price):0),0),c=y.value.reduce((f,g)=>f+(g.total_price?parseFloat(g.total_price):0),0),p=s*n/c/e;a.forEach(f=>{f.discount_before_tax_product=p})},J=()=>{const s=parseFloat(l.discount_before_tax)||0,a=y.value.filter(p=>p.gst==5&&p.total_price>0),e=y.value.filter(p=>p.gst==12&&p.total_price>0),n=y.value.filter(p=>p.gst==18&&p.total_price>0),c=y.value.filter(p=>p.gst==28&&p.total_price>0);G(s,a),G(s,e),G(s,n),G(s,c)};return ht(()=>l.discount_before_tax,s=>{J(),y.value.forEach(a=>{V(a)})}),(s,a)=>(u(),_(F,null,[i(o(xt),{title:"Challan"}),i(kt,null,{default:b(()=>[t("div",$t,[t("div",Ut,[Nt,t("div",jt,[t("div",qt,[t("p",Mt,m(o(r).challan.organization.name),1),t("div",At,[yt(t("input",{class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":a[0]||(a[0]=e=>o(l).date=e),onChange:a[1]||(a[1]=e=>o(l).validate("date"))},null,544),[[gt,o(l).date]])])])])]),t("form",{onSubmit:ft(ot,["prevent"]),class:""},[t("div",Et,[t("div",Ot,[t("div",zt,[t("div",Lt,[Rt,t("span",Wt,m(o(r).challan.customers.customer_name??"-"),1)]),t("div",Ht,[Qt,t("p",Kt,m(o(r).challan.customers.email??"-"),1)]),t("div",Zt,[Jt,t("p",Xt,m(o(r).challan.customers.contact_no??"-"),1)])]),t("div",Yt,[t("div",te,[ee,o(r).challan.customers.customer_type=="Retail"?(u(),_("span",se,m(M.value),1)):h("",!0),o(r).challan.customers.customer_type=="Tax"?(u(),_("span",oe,m(q.value),1)):h("",!0)]),t("div",le,[ae,(u(!0),_(F,null,U(k.challanData,(e,n)=>(u(),_("div",{key:n},[t("span",ne,m(e.challan_number)+",",1)]))),128))]),t("div",ce,[ie,(u(!0),_(F,null,U(k.challanData,(e,n)=>(u(),_("div",{key:n},[t("span",re,m(at(e.date))+",",1)]))),128))])])])]),t("div",de,[t("table",ue,[t("thead",null,[t("tr",null,[t("th",_e,[i(st,{checked:_t.value,"onUpdate:checked":mt},null,8,["checked"])]),me,o(r).category=="Service"?(u(),_("th",pe,"Part No")):h("",!0),he,xe,ye,ge,fe,ve,o(r).challan.customers.gst_type=="IGST"?(u(),_("th",we,"IGST(%)")):h("",!0),o(r).challan.customers.gst_type=="IGST"?(u(),_("th",be,"IGST (₹)")):h("",!0),o(r).challan.customers.gst_type=="CGST/SGST"?(u(),_("th",ke,"CGST(%)")):h("",!0),o(r).challan.customers.gst_type=="CGST/SGST"?(u(),_("th",Ve,"SGST(%)")):h("",!0),o(r).challan.customers.gst_type=="CGST/SGST"?(u(),_("th",Se,"Total GST (₹)")):h("",!0),Ce,Ie,De,Te])]),t("tbody",Pe,[(u(!0),_(F,null,U(y.value,(e,n)=>(u(),_("tr",{key:n},[t("td",Fe,[t("div",Ge,[i(st,{name:"check",checked:e.check,"onUpdate:checked":c=>e.check=c},null,8,["checked","onUpdate:checked"])]),o(l).errors[`selectedProductItem.${n}.check`]?(u(),_("p",$e,m(o(l).errors[`selectedProductItem.${n}.check`]),1)):h("",!0)]),t("td",Ue,m(e.item_code??"-"),1),o(r).category=="Service"?(u(),_("td",Ne,m(e.item_code??"-"),1)):h("",!0),t("td",je,m(e.product_name??"-"),1),t("td",qe,m(e.hsn_code??"-"),1),t("td",Me,m(e.batch??"-"),1),t("td",Ae,[i(x,{id:"qty",type:"text",modelValue:e.qty,"onUpdate:modelValue":c=>e.qty=c,onInput:c=>V(e,n),onChange:c=>C("selectedProductItem."+n+".qty"),class:I({error:o(l).errors[`selectedProductItem.${n}.qty`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",Be,m(e.price??"-"),1),t("td",Ee,[i(x,{id:"sell_price",type:"text",modelValue:e.sell_price,"onUpdate:modelValue":c=>e.sell_price=c,onInput:c=>V(e,n),onChange:c=>C("selectedProductItem."+n+".sell_price"),class:I({error:o(l).errors[`selectedProductItem.${n}.sell_price`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),o(r).challan.customers.gst_type=="IGST"?(u(),_("td",Oe,[i(x,{id:"gst",type:"text",modelValue:e.gst,"onUpdate:modelValue":c=>e.gst=c,onInput:c=>V(e,n),onChange:c=>C("selectedProductItem."+n+".gst"),class:I({error:o(l).errors[`selectedProductItem.${n}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):h("",!0),o(r).challan.customers.gst_type=="CGST/SGST"?(u(),_("td",ze,[i(x,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":c=>e.sgst=c,onInput:c=>V(e,n),onChange:c=>C("selectedProductItem."+n+".gst"),class:I({error:o(l).errors[`selectedProductItem.${n}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):h("",!0),o(r).challan.customers.gst_type=="CGST/SGST"?(u(),_("td",Le,[i(x,{id:"gst",type:"text",modelValue:e.sgst,"onUpdate:modelValue":c=>e.sgst=c,onInput:c=>V(e,n),onChange:c=>C("selectedProductItem."+n+".gst"),class:I({error:o(l).errors[`selectedProductItem.${n}.gst`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])])):h("",!0),t("td",Re,m(e.total_gst_amount),1),t("td",We,[i(x,{id:"discount",type:"text",modelValue:e.discount,"onUpdate:modelValue":c=>e.discount=c,onInput:c=>V(e,n),onChange:c=>C("selectedProductItem."+n+".discount"),class:I({error:o(l).errors[`selectedProductItem.${n}.discount`]})},null,8,["modelValue","onUpdate:modelValue","onInput","onChange","class"])]),t("td",He,m(e.discount_amount??"-"),1),t("td",Qe,m(e.total_amount??"-"),1),t("td",Ke,[i(x,{id:"description",type:"text",modelValue:e.description,"onUpdate:modelValue":c=>e.description=c,onChange:c=>C("selectedProductItem."+n+".description"),class:I({error:o(l).errors[`selectedProductItem.${n}.description`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"])])]))),128))])])]),o(r).challan.documents&&o(r).challan.documents.length>0?(u(),_("div",Ze,[t("table",Je,[Xe,t("tbody",Ye,[(u(!0),_(F,null,U(o(r).challan.documents,(e,n)=>(u(),_("tr",{key:o(r).challan.id,class:""},[t("td",ts,m(e.orignal_name),1),t("td",es,[t("button",{type:"button",onClick:c=>ct(e.id)},ls,8,ss),t("button",{type:"button",onClick:c=>dt(e.name)},cs,8,as),t("button",{type:"button",onClick:c=>ut(e.name)},ds,8,is)])]))),128))])])])):h("",!0),t("div",us,[t("div",_s,[t("div",ms,[t("div",ps,[t("div",hs,[i(v,{for:"note",value:"Upload Documents"}),i(Pt,{inputId:"document",inputName:"document",onFiles:nt})])]),t("div",xs,[t("div",ys,[i(v,{for:"company_name",value:"Transport"}),i(x,{id:"gst",type:"text",modelValue:o(l).dispatch,"onUpdate:modelValue":a[2]||(a[2]=e=>o(l).dispatch=e)},null,8,["modelValue"])]),t("div",gs,[i(v,{for:"company_name",value:"Dispatch"}),i(x,{id:"transport",type:"text",modelValue:o(l).transport,"onUpdate:modelValue":a[3]||(a[3]=e=>o(l).transport=e)},null,8,["modelValue"])]),t("div",fs,[i(v,{for:"eway_bill",value:"Eway Bill"}),i(x,{id:"eway_bill",type:"text",modelValue:o(l).eway_bill,"onUpdate:modelValue":a[4]||(a[4]=e=>o(l).eway_bill=e)},null,8,["modelValue"])])]),t("div",vs,[t("div",ws,[i(v,{for:"company_name",value:"PO Number"}),i(x,{id:"gst",type:"text",modelValue:o(l).customer_po_number,"onUpdate:modelValue":a[5]||(a[5]=e=>o(l).customer_po_number=e)},null,8,["modelValue"])]),t("div",bs,[i(v,{for:"company_name",value:"PO Date"}),i(x,{id:"customer_po_date",type:"date",modelValue:o(l).customer_po_date,"onUpdate:modelValue":a[6]||(a[6]=e=>o(l).customer_po_date=e)},null,8,["modelValue"])]),t("div",ks,[i(v,{for:"due_days",value:"Due Days"}),i(x,{id:"due_days",type:"text",modelValue:o(l).due_days,"onUpdate:modelValue":a[7]||(a[7]=e=>o(l).due_days=e)},null,8,["modelValue"])])]),t("div",Vs,[t("div",Ss,[i(v,{for:"patient_name",value:"Patient Name"}),i(x,{id:"patient_name",type:"text",modelValue:o(l).patient_name,"onUpdate:modelValue":a[8]||(a[8]=e=>o(l).patient_name=e)},null,8,["modelValue"])]),t("div",Cs,[i(v,{for:"cr_dr_note",value:"CR DR Note"}),i(x,{id:"cr_dr_note",type:"text",modelValue:o(l).cr_dr_note,"onUpdate:modelValue":a[9]||(a[9]=e=>o(l).cr_dr_note=e)},null,8,["modelValue"])])]),t("div",null,[i(v,{for:"note",value:"Note"}),i(It,{id:"note",type:"text",modelValue:o(l).note,"onUpdate:modelValue":a[10]||(a[10]=e=>o(l).note=e),onChange:a[11]||(a[11]=e=>o(l).validate("note"))},null,8,["modelValue"]),o(l).invalid("note")?(u(),vt(St,{key:0,class:"",message:o(l).errors.note},null,8,["message"])):h("",!0)])]),t("div",Is,[t("div",Ds,[t("div",Ts,[Ps,t("p",Fs,m(D(W.value)),1)]),t("div",Gs,[$s,t("div",Us,[i(x,{id:"discount_before_tax",type:"text",modelValue:o(l).discount_before_tax,"onUpdate:modelValue":a[12]||(a[12]=e=>o(l).discount_before_tax=e)},null,8,["modelValue"])])]),t("div",Ns,[js,t("div",qs,[i(x,{id:"overall_discount",type:"text",modelValue:o(l).overall_discount,"onUpdate:modelValue":a[13]||(a[13]=e=>o(l).overall_discount=e)},null,8,["modelValue"])])]),t("div",Ms,[As,t("p",Bs,m(D(H.value)),1)]),o(r).challan.customers.gst_type=="IGST"?(u(),_("div",Es,[Os,t("p",zs,m(D(S.value)),1)])):h("",!0),o(r).challan.customers.gst_type=="CGST/SGST"?(u(),_("div",Ls,[Rs,t("p",Ws,m(D(S.value/2)),1)])):h("",!0),o(r).challan.customers.gst_type=="CGST/SGST"?(u(),_("div",Hs,[Qs,t("p",Ks,m(D(S.value/2)),1)])):h("",!0),t("div",Zs,[Js,t("p",Xs,m(D(R.value)),1)])])])])]),t("div",Ys,[t("div",to,[i(Vt,{href:s.route("challan.index")},{svg:b(()=>[eo]),_:1},8,["href"]),i(Ct,{disabled:o(l).processing},{default:b(()=>[N("Submit")]),_:1},8,["disabled"])])])],40,Bt)]),i(et,{show:A.value,onClose:B},{default:b(()=>[t("div",so,[oo,t("div",lo,[i(tt,{onClick:B},{default:b(()=>[N(" Cancel ")]),_:1}),i(Dt,{class:"ml-3",onClick:it},{default:b(()=>[N(" Delete ")]),_:1})])])]),_:1},8,["show"]),i(et,{show:E.value,onClose:Z,maxWidth:rt.value},{default:b(()=>[t("div",ao,[i(Tt,{fileUrl:o(L)+K.value},null,8,["fileUrl"]),t("div",no,[i(tt,{onClick:Z},{default:b(()=>[N(" Cancel ")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},So=Gt(co,[["__scopeId","data-v-e976bfe3"]]);export{So as default};
