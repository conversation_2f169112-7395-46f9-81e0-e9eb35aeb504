import{K as v,h as g,o as _,c as p,a as t,u as e,w as l,F as x,Z as y,b as a,d as b,n as c,g as h,T as V,f as $}from"./app-03250c83.js";import{_ as C,a as w}from"./AdminLayout-a6b1643c.js";import{_ as f}from"./InputError-564dc17f.js";import{_ as m}from"./InputLabel-28ecec2a.js";import{P as N}from"./PrimaryButton-e6f8c536.js";import{_ as u}from"./TextInput-374b3fdd.js";import"./_plugin-vue_export-helper-c27b6911.js";const E={class:"h-screen"},S={class:"animate-top bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},B=a("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Report",-1),P={class:"border-b border-gray-900/10 pb-12"},T={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-10"},k={class:"sm:col-span-3"},U={class:"sm:col-span-3"},j={class:"sm:col-span-3"},F={class:"flex mt-6 items-center justify-between"},R={class:"ml-auto flex items-center justify-end gap-x-6"},z=a("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1),D={key:0,class:"text-sm text-gray-600"},J={__name:"Edit",props:["data"],setup(K){const d=v().props.data[0],s=g({customer_id:d.customer_id,id:d.id,product_code:d.product_code,product_name:d.product_name,serial_no:d.serial_no}),i=n=>{s.errors[n]=null};return(n,o)=>(_(),p(x,null,[t(e(y),{title:"Edit Report"}),t(C,null,{default:l(()=>[a("div",E,[a("div",S,[B,a("form",{onSubmit:o[6]||(o[6]=b(r=>e(s).patch(n.route("service-reports.update")),["prevent"]))},[a("div",P,[a("div",T,[a("div",k,[t(m,{for:"product_code",value:"Product Code"}),t(u,{id:"product_code",type:"text",onChange:o[0]||(o[0]=r=>i("data.product_code")),modelValue:e(s).product_code,"onUpdate:modelValue":o[1]||(o[1]=r=>e(s).product_code=r),class:c({"error rounded-md":e(s).errors["data.product_code"]})},null,8,["modelValue","class"])]),a("div",U,[t(m,{for:"product_name",value:"Product Name"}),t(u,{id:"product_name",type:"text",onChange:o[2]||(o[2]=r=>i("data.product_name")),modelValue:e(s).product_name,"onUpdate:modelValue":o[3]||(o[3]=r=>e(s).product_name=r),class:c({"error rounded-md":e(s).errors["data.product_name"]})},null,8,["modelValue","class"]),t(f,{class:"",message:e(s).errors.product_name},null,8,["message"])]),a("div",j,[t(m,{for:"serial_no",value:"Serial No"}),t(u,{id:"serial_no",type:"text",onChange:o[4]||(o[4]=r=>i("data.serial_no")),modelValue:e(s).serial_no,"onUpdate:modelValue":o[5]||(o[5]=r=>e(s).serial_no=r),class:c({"error rounded-md":e(s).errors["data.serial_no"]})},null,8,["modelValue","class"]),t(f,{class:"",message:e(s).errors.serial_no},null,8,["message"])])])]),a("div",F,[a("div",R,[t(w,{href:n.route("service-reports.show",{id:e(d).customer_id})},{svg:l(()=>[z]),_:1},8,["href"]),t(N,{disabled:e(s).processing},{default:l(()=>[h("Save")]),_:1},8,["disabled"]),t(V,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:l(()=>[e(s).recentlySuccessful?(_(),p("p",D,"Saved.")):$("",!0)]),_:1})])])],32)])])]),_:1})],64))}};export{J as default};
