import{_ as M,b as N,a as D}from"./AdminLayout-d9d2bc31.js";import{_ as u}from"./CreateButton-7995b8ff.js";import{_ as S}from"./SecondaryButton-69637431.js";import{D as V}from"./DangerButton-b7cb11b9.js";import{M as z}from"./Modal-85d770f4.js";import{_ as A}from"./Pagination-b0edb9e0.js";import{r as p,o as a,c as i,a as o,u as c,w as s,F as m,Z as E,b as t,g as n,i as x,e as g,f as y,t as b}from"./app-4f4c883b.js";import{_ as T}from"./ArrowIcon-19315680.js";import{s as F}from"./sortAndSearch-2a277c12.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const I={class:"animate-top"},J={class:"sm:flex sm:items-center"},O=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Jobcard Checklists")],-1),U={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},H={class:"flex justify-end w-20"},K={class:"flex justify-end"},P={class:"mt-8 overflow-x-auto sm:rounded-lg"},Y={class:"shadow sm:rounded-lg"},Z={class:"w-full text-sm text-left rtl:text-right text-gray-500"},q={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},G={class:"border-b-2"},Q=["onClick"],R={key:0},W={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},X={class:"items-center px-4 py-2.5"},tt={class:"flex items-center justify-start gap-4"},et=t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),st=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),ot=t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1),at=["onClick"],lt=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),it=t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1),rt=[lt,it],nt={key:1},ct=t("tr",{class:"bg-white"},[t("td",{colspan:"8",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),dt=[ct],_t={class:"p-6"},mt=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1),ht={class:"mt-6 flex justify-end"},Lt={__name:"List",props:["data","permissions"],setup(l){const{form:k,search:ft,sort:w,fetchData:ut,sortKey:v,sortDirection:C}=F("jobcard-checklist.index"),d=p(!1),h=p(null),j=[{field:"type",label:"TYPE",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],B=r=>{h.value=r,d.value=!0},_=()=>{d.value=!1},$=()=>{k.delete(route("jobcard-checklist.destroy",{id:h.value}),{onSuccess:()=>_()})};return(r,pt)=>(a(),i(m,null,[o(c(E),{title:"Jobcard Checklists"}),o(M,null,{default:s(()=>[t("div",I,[t("div",J,[O,t("div",U,[t("div",H,[o(u,{href:r.route("setting")},{default:s(()=>[n(" Back ")]),_:1},8,["href"])]),t("div",K,[o(u,{href:r.route("jobcard-checklist.create")},{default:s(()=>[n(" Add Checklist ")]),_:1},8,["href"])])])]),t("div",P,[t("div",Y,[t("table",Z,[t("thead",q,[t("tr",G,[(a(),i(m,null,x(j,(e,f)=>t("th",{key:f,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:L=>c(w)(e.field,e.sortable)},[n(b(e.label)+" ",1),e.sortable?(a(),g(T,{key:0,isSorted:c(v)===e.field,direction:c(C)},null,8,["isSorted","direction"])):y("",!0)],8,Q)),64))])]),l.data.data&&l.data.data.length>0?(a(),i("tbody",R,[(a(!0),i(m,null,x(l.data.data,(e,f)=>(a(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",W,b(e.type??"-"),1),t("td",X,[t("div",tt,[o(N,{align:"center",width:"48"},{trigger:s(()=>[et]),content:s(()=>[o(D,{href:r.route("jobcard-checklist.edit",{id:e.id})},{svg:s(()=>[st]),text:s(()=>[ot]),_:2},1032,["href"]),t("button",{type:"button",onClick:L=>B(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},rt,8,at)]),_:2},1024)])])]))),128))])):(a(),i("tbody",nt,dt))])])]),l.data.data&&l.data.data.length>0?(a(),g(A,{key:0,class:"mt-6",links:l.data.links},null,8,["links"])):y("",!0)]),o(z,{show:d.value,onClose:_},{default:s(()=>[t("div",_t,[mt,t("div",ht,[o(S,{onClick:_},{default:s(()=>[n(" Cancel ")]),_:1}),o(V,{class:"ml-3",onClick:$},{default:s(()=>[n(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{Lt as default};
