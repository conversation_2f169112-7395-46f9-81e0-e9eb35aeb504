<template>
    <div class="responsive-table-container">
        <!-- Desktop Table View -->
        <div class="hidden lg:block">
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table class="min-w-full divide-y divide-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th v-for="header in headers" :key="header.key" 
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {{ header.label }}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <slot name="desktop-rows" />
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Mobile Card View -->
        <div class="lg:hidden space-y-4">
            <slot name="mobile-cards" />
        </div>
    </div>
</template>

<script setup>
defineProps({
    headers: {
        type: Array,
        required: true,
        default: () => []
    }
});
</script>

<style scoped>
.responsive-table-container {
    @apply w-full;
}

/* Mobile card styling */
@media (max-width: 1023px) {
    .mobile-card {
        @apply bg-white rounded-lg shadow-sm border p-4 space-y-3;
    }
    
    .mobile-card-row {
        @apply flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0;
    }
    
    .mobile-card-label {
        @apply text-sm font-medium text-gray-500;
    }
    
    .mobile-card-value {
        @apply text-sm text-gray-900 text-right;
    }
}
</style>
