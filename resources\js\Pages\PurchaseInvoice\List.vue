<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import ActionLink from '@/Components/ActionLink.vue';
import CreateButton from '@/Components/CreateButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import InputError from '@/Components/InputError.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import Modal from '@/Components/Modal.vue';
import SwitchButton from '@/Components/SwitchButton.vue';
import Pagination from '@/Components/Pagination.vue';
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import SearchableDropdown from '@/Components/SearchableDropdown.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import Dropdown from '@/Components/Dropdown.vue';
import RadioButton from '@/Components/RadioButton.vue';
import DangerButton from '@/Components/DangerButton.vue';
import { Head , useForm, usePage} from '@inertiajs/vue3';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import InputLabel from '@/Components/InputLabel.vue';
import ArrowIcon from '@/Components/ArrowIcon.vue';
import { sortAndSearch } from '@/Composables/sortAndSearch.js';

const props = defineProps(['data', 'permissions', 'organization', 'companies', 'types', 'typeId', 'paymentType', 'bankinfo', 'organizationId', 'companyId']);
const { form, search, sort, fetchData, sortKey, sortDirection, updateParams } = sortAndSearch('purchaseinvoice.index',{
    organization_id: props.organizationId,
    company_id: props.companyId,
    type: props.typeId,
});

const filePath = usePage().props.filepath.view;
const invoiceData = ref([]);
// const form = useForm({});

const modalVisible = ref(false);
const makePaymentModal = ref(false);
const modalMaxWidth = ref('custom');
const paymentReceiveWidth = ref('custom2');
const payment_type = ref('');

const bankinfo = ref([]);
const creditData = ref([]);
const totalUnusedAmount = ref('');

const creditOption = ref('No');

const columns = [
    { field: 'customer_invoice_no',             label: 'INVOICE NUMBER',   sortable: true },
    { field: 'purchaseOrder.po_number',        label: 'PO NUMBER',        sortable: true },
    { field: 'type',                           label: 'TYPE',             sortable: true },
    { field: 'purchaseOrder.company.name',     label: 'COMPANY NAME',     sortable: true },
    { field: 'customer_invoice_date',           label: 'DATE',             sortable: true },
    { field: 'total_amount',                    label: 'AMOUNT (₹)',       sortable: true },
    { field: 'paid_amount',                     label: 'PAID AMOUNT (₹)',  sortable: true },
    { field: 'status',                          label: 'STATUS',           sortable: true },
    { field: 'action',                          label: 'ACTION',           sortable: false },
];

const radioOptions = [
    { value: 'Yes', label: 'Yes' },
    { value: 'No', label: 'No' }
];

const paymentform = computed(() => ({
    organization_id: '',
    company_id: '',
    org_bank_id: '',
    purchase_order_receive_id: '',
    invoice_no: '',
    payment_type: '',
    amount: '',
    check_number: '',
    date: '',
    note: '',
    invoice_amount: ''
}));


const openMakePaymentModal = (id, organization_id) => {
    paymentform.value.organization_id = '';
    paymentform.value.company_id = '';
    paymentform.value.org_bank_id = '';
    paymentform.value.purchase_order_receive_id = '';
    paymentform.value.invoice_no = '';
    paymentform.value.payment_type = '';
    paymentform.value.amount = '';
    paymentform.value.check_number = '';
    paymentform.value.date = '';
    paymentform.value.note = '';
    paymentform.value.invoice_amount = '';
    const invoice = props.data.data.find(invoice  => invoice.id === id);
    invoiceData.value = invoice
    const credit = invoiceData.value.purchase_order.credit.filter(customer => customer.organization_id === organization_id);
    creditData.value = credit
    makePaymentModal.value = true;
    paymentform.value.invoice_amount = invoiceData.value.total_amount;
    paymentform.value.company_id = invoiceData.value.purchase_order.company_id;
    paymentform.value.purchase_order_receive_id = invoiceData.value.id;
    paymentform.value.invoice_no = invoiceData.value.customer_invoice_no;
    paymentform.value.organization_id = invoiceData.value.purchase_order.organization_id;
    const bank = props.bankinfo.filter(bank  => bank.organization_id === organization_id);
    bankinfo.value = bank;
    totalUnusedAmount.value = creditData.value.reduce((sum, item) => sum + item.unused_amount, 0);
};

const totalAmountToCredit = computed(() => {
    return creditData.value.reduce((total, product) => {
        return total +  (product.amount_to_credit ? parseFloat(product.amount_to_credit) : 0);
    }, 0);
});

const setPaymentType = (id, name) => {
    paymentform.value.payment_type = id;
    payment_type.value = name;
    form.errors[`data.payment_type`] = null;

    if (name === "Cash") {
        paymentform.value.note = "Cash";
    } else if (paymentform.value.note === "Cash") {
        paymentform.value.note = "";
    }
};

const setBankInfo = (id, name) => {
    paymentform.value.org_bank_id = id;
    form.errors[`data.org_bank_id`] = null;
};

const acceptPayment = () => {
    form.post(route('purchaseinvoice.makepayment',{ form : paymentform.value, is_credit: creditOption.value, credit_data: creditData.value }), {
        onSuccess: () => {
            form.reset();
            makePaymentModal.value = false;
        },
        onError: (errors) => {
            // console.log(errors);
        }
    });
};

const paymentReceiveCloseModal = () => {
    makePaymentModal.value = false;
};

const closeModal = () => {
    modalVisible.value = false;
};

const organizationId = ref(props.organizationId);
const companyId = ref(props.companyId);
const type = ref(props.typeId);
const searchValue = ref('');

watch([organizationId, companyId ], () => {
    updateParams({
        organization_id: organizationId.value,
        company_id: companyId.value,
    });
});

const handleSearchChange = (value, organizationId, companyId, type) => {
    searchValue.value = value;
    form.get(route('purchaseinvoice.index',{search:value,  organization_id: organizationId,  company_id: companyId, type: type}),  {
        preserveState: true,
        // replace: true,
    });
};

const deleteModal = ref(false);
const selectedInvoiceId = ref(null);

const openDeleteModal = (userId) => {
  selectedInvoiceId.value = userId;
  deleteModal.value = true;
};

const closeDeleteModal = () => {
    deleteModal.value = false;
};

const deleteInvoice = () => {
    form.delete(route('purchaseinvoice.destroy',{id:selectedInvoiceId.value}), {
        onSuccess: () => closeDeleteModal()
    });
};

const setOrganization = (id, name) => {
    organizationId.value = id;
    handleSearchChange(searchValue.value , organizationId.value, companyId.value, type.value);
};

const setCompany = (id, name) => {
    companyId.value = id;
    handleSearchChange(searchValue.value , organizationId.value , companyId.value, type.value);
};

const setType = (id, name) => {
    type.value = id;
    handleSearchChange(searchValue.value , organizationId.value , companyId.value, type.value);
};

const getStatusBgClass = (status) => {
    switch (status) {
        case 'Unpaid':
            return 'bg-blue-100';
        case 'Partially Paid':
            return 'bg-yellow-100';
        case 'Paid':
            return 'bg-green-100';
        default:
            return 'bg-gray-100';
    }
};

const getStatusClass = (status) => {
    switch (status) {
        case 'Unpaid':
            return 'text-blue-600';
        case 'Partially Paid':
            return 'text-yellow-600';
        case 'Paid':
            return 'text-green-600';
        default:
            return 'text-gray-600';
    }
};

const generatePDF = () => {
    const doc = new jsPDF();
    const pdfContent = document.getElementById('pdf-content');

    // Render the HTML content to a canvas
    html2canvas(pdfContent, { scale: 3 }).then((canvas) => {
        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 190; // Adjust the width as needed
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        doc.addImage(imgData, 'PNG', 10, 10, imgWidth, imgHeight);
        doc.save('invoice.pdf');
    });
};

const clearError = (fieldName) => {
    form.errors[fieldName] = null;
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split('.');
    let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== '') {
        lastThree = ',' + lastThree;
    }
    let formattedIntegerPart = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};


</script>

<template>
    <Head title="Purchase Invoice"/>

    <AdminLayout>
        <div class="animate-top">
            <div class="flex justify-between items-center">
                <div class="items-start">
                    <h1 class="text-2xl font-semibold leading-7 text-gray-900">Purchase Invoice</h1>
                </div>
                <div class="flex justify-end">
                    <div class="flex space-x-6 mt-4 sm:mt-0 w-64">
                        <div class="flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full">
                            <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd"></path></svg>
                                <input id="search-field"  @input="handleSearchChange($event.target.value, organizationId, companyId, type)" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search">
                                <!-- <input id="search-field" v-model="search" @input="fetchData" class="rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm" placeholder="Search..." type="search" name="search"> -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                <div class="flex mb-2">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 6h18M4 10h16M5 14h14M6 18h12" />
                    </svg>
                    <InputLabel for="customer_id" value="Filters" />
                </div>
                <div class="grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12">
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Organization Name" />
                        <div class="relative mt-2">
                            <SimpleDropdown :options="organization"
                            v-model="organizationId"
                            @onchange="setOrganization"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Company Name" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="companies"
                            v-model="companyId"
                            @onchange="setCompany"
                            />
                        </div>
                    </div>
                    <div class="sm:col-span-4">
                        <InputLabel for="customer_id" value="Purchase Type" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="types"
                            v-model="type"
                            @onchange="setType"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-8 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                            <tr class="border-b-2">
                                <th v-for="(column, index) in columns" :key="index" scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer" @click="sort(column.field, column.sortable)">
                                    {{ column.label }}
                                    <ArrowIcon :isSorted="sortKey === column.field" :direction="sortDirection" v-if="column.sortable" />
                                </th>
                            </tr>
                        </thead>
                        <tbody v-if="data.data && (data.data.length > 0)">
                            <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="(poData, index) in data.data" :key="poData.id">
                                <td class="px-4 py-2.5 min-w-44">
                                    {{ poData.customer_invoice_no }}
                                </td>
                                <td class="px-4 py-2.5 min-w-44">
                                    {{ poData.purchase_order?.po_number ?? '-' }}
                                </td>
                                <td class="px-4 py-2.5">
                                    {{ poData.type }}
                                </td>
                                <td scope="row" class="px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap">
                                {{ poData.purchase_order?.company?.name ?? "" }}
                                </td>
                                <td class="px-4 py-2.5 min-w-32">
                                    {{ formatDate(poData.customer_invoice_date) }}
                                </td>
                                <td class="px-4 py-2.5 min-w-32">
                                    {{ formatAmount(poData.total_amount)}}
                                </td>
                                <td class="px-4 py-2.5 min-w-40">
                                {{ formatAmount(poData.paid_amount)}}
                                </td>
                                <td class="flex flex-1 items-center px-4 py-2.5">
                                    <div class="flex rounded-full px-4 py-1" :class="getStatusBgClass(poData.status)">
                                        <span class="text-sm font-semibold whitespace-nowrap" :class="getStatusClass(poData.status)">{{ poData.status }}</span>
                                    </div>
                                </td>
                                <td class="items-center px-4 py-2.5">
                                    <div class="flex items-center justify-start gap-4">
                                        <Dropdown align="right" width="48">
                                            <template #trigger>
                                                <button type="button" title="Open details" class="p-1 rounded hover:bg-gray-100 focus:bg-gray-100">
                                                    <svg viewBox="0 0 24 24" class="w-4 h-4 fill-current">
                                                        <path d="M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"></path>
                                                    </svg>
                                                </button>
                                            </template>

                                            <template #content>
                                                <ActionLink :href="route('purchaseinvoice.view',{id:poData.id, source: 'purchaseinvoice.index'})" v-if="permissions.canViewPurchaseInvoice">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            View Invoice
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <ActionLink  :href="route('purchaseinvoice.edit',{id:poData.id})" v-if="permissions.canEditPurchaseInvoice">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                                                                />
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Receive Product
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <!-- <button type="button"  @click="openMakePaymentModal(poData.id, poData.purchase_order.organization_id)"  class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-5 w-5">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"></path>
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Make Payment
                                                    </span>
                                                </button> -->
                                                <ActionLink v-if="poData.type === 'challan' && permissions.canEditPurchaseInvoice" :href="route('purchaseinvoice.convert-to-invoice', poData.id)">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m6.75 12l-3-3m0 0l-3 3m3-3v6m-1.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Convert to Invoice
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <ActionLink v-if="poData.purchase_order_receive_details.length > 0 && permissions.canEditPurchaseInvoice && poData.type === 'invoice'" :href="route('debitnote.add', poData.id)">
                                                    <template #svg>
                                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25M8.25 21l4.5-4.5L21 8.25l-1.5-1.5L12 14.25 8.25 18M3 16.5l2.25 2.25L12 12" />
                                                        </svg>
                                                    </template>
                                                    <template #text>
                                                        <span class="text-sm text-gray-700 leading-5">
                                                            Create Debit Note
                                                        </span>
                                                    </template>
                                                </ActionLink>
                                                <button type="button" v-if="poData.purchase_order_receive_details.length == 0" @click="openDeleteModal(poData.id)" class="flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5" x-tooltip="tooltip">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                        d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                                        />
                                                    </svg>
                                                    <span class="text-sm text-gray-700 leading-5">
                                                        Delete
                                                    </span>
                                                </button>

                                            </template>
                                        </Dropdown>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr class="bg-white">
                            <td colspan="9" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                No data found.
                            </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <Pagination v-if="data.data && (data.data.length > 0)" class="mt-6" :links="data.links"></Pagination>
        </div>
        <Modal :show="deleteModal" @close="closeDeleteModal">
              <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to delete this Invoice?
                </h2>
                <div class="mt-6 flex justify-end">
                    <SecondaryButton @click="closeDeleteModal"> Cancel </SecondaryButton>
                    <DangerButton
                        class="ml-3"
                        @click="deleteInvoice"
                    >
                        Delete
                    </DangerButton>
                </div>
            </div>
        </Modal>
        <Modal :show="makePaymentModal" @close="paymentReceiveCloseModal" :maxWidth="paymentReceiveWidth">
             <div class="p-6">
                <div class="flex items-center justify-between">
                    <h2 class="text-2xl font-semibold leading-7 text-gray-900">Make Payment</h2>
                    <div class="text-base font-semibold leading-6 text-gray-900" v-if="creditData.length > 0">
                        Credits Available:  ₹{{ formatAmount(totalUnusedAmount) }}
                    </div>
                </div>
                    <div class="border-b border-gray-900/10 pb-12">
                        <div class="mt-4 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                            <div class="sm:col-span-3">
                                <div class="inline-flex items-center justify-start w-full space-x-3">
                                    <InputLabel for="role_id" value="Invoice No:" />
                                    <p class="text-sm font-semibold text-gray-700">
                                    {{ paymentform.invoice_no }}
                                    </p>
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <div class="inline-flex items-center justify-start w-full space-x-3">
                                    <InputLabel for="role_id" value="Total Amount (₹):" />
                                    <p class="text-sm font-semibold text-gray-700">
                                    {{ formatAmount(paymentform.invoice_amount) }}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-5 sm:col-span-4">
                            <InputLabel for="role_id" value="Payment Through Credit ?" />
                            <div class="relative mt-2">
                                <RadioButton
                                    v-model="creditOption"
                                    :options="radioOptions"
                                />
                            </div>
                        </div>
                        <div v-if="creditOption =='No'" class="mt-5 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6">
                            <div class="sm:col-span-3">
                                <InputLabel for="role_id" value="Payment Type" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="paymentType"
                                    v-model="paymentform.payment_type"
                                    @onchange="setPaymentType"
                                    :class="{ 'error rounded-md': form.errors[`data.payment_type`] }"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="amount" value="Amount" />
                                <TextInput
                                    id="amount"
                                    type="text"
                                    @change="clearError('data.amount')"
                                    v-model="paymentform.amount"
                                    :class="{ 'error rounded-md': form.errors[`data.amount`] }"
                                />
                            </div>
                            <div v-if="payment_type == 'Cheque'" class="sm:col-span-3">
                                <InputLabel for="check_number" value="Cheque Number" />
                                <TextInput
                                    id="check_number"
                                    type="text"
                                    v-model="paymentform.check_number"
                                    :class="{ 'error rounded-md': form.errors[`data.check_number`] }"
                                />
                            </div>
                            <div class="sm:col-span-3">
                                <InputLabel for="date" value="Payment Date" />
                                <input
                                    v-model="paymentform.date"
                                    class="mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                    type="date"
                                     @change="clearError('data.date')"
                                    :class="{ 'error rounded-md': form.errors[`data.date`] }"
                                />
                            </div>
                            <div v-if="payment_type != 'Cash'" class="sm:col-span-3">
                                <InputLabel for="org_bank_id" value="Our Bank" />
                                <div class="relative mt-2">
                                    <SearchableDropdown :options="bankinfo"
                                    v-model="paymentform.org_bank_id"
                                    @onchange="setBankInfo"
                                    :class="{ 'error rounded-md': form.errors[`data.org_bank_id`] }"
                                    />
                                </div>
                            </div>
                            <div class="sm:col-span-6">
                                <InputLabel for="note" value="Note" />
                                <TextArea
                                    id="note"
                                    type="text"
                                    :rows="2"
                                    v-model="paymentform.note"
                                />
                            </div>
                        </div>
                        <div v-else>
                            <table class="mt-5 overflow-x-auto divide-y divide-gray-300 w-full"  v-if="creditData.length > 0">
                                <thead>
                                    <tr>
                                        <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Date</th>
                                        <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Bank</th>
                                        <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Credit Amount (₹)</th>
                                        <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Available Credit (₹)</th>
                                        <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Amount To Credit (₹)</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-300 bg-white">
                                    <tr v-for="(product, index)  in creditData" :key="index">
                                        <td class="whitespace-nowrap py-3 text-sm text-gray-900">{{ formatDate(product.date) }}</td>
                                        <td class="whitespace-nowrap py-3 text-sm text-gray-900">
                                            <div class="flex flex-col">
                                                <div class="text-sm text-gray-900">
                                                    {{ product.paymentpaid?.bank_info?.bank_name ? product.paymentpaid?.bank_info?.bank_name : 'Cash'  }}
                                                </div>
                                                <div class="text-sm text-gray-900">
                                                    {{ product.paymentpaid?.bank_info?.account_number ? product.paymentpaid?.bank_info?.bank_name : 'Cash' }}
                                                </div>
                                            </div>
                                        </td>
                                        <td class="whitespace-nowrap py-3 text-sm text-gray-900">{{ formatAmount(product.amount) }}</td>
                                        <td class="whitespace-nowrap py-3 text-sm text-gray-900">{{ formatAmount(product.unused_amount) }}</td>
                                        <td class="whitespace-nowrap py-3 text-sm text-gray-900" style="width: 22%;">
                                            <TextInput
                                                id="amount_to_credit"
                                                type="text"
                                                v-model="product.amount_to_credit"
                                                @change="clearError('creditData.' + index + '.amount_to_credit')"
                                                :class="{ 'error': form.errors[`creditData.${index}.amount_to_credit`] }"
                                            />
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="mt-4 flex justify-end">
                                <p class="text-base font-semibold">Total Amount To Credit: {{ formatAmount(totalAmountToCredit) }}</p>
                            </div>
                        </div>
                    </div>
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="paymentReceiveCloseModal"> Cancel </SecondaryButton>
                    <div class="w-36">
                    <PrimaryButton
                        class="ml-3 w-20"
                        @click="acceptPayment"
                    >
                    Save
                    </PrimaryButton>
                    </div>
                </div>
             </div>
        </Modal>
    </AdminLayout>
</template>

<style scoped>
    .error {
        border: 1px solid red;
    }
    .container1 {
        max-width: 800px;
    }
    .container1 p {
        font-size: 12px;
    }
    #pdf-content table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
    #pdf-content th, #pdf-content td{
        border: 0.5px solid rgb(55 65 81);;
        padding: 6px 4px;
        text-align: left;
        font-size: 12px;
    }
    #pdf-content th {
        background-color: #f2f2f2;
        font-size: 13px;
    }
</style>


