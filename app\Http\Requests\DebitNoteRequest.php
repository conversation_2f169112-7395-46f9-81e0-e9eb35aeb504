<?php

namespace App\Http\Requests;

use App\Models\PurchaseOrderReceiveDetails;
use App\Models\SerialNumbers;
use Illuminate\Foundation\Http\FormRequest;

class DebitNoteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'reason' => 'required|string',
            'credit_note_number' => 'required|string',
            'invoicedProduct.*.qty' => 'required|integer'
        ];

        return $rules;
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $invoicedProducts = $this->input('selectedProductItem');
            $serialNumberIds = [];

            foreach ($invoicedProducts as $key => $product) {
                $purchaseDetails = PurchaseOrderReceiveDetails::find($product['purchase_detail_id']);
                if ($product['qty'] > $purchaseDetails->receive_qty) {
                    $validator->errors()->add("selectedProductItem.$key.qty", __('Qty not Match'));
                }
            }
        });
    }
}
