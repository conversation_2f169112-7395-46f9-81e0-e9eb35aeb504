<?php

namespace App\Http\Requests;

use App\Models\SerialNumbers;
use Illuminate\Foundation\Http\FormRequest;

class DebitNoteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'reason' => 'required|string',
            'credit_note_number' => 'required|string',
            'selectedProductItem.*.qty' => 'required|integer|min:1',
            'selectedProductItem.*.serial_number_id' => 'required|integer|exists:serial_numbers,id',
            'selectedProductItem.*.purchase_detail_id' => 'required|integer|exists:purchase_order_receive_details,id'
        ];

        return $rules;
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $selectedProducts = $this->input('selectedProductItem', []);

            foreach ($selectedProducts as $key => $product) {
                // Check if serial number exists and has available quantity
                $serialNumber = SerialNumbers::find($product['serial_number_id']);
                if ($serialNumber) {
                    $availableQty = $serialNumber->receive_qty - $serialNumber->sell_qty;
                    if ($product['qty'] > $availableQty) {
                        $validator->errors()->add("selectedProductItem.$key.qty",
                            "Quantity cannot exceed available quantity of {$availableQty}");
                    }
                }
            }
        });
    }
}
