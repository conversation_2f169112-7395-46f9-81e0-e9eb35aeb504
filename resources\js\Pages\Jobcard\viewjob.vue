<script setup>
import { ref } from 'vue';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import CreateButton from '@/Components/CreateButton.vue';
import InputLabel from '@/Components/InputLabel.vue';
import CheckboxWithLabel from '@/Components/CheckboxWithLabel.vue';
import Modal from '@/Components/Modal.vue';
import FileViewer from '@/Components/FileViewer.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import { Head , usePage} from '@inertiajs/vue3';

const props = defineProps(['data', 'checklist', 'filePath']);

const checkedValues = ref([]);
const userData = usePage().props.data[0];
checkedValues.value = userData.job_card_checks.map(check => check.job_card_checklist_id);

const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
};

// Document preview modal state
const documentPreviewModal = ref(false);
const selectedDocument = ref('');

// Document preview functionality
const openPreviewModal = (documentName) => {
    selectedDocument.value = documentName;
    documentPreviewModal.value = true;
};

const closeDocumentPreviewModal = () => {
    documentPreviewModal.value = false;
    selectedDocument.value = '';
};

</script>

<template>
    <Head title="Jobcard"/>

    <AdminLayout>
        <div class="animate-top h-screen">
        <form @submit.prevent="submit" class="">
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <h1 class="text-2xl font-semibold leading-7 text-gray-900">Jobcard Detail</h1>
            </div>
            <div class="flex items-center space-x-4">
                <div>
                    <!-- <p class="text-sm font-semibold text-gray-900">{{ data[0].organization.name }}</p> -->
                </div>
                <div class="flex justify-end w-20">
                    <CreateButton :href="route('jobcard.index')">
                        Back
                    </CreateButton>
                </div>
            </div>
        </div>
        <div class="mt-6 shadow ring-1 space-y-1 ring-black ring-opacity-5 sm:rounded-lg bg-white p-6">
            <div class="inline-flex items-start space-x-6 justify-start w-full">
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Hospital Name:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].hospital_name ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Contact No:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].contact_no ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Address:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].address ?? '-'}}</p>
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Jobcard Number:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].job_card_number ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Date:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ formatDate(data[0].date) ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900">Engineer Name:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].users.first_name ?? '-'}} {{ data[0].users.last_name ?? '-'}}</p>
                    </div>
                    <div class="inline-flex items-center justify-start w-full space-x-2">
                        <p class="text-sm font-semibold text-gray-900 w-28">Job Status:</p>
                        <p class="text-sm leading-6 text-gray-700">{{ data[0].job_status ?? '-'}}</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-6 bg-white p-4 shadow sm:p-6 sm:rounded-lg border overflow-x-auto">
            <table class="overflow-x-auto divide-y divide-gray-300 w-full">
                <thead>
                    <tr>
                        <th scope="col" class="py-3.5 pr-3 text-left text-sm font-semibold text-gray-900">Equipment</th>
                        <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Model</th>
                        <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Serial No</th>
                    </tr>
                </thead>
                 <tbody class="divide-y divide-gray-300 bg-white">
                    <tr>
                        <td class="whitespace-nowrap pr-4 py-3 text-sm text-gray-900">{{ data[0].product_name ?? '-' }}</td>
                        <td class="whitespace-nowrap px-4 py-3 text-sm text-gray-900">{{ data[0].product_code ?? '-' }}</td>
                        <td class="whitespace-nowrap px-4 py-3 text-sm text-gray-900">{{ data[0].serial_no ?? '-'}}</td>
                    </tr>
                 </tbody>
            </table>
            <div class="mt-6 space-y-1">
                <div class="inline-flex items-center justify-start w-full space-x-2">
                    <p class="text-sm font-semibold text-gray-900">Problem Description:</p>
                    <p class="text-sm leading-6 text-gray-700">{{ data[0].problem_description ?? '-'}}</p>
                </div>
                <div class="inline-flex items-center justify-start w-full space-x-2">
                    <p class="text-sm font-semibold text-gray-900">Parts Required:</p>
                    <p class="text-sm leading-6 text-gray-700">{{ data[0].parts_required ?? '-'}}</p>
                </div>
                <div class="flex flex-col lg:flex-row lg:space-x-8 space-y-4 lg:space-y-0">
                    <!-- Warranty Status - Left Side -->
                    <div class="flex-shrink-0 lg:w-1/3">
                        <p class="text-sm font-semibold text-gray-900 mb-2">Warranty Status:</p>
                        <div class="space-y-2">
                            <div class="flex items-center space-x-2">
                                <div class="flex items-center space-x-1">
                                    <div class="w-4 h-4 rounded-full border-2 border-gray-300 flex items-center justify-center">
                                        <div v-if="data[0].warranty_status === 'warranty'" class="w-2 h-2 rounded-full bg-indigo-600"></div>
                                    </div>
                                    <span class="text-sm text-gray-700">Warranty</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="flex items-center space-x-1">
                                    <div class="w-4 h-4 rounded-full border-2 border-gray-300 flex items-center justify-center">
                                        <div v-if="data[0].warranty_status === 'out_of_warranty'" class="w-2 h-2 rounded-full bg-indigo-600"></div>
                                    </div>
                                    <span class="text-sm text-gray-700">Out of Warranty</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="flex items-center space-x-1">
                                    <div class="w-4 h-4 rounded-full border-2 border-gray-300 flex items-center justify-center">
                                        <div v-if="data[0].warranty_status === 'amc'" class="w-2 h-2 rounded-full bg-indigo-600"></div>
                                    </div>
                                    <span class="text-sm text-gray-700">AMC</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="flex items-center space-x-1">
                                    <div class="w-4 h-4 rounded-full border-2 border-gray-300 flex items-center justify-center">
                                        <div v-if="data[0].warranty_status === 'cmc'" class="w-2 h-2 rounded-full bg-indigo-600"></div>
                                    </div>
                                    <span class="text-sm text-gray-700">CMC</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Checklist - Right Side -->
                    <div class="flex-1">
                        <InputLabel for="engineer_id" value="Checklist :" />
                        <div class="grid sm:grid-cols-6 relative mt-2">
                            <CheckboxWithLabel
                            v-for="item in checklist"
                            :key="item.id"
                            :checked="checkedValues"
                            :value="item.id"
                            :label="item.type"
                            @update:checked="updateChecked"
                            />
                        </div>
                    </div>
                </div>
                <div class="inline-flex items-center justify-start w-full space-x-2">
                    <p class="text-sm font-semibold text-gray-900">Close Note:</p>
                    <p class="text-sm leading-6 text-gray-700">{{ data[0].close_note ?? '-'}}</p>
                </div>

                <div class="inline-flex items-center justify-start w-full space-x-2">
                    <p class="text-sm font-semibold text-gray-900">Close Date:</p>
                    <p class="text-sm leading-6 text-gray-700">{{ data[0]?.close_date ? formatDate(data[0].close_date) : '-'}}</p>
                </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-4">
                            <!-- Display existing quotation documents -->
                            <div class="bg-white p-1 shadow sm:rounded-lg border" v-if="userData.quotation_documents && userData.quotation_documents.length > 0">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">QUOTATION DOCUMENT</th>
                                            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">ACTION</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-300 bg-white">
                                        <tr v-for="document in userData.quotation_documents" :key="document.id">
                                            <td class="whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ document.orignal_name }}
                                            </td>
                                            <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center">
                                                <button type="button" @click="openPreviewModal(document.name)">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path></svg>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!-- Display existing PO documents -->
                            <div class="bg-white p-1 shadow sm:rounded-lg border" v-if="userData.po_documents && userData.po_documents.length > 0">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">PO DOCUMENT</th>
                                            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">ACTION</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-300 bg-white">
                                        <tr v-for="document in userData.po_documents" :key="document.id">
                                            <td class="whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6">{{ document.orignal_name }}
                                            </td>
                                            <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center">
                                                <button type="button" @click="openPreviewModal(document.name)">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6" x-tooltip="tooltip"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path></svg>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
        </div>
        </form>
        </div>

        <!-- Document Preview Modal -->
        <Modal :show="documentPreviewModal" @close="closeDocumentPreviewModal" maxWidth="xl">
            <div class="p-6">
                <FileViewer :fileUrl="filePath?.view + selectedDocument" />
                <div class="mt-6 px-4 flex justify-end">
                    <SecondaryButton @click="closeDocumentPreviewModal">Close</SecondaryButton>
                </div>
            </div>
        </Modal>
    </AdminLayout>

</template>
