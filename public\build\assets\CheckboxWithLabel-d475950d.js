import{j as d,o as i,c as n,k as u,D as p,b as l,t as h}from"./app-6a429cee.js";/* empty css                                                                          */import{_ as m}from"./_plugin-vue_export-helper-c27b6911.js";const f={class:"flex items-center space-x-2 mt-2 sm:col-span-3"},k=["value"],v={class:"font-sm text-gray-900 font-medium"},x={__name:"CheckboxWithLabel",props:{checked:{type:Array,default:()=>[]},value:{default:null},label:{type:String,default:""}},emits:["update:checked"],setup(s,{emit:r}){const e=s,c=d({get(){return Array.isArray(e.checked)?e.checked.includes(e.value):!1},set(o){const t=Array.isArray(e.checked)?[...e.checked]:[];if(o)t.includes(e.value)||t.push(e.value);else{const a=t.indexOf(e.value);a>-1&&t.splice(a,1)}r("update:checked",t)}});return(o,t)=>(i(),n("label",f,[u(l("input",{type:"checkbox",value:s.value,"onUpdate:modelValue":t[0]||(t[0]=a=>c.value=a),class:"rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"},null,8,k),[[p,c.value]]),l("div",v,h(s.label),1)]))}},g=m(x,[["__scopeId","data-v-2aea42c0"]]);export{g as C};
