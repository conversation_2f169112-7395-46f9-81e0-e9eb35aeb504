import{h as d,o as m,e as l,w as r,a as e,u as o,Z as c,b as t,g as p,n as u,d as f}from"./app-b7a94f67.js";import{G as _}from"./GuestLayout-459b332f.js";import{_ as w}from"./InputError-86b88c86.js";import{_ as g}from"./InputLabel-11b5d690.js";import{P as x}from"./PrimaryButton-4ffecd1c.js";import{_ as h}from"./TextInput-fea73171.js";import"./_plugin-vue_export-helper-c27b6911.js";const y=t("h2",{class:"text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"},"Confirm Password",-1),b=t("div",{class:"mb-4 mt-2 text-sm text-gray-500"}," This is a secure area of the application. Please confirm your password before continuing. ",-1),P=["onSubmit"],C={class:"flex justify-end mt-4"},j={__name:"ConfirmPassword",setup(V){const s=d({password:""}),i=()=>{s.post(route("password.confirm"),{onFinish:()=>s.reset()})};return(v,a)=>(m(),l(_,null,{default:r(()=>[e(o(c),{title:"Confirm Password"}),y,b,t("form",{onSubmit:f(i,["prevent"])},[t("div",null,[e(g,{for:"password",value:"Password"}),e(h,{id:"password",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:o(s).password,"onUpdate:modelValue":a[0]||(a[0]=n=>o(s).password=n),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),e(w,{class:"mt-2",message:o(s).errors.password},null,8,["message"])]),t("div",C,[e(x,{class:u(["",{"opacity-25":o(s).processing}]),disabled:o(s).processing},{default:r(()=>[p(" Confirm ")]),_:1},8,["class","disabled"])])],40,P)]),_:1}))}};export{j as default};
