import{r as u,o as t,c as s,a as _,u as w,w as f,F as m,Z as S,b as e,g as C,i as x,t as d,k as E,v as V,f as A,Y as g}from"./app-4ea19997.js";import{_ as B}from"./AdminLayout-5eccc000.js";import{_ as $}from"./CreateButton-19955a3e.js";/* empty css                                                              */const z={class:"animate-top"},F={class:"flex justify-between items-center"},T=e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Number Setting",-1),j={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},D={class:"flex justify-end w-20"},O={class:"mt-8 overflow-x-auto sm:rounded-lg"},P={class:"shadow sm:rounded-lg"},U={class:"w-full text-sm text-left text-gray-500"},I={class:"text-xs text-gray-700 uppercase bg-gray-50"},L={class:"border-b-2"},M=e("th",{class:"px-4 py-4 text-sm font-semibold text-gray-900"}," Actions ",-1),R={key:0},Y={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},Z={class:"px-4 py-2.5"},q={class:"px-4 py-2.5"},G={key:1},H={class:"px-4 py-2.5"},J=["onClick"],K=["onClick"],Q={key:1},W=e("tr",{class:"bg-white"},[e("td",{colspan:"4",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),X=[W],ae={__name:"NumberSetting",props:{data:Array,permissions:Array},setup(p){const b=p,i=u(null),a=u(""),v=(l,n)=>{i.value=l,a.value=n},k=l=>{if(!a.value||isNaN(a.value)){alert("Please enter a valid number.");return}g.post(route("setting.updateNumber",l.id),{number:a.value},{preserveScroll:!0,onSuccess:()=>{g.reload({only:["data"]}),i.value=null,a.value=null},onError:n=>{console.error("Update failed:",n),alert("Failed to update number. Please try again.")}})},N=u([{label:"Organization Name",field:"organization_name",sortable:!0},{label:"Type",field:"type",sortable:!0},{label:"Number",field:"number",sortable:!0}]);return(l,n)=>(t(),s(m,null,[_(w(S),{title:"Number Setting"}),_(B,null,{default:f(()=>{var h;return[e("div",z,[e("div",F,[T,e("div",j,[e("div",D,[_($,{href:l.route("setting")},{default:f(()=>[C(" Back ")]),_:1},8,["href"])])])]),e("div",O,[e("div",P,[e("table",U,[e("thead",I,[e("tr",L,[(t(!0),s(m,null,x(N.value,(o,r)=>(t(),s("th",{key:r,class:"px-4 py-4 text-sm font-semibold text-gray-900 cursor-pointer"},d(o.label),1))),128)),M])]),(h=b.data)!=null&&h.length?(t(),s("tbody",R,[(t(!0),s(m,null,x(b.data,(o,r)=>{var y;return t(),s("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:o.id},[e("td",Y,d(((y=o.organization)==null?void 0:y.name)??"N/A"),1),e("td",Z,d(o.type),1),e("td",q,[i.value===r?E((t(),s("input",{key:0,"onUpdate:modelValue":n[0]||(n[0]=c=>a.value=c),type:"text",class:"border rounded px-2 py-1 w-24"},null,512)),[[V,a.value]]):(t(),s("span",G,d(o.number),1))]),e("td",H,[i.value===r?(t(),s("button",{key:0,onClick:c=>k(o),class:"text-green-600 font-semibold hover:underline"}," Save ",8,J)):p.permissions.canEditRoles?(t(),s("button",{key:1,onClick:c=>v(r,o.number),class:"text-blue-600 font-semibold hover:underline"}," Edit ",8,K)):A("",!0)])])}),128))])):(t(),s("tbody",Q,X))])])])])]}),_:1})],64))}};export{ae as default};
