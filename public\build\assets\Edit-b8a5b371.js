import{K as b,h as z,o as v,c as f,a,u as o,w as g,F as C,Z as U,b as n,d as $,n as y,f as V,g as w,T as N,s as O,x as T}from"./app-b7a94f67.js";import{_ as S,a as k}from"./AdminLayout-0f1fdf67.js";import{_ as m}from"./InputError-86b88c86.js";import{_ as r}from"./InputLabel-11b5d690.js";import{P as A}from"./PrimaryButton-4ffecd1c.js";import{_ as u}from"./TextInput-fea73171.js";import{_ as E}from"./TextArea-500c5ac8.js";import{_ as c}from"./SearchableDropdown-711fb977.js";import{_ as I}from"./_plugin-vue_export-helper-c27b6911.js";const x=i=>(O("data-v-63bd7ab9"),i=i(),T(),i),h={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},B=x(()=>n("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Customer",-1)),j={class:"border-b border-gray-900/10 pb-12"},D={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},L={class:"sm:col-span-3"},P={class:"sm:col-span-3"},F={class:"sm:col-span-2"},G={class:"relative mt-2"},K={class:"sm:col-span-2"},M={class:"relative mt-2"},Z={class:"sm:col-span-2"},q={class:"relative mt-2"},H={class:"sm:col-span-2"},J={class:"sm:col-span-2"},Q={class:"sm:col-span-2"},R={class:"sm:col-span-2"},W={class:"sm:col-span-2"},X={class:"sm:col-span-2"},Y={class:"relative mt-2"},ee={key:0,class:"sm:col-span-2"},se={class:"relative mt-2"},oe={class:"sm:col-span-6"},te={class:"flex mt-6 items-center justify-between"},ae={class:"ml-auto flex items-center justify-end gap-x-6"},ne=x(()=>n("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),le={key:0,class:"text-sm text-gray-600"},re={__name:"Edit",props:{data:Object,types:Array,customer_type:Array,gst_type:Array,is_organization:Array,organization:Array},setup(i){const l=b().props.data,e=z({id:l.id,customer_name:l.customer_name,customer_type:l.customer_type,gst_type:l.gst_type,person_name:l.person_name,address:l.address,city:l.city,contact_no:l.contact_no,email:l.email,drug_licence_no:l.drug_licence_no,gst_no:l.gst_no,type:l.type,is_organization:l.is_organization,organization_id:l.organization_id}),_=(p,s,t)=>{switch(p){case"customer_type":e.errors.customer_type=null;break;case"gst_type":e.errors.gst_type=null;break;case"type":e.errors.type=null;break;default:console.warn(`Unhandled field: ${p}`)}e[p]=s};return(p,s)=>(v(),f(C,null,[a(o(U),{title:"Customers"}),a(S,null,{default:g(()=>[n("div",h,[B,n("form",{onSubmit:s[23]||(s[23]=$(t=>o(e).patch(p.route("customers.update")),["prevent"]))},[n("div",j,[n("div",D,[n("div",L,[a(r,{for:"customer_name",value:"Customer Name"}),a(u,{id:"customer_name",type:"text",modelValue:o(e).customer_name,"onUpdate:modelValue":s[0]||(s[0]=t=>o(e).customer_name=t),onChange:s[1]||(s[1]=t=>o(e).validate("customer_name"))},null,8,["modelValue"]),a(m,{class:"",message:o(e).errors.customer_name},null,8,["message"])]),n("div",P,[a(r,{for:"person_name",value:"Contact Person Name"}),a(u,{id:"person_name",type:"text",modelValue:o(e).person_name,"onUpdate:modelValue":s[2]||(s[2]=t=>o(e).person_name=t)},null,8,["modelValue"])]),n("div",F,[a(r,{for:"type",value:"Customer Type"}),n("div",G,[a(c,{options:i.customer_type,modelValue:o(e).customer_type,"onUpdate:modelValue":s[3]||(s[3]=t=>o(e).customer_type=t),onOnchange:s[4]||(s[4]=(t,d)=>_("customer_type",t,d)),onChange:s[5]||(s[5]=t=>o(e).validate("customer_type"))},null,8,["options","modelValue"])]),a(m,{class:"",message:o(e).errors.customer_type},null,8,["message"])]),n("div",K,[a(r,{for:"type",value:"GST Type"}),n("div",M,[a(c,{options:i.gst_type,modelValue:o(e).gst_type,"onUpdate:modelValue":s[6]||(s[6]=t=>o(e).gst_type=t),onOnchange:s[7]||(s[7]=(t,d)=>_("gst_type",t,d)),onChange:s[8]||(s[8]=t=>o(e).validate("gst_type"))},null,8,["options","modelValue"])]),a(m,{class:"",message:o(e).errors.gst_type},null,8,["message"])]),n("div",Z,[a(r,{for:"type",value:"Occupation Type"}),n("div",q,[a(c,{options:i.types,modelValue:o(e).type,"onUpdate:modelValue":s[9]||(s[9]=t=>o(e).type=t),onOnchange:s[10]||(s[10]=(t,d)=>_("type",t,d))},null,8,["options","modelValue"])]),a(m,{class:"",message:o(e).errors.type},null,8,["message"])]),n("div",H,[a(r,{for:"email",value:"Email"}),a(u,{id:"email",type:"email",modelValue:o(e).email,"onUpdate:modelValue":s[11]||(s[11]=t=>o(e).email=t)},null,8,["modelValue"]),a(m,{class:"",message:o(e).errors.email},null,8,["message"])]),n("div",J,[a(r,{for:"contact_no",value:"Contact No"}),a(u,{id:"contact_no",type:"text",numeric:!0,maxLength:"10",modelValue:o(e).contact_no,"onUpdate:modelValue":s[12]||(s[12]=t=>o(e).contact_no=t),onChange:s[13]||(s[13]=t=>o(e).validate("contact_no"))},null,8,["modelValue"]),a(m,{class:"",message:o(e).errors.contact_no},null,8,["message"])]),n("div",Q,[a(r,{for:"drug_licence_no",value:"Drug Licence No"}),a(u,{id:"drug_licence_no",type:"text",modelValue:o(e).drug_licence_no,"onUpdate:modelValue":s[14]||(s[14]=t=>o(e).drug_licence_no=t)},null,8,["modelValue"])]),n("div",R,[a(r,{for:"gst_no",value:"GST No"}),a(u,{id:"gst_no",type:"text",maxLength:"15",modelValue:o(e).gst_no,"onUpdate:modelValue":s[15]||(s[15]=t=>o(e).gst_no=t),class:y({"error rounded-md":o(e).errors.gst_no})},null,8,["modelValue","class"])]),n("div",W,[a(r,{for:"city",value:"City"}),a(u,{id:"city",type:"text",modelValue:o(e).city,"onUpdate:modelValue":s[16]||(s[16]=t=>o(e).city=t),class:y({"error rounded-md":o(e).errors.city})},null,8,["modelValue","class"])]),n("div",X,[a(r,{for:"type",value:"Is Organization ?"}),n("div",Y,[a(c,{options:i.is_organization,modelValue:o(e).is_organization,"onUpdate:modelValue":s[17]||(s[17]=t=>o(e).is_organization=t),onOnchange:s[18]||(s[18]=(t,d)=>_("is_organization",t,d)),class:y({"error rounded-md":o(e).errors.is_organization})},null,8,["options","modelValue","class"])])]),o(l).is_organization=="yes"||o(e).is_organization=="yes"?(v(),f("div",ee,[a(r,{for:"type",value:"Organization Name"}),n("div",se,[a(c,{options:i.organization,modelValue:o(e).organization_id,"onUpdate:modelValue":s[19]||(s[19]=t=>o(e).organization_id=t),onOnchange:s[20]||(s[20]=(t,d)=>_("organization_id",t,d)),class:y({"error rounded-md":o(e).errors.organization_id})},null,8,["options","modelValue","class"])])])):V("",!0),n("div",oe,[a(r,{for:"address",value:"Address"}),a(E,{id:"address",type:"text",rows:4,modelValue:o(e).address,"onUpdate:modelValue":s[21]||(s[21]=t=>o(e).address=t),onChange:s[22]||(s[22]=t=>o(e).validate("address"))},null,8,["modelValue"]),a(m,{class:"",message:o(e).errors.address},null,8,["message"])])])]),n("div",te,[n("div",ae,[a(k,{href:p.route("customers.index")},{svg:g(()=>[ne]),_:1},8,["href"]),a(A,{disabled:o(e).processing},{default:g(()=>[w("Update")]),_:1},8,["disabled"]),a(N,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:g(()=>[o(e).recentlySuccessful?(v(),f("p",le,"Saved.")):V("",!0)]),_:1})])])],32)])]),_:1})],64))}},ve=I(re,[["__scopeId","data-v-63bd7ab9"]]);export{ve as default};
