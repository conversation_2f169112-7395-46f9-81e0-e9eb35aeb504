import{_ as H,b as G,a as w}from"./AdminLayout-b73e8538.js";import{_ as B}from"./CreateButton-8dce0eb5.js";import{_ as N}from"./SecondaryButton-ae621792.js";import{D as K}from"./DangerButton-ce6d88c3.js";import{M as j}from"./Modal-8b2a2aa4.js";import{_ as Z}from"./Pagination-3fdd18f9.js";import{_ as q}from"./FileViewer-e76e0359.js";import{K as J,h as Q,r as m,o as i,c as r,a as n,u as A,w as l,F as _,Z as X,b as t,t as d,g as u,i as g,e as D,f as I,s as tt,x as et}from"./app-6cdaf2bc.js";import{_ as st}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const o=a=>(tt("data-v-71b7a9e2"),a=a(),et(),a),ot={class:"animate-top"},lt={class:"sm:flex sm:items-center"},nt={class:"sm:flex-auto"},at={class:"text-2xl font-semibold leading-7 text-gray-900"},ct={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},it={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},rt={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},dt=o(()=>t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),pt={class:"flex justify-end w-20"},mt={class:"flex justify-end"},ht={class:"mt-8 overflow-x-auto sm:rounded-lg"},_t={class:"shadow sm:rounded-lg"},ut={class:"w-full text-sm text-left rtl:text-right text-gray-500"},gt=o(()=>t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2 grid grid-cols-1 gap-x-2 sm:grid-cols-12"},[t("th",{scope:"col",class:"sm:col-span-2 px-4 py-4 text-sm font-semi bold text-gray-900"},"PRODUCT NAME"),t("th",{scope:"col",class:"sm:col-span-3 px-4 py-4 text-sm font-semi bold text-gray-900"},"COMPANY NAME"),t("th",{scope:"col",class:"sm:col-span-2 px-4 py-4 text-sm font-semi bold text-gray-900"},"SERIAL NO"),t("th",{scope:"col",class:"sm:col-span-2 px-4 py-4 text-sm font-semi bold text-gray-900"},"REPORT TYPE"),t("th",{scope:"col",class:"sm:col-span-2 px-4 py-4 text-sm font-semi bold text-gray-900"},"DATE"),t("th",{scope:"col",class:"sm:col-span-1 px-4 py-4 text-sm font-semi bold text-gray-900"},"ACTION")])],-1)),xt={key:0},ft={class:"sm:col-span-2 px-4 py-2.5"},yt={class:"sm:col-span-3 px-4 py-2.5"},vt={class:"sm:col-span-2 px-4 py-2.5"},wt={class:"sm:col-span-1 px-4 py-2.5"},bt={class:"flex items-center justify-start gap-2"},kt=["onClick"],Ct=o(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),Mt=[Ct],Et=o(()=>t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)),Rt=o(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),St=o(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),Tt=["onClick"],Bt=o(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),Nt=o(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)),jt=[Bt,Nt],At=o(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7-7m0 0L5 14m7-7v12"})],-1)),It=o(()=>t("span",{class:"text-sm text-gray-700 leading-5"},"Upload Report",-1)),Lt=o(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),Pt=o(()=>t("span",{class:"text-sm text-gray-700 leading-5"}," View History ",-1)),$t={key:0,class:"divide-y divide-gray-300 sm:col-span-12 product-details border mx-6 mb-4"},Ot=o(()=>t("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-5 bg-gray-50"},[t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"REPORT DATE"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"REPORT NAME"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"REPORT TYPE "),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"SERVICE ENGINEER"),t("th",{scope:"col",class:"py-2 sm:col-span-1 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"ACTION")],-1)),Vt={class:"divide-y divide-gray-300 bg-white grid grid-cols-1 overflow-y-auto",style:{"max-height":"184px"}},zt={class:"py-2 pl-4 pr-3 sm:col-span-1 sm:pl-6"},Ut={class:"py-2 pl-4 pr-3 sm:col-span-1 sm:pl-6"},Wt={class:"py-2 pl-4 pr-3 sm:col-span-1 sm:pl-6"},Yt={class:"py-2 pl-4 pr-3 sm:col-span-1 sm:pl-6"},Ft={class:"sm:col-span-1 py-2 pl-4 pr-3 sm:pl-6 space-x-2 items-center"},Ht=["onClick"],Gt=o(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),Kt=[Gt],Zt=["onClick"],qt=o(()=>t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),Jt=[qt],Qt={key:1},Xt=o(()=>t("tr",{class:"bg-white"},[t("td",{colspan:"5",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),Dt=[Xt],te={class:"p-6"},ee=o(()=>t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),se={class:"mt-6 flex justify-end"},oe={class:"p-6"},le={class:"mt-6 px-4 flex justify-end"},ne={__name:"TempShow",props:["data","organization","customer","permissions","filepath"],setup(a){const L=a,b=J().props.filepath.view,k=Q({}),x=m(!1),C=m(null),P=s=>{C.value=s,x.value=!0},f=()=>{x.value=!1},$=()=>{k.delete(route("service-reports.destroy",{id:C.value}),{onSuccess:()=>f()})},M=s=>{const p=new Date(s),e={year:"numeric",month:"short",day:"numeric"};return p.toLocaleDateString("en-US",e)},O=s=>s&&s.length>35?s.substring(0,35)+"...":s,V=s=>{k.get(route("service-reports.show",{search:s,id:L.customer.id}),{preserveState:!0})},y=m([]),z=s=>{y.value[s]=!y.value[s]},v=m(!1),E=m(null),U=m("custom"),W=s=>{E.value=s,v.value=!0},R=()=>{v.value=!1},Y=s=>{const p=window.location.origin+b+s,e=document.createElement("a");e.href=p,e.setAttribute("download",s),document.body.appendChild(e),e.click(),document.body.removeChild(e)};return(s,p)=>(i(),r(_,null,[n(A(X),{title:"Service Report"}),n(H,null,{default:l(()=>[t("div",ot,[t("div",lt,[t("div",nt,[t("h1",at,d(a.customer.customer_name),1)]),t("div",ct,[t("div",it,[t("div",rt,[dt,t("input",{id:"search-field",onInput:p[0]||(p[0]=e=>V(e.target.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),t("div",pt,[n(B,{href:s.route("customers.index")},{default:l(()=>[u(" Back ")]),_:1},8,["href"])]),t("div",mt,[n(B,{href:s.route("service-reports.create",{id:a.customer.id})},{default:l(()=>[u(" Create Report ")]),_:1},8,["href"])])])]),t("div",ht,[t("div",_t,[t("table",ut,[gt,a.data.data&&a.data.data.length>0?(i(),r("tbody",xt,[(i(!0),r(_,null,g(a.data.data,(e,S)=>{var T;return i(),r("tr",{class:"odd:bg-white even:bg-gray-50 border-b grid grid-cols-1 gap-x-2 sm:grid-cols-12",key:e.id},[t("td",ft,d(e.product_code??"")+" : "+d(e.product_name??""),1),t("td",yt,d(O((T=e==null?void 0:e.company)==null?void 0:T.name)??"-"),1),t("td",vt,d(e.serial_no??"-"),1),(i(!0),r(_,null,g(e.report_detail,(c,h)=>(i(),r("td",{key:h,class:"sm:col-span-2 px-4 py-2.5 font-medium text-gray-900"},d(c.type??"-"),1))),128)),(i(!0),r(_,null,g(e.report_detail,(c,h)=>(i(),r("td",{key:h,class:"sm:col-span-2 px-4 py-2.5"},d(M(c.date)??"-"),1))),128)),t("td",wt,[t("div",bt,[t("button",{onClick:c=>z(S)},Mt,8,kt),n(G,{align:"right",width:"48"},{trigger:l(()=>[Et]),content:l(()=>[n(w,{href:s.route("service-reports.edit",{id:e.id})},{svg:l(()=>[Rt]),text:l(()=>[St]),_:2},1032,["href"]),t("button",{type:"button",onClick:c=>P(e.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},jt,8,Tt),n(w,{href:s.route("upload-service-report",{id:e.id})},{svg:l(()=>[At]),text:l(()=>[It]),_:2},1032,["href"]),n(w,{href:s.route("service-reports.view",{id:e.id})},{svg:l(()=>[Lt]),text:l(()=>[Pt]),_:2},1032,["href"])]),_:2},1024)])]),y.value[S]&&e.report_detail.length>0?(i(),r("div",$t,[Ot,t("tbody",Vt,[(i(!0),r(_,null,g(e.report_detail,(c,h)=>(i(),r("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-5",key:h},[t("td",zt,d(M(c.date)??"-"),1),t("td",Ut,d(c.document_name??"-"),1),t("td",Wt,d(c.type??"-"),1),t("td",Yt,d(c.engineer.first_name??"-")+" "+d(c.engineer.last_name??"-"),1),t("td",Ft,[t("button",{type:"button",onClick:F=>W(c.document_name)},Kt,8,Ht),t("button",{type:"button",onClick:F=>Y(c.document_name)},Jt,8,Zt)])]))),128))])])):I("",!0)])}),128))])):(i(),r("tbody",Qt,Dt))])]),a.data.data&&a.data.data.length>0?(i(),D(Z,{key:0,class:"mt-6",links:a.data.links},null,8,["links"])):I("",!0)])]),n(j,{show:x.value,onClose:f},{default:l(()=>[t("div",te,[ee,t("div",se,[n(N,{onClick:f},{default:l(()=>[u(" Cancel ")]),_:1}),n(K,{class:"ml-3",onClick:$},{default:l(()=>[u(" Delete ")]),_:1})])])]),_:1},8,["show"]),n(j,{show:v.value,onClose:R,maxWidth:U.value},{default:l(()=>[t("div",oe,[n(q,{fileUrl:A(b)+E.value},null,8,["fileUrl"]),t("div",le,[n(N,{onClick:R},{default:l(()=>[u(" Cancel ")]),_:1})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},ge=st(ne,[["__scopeId","data-v-71b7a9e2"]]);export{ge as default};
