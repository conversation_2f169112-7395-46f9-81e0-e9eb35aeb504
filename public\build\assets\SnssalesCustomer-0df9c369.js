import{K as Q,r as f,l as Y,o,c as l,a as p,u as b,w as I,F as _,Z,b as t,g as T,k as U,v as $,n as D,i as x,e as A,f as z,t as c}from"./app-97275a91.js";import{_ as J}from"./AdminLayout-595ad5a7.js";import{_ as W}from"./CreateButton-91ea7c7b.js";/* empty css                                                              */import{_ as tt}from"./Pagination-5e2f223d.js";import{_ as et}from"./SimpleDropdown-f072c5ba.js";import{_ as N}from"./InputLabel-eb73087c.js";import{_ as st}from"./SearchableDropdown-9d1b12d3.js";import{_ as ot}from"./ArrowIcon-572ff5c4.js";import{s as at}from"./sortAndSearch-6f0bc414.js";const lt={class:"animate-top"},rt={class:"flex justify-between items-center"},nt=t("div",{class:"items-start"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"SNS Customer Sales Report")],-1),it={class:"flex mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},dt={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},ct={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},mt=t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1),ut={class:"flex ml-6"},pt={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},gt={class:"flex justify-between mb-2"},_t={class:"flex"},ht=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),vt={class:"inline-flex items-center space-x-4 justify-end w-full"},ft=["src"],xt={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center"},yt={class:"sm:col-span-3"},bt={class:"relative mt-2"},wt={class:"sm:col-span-3"},kt={class:"relative mt-2"},St={class:"sm:col-span-3"},Ct={class:"sm:col-span-3"},Nt={class:"mt-8 overflow-x-auto sm:rounded-lg"},zt={class:"shadow sm:rounded-lg"},Mt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Ot={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Dt={class:"border-b-2 grid grid-cols-12 gap-x-2"},Et=["onClick"],Vt={key:0,class:"divide-y divide-gray-300 bg-white"},It={class:"sm:col-span-8 px-3 py-3 text-sm text-gray-900"},Tt={class:"sm:col-span-4 px-3 py-3 text-sm text-gray-500"},Ut={class:"flex items-center justify-start gap-6"},$t=["onClick"],At=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1),Lt=[At],Rt={key:0,class:"divide-y divide-gray-300 sm:col-span-12 product-details border mx-6 mb-4"},Bt=t("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-10 bg-gray-50"},[t("th",{scope:"col",class:"py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"PRODUCT CODE"),t("th",{scope:"col",class:"py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"PRODUCT NAME"),t("th",{scope:"col",class:"py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"INVOICE NO"),t("th",{scope:"col",class:"py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"DATE"),t("th",{scope:"col",class:"py-2 sm:col-span-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"},"QTY")],-1),jt={class:"divide-y divide-gray-300 bg-white grid grid-cols-1"},qt={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},Ft={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},Pt={class:"py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6"},Gt={class:"py-2 pl-4 pr-3 sm:col-span-1 text-sm font-medium text-gray-500 sm:pl-6"},Kt={class:"divide-y divide-gray-300 bg-white grid grid-cols-1"},Xt={key:0,class:"grid grid-cols-1 gap-x-6 sm:grid-cols-10 w-full"},Ht={class:"py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6"},Qt={class:"py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6"},Yt={class:"py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6"},Zt={class:"py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6"},Jt={class:"py-2 pl-4 pr-3 sm:col-span-2 text-sm font-medium text-gray-500 sm:pl-6"},Wt={key:1},te=t("tr",{class:"bg-white"},[t("td",{colspan:"8",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),ee=[te],ge={__name:"SnssalesCustomer",props:["data","organization","search","customers","organizationId","customerId"],setup(g){const y=g,{form:M,search:se,sort:L,fetchData:oe,sortKey:R,sortDirection:B,updateParams:j}=at("sns-cusomersales-report",{organization_id:y.organizationId,customer_id:y.customerId,from_date:y.from_date,to_date:y.to_date});Q().props.data.links.find(s=>s.active===!0);const n=f(y.organizationId),d=f(y.customerId),E=f("ALL CUSTOMERS"),m=f(""),u=f(""),w=f("");Y([n,d,m,u],()=>{j({organization_id:n.value,customer_id:d.value,from_date:m.value,to_date:u.value})});const q=[{field:"customer_name",label:"CUSTOMER NAME",sortable:!0,colSpan:"col-span-8"},{field:"details",label:"SALES DETAILS",sortable:!1,colSpan:"col-span-4"}],k=(s,a,e,h,r)=>{w.value=s,M.get(route("sns-cusomersales-report",{search:s,organization_id:a,customer_id:e,from_date:h,to_date:r}),{preserveState:!0})},F=(s,a)=>{n.value=s,k(w.value,n.value,d.value,m.value,u.value)},P=(s,a)=>{d.value=s,E.value=a,k(w.value,n.value,d.value,m.value,u.value)},O=f([]),G=s=>{O.value[s]=!O.value[s]},V=s=>{const a=new Date(s),e={year:"numeric",month:"short",day:"numeric"};return a.toLocaleDateString("en-US",e)},K=()=>{let s="";switch(n.value){case 1:s="MC";break;case 2:s="HC";break;case 3:s="NOX";break;default:s="All_Organizations";break}const a=E.value.replace(/\s+/g,"_"),e=`SNS_Customer_Salse_Report_${s}_${a}`,h={organization_id:n.value,customer_id:d.value,from_date:m.value,to_date:u.value},S=`/export-sns-cusomersales-report?${new URLSearchParams(h).toString()}`;fetch(S,{method:"GET"}).then(i=>{if(!i.ok)throw new Error("Network response was not ok");return i.blob()}).then(i=>{const v=window.URL.createObjectURL(new Blob([i])),C=document.createElement("a");C.href=v,C.setAttribute("download",e+".xlsx"),document.body.appendChild(C),C.click(),document.body.removeChild(C)}).catch(i=>{console.error("Error exporting data:",i)})},X=()=>{k(w.value,n.value,d.value,m.value,u.value)},H=()=>{k(w.value,n.value,d.value,m.value,u.value)};return(s,a)=>(o(),l(_,null,[p(b(Z),{title:"Customer Sales Report"}),p(J,null,{default:I(()=>[t("div",lt,[t("div",rt,[nt,t("div",it,[t("div",dt,[t("div",ct,[mt,t("input",{id:"search-field",onInput:a[0]||(a[0]=e=>k(e.target.value,n.value,d.value,m.value,u.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),t("div",ut,[p(W,{href:s.route("reports")},{default:I(()=>[T(" Back ")]),_:1},8,["href"])])])]),t("div",pt,[t("div",gt,[t("div",_t,[ht,p(N,{for:"customer_id",value:"Filters"})]),t("div",vt,[t("button",{onClick:K},[t("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,ft)])])]),t("div",xt,[t("div",yt,[p(N,{for:"customer_id",value:"Organization Name"}),t("div",bt,[p(et,{options:g.organization,modelValue:n.value,"onUpdate:modelValue":a[1]||(a[1]=e=>n.value=e),onOnchange:F},null,8,["options","modelValue"])])]),t("div",wt,[p(N,{for:"customer_id",value:"Customer Name"}),t("div",kt,[p(st,{options:g.customers,modelValue:d.value,"onUpdate:modelValue":a[2]||(a[2]=e=>d.value=e),onOnchange:P},null,8,["options","modelValue"])])]),t("div",St,[p(N,{for:"date",value:"From Date"}),U(t("input",{"onUpdate:modelValue":a[3]||(a[3]=e=>m.value=e),class:D(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":b(M).errors.from_date}]),type:"date",onChange:X},null,34),[[$,m.value]])]),t("div",Ct,[p(N,{for:"date",value:"To Date"}),U(t("input",{"onUpdate:modelValue":a[4]||(a[4]=e=>u.value=e),class:D(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":b(M).errors.to_date}]),type:"date",onChange:H},null,34),[[$,u.value]])])])]),t("div",Nt,[t("div",zt,[t("table",Mt,[t("thead",Ot,[t("tr",Dt,[(o(),l(_,null,x(q,(e,h)=>t("th",{key:h,scope:"col",class:D(["px-4 py-4 text-sm font-semibold text-gray-900 cursor-pointer",e.colSpan]),onClick:r=>b(L)(e.field,e.sortable)},[T(c(e.label)+" ",1),e.sortable?(o(),A(ot,{key:0,isSorted:b(R)===e.field,direction:b(B)},null,8,["isSorted","direction"])):z("",!0)],10,Et)),64))])]),g.data.data&&g.data.data.length>0?(o(),l("tbody",Vt,[(o(!0),l(_,null,x(g.data.data,(e,h)=>(o(),l("tr",{class:"odd:bg-white even:bg-gray-50 border-b grid grid-cols-1 gap-x-2 sm:grid-cols-12",key:e.id},[t("td",It,c(e.customer_name??"-"),1),t("td",Tt,[t("div",Ut,[t("button",{onClick:r=>G(h)},Lt,8,$t)])]),O.value[h]&&e.invoices.length>0?(o(),l("div",Rt,[Bt,t("tbody",jt,[(o(!0),l(_,null,x(e.invoice_details,(r,S)=>(o(),l("tr",{class:"grid grid-cols-1 gap-x-6 sm:grid-cols-5",key:S},[t("td",qt,c(r.invoice.invoice_no??"-"),1),t("td",Ft,c(V(r.invoice.date)),1),t("td",Pt,c(r.invoice.customers.customer_name??"-"),1),t("td",Gt,c(r.qty??"-"),1)]))),128))]),t("tbody",Kt,[(o(!0),l(_,null,x(e.invoices,(r,S)=>(o(),l(_,{key:S},[r.invoice_detail.length?(o(),l("tr",Xt,[t("td",Ht,[(o(!0),l(_,null,x(r.invoice_detail,(i,v)=>(o(),l("div",{key:v},c(i.product.item_code??"-"),1))),128))]),t("td",Qt,[(o(!0),l(_,null,x(r.invoice_detail,(i,v)=>(o(),l("div",{key:v},c(i.product.name??"-"),1))),128))]),t("td",Yt,c(r.invoice_no??"-"),1),t("td",Zt,c(V(r.date)??"-"),1),t("td",Jt,[(o(!0),l(_,null,x(r.invoice_detail,(i,v)=>(o(),l("div",{key:v},c(i.qty??"-"),1))),128))])])):z("",!0)],64))),128))])])):z("",!0)]))),128))])):(o(),l("tbody",Wt,ee))])]),g.data.data&&g.data.data.length>0?(o(),A(tt,{key:0,class:"mt-6",links:g.data.links},null,8,["links"])):z("",!0)])])]),_:1})],64))}};export{ge as default};
