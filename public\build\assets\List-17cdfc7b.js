import{K as Se,r as y,l as Ve,o as d,c as u,a as i,u as f,w as m,F as U,Z as Te,b as e,g as T,f as _,i as L,e as j,t as a,n as O,k as ze,v as Oe,s as Ie,x as Me}from"./app-ce7743ab.js";import{_ as je,b as $e,a as F}from"./AdminLayout-6af2fc6a.js";import{_ as Ue}from"./CreateButton-ae748c59.js";import{_ as W}from"./SecondaryButton-aec1a882.js";import{_ as D}from"./TextInput-65921831.js";import{_ as Ge}from"./TextArea-5fab1749.js";import{P as ee}from"./PrimaryButton-6ff8a943.js";import{D as Be}from"./DangerButton-ca58e5a5.js";import{M as q}from"./Modal-599968f2.js";import{_ as Ne}from"./Pagination-ae99ac61.js";import{_ as te}from"./SimpleDropdown-7e23adea.js";import{_ as H}from"./SearchableDropdown-6fd7fbbe.js";import{_ as Y}from"./SearchableDropdownNew-39922dcb.js";import"./html2canvas.esm-1d26a94c.js";import{_ as h}from"./InputLabel-3aa35471.js";import{_ as Ae}from"./ArrowIcon-73c874d9.js";import{s as Re}from"./sortAndSearch-31104ada.js";import{_ as Ee}from"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const o=r=>(Ie("data-v-feaa4f5a"),r=r(),Me(),r),Le={class:"animate-top"},Fe={class:"flex justify-between items-center"},We=o(()=>e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Company PO")],-1)),qe={class:"flex justify-end"},He={class:"flex space-x-6 mt-4 sm:mt-0 w-64"},Ye={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},Ke=o(()=>e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),Qe={key:0,class:"mt-4 sm:ml-6 sm:mt-0 sm:flex-none"},Ze={class:"flex justify-end"},Je={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},Xe={class:"flex mb-2"},De=o(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),et={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},tt={class:"sm:col-span-4"},st={class:"relative mt-2"},ot={class:"sm:col-span-4"},lt={class:"relative mt-2"},at={class:"sm:col-span-4"},nt={class:"relative mt-2"},it={class:"sm:col-span-4"},dt={class:"relative mt-2"},rt={class:"sm:col-span-4"},ct={class:"relative mt-2"},ut={class:"mt-8 overflow-x-auto sm:rounded-lg"},mt={class:"shadow sm:rounded-lg"},_t={class:"w-full text-sm text-left rtl:text-right text-gray-500"},pt={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},yt={class:"border-b-2"},ht=["onClick"],vt={key:0},gt={class:"px-4 py-2.5 min-w-44"},ft={class:"px-4 py-2.5 min-w-36"},xt={class:"px-4 py-2.5"},wt={class:"px-4 py-2.5"},bt={scope:"row",class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap"},kt={class:"px-4 py-2.5 min-w-32"},Ct={class:"px-4 py-2.5 min-w-32"},Pt={class:"flex flex-1 items-center px-4 py-2.5"},St={class:"items-center px-4 py-2.5"},Vt={class:"flex items-center justify-start gap-4"},Tt=o(()=>e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)),zt=o(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)),Ot=o(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)),It=["onClick"],Mt=o(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)),jt=o(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)),$t=[Mt,jt],Ut=o(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)),Gt=o(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Receive PO ",-1)),Bt=o(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)),Nt=o(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," View PO ",-1)),At=["onClick"],Rt=o(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M6 2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4a2 2 0 012-2zm2 6h8m-4 4h4"})],-1)),Et=o(()=>e("span",{class:"text-sm text-gray-700 leading-5"}," Generate PDF ",-1)),Lt=[Rt,Et],Ft={key:1},Wt=o(()=>e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)),qt=[Wt],Ht={class:"p-6"},Yt=o(()=>e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this Po? ",-1)),Kt={class:"mt-6 flex justify-end"},Qt={class:"p-6"},Zt={id:"pdf-content"},Jt={class:"container1"},Xt={key:0,class:"header",style:{display:"flex","align-items":"center","justify-content":"space-between","margin-bottom":"10px"}},Dt=["src"],es=o(()=>e("p",null,[e("strong",{style:{"font-size":"20px"}},"Purchase Order")],-1)),ts=o(()=>e("h1",null,[e("div",{style:{width:"120px"}})],-1)),ss={key:1,class:"header",style:{"align-items":"start","justify-content":"center","text-align":"center"}},os=["src"],ls=o(()=>e("div",{style:{"align-items":"center","justify-content":"space-between","margin-bottom":"5px"}},[e("p",{style:{"font-size":"20px"}},[e("strong",null,"Purchase Order")])],-1)),as={style:{display:"flex","justify-content":"space-between"}},ns={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},is={style:{"font-size":"14px","margin-top":"10px"}},ds=o(()=>e("p",{style:{"margin-bottom":"4px"}},[e("strong")],-1)),rs=o(()=>e("p",{style:{"margin-bottom":"4px"}},[e("strong")],-1)),cs={key:0,style:{display:"flex"}},us=o(()=>e("p",{style:{width:"60px"}},[e("strong",null,"Originator")],-1)),ms={style:{display:"flex"}},_s=o(()=>e("p",{style:{width:"40px"}},[e("strong",null,"Phone")],-1)),ps={style:{display:"flex"}},ys=o(()=>e("p",{style:{width:"40px"}},[e("strong",null,"Email")],-1)),hs={style:{display:"flex"}},vs=o(()=>e("p",{style:{width:"40px"}},[e("strong",null,"GST")],-1)),gs={class:"invoice-details",style:{"margin-bottom":"20px","justify-items":"start",width:"320px"}},fs={style:{display:"flex"}},xs=o(()=>e("p",{style:{width:"70px"}},[e("strong",null,"PO Number")],-1)),ws={style:{display:"flex"}},bs=o(()=>e("p",{style:{width:"70px"}},[e("strong",null,"PO Date")],-1)),ks=o(()=>e("p",{style:{"margin-bottom":"4px"}},[e("strong")],-1)),Cs={style:{"font-size":"14px","margin-top":"10px"}},Ps=o(()=>e("p",{style:{"margin-bottom":"4px"}},[e("strong")],-1)),Ss={style:{display:"flex"}},Vs=o(()=>e("p",{style:{width:"40px"}},[e("strong",null,"Phone")],-1)),Ts={style:{display:"flex"}},zs=o(()=>e("p",{style:{width:"40px"}},[e("strong",null,"Email")],-1)),Os={style:{display:"flex"}},Is=o(()=>e("p",{style:{width:"40px"}},[e("strong",null,"GST")],-1)),Ms=o(()=>e("th",null,"SN",-1)),js=o(()=>e("th",null,"Code",-1)),$s=o(()=>e("th",null,"Product Description",-1)),Us=o(()=>e("th",null,"HSN",-1)),Gs=o(()=>e("th",null,"Qty",-1)),Bs=o(()=>e("th",null,"Price (₹)",-1)),Ns=o(()=>e("th",null,"Total Price (₹)",-1)),As={key:0},Rs={key:1},Es={key:2},Ls=o(()=>e("th",null,"Total Gst",-1)),Fs=o(()=>e("th",null,"Total Amount",-1)),Ws={key:0},qs={key:1},Hs={key:2},Ys={style:{display:"flex","justify-content":"space-between"}},Ks={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"260px"}},Qs={class:"invoice-details",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},Zs={style:{display:"flex"}},Js=o(()=>e("p",{style:{width:"105px"}},[e("strong",null,"Sub Total (₹)")],-1)),Xs={style:{display:"flex"}},Ds=o(()=>e("p",{style:{width:"105px"}},[e("strong",null,"Total Discount (₹)")],-1)),eo={style:{display:"flex"}},to=o(()=>e("p",{style:{width:"105px"}},[e("strong",null,"Total GST (₹)")],-1)),so={style:{display:"flex"}},oo=o(()=>e("p",{style:{width:"105px"}},[e("strong",null,"Total Amount (₹)")],-1)),lo={style:{display:"flex","justify-content":"space-between"}},ao=o(()=>e("div",{class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"400"}},null,-1)),no={class:"",style:{"margin-bottom":"20px","justify-items":"start",width:"300px"}},io=o(()=>e("p",null,[e("strong",null,"FOR,")],-1)),ro=["src"],co=o(()=>e("div",{id:"footer",style:{padding:"8px 0px"}},null,-1)),uo={class:"mt-6 px-4 flex justify-end"},mo={class:"flex flex-col justify-end space-y-6"},_o={class:"flex items-center space-x-2"},po={class:"flex justify-end"},yo={class:"w-36"},ho={class:"p-6"},vo=o(()=>e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Make Payment",-1)),go={class:"border-b border-gray-900/10 pb-12"},fo={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},xo={class:"sm:col-span-3"},wo={class:"relative mt-2"},bo={class:"sm:col-span-3"},ko={class:"sm:col-span-3"},Co={class:"sm:col-span-3"},Po={class:"sm:col-span-6"},So={class:"relative mt-2"},Vo={class:"sm:col-span-6"},To={class:"mt-6 px-4 flex justify-end"},zo={class:"w-36"},Oo={__name:"List",props:["data","types","typeId","permissions","organization","paymentType","bankinfo","companies","organizationId","companyId","categoryId","category","salesuser","salesUserId","pagetypes"],setup(r){const P=r,{form:v,search:Io,sort:se,fetchData:Mo,sortKey:oe,sortDirection:le,updateParams:ae}=Re("companypo.index",{organization_id:P.organizationId,company_id:P.companyId,sales_user_id:P.salesUserId,category:P.categoryId}),G=Se().props.filepath.view,l=y([]),B=y(!1),K=y(null),ne=[{field:"po_number",label:"PO NUMBER",sortable:!0},{field:"sales_order_no",label:"SO NUMBER",sortable:!0},{field:"type",label:"TYPE",sortable:!0},{field:"category",label:"CATEGORY",sortable:!0},{field:"company.name",label:"COMPANY",sortable:!0},{field:"date",label:"DATE",sortable:!0},{field:"total_amount",label:"AMOUNT (₹)",sortable:!0},{field:"status",label:"STATUS",sortable:!0},{field:"action",label:"ACTION",sortable:!1}],ie=n=>{K.value=n,B.value=!0},N=()=>{B.value=!1},A=y(!1),de=y("custom"),re=n=>{const s=P.data.data.find(t=>t.id===n);l.value=s,A.value=!0},Q=()=>{A.value=!1},ce=()=>{v.delete(route("companypo.destroy",{id:K.value}),{onSuccess:()=>N()})},x=y(P.organizationId),w=y(P.companyId),b=y(P.categoryId),k=y(P.salesUserId),S=y(P.typeId),I=y("");Ve([x,w,k,b],()=>{ae({organization_id:x.value,company_id:w.value,sales_user_id:k.value,category:b.value})});const M=(n,s,t,V,C,z)=>{I.value=n,v.get(route("companypo.index",{search:n,organization_id:s,company_id:t,category:V,sales_user_id:C,type:z}),{preserveState:!0})},ue=(n,s)=>{x.value=n,M(I.value,x.value,w.value,b.value,k.value,S.value)},me=(n,s)=>{w.value=n,M(I.value,x.value,w.value,b.value,k.value,S.value)},_e=(n,s)=>{b.value=n,M(I.value,x.value,w.value,b.value,k.value,S.value)},pe=(n,s)=>{S.value=n,M(I.value,x.value,w.value,b.value,k.value,S.value)},ye=(n,s)=>{k.value=n,M(I.value,x.value,w.value,b.value,k.value,S.value)},he=n=>{switch(n){case"Open":return"bg-blue-100";case"Partially Received":return"bg-yellow-100";case"Completed":return"bg-green-100";default:return"bg-gray-100"}},ve=n=>{switch(n){case"Open":return"text-blue-600";case"Partially Received":return"text-yellow-600";case"Completed":return"text-green-600";default:return"text-gray-600"}},ge=y([]),R=y(!1),fe=y("custom2"),Z=()=>{R.value=!1},J=n=>{const s=new Date(n),t={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",t)},g=n=>{let s=n.toFixed(2).toString(),[t,V]=s.split("."),C=t.substring(t.length-3),z=t.substring(0,t.length-3);return z!==""&&(C=","+C),`${z.replace(/\B(?=(\d{2})+(?!\d))/g,",")+C}.${V}`},p={organization_id:"",company_id:"",org_bank_id:"",purchase_order_id:"",po_number:"",payment_type:"",amount:"",check_number:"",date:"",note:""},xe=(n,s)=>{p.payment_type=n,v.errors["data.payment_type"]=null},we=(n,s)=>{p.org_bank_id=n,v.errors["data.org_bank_id"]=null},be=()=>{v.post(route("companypo.paymentpay",{data:p}),{onSuccess:()=>{v.reset(),R.value=!1},onError:n=>{}})},X=n=>{v.errors[n]=null},$=y("portrait"),ke=(n,s)=>{$.value=n},Ce=(n,s)=>{window.open(`/companypo/download/${n}/${s}`,"_blank")};return(n,s)=>(d(),u(U,null,[i(f(Te),{title:"Company PO"}),i(je,null,{default:m(()=>[e("div",Le,[e("div",Fe,[We,e("div",qe,[e("div",He,[e("div",Ye,[Ke,e("input",{id:"search-field",onInput:s[0]||(s[0]=t=>M(t.target.value,x.value,w.value,b.value,k.value,S.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])]),r.permissions.canCreateCompanypo?(d(),u("div",Qe,[e("div",Ze,[i(Ue,{href:n.route("companypo.create")},{default:m(()=>[T(" Create PO ")]),_:1},8,["href"])])])):_("",!0)])]),e("div",Je,[e("div",Xe,[De,i(h,{for:"customer_id",value:"Filters"})]),e("div",et,[e("div",tt,[i(h,{for:"customer_id",value:"Organization Name"}),e("div",st,[i(te,{options:r.organization,modelValue:x.value,"onUpdate:modelValue":s[1]||(s[1]=t=>x.value=t),onOnchange:ue},null,8,["options","modelValue"])])]),e("div",ot,[i(h,{for:"customer_id",value:"Company Name"}),e("div",lt,[i(Y,{options:r.companies,modelValue:w.value,"onUpdate:modelValue":s[2]||(s[2]=t=>w.value=t),onOnchange:me},null,8,["options","modelValue"])])]),e("div",at,[i(h,{for:"customer_id",value:"Person Name"}),e("div",nt,[i(Y,{options:r.salesuser,modelValue:k.value,"onUpdate:modelValue":s[3]||(s[3]=t=>k.value=t),onOnchange:ye},null,8,["options","modelValue"])])]),e("div",it,[i(h,{for:"customer_id",value:"Category"}),e("div",dt,[i(te,{options:r.category,modelValue:b.value,"onUpdate:modelValue":s[4]||(s[4]=t=>b.value=t),onOnchange:_e},null,8,["options","modelValue"])])]),e("div",rt,[i(h,{for:"customer_id",value:"Purchase Type"}),e("div",ct,[i(Y,{options:r.types,modelValue:S.value,"onUpdate:modelValue":s[5]||(s[5]=t=>S.value=t),onOnchange:pe},null,8,["options","modelValue"])])])])]),e("div",ut,[e("div",mt,[e("table",_t,[e("thead",pt,[e("tr",yt,[(d(),u(U,null,L(ne,(t,V)=>e("th",{key:V,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:C=>f(se)(t.field,t.sortable)},[T(a(t.label)+" ",1),t.sortable?(d(),j(Ae,{key:0,isSorted:f(oe)===t.field,direction:f(le)},null,8,["isSorted","direction"])):_("",!0)],8,ht)),64))])]),r.data.data&&r.data.data.length>0?(d(),u("tbody",vt,[(d(!0),u(U,null,L(r.data.data,(t,V)=>(d(),u("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",gt,a(t.po_number),1),e("td",ft,a(t.sales_order_no??"-"),1),e("td",xt,a(t.type),1),e("td",wt,a(t.category),1),e("td",bt,a(t.company.name),1),e("td",kt,a(J(t.date)),1),e("td",Ct,a(g(t.total_amount)),1),e("td",Pt,[e("div",{class:O(["flex rounded-full px-4 py-1",he(t.status)])},[e("span",{class:O(["text-sm font-semibold whitespace-nowrap",ve(t.status)])},a(t.status),3)],2)]),e("td",St,[e("div",Vt,[i($e,{align:"right",width:"48"},{trigger:m(()=>[Tt]),content:m(()=>[t.status!="Completed"&&r.permissions.canEditCompanypo?(d(),j(F,{key:0,href:n.route("companypo.edit",{id:t.id})},{svg:m(()=>[zt]),text:m(()=>[Ot]),_:2},1032,["href"])):_("",!0),t.status=="Open"&&r.permissions.canDeleteCompanypo?(d(),u("button",{key:1,type:"button",onClick:C=>ie(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},$t,8,It)):_("",!0),t.status!="Completed"&&r.permissions.canReceiveCompanypo?(d(),j(F,{key:2,href:n.route("companypo.receivepo",{id:t.id})},{svg:m(()=>[Ut]),text:m(()=>[Gt]),_:2},1032,["href"])):_("",!0),r.permissions.canViewCompanypo?(d(),j(F,{key:3,href:n.route("companypo.viewpo",{id:t.id})},{svg:m(()=>[Bt]),text:m(()=>[Nt]),_:2},1032,["href"])):_("",!0),e("button",{type:"button",onClick:C=>re(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},Lt,8,At)]),_:2},1024)])])]))),128))])):(d(),u("tbody",Ft,qt))])])]),r.data.data&&r.data.data.length>0?(d(),j(Ne,{key:0,class:"mt-6",links:r.data.links},null,8,["links"])):_("",!0)]),i(q,{show:B.value,onClose:N},{default:m(()=>[e("div",Ht,[Yt,e("div",Kt,[i(W,{onClick:N},{default:m(()=>[T(" Cancel ")]),_:1}),i(Be,{class:"ml-3",onClick:ce},{default:m(()=>[T(" Delete ")]),_:1})])])]),_:1},8,["show"]),i(q,{show:A.value,onClose:Q,maxWidth:de.value},{default:m(()=>{var t,V,C,z,E;return[e("div",Qt,[e("div",Zt,[e("div",Jt,[l.value.organization.id=="3"?(d(),u("div",Xt,[e("img",{class:"w-20 h-20",src:f(G)+l.value.organization.logo,alt:"logo"},null,8,Dt),es,ts])):_("",!0),l.value.organization.id=="1"||l.value.organization.id=="2"?(d(),u("div",ss,[e("img",{class:"w-full h-10",src:f(G)+l.value.organization.logo,alt:"logo"},null,8,os),ls])):_("",!0),e("div",as,[e("div",ns,[e("p",null,[e("strong",is,a(l.value.organization.name),1)]),ds,e("p",null,a(l.value.organization.address_line_1),1),e("p",null,a(l.value.organization.address_line_2),1),e("p",null,a(l.value.organization.pincode)+" , "+a(l.value.organization.city),1),rs,(t=l.value)!=null&&t.users?(d(),u("div",cs,[us,e("p",null,": "+a((C=(V=l.value)==null?void 0:V.users)==null?void 0:C.first_name)+" "+a((E=(z=l.value)==null?void 0:z.users)==null?void 0:E.last_name),1)])):_("",!0),e("div",ms,[_s,e("p",null,": "+a(l.value.organization.contact_no),1)]),e("div",ps,[ys,e("p",null,": "+a(l.value.organization.email),1)]),e("div",hs,[vs,e("p",null,": "+a(l.value.organization.gst_no),1)])]),e("div",gs,[e("div",fs,[xs,e("p",null,": "+a(l.value.po_number),1)]),e("div",ws,[bs,e("p",null,": "+a(J(l.value.date)),1)]),ks,e("p",null,[e("strong",Cs,a(l.value.company.name),1)]),e("p",null,a(l.value.company.address),1),Ps,e("div",Ss,[Vs,e("p",null,": "+a(l.value.company.contact_no),1)]),e("div",Ts,[zs,e("p",null,": "+a(l.value.company.email),1)]),e("div",Os,[Is,e("p",null,": "+a(l.value.company.gst_no),1)])])]),e("table",null,[e("thead",null,[e("tr",null,[Ms,js,$s,Us,Gs,Bs,Ns,l.value.company.gst_type=="IGST"&&l.value.company.company_type!="Retail"?(d(),u("th",As,"IGST (%)")):_("",!0),l.value.company.gst_type=="CGST/SGST"&&l.value.company.company_type!="Retail"?(d(),u("th",Rs,"CGST (%)")):_("",!0),l.value.company.gst_type=="CGST/SGST"&&l.value.company.company_type!="Retail"?(d(),u("th",Es,"SGST (%)")):_("",!0),Ls,Fs])]),e("tbody",null,[(d(!0),u(U,null,L(l.value.purchase_order_detail,(c,Pe)=>(d(),u("tr",{key:c.id,class:""},[e("td",null,a(Pe+1),1),e("td",null,a(c.product.item_code),1),e("td",null,a(c.product.name)+" "+a(c.description),1),e("td",null,a(c.product.hsn_code),1),e("td",null,a(c.pkg_of_qty!=null?c.pkg_of_qty:c.qty),1),e("td",null,a(g(c.price)),1),e("td",null,a(g(c.total_price)),1),l.value.company.gst_type=="IGST"&&l.value.company.company_type!="Retail"?(d(),u("td",Ws,a(g(c.gst)),1)):_("",!0),l.value.company.gst_type=="CGST/SGST"&&l.value.company.company_type!="Retail"?(d(),u("td",qs,a(g(c.gst/2)),1)):_("",!0),l.value.company.gst_type=="CGST/SGST"&&l.value.company.company_type!="Retail"?(d(),u("td",Hs,a(g(c.gst/2)),1)):_("",!0),e("td",null,a(g(c.total_gst_amount)),1),e("td",null,a(g(c.total_amount)),1)]))),128))])]),e("div",Ys,[e("div",Ks,[e("p",null,a(l.value.note),1)]),e("div",Qs,[e("div",Zs,[Js,e("p",null,": "+a(g(l.value.sub_total)),1)]),e("div",Xs,[Ds,e("p",null,": "+a(g(l.value.total_discount)),1)]),e("div",eo,[to,e("p",null,": "+a(g(l.value.total_gst)),1)]),e("div",so,[oo,e("p",null,": "+a(g(l.value.total_amount)),1)])])]),e("div",lo,[ao,e("div",no,[io,e("p",null,[e("strong",null,a(l.value.organization.name),1)]),e("img",{class:"h-28",src:f(G)+l.value.organization.signature,alt:"logo"},null,8,ro)])])])]),co,e("div",uo,[e("div",mo,[e("div",_o,[i(h,{for:"customer_id",value:"Page Type :"}),i(H,{options:r.pagetypes,modelValue:$.value,"onUpdate:modelValue":s[6]||(s[6]=c=>$.value=c),onOnchange:ke},null,8,["options","modelValue"])]),e("div",po,[i(W,{onClick:Q},{default:m(()=>[T(" Cancel ")]),_:1}),e("div",yo,[i(ee,{class:"ml-3 w-20",onClick:s[7]||(s[7]=c=>Ce(l.value.id,$.value))},{default:m(()=>[T(" Generate Pdf ")]),_:1})])])])])])]}),_:1},8,["show","maxWidth"]),i(q,{show:R.value,onClose:Z,maxWidth:fe.value},{default:m(()=>[e("div",ho,[vo,e("div",go,[e("div",fo,[e("div",xo,[i(h,{for:"role_id",value:"Payment Type"}),e("div",wo,[i(H,{options:r.paymentType,modelValue:p.payment_type,"onUpdate:modelValue":s[8]||(s[8]=t=>p.payment_type=t),onOnchange:xe,class:O({"error rounded-md":f(v).errors["data.payment_type"]})},null,8,["options","modelValue","class"])])]),e("div",bo,[i(h,{for:"amount",value:"Amount"}),i(D,{id:"amount",type:"text",numeric:!0,onChange:s[9]||(s[9]=t=>X("data.amount")),class:O({"error rounded-md":f(v).errors["data.amount"]}),modelValue:p.amount,"onUpdate:modelValue":s[10]||(s[10]=t=>p.amount=t)},null,8,["class","modelValue"])]),e("div",ko,[i(h,{for:"check_number",value:"Cheque Number"}),i(D,{id:"check_number",type:"text",modelValue:p.check_number,"onUpdate:modelValue":s[11]||(s[11]=t=>p.check_number=t),class:O({"error rounded-md":f(v).errors["data.check_number"]})},null,8,["modelValue","class"])]),e("div",Co,[i(h,{for:"date",value:"Payment Date"}),ze(e("input",{"onUpdate:modelValue":s[12]||(s[12]=t=>p.date=t),class:O(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":f(v).errors["data.date"]}]),type:"date",onChange:s[13]||(s[13]=t=>X("data.date"))},null,34),[[Oe,p.date]])]),e("div",Po,[i(h,{for:"org_bank_id",value:"Bank"}),e("div",So,[i(H,{options:ge.value,modelValue:p.org_bank_id,"onUpdate:modelValue":s[14]||(s[14]=t=>p.org_bank_id=t),onOnchange:we,class:O({"error rounded-md":f(v).errors["data.org_bank_id"]})},null,8,["options","modelValue","class"])])]),e("div",Vo,[i(h,{for:"note",value:"Note"}),i(Ge,{id:"note",type:"text",rows:3,modelValue:p.note,"onUpdate:modelValue":s[15]||(s[15]=t=>p.note=t)},null,8,["modelValue"])])])]),e("div",To,[i(W,{onClick:Z},{default:m(()=>[T(" Cancel ")]),_:1}),e("div",zo,[i(ee,{class:"ml-3 w-20",onClick:be},{default:m(()=>[T(" Save ")]),_:1})])])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}},Xo=Ee(Oo,[["__scopeId","data-v-feaa4f5a"]]);export{Xo as default};
