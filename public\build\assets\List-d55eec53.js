import{_ as w,b,a as v}from"./AdminLayout-aac65a75.js";import{_ as u}from"./CreateButton-7506df4f.js";import{_ as k}from"./SecondaryButton-12775633.js";import{D as C}from"./DangerButton-dc982a69.js";import{M as R}from"./Modal-b2e3ff36.js";import{h as B,r as h,o as s,c as a,a as o,u as L,w as e,F as _,Z as M,b as t,g as i,f as m,i as j,t as N,e as $}from"./app-b320a640.js";import"./_plugin-vue_export-helper-c27b6911.js";/* empty css                                                              */const E={class:"animate-top"},V={class:"flex justify-between items-center"},z=t("div",{class:"items-start"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Roles & Permissions")],-1),A={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},D={class:"flex justify-end w-20"},O={key:0,class:"flex justify-end"},P={class:"mt-8 overflow-x-auto sm:rounded-lg"},T={class:"shadow sm:rounded-lg"},F={class:"w-full text-sm text-left rtl:text-right text-gray-500"},I=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"ROLE NAME"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"ACTION")])],-1),S={key:0},H={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},Z=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1),q=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1),G=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1),J=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1),K=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1),Q=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1),U={class:"items-center px-4 py-2.5"},W={class:"flex items-center justify-start gap-4"},X=t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1),Y=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1),tt=t("span",{class:"text-sm text-gray-700 leading-5"},"Edit",-1),et=["onClick"],st=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),ot=t("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1),at=[st,ot],lt={key:1},rt=t("tr",{class:"bg-white"},[t("td",{colspan:"3",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),nt=[rt],it={class:"p-6"},ct=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this Role & Permissions? ",-1),dt={class:"mt-6 flex justify-end"},vt={__name:"List",props:["roles","permissions"],setup(r){const c=r,y=B({}),d=h(!1),x=h(null),f=l=>{x.value=l,d.value=!0},p=()=>{d.value=!1},g=()=>{y.delete(route("roles.destroy",{id:x.value}),{onSuccess:()=>p()})};return(l,pt)=>(s(),a(_,null,[o(L(M),{title:"Role-Permission"}),o(w,null,{default:e(()=>[t("div",E,[t("div",V,[z,t("div",A,[t("div",D,[o(u,{href:l.route("setting")},{default:e(()=>[i(" Back ")]),_:1},8,["href"])]),r.permissions.canCreateRoles?(s(),a("div",O,[o(u,{href:l.route("roles.create")},{default:e(()=>[i(" Add Role ")]),_:1},8,["href"])])):m("",!0)])]),t("div",P,[t("div",T,[t("table",F,[I,c.roles&&c.roles.length>0?(s(),a("tbody",S,[(s(!0),a(_,null,j(c.roles,(n,mt)=>(s(),a("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:n.id},[t("td",H,N(n.name),1),Z,q,G,J,K,Q,t("td",U,[t("div",W,[o(b,{align:"right",width:"48"},{trigger:e(()=>[X]),content:e(()=>[r.permissions.canEditRoles?(s(),$(v,{key:0,href:l.route("roles.edit",{id:n.id})},{svg:e(()=>[Y]),text:e(()=>[tt]),_:2},1032,["href"])):m("",!0),r.permissions.canDeleteRoles?(s(),a("button",{key:1,type:"button",onClick:xt=>f(n.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},at,8,et)):m("",!0)]),_:2},1024)])])]))),128))])):(s(),a("tbody",lt,nt))])])])]),o(R,{show:d.value,onClose:p},{default:e(()=>[t("div",it,[ct,t("div",dt,[o(k,{onClick:p},{default:e(()=>[i(" Cancel")]),_:1}),o(C,{class:"ml-3",onClick:g},{default:e(()=>[i(" Delete ")]),_:1})])])]),_:1},8,["show"])]),_:1})],64))}};export{vt as default};
