import{m as C,p as O,j as z,r as b,o,c as n,b as t,B as M,k as N,E as A,a as p,w as r,n as l,T as D,e as u,u as q,y as E,t as w,f as s,G as B,K as S,F,i as H,q as V,h as P,g as c}from"./app-4c3f0163.js";const I={class:""},T={__name:"Dropdown",props:{align:{type:String,default:"right"},width:{type:String,default:"48"},contentClasses:{type:String,default:"py-1 bg-white"}},setup(a){const i=a,d=j=>{g.value&&j.key==="Escape"&&(g.value=!1)};C(()=>document.addEventListener("keydown",d)),O(()=>document.removeEventListener("keydown",d));const v=z(()=>({48:"w-48"})[i.width.toString()]),e=z(()=>i.align==="left"?"origin-top-left left-0":i.align==="right"?"origin-top-right right-0":"origin-top"),g=b(!1);return(j,k)=>(o(),n("div",I,[t("div",{onClick:k[0]||(k[0]=L=>g.value=!g.value)},[M(j.$slots,"trigger")]),N(t("div",{class:"fixed inset-0 z-40",onClick:k[1]||(k[1]=L=>g.value=!1)},null,512),[[A,g.value]]),p(D,{"enter-active-class":"transition ease-out duration-200","enter-from-class":"opacity-0 scale-95","enter-to-class":"opacity-100 scale-100","leave-active-class":"transition ease-in duration-75","leave-from-class":"opacity-100 scale-100","leave-to-class":"opacity-0 scale-95"},{default:r(()=>[N(t("div",{class:l(["absolute z-50 mt-2 rounded-md shadow-lg",[v.value,e.value]]),style:{display:"none"},onClick:k[2]||(k[2]=L=>g.value=!1)},[t("div",{class:l(["rounded-md ring-1 ring-black ring-opacity-5",a.contentClasses])},[M(j.$slots,"content")],2)],2),[[A,g.value]])]),_:3})]))}};const h={__name:"SideMenu",props:{href:{type:String,required:!0},active:{type:Boolean}},setup(a){const i=a,d=z(()=>i.active?"block bg-indigo-600 text-white group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition duration-150 ease-in-out":"block text-gray-700 hover:text-white hover:bg-indigo-600 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition duration-150 ease-in-out");return(v,e)=>(o(),u(q(E),{href:a.href,class:l(d.value)},{default:r(()=>[M(v.$slots,"svg"),M(v.$slots,"name")]),_:3},8,["href","class"]))}},U={key:0,id:"toast-success","aria-live":"assertive",class:"pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6 z-40"},R={class:"flex w-full flex-col items-center space-y-4 sm:items-end"},G={class:"pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-blue-100 border border-blue-400 shadow-lg ring-1 ring-black ring-opacity-5"},J={class:"p-4"},W={class:"flex items-start"},Q=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-6 w-6 text-blue-700",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("circle",{cx:"12",cy:"12",r:"9","stroke-linecap":"round","stroke-linejoin":"round"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 8v0.01M12 11v5"})])],-1),K={class:"ml-3 w-0 flex-1 pt-0.5"},Y={class:"text-sm font-medium text-blue-700"},X=B('<div class="ml-4 flex flex-shrink-0"><button type="button" class="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"><span class="sr-only">Close</span><svg class="h-5 w-5 text-blue-700 bg-blue-100" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"></path></svg></button></div>',1),Z={__name:"ToastNotification",props:{message:String},setup(a){const i=b(!1),d=()=>{i.value=!1,S().props.flash.message=""};return C(()=>{i.value=!0,setTimeout(()=>d(),2e3)}),(v,e)=>i.value&&a.message?(o(),n("div",U,[t("div",R,[t("div",G,[t("div",J,[t("div",W,[Q,t("div",K,[t("p",Y,w(a.message)+"!",1)]),X])])])])])):s("",!0)}},ee={key:0,id:"toast-success","aria-live":"assertive",class:"pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6 z-40"},te={class:"flex w-full flex-col items-center space-y-4 sm:items-end"},re={class:"pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-green-100 border border-green-400 shadow-lg ring-1 ring-black ring-opacity-5"},oe={class:"p-4"},ne={class:"flex items-start"},se=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-6 w-6 text-green-700",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1),ie={class:"ml-3 w-0 flex-1 pt-0.5"},ae={class:"text-sm font-medium text-green-700"},le=B('<div class="ml-4 flex flex-shrink-0"><button type="button" class="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"><span class="sr-only">Close</span><svg class="h-5 w-5 text-green-700 bg-green-100" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"></path></svg></button></div>',1),ue={__name:"ToastNotificationSuccess",props:{message:String},setup(a){const i=b(!1),d=()=>{i.value=!1,S().props.flash.success=""};return C(()=>{i.value=!0,setTimeout(()=>d(),2e3)}),(v,e)=>i.value&&a.message?(o(),n("div",ee,[t("div",te,[t("div",re,[t("div",oe,[t("div",ne,[se,t("div",ie,[t("p",ae,w(a.message)+"!",1)]),le])])])])])):s("",!0)}},ce={key:0,id:"toast-success","aria-live":"assertive",class:"pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6 z-40"},de={class:"flex w-full flex-col items-center space-y-4 sm:items-end"},he={class:"pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-red-100 border border-red-400 shadow-lg ring-1 ring-black ring-opacity-5"},pe={class:"p-4"},ve={class:"flex items-start"},ge=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("circle",{cx:"12",cy:"12",r:"9","stroke-linecap":"round","stroke-linejoin":"round"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8 8l8 8m0-8L8 16"})])],-1),fe={class:"ml-3 w-0 flex-1 pt-0.5"},me={class:"text-sm font-medium text-red-700"},we=B('<div class="ml-4 flex flex-shrink-0"><button type="button" class="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"><span class="sr-only">Close</span><svg class="h-5 w-5 text-red-700 bg-red-100" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"></path></svg></button></div>',1),ke={__name:"ToastNotificationError",props:{message:String},setup(a){const i=b(!1),d=()=>{i.value=!1,S().props.flash.error=""};return C(()=>{i.value=!0,setTimeout(()=>d(),2e3)}),(v,e)=>i.value&&a.message?(o(),n("div",ce,[t("div",de,[t("div",he,[t("div",pe,[t("div",ve,[ge,t("div",fe,[t("p",me,w(a.message)+"!",1)]),we])])])])])):s("",!0)}},ye={key:0,id:"toast-success","aria-live":"assertive",class:"pointer-events-none fixed inset-0 flex items-end px-4 py-6 sm:items-start sm:p-6 z-40"},_e={class:"flex w-full flex-col items-center space-y-4 sm:items-end"},be={class:"pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg bg-yellow-100 border border-yellow-400 shadow-lg ring-1 ring-black ring-opacity-5"},$e={class:"p-4"},xe={class:"flex items-start"},Me=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-6 w-6 text-yellow-700",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 2L2 22h20L12 2zM12 16v-4M12 20v-2"})])],-1),Ce={class:"ml-3 w-0 flex-1 pt-0.5"},je={class:"text-sm font-medium text-yellow-700"},Le=B('<div class="ml-4 flex flex-shrink-0"><button type="button" class="inline-flex rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"><span class="sr-only">Close</span><svg class="h-5 w-5 text-yellow-700 bg-yellow-100" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"></path></svg></button></div>',1),Be={__name:"ToastNotificationWarning",props:{message:String},setup(a){const i=b(!1),d=()=>{i.value=!1,S().props.flash.warning=""};return C(()=>{i.value=!0,setTimeout(()=>d(),2e3)}),(v,e)=>i.value&&a.message?(o(),n("div",ye,[t("div",_e,[t("div",be,[t("div",$e,[t("div",xe,[Me,t("div",Ce,[t("p",je,w(a.message)+"!",1)]),Le])])])])])):s("",!0)}};const _={__name:"ActionLink",props:{href:{type:String,required:!0}},setup(a){return(i,d)=>(o(),u(q(E),{href:a.href,class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none transition duration-150 ease-in-out w-full"},{default:r(()=>[M(i.$slots,"svg"),M(i.$slots,"text")]),_:3},8,["href"]))}},ze={class:"relative"},Se=t("span",{class:"sr-only"},"View Notifications",-1),Ne=t("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"})],-1),Ae={key:0,class:"absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform bg-red-600 rounded-full"},Ve={key:0,class:"absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50"},Te=t("div",{class:"p-4 border-b border-gray-200"},[t("h3",{class:"text-lg font-semibold text-gray-900"},"Notifications")],-1),Oe={class:"max-h-96 overflow-y-auto"},qe={key:0,class:"p-4 text-center text-gray-500"},Ee={key:1},De=["onClick"],Fe={class:"flex items-start space-x-3"},He={class:"flex-shrink-0"},Pe={key:0,class:"w-2 h-2 bg-blue-600 rounded-full"},Ie={key:1,class:"w-2 h-2 bg-gray-300 rounded-full"},Ue={class:"flex-1 min-w-0"},Re={class:"text-sm text-gray-900"},Ge={class:"text-xs text-gray-500 mt-1"},Je={key:0,class:"p-4 border-t border-gray-200"},We={__name:"NotificationDropdown",props:{notifications:{type:Array,default:()=>[]}},setup(a){const i=a,d=b(!1),v=z(()=>i.notifications.filter(f=>!f.read_at).length),e=()=>{d.value=!d.value},g=async f=>{try{await V.post(`/notifications/${f}/mark-as-read`);const $=i.notifications.find(m=>m.id===f);$&&($.read_at=new Date().toISOString())}catch($){console.error("Failed to mark notification as read:",$)}},j=async()=>{try{await V.post("/notifications/mark-all-as-read"),i.notifications.forEach(f=>{f.read_at=new Date().toISOString()}),d.value=!1}catch(f){console.error("Failed to mark all as read:",f)}},k=f=>{const $=new Date(f),x=Math.floor((new Date-$)/1e3);if(x<60)return"Just now";if(x<3600){const y=Math.floor(x/60);return`${y} minute${y>1?"s":""} ago`}else if(x<86400){const y=Math.floor(x/3600);return`${y} hour${y>1?"s":""} ago`}else{const y=Math.floor(x/86400);return`${y} day${y>1?"s":""} ago`}},L=f=>{f.target.closest(".relative")||(d.value=!1)};return C(()=>{document.addEventListener("click",L)}),O(()=>{document.removeEventListener("click",L)}),(f,$)=>(o(),n("div",ze,[t("button",{onClick:e,type:"button",class:"-m-2.5 p-2.5 text-gray-400 hover:text-gray-500 relative"},[Se,Ne,v.value>0?(o(),n("span",Ae,w(v.value>99?"99+":v.value),1)):s("",!0)]),d.value?(o(),n("div",Ve,[Te,t("div",Oe,[a.notifications.length===0?(o(),n("div",qe," No notifications found ")):(o(),n("div",Ee,[(o(!0),n(F,null,H(a.notifications,m=>(o(),n("div",{key:m.id,class:l(["p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer",{"bg-blue-50":!m.read_at}]),onClick:x=>g(m.id)},[t("div",Fe,[t("div",He,[m.read_at?(o(),n("div",Ie)):(o(),n("div",Pe))]),t("div",Ue,[t("p",Re,w(m.data.message||"New notification"),1),t("p",Ge,w(k(m.created_at)),1)])])],10,De))),128))]))]),a.notifications.length>0?(o(),n("div",Je,[t("button",{onClick:j,class:"w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium"}," Mark all as read ")])):s("",!0)])):s("",!0)]))}};const Qe={class:"lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col border-t border-r"},Ke={class:"bg-white flex h-16 px-6 items-center px-10 shrink-0 w-full mt-2"},Ye=["src"],Xe={class:"flex grow flex-col gap-y-2 overflow-y-auto bg-gray-900 mt-2"},Ze={class:"flex flex-1 flex-col px-6"},et={role:"list",class:"flex flex-1 flex-col gap-y-7"},tt={role:"list",class:"-mx-2 space-y-1"},rt=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"},null,-1),ot=[rt],nt=t("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm3 14l-3-3-3 3M12 5a4 4 0 1 0 0 8 4 4 0 0 0 0-8z"},null,-1),st=[nt],it=t("path",{d:"M6 2h10l4 4v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"},null,-1),at=t("path",{d:"M14 2v4h4"},null,-1),lt=t("path",{d:"M8 10h8"},null,-1),ut=t("path",{d:"M8 14h6"},null,-1),ct=t("path",{d:"M8 18h8"},null,-1),dt=[it,at,lt,ut,ct],ht=t("rect",{x:"5",y:"4",width:"14",height:"20",rx:"2"},null,-1),pt=t("path",{d:"M7 4v2"},null,-1),vt=t("path",{d:"M17 4v2"},null,-1),gt=t("path",{d:"M7 8h10"},null,-1),ft=t("path",{d:"M7 12h10"},null,-1),mt=t("path",{d:"M7 16h6"},null,-1),wt=t("rect",{x:"22",y:"5",width:"3",height:"18",rx:"1"},null,-1),kt=t("path",{d:"M22 23h3v2h-3z"},null,-1),yt=[ht,pt,vt,gt,ft,mt,wt,kt],_t=t("path",{d:"M9 3.5l2.5 2.5 5-5","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),bt=t("g",{transform:"translate(0, 2)"},[t("path",{d:"M3 6h2l3.6 9.6a1 1 0 0 0 .94.7h8.92a1 1 0 0 0 .94-.7L21 8H6"}),t("circle",{cx:"9",cy:"20",r:"2"}),t("circle",{cx:"17",cy:"20",r:"2"})],-1),$t=[_t,bt],xt=t("path",{d:"M4 21V7a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v14"},null,-1),Mt=t("path",{d:"M8 10h2M8 14h2M8 18h2M14 10h2M14 14h2M14 18h2"},null,-1),Ct=t("path",{d:"M10 21v-4h4v4"},null,-1),jt=t("path",{d:"M2 21h20"},null,-1),Lt=[xt,Mt,Ct,jt],Bt=t("path",{d:"M6 2h10l4 4v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"},null,-1),zt=t("path",{d:"M14 2v4h4"},null,-1),St=t("path",{d:"M8 12h8l-1.5 4h-5z"},null,-1),Nt=t("circle",{cx:"10",cy:"18",r:"1"},null,-1),At=t("circle",{cx:"14",cy:"18",r:"1"},null,-1),Vt=[Bt,zt,St,Nt,At],Tt=t("rect",{x:"4",y:"3",width:"16",height:"18",rx:"2",ry:"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),Ot=t("path",{d:"M6 3v2M9 3v2M12 3v2M15 3v2M18 3v2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),qt=t("rect",{x:"5",y:"7",width:"3",height:"3",rx:"0.5",ry:"0.5","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),Et=t("path",{d:"M5.8 8.5l0.8 0.8 1.5-1.5","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),Dt=t("path",{d:"M10 8.5h8","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),Ft=t("rect",{x:"5",y:"12",width:"3",height:"3",rx:"0.5",ry:"0.5","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),Ht=t("path",{d:"M5.8 13.5l0.8 0.8 1.5-1.5","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),Pt=t("path",{d:"M10 13.5h8","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),It=t("rect",{x:"5",y:"17",width:"3",height:"3",rx:"0.5",ry:"0.5","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),Ut=t("path",{d:"M5.8 18.5l0.8 0.8 1.5-1.5","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),Rt=t("path",{d:"M10 18.5h8","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),Gt=[Tt,Ot,qt,Et,Dt,Ft,Ht,Pt,It,Ut,Rt],Jt=t("rect",{x:"3",y:"2",width:"18",height:"20",rx:"2",ry:"2"},null,-1),Wt=t("path",{d:"M3 6h14M3 10h12M3 14h10M3 18h8","stroke-linecap":"round"},null,-1),Qt=t("path",{d:"M16 15l4 4m-5-3l3 3m-2-4l4 4M14 17l5 5","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),Kt=[Jt,Wt,Qt],Yt=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"},null,-1),Xt=[Yt],Zt=t("rect",{x:"4",y:"3",width:"16",height:"18",rx:"2",ry:"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),er=t("text",{x:"7",y:"9","font-size":"2.5","font-weight":"normal",fill:"currentColor"},"INVOICE",-1),tr=t("path",{d:"M7 12h10","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),rr=t("path",{d:"M7 15h10","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),or=t("path",{d:"M7 18h10","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),nr=[Zt,er,tr,rr,or],sr=t("rect",{x:"6",y:"6",width:"36",height:"28",rx:"3",ry:"3",fill:"none"},null,-1),ir=t("circle",{cx:"16",cy:"14",r:"4",fill:"none"},null,-1),ar=t("path",{d:"M12 26v-5a6 6 0 0 1 8 0v5",fill:"none"},null,-1),lr=t("path",{d:"M24 14h12",fill:"none"},null,-1),ur=t("path",{d:"M24 20h12",fill:"none"},null,-1),cr=t("path",{d:"M24 26h12",fill:"none"},null,-1),dr=[sr,ir,ar,lr,ur,cr],hr=t("path",{d:"M6 2h12a2 2 0 0 1 2 2v2H4V4a2 2 0 0 1 2-2zm0 4h12v14H6V6z"},null,-1),pr=t("path",{d:"M8 12h8v2H8v-2zm0 4h8v2H8v-4z"},null,-1),vr=[hr,pr],gr=t("rect",{x:"6",y:"22",width:"14",height:"14",rx:"2",ry:"2",stroke:"currentColor",fill:"none","stroke-width":"3"},null,-1),fr=t("rect",{x:"26",y:"22",width:"14",height:"14",rx:"2",ry:"2",stroke:"currentColor",fill:"none","stroke-width":"3"},null,-1),mr=t("rect",{x:"16",y:"6",width:"16",height:"14",rx:"2",ry:"2",stroke:"currentColor",fill:"none","stroke-width":"3"},null,-1),wr=t("path",{d:"M16 20 L12 22",stroke:"currentColor","stroke-width":"3"},null,-1),kr=t("path",{d:"M32 20 L36 22",stroke:"currentColor","stroke-width":"3"},null,-1),yr=[gr,fr,mr,wr,kr],_r=t("rect",{x:"3",y:"5",width:"18",height:"14",rx:"2",ry:"2",stroke:"currentColor"},null,-1),br=t("path",{d:"M21 10h-6a2 2 0 0 0-2 2v0a2 2 0 0 0 2 2h6",stroke:"currentColor"},null,-1),$r=t("circle",{cx:"17",cy:"12",r:"1.5",stroke:"currentColor"},null,-1),xr=[_r,br,$r],Mr=t("path",{d:"M5 2h14a2 2 0 0 1 2 2v16l-2-1-2 1-2-1-2 1-2-1-2 1-2-1-2 1V4a2 2 0 0 1 2-2z",stroke:"currentColor"},null,-1),Cr=t("line",{x1:"8",y1:"6",x2:"16",y2:"6",stroke:"currentColor"},null,-1),jr=t("line",{x1:"8",y1:"10",x2:"16",y2:"10",stroke:"currentColor"},null,-1),Lr=t("line",{x1:"8",y1:"14",x2:"12",y2:"14",stroke:"currentColor"},null,-1),Br=[Mr,Cr,jr,Lr],zr=t("path",{d:"M3 10l9-7 9 7"},null,-1),Sr=t("path",{d:"M4 10v10a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V10"},null,-1),Nr=t("path",{d:"M8 21v-6h8v6"},null,-1),Ar=t("path",{d:"M12 14v7"},null,-1),Vr=[zr,Sr,Nr,Ar],Tr=t("path",{d:"M6 2h10l4 4v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"},null,-1),Or=t("path",{d:"M14 2v4h4"},null,-1),qr=t("path",{d:"M8 14v4"},null,-1),Er=t("path",{d:"M12 10v8"},null,-1),Dr=t("path",{d:"M16 12v6"},null,-1),Fr=[Tr,Or,qr,Er,Dr],Hr=t("rect",{x:"3",y:"4",width:"18",height:"16",rx:"2",ry:"2"},null,-1),Pr=t("path",{d:"M8 2v4"},null,-1),Ir=t("path",{d:"M16 2v4"},null,-1),Ur=t("path",{d:"M3 10h18"},null,-1),Rr=t("path",{d:"M7 14l2 2 3-3"},null,-1),Gr=[Hr,Pr,Ir,Ur,Rr],Jr=t("path",{d:"M3 4h18l-6 8v5l-6 3v-8z"},null,-1),Wr=[Jr],Qr=t("circle",{cx:"8",cy:"8",r:"4"},null,-1),Kr=t("path",{d:"M3 20v-2a6 6 0 0 1 12 0v2"},null,-1),Yr=t("rect",{x:"14",y:"6",width:"6",height:"10",rx:"1"},null,-1),Xr=t("path",{d:"M16 8h2"},null,-1),Zr=t("path",{d:"M16 10h2"},null,-1),eo=t("path",{d:"M16 12h2"},null,-1),to=t("path",{d:"M14 17l2 2 4-4"},null,-1),ro=[Qr,Kr,Yr,Xr,Zr,eo,to],oo={class:"mt-auto px-4 mt-1"},no=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"},null,-1),so=t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},null,-1),io=[no,so],ao={class:"lg:pl-72 border-t"},lo={class:"sticky top-0 z-20 flex h-16 shrink-0 items-center gap-x-4 bg-white px-4 shadow sm:gap-x-6 sm:px-6 lg:px-8"},uo=B('<button type="button" class="-m-2.5 p-2.5 text-gray-700 lg:hidden"><span class="sr-only">Open sidebar</span><svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path></svg></button><div class="h-6 w-px bg-gray-200 lg:hidden" aria-hidden="true"></div>',2),co={class:"flex flex-1 gap-x-4 self-stretch lg:gap-x-6 justify-between"},ho={class:"flex items-center"},po=t("div",{class:"flex w-32"},[t("a",{class:"flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600",href:"#"}," Add New +")],-1),vo=t("span",{class:"text-sm text-gray-700 leading-6"},"Add New User",-1),go=t("span",{class:"text-sm text-gray-700 leading-6"},"Add New Customer",-1),fo=t("span",{class:"text-sm text-gray-700 leading-6"},"Add New Company",-1),mo=t("span",{class:"text-sm text-gray-700 leading-6"},"Create Challan",-1),wo=t("span",{class:"text-sm text-gray-700 leading-6"},"Create Order",-1),ko=t("span",{class:"text-sm text-gray-700 leading-6"},"Purchase Order",-1),yo={class:"flex items-center gap-x-4 lg:gap-x-6"},_o=t("div",{class:"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200","aria-hidden":"true"},null,-1),bo={class:"relative"},$o={type:"button",class:"-m-1.5 flex items-center p-1.5",id:"user-menu-button","aria-expanded":"false","aria-haspopup":"true"},xo=t("span",{class:"sr-only"},"Open User Menu",-1),Mo=t("img",{class:"h-8 w-8 rounded-full bg-gray-50",src:"https://img.freepik.com/premium-photo/avatar-resourcing-company_1254967-6696.jpg?size=626&ext=jpg&ga=GA1.1.*********.1729255085&semt=ais_hybrid",alt:""},null,-1),Co={class:"hidden lg:flex lg:items-center"},jo={class:"ml-4 text-sm font-semibold leading-6 text-gray-900","aria-hidden":"true"},Lo=t("svg",{class:"ml-2 h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z","clip-rule":"evenodd"})],-1),Bo=t("span",{class:"text-sm text-gray-700 leading-5"},"Your Profile",-1),zo=t("span",{class:"text-sm text-gray-700 leading-5"},"Sign Out",-1),So={key:0},No={key:1},Ao={key:2},Vo={key:3},To={class:"py-10 bg-slate-100"},Oo={class:"px-4 sm:px-6 lg:px-8 min-h-screen"},Eo={__name:"AdminLayout",props:{notifications:{type:Array,default:()=>[]}},setup(a){const i=b("/uploads/companyprofile/defaultimg.png");P({});const d=e=>{i.value=e},v=b("/uploads/companyprofile/visionlogo.png");return C(async()=>{try{const e=await fetch("/api/logo");if(e.ok){const g=await e.json();g.logoUrl?d("/uploads/companyprofile/"+g.logoUrl):d("/uploads/companyprofile/defaultimg.png")}}catch(e){console.error("Error fetching logo:",e)}}),(e,g)=>(o(),n("div",null,[t("div",Qe,[t("div",Ke,[t("img",{class:"h-10",src:v.value,alt:"LOGO"},null,8,Ye)]),t("div",Xe,[t("nav",Ze,[t("ul",et,[t("li",null,[t("ul",tt,[t("li",null,[e.$can("Dashboard")?(o(),u(h,{key:0,href:e.route("dashboard"),active:e.route().current("dashboard")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0",e.route().current("dashboard")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},ot,2))]),name:r(()=>[c("Dashboard")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Customer")?(o(),u(h,{key:0,href:e.route("customers.index"),active:e.route().current("customers.index")||e.route().current("customers.create")||e.route().current("customers.edit")||e.route().current("customers.transaction")||e.route().current("customers.credit")||e.route().current("service-reports.show")||e.route().current("service-reports.create")||e.route().current("service-reports.edit")||e.route().current("upload-service-report")||e.route().current("service-reports.view")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0",e.route().current("customers.index")||e.route().current("customers.create")||e.route().current("customers.edit")||e.route().current("customers.transaction")||e.route().current("customers.credit")||e.route().current("service-reports.show")||e.route().current("service-reports.create")||e.route().current("service-reports.edit")||e.route().current("upload-service-report")||e.route().current("service-reports.view")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},st,2))]),name:r(()=>[c("Customers")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Quotation")?(o(),u(h,{key:0,href:e.route("quotation.index"),active:e.route().current("quotation.index")||e.route().current("quotation.create")||e.route().current("quotation.edit")||e.route().current("quotation.view")||e.route().current("quotation.order")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0",e.route().current("quotation.index")||e.route().current("quotation.create")||e.route().current("quotation.edit")||e.route().current("quotation.view")||e.route().current("quotation.order")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},dt,2))]),name:r(()=>[c("Quotation")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Orders")?(o(),u(h,{key:0,href:e.route("proforma-invoice.index"),active:e.route().current("proforma-invoice.index")||e.route().current("proforma-invoice.create")||e.route().current("proforma-invoice.edit")||e.route().current("proforma-invoice.view")||e.route().current("proforma-invoice.deliver")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0",e.route().current("proforma-invoice.index")||e.route().current("proforma-invoice.create")||e.route().current("proforma-invoice.edit")||e.route().current("proforma-invoice.view")||e.route().current("proforma-invoice.deliver")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 32 32",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},yt,2))]),name:r(()=>[c("Proforma Invoice")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Orders")?(o(),u(h,{key:0,href:e.route("orders.index"),active:e.route().current("orders.index")||e.route().current("orders.create")||e.route().current("orders.edit")||e.route().current("orders.view")||e.route().current("orders.deliver")||e.route().current("orders.generate")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0",e.route().current("orders.index")||e.route().current("orders.create")||e.route().current("orders.edit")||e.route().current("orders.view")||e.route().current("orders.deliver")||e.route().current("orders.generate")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},$t,2))]),name:r(()=>[c("Orders")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Company")?(o(),u(h,{key:0,href:e.route("companies.index"),active:e.route().current("companies.index")||e.route().current("companies.create")||e.route().current("companies.edit")||e.route().current("products.show")||e.route().current("products.create")||e.route().current("products.edit")||e.route().current("companies.transaction")||e.route().current("companies.credit")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0",e.route().current("companies.index")||e.route().current("companies.create")||e.route().current("companies.edit")||e.route().current("products.show")||e.route().current("products.create")||e.route().current("products.edit")||e.route().current("companies.transaction")||e.route().current("companies.credit")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Lt,2))]),name:r(()=>[c("Company")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Companypo")?(o(),u(h,{key:0,href:e.route("companypo.index"),active:e.route().current("companypo.index")||e.route().current("companypo.create")||e.route().current("companypo.receivepo")||e.route().current("companypo.viewpo")||e.route().current("companypo.edit")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0",e.route().current("companypo.index")||e.route().current("companypo.create")||e.route().current("companypo.receivepo")||e.route().current("companypo.viewpo")||e.route().current("companypo.edit")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Vt,2))]),name:r(()=>[c("Company PO")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List PurchaseInvoice")?(o(),u(h,{key:0,href:e.route("purchaseinvoice.index"),active:e.route().current("purchaseinvoice.index")||e.route().current("purchaseinvoice.view")||e.route().current("purchaseinvoice.edit")||e.route().current("purchaseinvoice.convert-to-invoice")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0",e.route().current("purchaseinvoice.index")||e.route().current("purchaseinvoice.view")||e.route().current("purchaseinvoice.edit")||e.route().current("purchaseinvoice.convert-to-invoice")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},Gt,2))]),name:r(()=>[c(" Purchase ")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Challan")?(o(),u(h,{key:0,href:e.route("challan.index"),active:e.route().current("challan.index")||e.route().current("challan.create")||e.route().current("challan.edit")||e.route().current("challan.view")||e.route().current("challan.invoice")||e.route().current("challan.combine-invoice")||e.route().current("challantransfer.edit")||e.route().current("challantransfer")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0 transition-colors duration-200",{"text-white":["challan.index","challan.create","challan.edit","challan.view","challan.invoice","challan.combine-invoice","challantransfer.edit","challantransfer"].includes(e.route().current()),"text-gray-700 group-hover:text-white":!["challan.index","challan.create","challan.edit","challan.view","challan.invoice","challan.combine-invoice","challantransfer.edit","challantransfer"].includes(e.route().current())}]),viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor","stroke-width":"1.5"},Kt,2))]),name:r(()=>[c("Challan")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Users")?(o(),u(h,{key:0,href:e.route("users.index"),active:e.route().current("users.index")||e.route().current("users.create")||e.route().current("users.edit")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0",e.route().current("users.index")||e.route().current("users.create")||e.route().current("users.edit")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},Xt,2))]),name:r(()=>[c("Users")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Invoice")?(o(),u(h,{key:0,href:e.route("invoice.index"),active:e.route().current("invoice.index")||e.route().current("invoice.view")||e.route().current("stocktransfer")||e.route().current("invoice.create")||e.route().current("invoice.edit")||e.route().current("stocktransfer.edit")||e.route().current("challaninvoice.edit")||e.route().current("creditnote.add")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0",e.route().current("invoice.index")||e.route().current("invoice.view")||e.route().current("stocktransfer")||e.route().current("invoice.create")||e.route().current("invoice.edit")||e.route().current("stocktransfer.edit")||e.route().current("challaninvoice.edit")||e.route().current("creditnote.add")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},nr,2))]),name:r(()=>[c("Invoice")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Jobcard")?(o(),u(h,{key:0,href:e.route("jobcard.index"),active:e.route().current("jobcard.index")||e.route().current("jobcard.show")||e.route().current("jobcard.create")||e.route().current("jobcard.edit")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0 transition-colors duration-200",{"text-white":["jobcard.index","jobcard.show","jobcard.create","jobcard.edit"].includes(e.route().current()),"text-gray-700 group-hover:text-white":!["jobcard.index","jobcard.show","jobcard.create","jobcard.edit"].includes(e.route().current())}]),viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},dr,2))]),name:r(()=>[c("Service Jobcard")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Maintenance")?(o(),u(h,{key:0,href:e.route("maintenance-contract.index"),active:e.route().current("maintenance-contract.index")||e.route().current("maintenance-contract.create")||e.route().current("maintenance-contract.edit")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6",e.route().current("maintenance-contract.index")||e.route().current("maintenance-contract.create")||e.route().current("maintenance-contract.edit")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},vr,2))]),name:r(()=>[c("Maintenance Contract")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("Sales Stock")||e.$can("Service Stock")?(o(),u(h,{key:0,href:e.route("salesstock"),active:e.route().current("salesstock")||e.route().current("servicestock")||e.route().current("products.history")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0",e.route().current("salesstock")||e.route().current("servicestock")||e.route().current("products.history")?"text-white":"text-gray-700 group-hover:text-white"]),viewBox:"0 0 48 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor","stroke-width":"3","stroke-linecap":"round","stroke-linejoin":"round"},yr,2))]),name:r(()=>[c("Stock")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Bank Transaction")?(o(),u(h,{key:0,href:e.route("payment.index"),active:e.route().current("payment.index")||e.route().current("payment.create")||e.route().current("payment.edit")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6",e.route().current("payment.index")||e.route().current("payment.create")||e.route().current("payment.edit")?"text-white":"text-gray-700 group-hover:text-white"]),viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},xr,2))]),name:r(()=>[c("Payment")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Bank Transaction")?(o(),u(h,{key:0,href:e.route("receipt.index"),active:e.route().current("receipt.index")||e.route().current("receipt.create")||e.route().current("receipt.edit")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6",e.route().current("receipt.index")||e.route().current("receipt.create")||e.route().current("receipt.edit")?"text-white":"text-gray-700 group-hover:text-white"]),viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Br,2))]),name:r(()=>[c("Receipt")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Bank Transaction")?(o(),u(h,{key:0,href:e.route("banktransaction.index"),active:e.route().current("banktransaction.index")||e.route().current("banktransaction.show")||e.route().current("banktransaction.create")||e.route().current("banktransaction.edit")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6",e.route().current("banktransaction.index")||e.route().current("banktransaction.show")||e.route().current("banktransaction.create")||e.route().current("banktransaction.edit")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Vr,2))]),name:r(()=>[c("Bank Transactions")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Reports")?(o(),u(h,{key:0,href:e.route("reports"),active:e.route().current("reports")||e.route().current("engineer-business.report")||e.route().current("invoice.pending-amount")||e.route().current("sns-stock.report")||e.route().current("sns-sales.report")||e.route().current("sns-sales.report")||e.route().current("sns-cusomersales-report")||e.route().current("hsn.sales.summary")||e.route().current("customer-transaction.report")||e.route().current("creditnote.index")||e.route().current("creditnote.show")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6",e.route().current("reports")||e.route().current("engineer-business.report")||e.route().current("invoice.pending-amount")||e.route().current("sns-stock.report")||e.route().current("sns-sales.report")||e.route().current("sns-customersales-report")||e.route().current("hsn.sales.summary")||e.route().current("creditnote.index")||e.route().current("customer-transaction.report")||e.route().current("creditnote.show")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Fr,2))]),name:r(()=>[c("Reports")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Weekly Planning")?(o(),u(h,{key:0,href:e.route("weeklyplan.index"),active:e.route().current("weeklyplan.index")||e.route().current("weeklyplan.create")||e.route().current("weeklyplan.edit")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6",e.route().current("weeklyplan.index")||e.route().current("weeklyplan.create")||e.route().current("weeklyplan.edit")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Gr,2))]),name:r(()=>[c("Daily Plan")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("List Funnel")?(o(),u(h,{key:0,href:e.route("funnel.index"),active:e.route().current("funnel.index")||e.route().current("funnel.create")||e.route().current("funnel.edit")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0",e.route().current("funnel.index")||e.route().current("funnel.create")||e.route().current("funnel.edit")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},Wr,2))]),name:r(()=>[c("Funnel")]),_:1},8,["href","active"])):s("",!0)]),t("li",null,[e.$can("Activity Log")?(o(),u(h,{key:0,href:e.route("logs"),active:e.route().current("logs")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0",e.route().current("logs")?"text-white":"text-gray-700 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},ro,2))]),name:r(()=>[c("User Activity")]),_:1},8,["href","active"])):s("",!0)])])])])])]),t("div",oo,[e.$can("List Setting")?(o(),u(h,{key:0,href:e.route("setting"),active:e.route().current("setting")||e.route().current("organization.index")||e.route().current("organization.create")||e.route().current("organization.edit")||e.route().current("manage-prefix")||e.route().current("roles.permission")||e.route().current("bankinfo.index")||e.route().current("bankinfo.create")||e.route().current("bankinfo.edit")||e.route().current("account-type.index")||e.route().current("account-type.create")||e.route().current("account-type.edit")||e.route().current("roles.index")||e.route().current("roles.create")||e.route().current("roles.edit")||e.route().current("jobcard-checklist.index")||e.route().current("jobcard-checklist.create")||e.route().current("jobcard-checklist.edit")||e.route().current("mail-configs.index")||e.route().current("mail-configs.create")||e.route().current("mail-configs.edit")||e.route().current("emailtemplates.index")||e.route().current("emailtemplates.create")||e.route().current("emailtemplates.edit")},{svg:r(()=>[(o(),n("svg",{class:l(["h-6 w-6 shrink-0 text-gray-700",e.route().current("setting")||e.route().current("organization.index")||e.route().current("organization.create")||e.route().current("organization.edit")||e.route().current("manage-prefix")||e.route().current("roles.permission")||e.route().current("bankinfo.index")||e.route().current("bankinfo.create")||e.route().current("bankinfo.edit")||e.route().current("account-type.index")||e.route().current("account-type.create")||e.route().current("account-type.edit")||e.route().current("jobcard-checklist.index")||e.route().current("jobcard-checklist.create")||e.route().current("jobcard-checklist.edit")||e.route().current("mail-configs.index")||e.route().current("mail-configs.create")||e.route().current("mail-configs.edit")||e.route().current("emailtemplates.index")||e.route().current("emailtemplates.create")||e.route().current("emailtemplates.edit")||e.route().current("roles.index")||e.route().current("roles.create")||e.route().current("roles.edit")?"text-white":"text-gray-500 group-hover:text-white"]),fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true"},io,2))]),name:r(()=>[c("Settings")]),_:1},8,["href","active"])):s("",!0)])]),t("div",ao,[t("div",lo,[uo,t("div",co,[t("div",ho,[p(T,{align:"left",width:"48"},{trigger:r(()=>[po]),content:r(()=>[p(_,{href:e.route("users.create")},{svg:r(()=>[]),text:r(()=>[vo]),_:1},8,["href"]),p(_,{href:e.route("customers.create")},{svg:r(()=>[]),text:r(()=>[go]),_:1},8,["href"]),p(_,{href:e.route("companies.create")},{svg:r(()=>[]),text:r(()=>[fo]),_:1},8,["href"]),p(_,{href:e.route("challan.create")},{svg:r(()=>[]),text:r(()=>[mo]),_:1},8,["href"]),p(_,{href:e.route("orders.create")},{svg:r(()=>[]),text:r(()=>[wo]),_:1},8,["href"]),p(_,{href:e.route("companypo.create")},{svg:r(()=>[]),text:r(()=>[ko]),_:1},8,["href"])]),_:1})]),t("div",yo,[p(We,{notifications:a.notifications},null,8,["notifications"]),_o,t("div",bo,[p(T,{align:"right",width:"48"},{trigger:r(()=>[t("button",$o,[xo,Mo,t("span",Co,[t("span",jo,w(e.$page.props.auth.user.name),1),Lo])])]),content:r(()=>[p(_,{href:e.route("profile.edit"),as:"button"},{svg:r(()=>[]),text:r(()=>[Bo]),_:1},8,["href"]),p(_,{href:e.route("logout"),method:"post",as:"button"},{svg:r(()=>[]),text:r(()=>[zo]),_:1},8,["href"])]),_:1})])])])]),e.$page.props.flash.message?(o(),n("div",So,[p(Z,{message:e.$page.props.flash.message},null,8,["message"])])):s("",!0),e.$page.props.flash.success?(o(),n("div",No,[p(ue,{message:e.$page.props.flash.success},null,8,["message"])])):s("",!0),c(w(e.$page.props.flash.error)+" ",1),e.$page.props.flash.error?(o(),n("div",Ao,[p(ke,{message:e.$page.props.flash.error},null,8,["message"])])):s("",!0),e.$page.props.flash.warning?(o(),n("div",Vo,[p(Be,{message:e.$page.props.flash.warning},null,8,["message"])])):s("",!0),t("main",To,[t("div",Oo,[M(e.$slots,"default")])])])]))}};export{Eo as _,_ as a,T as b};
