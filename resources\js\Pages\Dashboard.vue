<script setup>
import AdminLayout from "@/Layouts/AdminLayout.vue";
import { onBeforeMount, ref, computed } from "vue";
import SimpleDropdown from '@/Components/SimpleDropdown.vue';
import BarChart from '@/Components/BarChart.vue';
import Pie<PERSON>hart from '@/Components/PieChart.vue';
import MultiLineChart from '@/Components/MultiLineChart.vue';
import Pagination from '@/Components/Pagination.vue';
import { Head , useForm} from '@inertiajs/vue3';
import ActionLink from '@/Components/ActionLink.vue';

const form = useForm({});

const props = defineProps([
    "organizationId",
    "permissions",
    "organization",
    "user",
    "recentInvoices",
    "date",
    "customerCount",
    "ordersCount",
    "purchaseCount",
    "jobCardCount",
    "productCount",
    "taxInvoiceCount",
    "retailInvoiceCount",
    "companyCount",
    "quotationCount",
    "customer_id",
    "recentQuotations",
    "recentPurchaseOrder",
    "productsWithLowStock",
    "pieChartData",
    "salesDataByMonth",
    "purchaseDataByMonth",
    "salesEngdatasets"
]);

// const chartData = {
//   labels: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
//   datasets: [
//     {
//       label: 'Engineer A',
//       data: [120, 135, 140, 150, 160, 170, 180, 200, 195, 180, 170, 160],
//       borderColor: 'rgba(255, 99, 132, 1)',
//       backgroundColor: 'rgba(255, 99, 132, 0.2)',
//       fill: false,
//       tension: 0.4
//     },
//     {
//       label: 'Engineer B',
//       data: [110, 125, 130, 140, 150, 160, 165, 180, 175, 160, 155, 150],
//       borderColor: 'rgba(54, 162, 235, 1)',
//       backgroundColor: 'rgba(54, 162, 235, 0.2)',
//       fill: false,
//       tension: 0.4
//     }
//   ]
// }

const salesChartData = computed(() => ({
  labels: ['Apr', 'May', 'June', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
  datasets: [{
    label: 'Sales',
    data: props.salesDataByMonth,
    backgroundColor: [
        'rgba(255, 99, 132, 1)',     // Red
        'rgba(54, 162, 235, 1)',     // Blue
        'rgba(255, 206, 86, 1)',     // Yellow
        'rgba(75, 192, 192, 1)',     // Green
        'rgba(153, 102, 255, 1)',    // Purple
        'rgba(201, 203, 207, 1)',    // Gray
        'rgba(255, 159, 64, 1)',     // Orange
        'rgba(100, 149, 237, 1)',    // Cornflower Blue
        'rgba(255, 105, 180, 1)',    // Hot Pink
        'rgba(60, 179, 113, 1)',     // Medium Sea Green
        'rgba(147, 112, 219, 1)',    // Medium Purple
        'rgba(0, 191, 255, 1)',      // Deep Sky Blue
        'rgba(220, 20, 60, 1)',      // Crimson
        'rgba(255, 140, 0, 1)',      // Dark Orange
        'rgba(70, 130, 180, 1)',     // Steel Blue
        'rgba(199, 21, 133, 1)'      // Medium Violet Red
    ]
  }]
}));

const purchaseChartData = computed(() => ({
  labels: ['Apr','May','June','Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar',],
  datasets: [{
    label: 'Purchase',
    data: props.purchaseDataByMonth,
    backgroundColor: [
        'rgba(255, 99, 132, 1)',     // Red
        'rgba(54, 162, 235, 1)',     // Blue
        'rgba(255, 206, 86, 1)',     // Yellow
        'rgba(75, 192, 192, 1)',     // Green
        'rgba(153, 102, 255, 1)',    // Purple
        'rgba(201, 203, 207, 1)',    // Gray
        'rgba(255, 159, 64, 1)',     // Orange
        'rgba(100, 149, 237, 1)',    // Cornflower Blue
        'rgba(255, 105, 180, 1)',    // Hot Pink
        'rgba(60, 179, 113, 1)',     // Medium Sea Green
        'rgba(147, 112, 219, 1)',    // Medium Purple
        'rgba(0, 191, 255, 1)',      // Deep Sky Blue
        'rgba(220, 20, 60, 1)',      // Crimson
        'rgba(255, 140, 0, 1)',      // Dark Orange
        'rgba(70, 130, 180, 1)',     // Steel Blue
        'rgba(199, 21, 133, 1)'      // Medium Violet Red
   ]
  }]
}));


const chartOptions = {
  responsive: true,
  scales: {
    x: {
      beginAtZero: true
    },
    y: {
      beginAtZero: true
    }
  },
  plugins: {
    legend: {
      position: 'top',
    },
    tooltip: {
      callbacks: {
        label: (context) => {
          let label = context.dataset.label || '';
          if (label) {
            label += ': ';
          }
          if (context.parsed.y !== null) {
            label += new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            currencyDisplay: 'symbol'
            }).format(context.parsed.y);
          }
          return label;
        }
      }
    }
  }
};

const pieChartData = computed(() => ({
  labels: props.pieChartData.labels,
  datasets: [{
    label: 'Votes',
    data:  props.pieChartData.datasets[0].data,
    backgroundColor: [
        'rgba(255, 99, 132, 1)',     // Red
        'rgba(54, 162, 235, 1)',     // Blue
        'rgba(255, 206, 86, 1)',     // Yellow
        'rgba(75, 192, 192, 1)',     // Green
        'rgba(153, 102, 255, 1)',    // Purple
        'rgba(201, 203, 207, 1)',    // Gray
        'rgba(255, 159, 64, 1)',     // Orange
        'rgba(100, 149, 237, 1)',    // Cornflower Blue
        'rgba(255, 105, 180, 1)',    // Hot Pink
        'rgba(60, 179, 113, 1)',     // Medium Sea Green
        'rgba(147, 112, 219, 1)',    // Medium Purple
        'rgba(0, 191, 255, 1)',      // Deep Sky Blue
        'rgba(220, 20, 60, 1)',      // Crimson
        'rgba(255, 140, 0, 1)',      // Dark Orange
        'rgba(70, 130, 180, 1)',     // Steel Blue
        'rgba(199, 21, 133, 1)'      // Medium Violet Red
    ],
    borderWidth: 1
  }]
}));

const pieChartOptions = {
  responsive: true,
  plugins: {
    legend: {
      position: 'top',
    },
    tooltip: {
      callbacks: {
        label: (context) => {
        //   let label = context.label || '';
        //     if (label) {
        //         label += ': ';
        //     }
        //   if (context.raw !== null) {
        //     label += new Intl.NumberFormat().format(context.raw);
        //   }
            return Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR',
            currencyDisplay: 'symbol'
            }).format(context.raw);
        }
      }
    }
  }
};

const lineChartData = computed(() => ({
  labels: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
  datasets: props.salesEngdatasets // dynamically from PHP
}));

onBeforeMount(async () => {
    localStorage.setItem("permissions", JSON.stringify(props.permissions));
});

const organizationId = ref(props.organizationId);

const handleSearchChange = (organizationId) => {
    form.get(route('dashboard', { organization_id: organizationId }), {
        preserveState: true,
        // replace: true,
    });
};

const setOrganization = (id, name) => {
    organizationId.value = id;
    handleSearchChange(organizationId.value);
};
const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { year: "numeric", month: "short", day: "numeric" };
    return date.toLocaleDateString("en-US", options);
};

const formatAmount = (amount) => {
    let amountStr = amount.toFixed(2).toString();
    let [integerPart, decimalPart] = amountStr.split(".");
    let lastThree = integerPart.substring(integerPart.length - 3);
    let otherNumbers = integerPart.substring(0, integerPart.length - 3);
    if (otherNumbers !== "") {
        lastThree = "," + lastThree;
    }
    let formattedIntegerPart =
        otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ",") + lastThree;
    let formattedAmount = `${formattedIntegerPart}.${decimalPart}`;
    return formattedAmount;
};
</script>

<template>
    <Head title="Dashboard" />

    <AdminLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-gray-700 leading-tight">
                Dashboard
            </h2>
        </template>
        <div class="animate-top">
            <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-6">
                <div class="items-start">
                    <h1 class="text-xl lg:text-2xl font-semibold leading-7 text-gray-900">Dashboard</h1>
                </div>
                <div class="flex justify-end">
                    <div class="relative w-full lg:w-80">
                        <SimpleDropdown
                            :options="organization"
                            v-model="organizationId"
                            @onchange="setOrganization"
                        />
                    </div>
                </div>
            </div>
            <div class="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                <div class="w-full cursor-pointer hover:shadow-md shadow rounded-lg">
                    <div class="w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6">
                        <svg class="w-12 h-12 lg:w-16 lg:h-16 fill-current mr-4 flex-shrink-0" viewBox="0 0 24 24">
                            <circle cx="6" cy="8" r="3"/>
                            <path d="M2 18v-1.5c0-1.8 2.5-3.5 4.5-3.5 1 0 2 .2 2.8.6-1.1.8-1.8 2.1-1.8 3.4V18H2z"/>
                            <circle cx="12" cy="7" r="4"/>
                            <path d="M12 12c-3.5 0-7 1.8-7 4v2h14v-2c0-2.2-3.5-4-7-4z"/>
                            <circle cx="18" cy="8" r="3"/>
                            <path d="M22 18v-1.5c0-1.8-2.5-3.5-4.5-3.5-1 0-2 .2-2.8.6 1.1.8 1.8 2.1 1.8 3.4V18H22z"/>
                        </svg>
                        <div class="text-gray-700 min-w-0">
                            <p class="font-semibold text-2xl lg:text-3xl">{{ customerCount }}</p>
                            <p class="text-sm lg:text-base">Total Customers</p>
                        </div>
                    </div>
                </div>
                <div class="w-full cursor-pointer hover:shadow-md shadow rounded-lg">
                    <div class="w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6">
                        <svg class="w-12 h-12 lg:w-16 lg:h-16 fill-current mr-4 flex-shrink-0" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="3" y="6" width="18" height="12" rx="2" ry="2" stroke="currentColor" fill="none"/>
                            <path d="M6 10h12M6 14h12" stroke="currentColor"/>
                            <path d="M9 6V4h6v2M10 18v2h4v-2" stroke="currentColor"/>
                        </svg>
                        <div class="text-gray-700 min-w-0">
                            <p class="font-semibold text-2xl lg:text-3xl">{{ productCount }}</p>
                            <p class="text-sm lg:text-base">Total Products</p>
                        </div>
                    </div>
                </div>
                <div class="w-full cursor-pointer hover:shadow-md shadow rounded-lg">
                    <div class="w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6">
                        <svg class="w-12 h-12 lg:w-16 lg:h-16 fill-current mr-4 flex-shrink-0" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 21v-13c0-1.1.9-2 2-2h5V4c0-1.1.9-2 2-2h6c1.1 0 2 .9 2 2v17h2v2H1v-2h2zm2 0h4v-3h2v3h4v-5H5v5zm10-10h2v2h-2v-2zm0-3h2v2h-2v-2zm0-3h2v2h-2v-2z"/>
                        </svg>
                        <div class="text-gray-700 min-w-0">
                            <p class="font-semibold text-2xl lg:text-3xl">{{ companyCount }}</p>
                            <p class="text-sm lg:text-base">Total Companies</p>
                        </div>
                    </div>
                </div>
                <div class="w-full cursor-pointer hover:shadow-md shadow rounded-lg">
                    <div class="w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6">
                        <svg class="w-12 h-12 lg:w-16 lg:h-16 fill-current mr-4 flex-shrink-0" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6H6zm7 7V3.5L18.5 9H13zM8 12h8v2H8v-2zm0 4h5v2H8v-2zm0-8h8v2H8V8z"/>
                        </svg>
                        <div class="text-gray-700 min-w-0">
                            <p class="font-semibold text-2xl lg:text-3xl">{{ quotationCount }}</p>
                            <p class="text-sm lg:text-base">Total Quotations</p>
                        </div>
                    </div>
                </div>
                <div class="w-full cursor-pointer hover:shadow-md shadow rounded-lg">
                    <div class="w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6">
                        <svg class="w-12 h-12 lg:w-16 lg:h-14 fill-current mr-4 flex-shrink-0" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 3h2l1.6 6.6 1.4 5.4c.2.8.9 1.4 1.7 1.4h7c.8 0 1.5-.6 1.7-1.4l1.6-6.6H7.2l-.5-2H21V5H6.2L5.8 3H3zm5 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM9 10h8v2H9v-2zm0 4h5v2H9v-2z"/>
                        </svg>
                        <div class="text-gray-700 min-w-0">
                            <p class="font-semibold text-2xl lg:text-3xl">{{ ordersCount }}</p>
                            <p class="text-sm lg:text-base">Total Orders</p>
                        </div>
                    </div>
                </div>
                <div class="w-full cursor-pointer hover:shadow-md shadow rounded-lg">
                    <div class="w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6">
                        <svg class="w-12 h-12 lg:w-16 lg:h-14 fill-current mr-4 flex-shrink-0" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2C5.447 2 5 2.447 5 3V21C5 21.553 5.447 22 6 22H18C18.553 22 19 21.553 19 21V3C19 2.447 18.553 2 18 2H6ZM7 4H17V20H7V4ZM9 7H15V9H9V7ZM9 11H15V13H9V11ZM9 15H15V17H9V15Z"/>
                        </svg>
                        <div class="text-gray-700 min-w-0">
                            <p class="font-semibold text-2xl lg:text-3xl">{{ taxInvoiceCount }}</p>
                            <p class="text-sm lg:text-base">Tax Invoice</p>
                        </div>
                    </div>
                </div>
                <div class="w-full cursor-pointer hover:shadow-md shadow rounded-lg">
                    <div class="w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6">
                        <svg class="w-12 h-12 lg:w-16 lg:h-14 fill-current mr-4 flex-shrink-0" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6 2C5.447 2 5 2.447 5 3V21C5 21.553 5.447 22 6 22H18C18.553 22 19 21.553 19 21V3C19 2.447 18.553 2 18 2H6ZM7 4H17V20H7V4ZM9 7H15V9H9V7ZM9 11H15V13H9V11ZM9 15H15V17H9V15Z"/>
                        </svg>
                        <div class="text-gray-700 min-w-0">
                            <p class="font-semibold text-2xl lg:text-3xl">{{ retailInvoiceCount }}</p>
                            <p class="text-sm lg:text-base">Retail Invoice</p>
                        </div>
                    </div>
                </div>
                <div class="w-full cursor-pointer hover:shadow-md shadow rounded-lg">
                    <div class="w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6">
                        <svg class="w-12 h-12 lg:w-16 lg:h-14 fill-current mr-4 flex-shrink-0" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 1a4 4 0 0 0-4 4v2H5a2 2 0 0 0-2 2v11a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3V5a4 4 0 0 0-4-4zm-2 4a2 2 0 1 1 4 0v2h-4V5zm2 7.5c-1.4 0-2.5.9-2.5 2h1.5c0-.5.5-1 1-1s1 .5 1 1c0 .5-.3.7-.8 1l-.2.1c-.6.3-1.3.9-1.3 1.9h1.5c0-.5.3-.7.8-1l.2-.1c.6-.4 1.3-.9 1.3-1.9 0-1.2-1.1-2-2.5-2zM11 18v1h2v-1h-2z"/>
                        </svg>
                        <div class="text-gray-700 min-w-0">
                            <p class="font-semibold text-2xl lg:text-3xl">{{ purchaseCount }}</p>
                            <p class="text-sm lg:text-base">Total Purchase</p>
                        </div>
                    </div>
                </div>
                <div class="w-full cursor-pointer hover:shadow-md shadow rounded-lg">
                    <div class="w-full bg-white border text-indigo-600 rounded-lg flex items-center p-4 lg:p-6">
                        <svg class="w-12 h-12 lg:w-16 lg:h-14 fill-current mr-4 flex-shrink-0" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 5c0-1.1.9-2 2-2h14c1.1 0 2 .9 2 2v14c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2V5zm2 0v14h14V5H5zm2 4h10v2H7V9zm0 4h6v2H7v-2z"/>
                        </svg>
                        <div class="text-gray-700 min-w-0">
                            <p class="font-semibold text-2xl lg:text-3xl">{{ jobCardCount }}</p>
                            <p class="text-sm lg:text-base">Total Jobcard</p>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="user.role_id == 1" class="mt-6 w-full">
                <MultiLineChart :chartData="lineChartData" />
            </div>
            <div v-if="user.role_id == 1" class="mt-6 flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
                <div class="w-full lg:w-2/3">
                    <div class="space-y-6">
                        <div>
                            <p class="text-lg lg:text-xl font-semibold mb-4 lg:mb-6">
                                Sales
                            </p>
                            <BarChart :chartData="salesChartData" :chartOptions="chartOptions"></BarChart>
                        </div>
                        <div>
                            <p class="text-lg lg:text-xl font-semibold mb-4 lg:mb-6">
                                Purchase
                            </p>
                            <BarChart :chartData="purchaseChartData" :chartOptions="chartOptions"></BarChart>
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-1/3">
                    <div>
                        <p class="text-lg lg:text-xl font-semibold mb-4 lg:mb-6">
                            Sales By Persons
                        </p>
                        <PieChart :chartData="pieChartData" :chartOptions="pieChartOptions" />
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 my-6">
                <div class="w-full cursor-pointer">
                    <p class="text-xl font-semibold mb-4">
                        Recent Quotations
                    </p>
                    <div v-if="recentQuotations.length === 0" class="w-full bg-red-50 border rounded-lg p-4 items-center">
                        <p class="text-sm font-semibold text-red-500">
                            No Records Found
                        </p>
                    </div>
                    <div v-if="recentQuotations.length > 0" class="w-full bg-white border rounded-lg p-4 overflow-y-auto hover:shadow-md shadow rounded-lg" style="max-height: 420px;">
                        <div
                            v-for="quotation in recentQuotations"
                            :key="quotation.id"
                            class="w-full bg-gray-50 border rounded-lg flex justify-between items-center px-4 py-2 mb-4 hover:bg-gray-100">
                            <ActionLink :href="route('quotation.view', { id: quotation.id, source: 'dashboard' })">
                                <template #text>
                                    <div>
                                        <p class="text-sm font-semibold">
                                            {{ quotation?.customers?.customer_name ?? "" }}
                                        </p>
                                        <p class="text-sm font-semibold text-gray-500">
                                            {{ (quotation.quotation_number) }} - {{ formatDate(quotation.date) }}
                                        </p>
                                        <p class="font-semibold text-base text-green-600">
                                            ₹{{ formatAmount(quotation.total_amount) }}
                                        </p>
                                    </div>
                                </template>
                            </ActionLink>
                        </div>
                    </div>
                </div>
                <div class="w-full cursor-pointer">
                    <p class="text-xl font-semibold mb-4">
                        Recent Purchase
                    </p>
                    <div v-if="recentPurchaseOrder.length === 0" class="w-full bg-red-50 border rounded-lg p-4 items-center">
                        <p class="text-sm font-semibold text-red-500">
                            No Records Found
                        </p>
                    </div>
                    <div v-if="recentPurchaseOrder.length > 0" class="w-full bg-white border rounded-lg p-4 overflow-y-auto hover:shadow-md shadow rounded-lg" style="max-height: 420px;">
                        <div
                            v-for="purchaseOrder in recentPurchaseOrder"
                            :key="purchaseOrder.id"
                            class="w-full bg-gray-50 border rounded-lg flex justify-between items-center px-4 py-2 mb-4 hover:bg-gray-100"
                        >
                            <ActionLink :href="route('purchaseinvoice.view', { id: purchaseOrder.id, source: 'dashboard'})">
                                <template #text>
                                    <div>
                                        <p class="text-sm font-semibold">
                                            {{ purchaseOrder ? purchaseOrder.purchase_order?.company?.name : "" }}
                                        </p>
                                        <p class="text-sm font-semibold text-gray-500">
                                            {{ (purchaseOrder.customer_invoice_no) }} - {{ formatDate(purchaseOrder.customer_invoice_date) }}
                                        </p>
                                        <p class="font-semibold text-base text-green-600">
                                            ₹{{ formatAmount(purchaseOrder.total_amount)}}
                                        </p>
                                    </div>
                                </template>
                            </ActionLink>
                        </div>
                    </div>
                </div>
                <div class="w-full cursor-pointer">
                    <p class="text-xl font-semibold mb-4">
                        Recent Invoices
                    </p>
                    <div v-if="recentInvoices.length === 0" class="w-full bg-red-50 border rounded-lg p-4 items-center">
                        <p class="text-sm font-semibold text-red-500">
                            No Records Found
                        </p>
                    </div>
                    <div v-if="recentInvoices.length > 0"
                        class="w-full bg-white border rounded-lg p-4 overflow-y-auto hover:shadow-md shadow rounded-lg" style="max-height: 420px;">
                        <!-- {{ recentInvoices }} -->
                        <div
                            v-for="invoice in recentInvoices"
                            :key="invoice.id"
                            class="w-full bg-gray-50 border rounded-lg flex justify-between items-center px-4 py-2 mb-4 hover:bg-gray-100"
                        >
                            <ActionLink :href="route('invoice.view', { id: invoice.id, source: 'dashboard'})">
                                <template #text>
                                    <div>
                                        <p class="text-sm font-semibold">
                                            {{ invoice?.customers?.customer_name ?? "" }}
                                        </p>
                                        <p class="text-sm font-semibold text-gray-500">
                                            {{ (invoice.invoice_no) }} - {{ formatDate(invoice.date) }}
                                        </p>
                                        <p class="font-semibold text-base text-green-600">
                                            ₹{{ formatAmount(invoice.total_amount)}}
                                        </p>
                                    </div>
                                </template>
                            </ActionLink>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-xl font-semibold my-4">
                Low Stock Products
            </p>
            <div class="mt-6 overflow-x-auto sm:rounded-lg">
                <div class="shadow sm:rounded-lg">
                    <table class="w-full text-sm text-left rtl:text-right text-gray-500">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50" style="border-top: 3px solid white;">
                            <tr class="border-b-2">
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">PRODUCT NAME</th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">COMPANY</th>
                                <th scope="col" class="px-4 py-4 text-sm font-semi bold text-gray-900">STOCK</th>
                            </tr>
                        </thead>
                        <tbody class="" v-if="productsWithLowStock.data && (productsWithLowStock.data.length > 0)">
                            <tr class="odd:bg-white even:bg-gray-50 border-b" v-for="userData in productsWithLowStock.data" :key="userData.id">
                                <td class="whitespace-nowrap px-4 py-2.5 font-medium text-gray-900">{{ userData.item_code }} {{ userData.item_code ? userData.item_code + '  : ' :  ''}}  {{ userData.name ?? '' }}</td>
                                <td class="whitespace-nowrap px-4 py-2.5">{{ userData.company_name }}</td>
                                <td class="whitespace-nowrap px-4 py-2.5">{{ userData.stock }}</td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr class="bg-white">
                                <td colspan="6" class="text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900">
                                    No data found.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <Pagination v-if="productsWithLowStock.data && (productsWithLowStock.data.length > 0)" class="mt-6" :links="productsWithLowStock.links"></Pagination>
            </div>
        </div>
    </AdminLayout>
</template>

<style scoped>
    .video-background {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 140%;
        object-fit: cover;
    }
</style>
