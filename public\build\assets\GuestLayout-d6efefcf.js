import{o as s,c as t,b as o,B as a}from"./app-6cdaf2bc.js";import{_ as r}from"./_plugin-vue_export-helper-c27b6911.js";const _={class:"min-h-screen loginview flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-gray-100"},d={class:"w-full sm:max-w-md mt-6 p-6 bg-white shadow overflow-hidden sm:rounded-lg border-gray-300"},n={__name:"GuestLayout",setup(c){return(e,l)=>(s(),t("div",_,[o("div",d,[a(e.$slots,"default",{},void 0,!0)])]))}},m=r(n,[["__scopeId","data-v-49e17d4d"]]);export{m as G};
