import{r as w,j as G,l as M,o as d,c as u,a as l,u as n,w as D,F as O,Z as se,b as o,t as _,f,d as oe,n as y,k as ae,v as ne,i as j,g as le,T as ie,s as re,x as de}from"./app-4f4c883b.js";import{_ as ue,a as ce}from"./AdminLayout-d9d2bc31.js";import{_ as h}from"./InputLabel-468796e0.js";import{P as me}from"./PrimaryButton-3e579b0b.js";import{_ as V}from"./TextInput-21f4f57b.js";import{_ as _e}from"./TextArea-b7098398.js";import{_ as pe}from"./RadioButton-ef33c90c.js";import{_ as B}from"./SearchableDropdown-ace42120.js";import{u as ve}from"./index-20fd5540.js";/* empty css                                                                          */import{_ as fe}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as ye}from"./Checkbox-731cb89b.js";const C=b=>(re("data-v-6c750d2f"),b=b(),de(),b),he={class:"h-screen animate-top"},ge={class:"bg-white p-4 shadow sm:p-8 sm:rounded-lg border"},xe={class:"sm:flex sm:items-center"},be=C(()=>o("div",{class:"sm:flex-auto"},[o("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Receive Payment")],-1)),ke={class:"flex items-center justify-between"},we={key:0,class:"text-base font-semibold leading-6 text-gray-900"},Ve=["onSubmit"],Fe={class:"border-b border-gray-900/10 pb-12"},Ae={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-8"},Ce={class:"sm:col-span-3"},Se={class:"relative mt-2"},Ne={class:"sm:col-span-3"},$e={class:"relative mt-2"},Ue={class:"sm:col-span-2"},Pe={class:"relative mt-2"},ze={key:0,class:"sm:col-span-3"},Te={class:"relative mt-2"},De={key:1,class:"sm:col-span-3"},Oe={key:2,class:"sm:col-span-2"},Be={key:3,class:"sm:col-span-1"},Ee={key:4,class:"sm:col-span-1"},Ie={key:5,class:"sm:col-span-1"},Me={key:6,class:"sm:col-span-3"},je={key:7,class:"sm:col-span-2"},Re={class:"mt-4 flex justify-start"},Le={class:"text-base font-semibold"},Ye={key:8,class:"sm:col-span-2"},qe={key:9,class:"sm:col-span-2"},He={key:10,class:"sm:col-span-2"},Ze={class:"relative mt-2"},Ge={key:11,class:"sm:col-span-3"},Je={class:"sm:col-span-6"},Ke={class:"overflow-x-auto divide-y divide-gray-300 w-full"},Qe=C(()=>o("div",{class:"w-full"},[o("thead",{class:"w-full"},[o("tr",{class:""},[o("th",{scope:"col",class:""}),o("th",{scope:"col",class:"whitespace-nowrap min-w-20 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Type "),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-6 text-left text-sm font-semibold text-gray-900"}," Invoice No "),o("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Total Amount (₹) "),o("th",{scope:"col",class:"whitespace-nowrap min-w-32 py-3 px-2 text-left text-sm font-semibold text-gray-900"}," Pending Amount (₹) "),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-4 text-left text-sm font-semibold text-gray-900"}," Amount (₹)"),o("th",{scope:"col",class:"whitespace-nowrap min-w-36 py-3 px-10 text-left text-sm font-semibold text-gray-900"}," Date")])])],-1)),We={style:{"overflow-y":"auto","max-height":"318px"}},Xe={class:"divide-y divide-gray-300 bg-white"},et={class:"whitespace-nowrap px-2 text-sm text-gray-900"},tt={class:"text-sm text-gray-900 leading-6 py-1.5"},st={class:"whitespace-nowrap min-w-20 px-2 text-sm text-gray-900"},ot={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},at={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},nt={class:"whitespace-nowrap min-w-32 px-2 text-sm text-gray-900"},lt={class:"whitespace-nowrap px-2 pb-2 text-sm text-gray-900 min-w-36"},it={key:0,class:"text-red-500 text-xs absolute"},rt={class:"whitespace-nowrap px-2 text-sm text-gray-900"},dt={class:"sm:col-span-2"},ut={class:"mt-2 p-3 bg-gray-50 rounded-md"},ct={class:"space-y-2 text-sm"},mt={class:"flex items-center gap-2"},_t=C(()=>o("hr",{class:"my-2"},null,-1)),pt={class:"flex justify-between items-center font-semibold"},vt=C(()=>o("span",null,"Settlement:",-1)),ft={key:0,class:"text-red-500 text-xs mt-1"},yt={key:12,class:"sm:col-span-6"},ht={key:0,class:"mt-5 overflow-x-auto divide-y divide-gray-300 w-full"},gt=C(()=>o("thead",null,[o("tr",null,[o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Date"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Bank"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Credit Amount (₹)"),o("th",{scope:"col",class:"py-3.5 pr-3 text-left text-sm font-semibold text-gray-900"},"Available Credit (₹)")])],-1)),xt={class:"divide-y divide-gray-300 bg-white"},bt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},kt={class:"whitespace-nowrap py-3 text-sm text-gray-900"},wt={class:"flex flex-col"},Vt={class:"text-sm text-gray-900"},Ft={class:"whitespace-nowrap py-3 text-sm text-gray-900"},At={class:"whitespace-nowrap py-3 text-sm text-gray-900"},Ct={class:"flex mt-6 items-center justify-between"},St={class:"ml-auto flex items-center justify-end gap-x-6"},Nt=C(()=>o("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)),$t={key:0,class:"text-sm text-gray-600"},Ut={__name:"Add",props:["paymentType","bankinfo","organization","customers","invoices","credit"],setup(b){const S=b;w([]);const t=ve("post","/receipt",{organization_id:"",customer_id:"",payment_type:"",date:"",note:"",amount:0,tds_amount:0,discount_amount:0,round_off:0,check_number:"",bank_name:"",org_bank_id:"",invoice:[],settled_amount:"",advance_amount:"",is_credit:"",credit_data:[],total_unused_amount:""}),N=w(""),J=()=>{t.settled_amount=U.value,t.advance_amount=Y.value,t.total_unused_amount=$.value,t.is_credit=m.value,t.invoice=g.value,t.credit_data=k.value,t.submit({preserveScroll:!0,onSuccess:()=>t.reset()})},K=(a,s)=>{N.value=s,t.payment_type=a,t.errors.payment_type=null,s==="Cash"?t.note="Cash":t.note==="Cash"&&(t.note="")},z=w([]),k=w([]),$=w(""),R=w([]),Q=(a,s)=>{const e=S.bankinfo.filter(r=>r.organization_id===a);R.value=e,t.customer_id&&L(t.customer_id,a);const i=S.credit.filter(r=>r.organization_id===a&&r.customer_id===t.customer_id);k.value=i,$.value=k.value.reduce((r,c)=>r+c.unused_amount,0),t.organization_id=a,t.errors.organization_id=null},W=(a,s)=>{L(a,t.organization_id);const e=S.credit.filter(i=>i.customer_id===a&&i.organization_id===t.organization_id);k.value=e,$.value=k.value.reduce((i,r)=>i+r.unused_amount,0),t.customer_id=a,t.errors.customer_id=null},L=(a,s)=>{if(!a||!s){z.value=[];return}const e=S.customers.find(c=>c.id===a),i=e==null?void 0:e.party_id,r=S.invoices.filter(c=>{const x=c.organization_id===s;return c.invoice_type==="sales"?x&&c.customer_id===a:c.invoice_type==="purchase"&&i?x&&c.party_id===i:!1});z.value=r},X=(a,s)=>{t.org_bank_id=a,t.errors.org_bank_id=null},U=G(()=>g.value.reduce((a,s)=>{if(s.check&&s.amount){const e=parseFloat(s.amount);return s.invoice_type==="sales"?a+e:a-e}return a},0)),Y=G(()=>{const a=parseFloat(t.amount||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0),s=parseFloat(t.round_off||0),e=U.value;return a-e-s}),E=()=>{},F=a=>{let s=a.toFixed(2).toString(),[e,i]=s.split("."),r=e.substring(e.length-3),c=e.substring(0,e.length-3);return c!==""&&(r=","+r),`${c.replace(/\B(?=(\d{2})+(?!\d))/g,",")+r}.${i}`},q=a=>{const s=new Date(a),e={year:"numeric",month:"short",day:"numeric"};return s.toLocaleDateString("en-US",e)},m=w("No"),ee=[{value:"Yes",label:"Yes"},{value:"No",label:"No"}],te=a=>{const s=m.value==="Yes"?parseFloat($.value||0):parseFloat(t.amount||0)+parseFloat(t.round_off||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0);if(!g.value[a].check){g.value[a].amount=0;return}const e=g.value.filter(v=>v.check),i=e.some(v=>v.invoice_type==="sales"),r=e.some(v=>v.invoice_type==="purchase");if(i&&r&&e.length>1&&H(s,e))return;let c=s;g.value.forEach((v,T)=>{if(v.check&&T!==a&&parseFloat(v.amount||0)>0){const P=parseFloat(v.amount||0);v.invoice_type==="sales"?c-=P:v.invoice_type==="purchase"&&(c+=P)}});const x=parseFloat(g.value[a].pending_amount||0),p=Math.min(x,Math.max(0,c));g.value[a].amount=p.toFixed(2)},H=(a,s)=>{const e=s.filter(p=>p.invoice_type==="sales"),i=s.filter(p=>p.invoice_type==="purchase"),r=e.reduce((p,v)=>p+parseFloat(v.pending_amount||0),0),c=i.reduce((p,v)=>p+parseFloat(v.pending_amount||0),0),x=r-c;return Math.abs(a-Math.abs(x))<=1?(s.forEach(p=>{p.amount=parseFloat(p.pending_amount||0).toFixed(2)}),!0):x>0&&a>=x?(s.forEach(p=>{p.amount=parseFloat(p.pending_amount||0).toFixed(2)}),!0):!1},g=w([{id:"",date:"",invoice_no:"",total_amount:"",pending_amount:"",check:!1,amount:0}]),I=()=>{g.value=z.value.map(a=>({id:a.id,date:a.date,invoice_no:a.invoice_no,total_amount:parseFloat(a.total_amount||0).toFixed(2),pending_amount:parseFloat(a.pending_amount||0).toFixed(2),invoice_type:a.invoice_type||"sales",check:!1,amount:"0.00"}))};M(z,()=>{I()}),M(m,()=>{I()}),M(()=>t.amount,()=>{if(m.value==="No"){I();const a=g.value.filter(i=>i.check),s=a.some(i=>i.invoice_type==="sales"),e=a.some(i=>i.invoice_type==="purchase");if(s&&e&&a.length>1){const i=parseFloat(t.amount||0)+parseFloat(t.round_off||0)+parseFloat(t.discount_amount||0)+parseFloat(t.tds_amount||0);H(i,a)}}});const A=a=>{t.errors[a]=null,t.errors.settled_amount=null};return(a,s)=>(d(),u(O,null,[l(n(se),{title:"Receipt"}),l(ue,null,{default:D(()=>[o("div",he,[o("div",ge,[o("div",xe,[be,o("div",ke,[k.value.length>0?(d(),u("div",we," Credits Available: ₹"+_(F($.value)),1)):f("",!0)])]),o("form",{onSubmit:oe(J,["prevent"]),class:""},[o("div",Fe,[o("div",Ae,[o("div",Ce,[l(h,{for:"payment_type",value:"Organization"}),o("div",Se,[l(B,{options:b.organization,modelValue:n(t).organization_id,"onUpdate:modelValue":s[0]||(s[0]=e=>n(t).organization_id=e),onOnchange:Q,class:y({"error rounded-md":n(t).errors.organization_id})},null,8,["options","modelValue","class"])])]),o("div",Ne,[l(h,{for:"payment_type",value:"Customer"}),o("div",$e,[l(B,{options:b.customers,modelValue:n(t).customer_id,"onUpdate:modelValue":s[1]||(s[1]=e=>n(t).customer_id=e),onOnchange:W,class:y({"error rounded-md":n(t).errors.customer_id})},null,8,["options","modelValue","class"])])]),o("div",Ue,[l(h,{for:"role_id",value:"Payment Through Credit ?"}),o("div",Pe,[l(pe,{modelValue:m.value,"onUpdate:modelValue":s[2]||(s[2]=e=>m.value=e),options:ee},null,8,["modelValue"])])]),m.value=="No"?(d(),u("div",ze,[l(h,{for:"payment_type",value:"Payment Type"}),o("div",Te,[l(B,{options:b.paymentType,modelValue:n(t).payment_type,"onUpdate:modelValue":s[3]||(s[3]=e=>n(t).payment_type=e),onOnchange:K,class:y({"error rounded-md":n(t).errors.payment_type})},null,8,["options","modelValue","class"])])])):f("",!0),m.value=="No"?(d(),u("div",De,[l(h,{for:"date",value:"Payment Date"}),ae(o("input",{"onUpdate:modelValue":s[4]||(s[4]=e=>n(t).date=e),class:y(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":n(t).errors.date}]),type:"date",onChange:s[5]||(s[5]=e=>A("date"))},null,34),[[ne,n(t).date]])])):f("",!0),m.value=="No"?(d(),u("div",Oe)):f("",!0),m.value=="No"?(d(),u("div",Be,[l(h,{for:"tds_amount",value:"TDS Amount"}),l(V,{type:"text",onChange:s[6]||(s[6]=e=>A("tds_amount")),onInput:s[7]||(s[7]=e=>E()),modelValue:n(t).tds_amount,"onUpdate:modelValue":s[8]||(s[8]=e=>n(t).tds_amount=e),class:y({"error rounded-md":n(t).errors.tds_amount})},null,8,["modelValue","class"])])):f("",!0),m.value=="No"?(d(),u("div",Ee,[l(h,{for:"discount_amount",value:"Discount Amount"}),l(V,{type:"text",onChange:s[9]||(s[9]=e=>A("discount_amount")),onInput:s[10]||(s[10]=e=>E()),modelValue:n(t).discount_amount,"onUpdate:modelValue":s[11]||(s[11]=e=>n(t).discount_amount=e),class:y({"error rounded-md":n(t).errors.discount_amount})},null,8,["modelValue","class"])])):f("",!0),m.value=="No"?(d(),u("div",Ie,[l(h,{for:"round_off",value:"Round Off"}),l(V,{type:"text",onChange:s[12]||(s[12]=e=>A("round_off")),modelValue:n(t).round_off,"onUpdate:modelValue":s[13]||(s[13]=e=>n(t).round_off=e),class:y({"error rounded-md":n(t).errors.round_off})},null,8,["modelValue","class"])])):f("",!0),m.value=="No"?(d(),u("div",Me,[l(h,{for:"amount",value:"Amount"}),l(V,{id:"amount",type:"text",onChange:s[14]||(s[14]=e=>A("amount")),onInput:s[15]||(s[15]=e=>E()),modelValue:n(t).amount,"onUpdate:modelValue":s[16]||(s[16]=e=>n(t).amount=e),class:y({"error rounded-md":n(t).errors.amount})},null,8,["modelValue","class"])])):f("",!0),m.value=="No"?(d(),u("div",je,[l(h,{for:"advance",value:"Advance(Ref) Amount"}),o("div",Re,[o("p",Le,_(F(Y.value)),1)])])):f("",!0),N.value=="Cheque"&&m.value=="No"?(d(),u("div",Ye,[l(h,{for:"check_number",value:"Cheque Number"}),l(V,{id:"check_number",type:"text",modelValue:n(t).check_number,"onUpdate:modelValue":s[17]||(s[17]=e=>n(t).check_number=e),class:y({"error rounded-md":n(t).errors["data.check_number"]})},null,8,["modelValue","class"])])):f("",!0),N.value=="Cheque"&&m.value=="No"?(d(),u("div",qe,[l(h,{for:"bank_name",value:"Bank Name"}),l(V,{id:"bank_name",type:"text",modelValue:n(t).bank_name,"onUpdate:modelValue":s[18]||(s[18]=e=>n(t).bank_name=e),class:y({"error rounded-md":n(t).errors["data.bank_name"]})},null,8,["modelValue","class"])])):f("",!0),N.value!="Cash"&&m.value=="No"?(d(),u("div",He,[l(h,{for:"org_bank_id",value:"Our Bank"}),o("div",Ze,[l(B,{options:R.value,modelValue:n(t).org_bank_id,"onUpdate:modelValue":s[19]||(s[19]=e=>n(t).org_bank_id=e),onOnchange:X,class:y({"error rounded-md":n(t).errors.org_bank_id})},null,8,["options","modelValue","class"])])])):f("",!0),N.value!="Cash"&&m.value=="No"?(d(),u("div",Ge)):f("",!0),o("div",Je,[o("table",Ke,[Qe,o("div",We,[o("tbody",Xe,[(d(!0),u(O,null,j(g.value,(e,i)=>(d(),u("tr",{key:i},[o("td",et,[o("div",tt,[l(ye,{name:"check",checked:e.check,"onUpdate:checked":r=>e.check=r,onChange:r=>te(i)},null,8,["checked","onUpdate:checked","onChange"])])]),o("td",st,[o("span",{class:y([e.invoice_type==="sales"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800","inline-flex items-center rounded-md px-2 py-1 text-xs font-medium"])},_(e.invoice_type==="sales"?"SALES":"PURCHASE"),3)]),o("td",ot,_(e.invoice_no),1),o("td",at,_(e.total_amount),1),o("td",nt,_(e.pending_amount),1),o("td",lt,[l(V,{id:"amount",type:"text",modelValue:e.amount,"onUpdate:modelValue":r=>e.amount=r,onChange:r=>A("invoice."+i+".amount"),class:y({error:n(t).errors[`invoice.${i}.amount`]})},null,8,["modelValue","onUpdate:modelValue","onChange","class"]),n(t).errors[`invoice.${i}.amount`]?(d(),u("p",it,_(n(t).errors[`invoice.${i}.amount`]),1)):f("",!0)]),o("td",rt,_(q(e.date)),1)]))),128))])])])]),o("div",dt,[l(h,{for:"note",value:"Settlement Summary"}),o("div",ut,[o("div",ct,[(d(!0),u(O,null,j(g.value.filter(e=>e.check),e=>(d(),u("div",{key:e.id,class:"flex justify-between items-center"},[o("div",mt,[o("span",{class:y([e.invoice_type==="sales"?"bg-green-100 text-green-800":"bg-blue-100 text-blue-800","inline-flex items-center rounded-md px-1.5 py-0.5 text-xs font-medium"])},_(e.invoice_type==="sales"?"S":"P"),3),o("span",null,_(e.invoice_no),1)]),o("span",{class:y([e.invoice_type==="sales"?"text-green-600":"text-blue-600","font-medium"])},_(e.invoice_type==="sales"?"+":"-")+"₹"+_(F(parseFloat(e.amount||0))),3)]))),128)),_t,o("div",pt,[vt,o("span",{class:y(U.value>=0?"text-green-600":"text-red-600")}," ₹"+_(F(Math.abs(U.value)))+" "+_(U.value>=0?"(Receive)":"(Pay)"),3)])])]),n(t).errors.settled_amount?(d(),u("p",ft,_(n(t).errors.settled_amount),1)):f("",!0)]),m.value=="No"?(d(),u("div",yt,[l(h,{for:"note",value:"Note"}),l(_e,{id:"note",type:"text",rows:2,modelValue:n(t).note,"onUpdate:modelValue":s[20]||(s[20]=e=>n(t).note=e)},null,8,["modelValue"])])):f("",!0)]),k.value.length>0&&m.value=="Yes"?(d(),u("table",ht,[gt,o("tbody",xt,[(d(!0),u(O,null,j(k.value,(e,i)=>{var r,c,x,p,v,T,P,Z;return d(),u("tr",{key:i},[o("td",bt,_(q(e.date)),1),o("td",kt,[o("div",wt,[o("div",Vt,_((c=(r=e.paymentreceive)==null?void 0:r.bank_info)!=null&&c.bank_name?(p=(x=e.paymentreceive)==null?void 0:x.bank_info)==null?void 0:p.bank_name:"Cash")+" - "+_((T=(v=e.paymentreceive)==null?void 0:v.bank_info)!=null&&T.account_number?(Z=(P=e.paymentreceive)==null?void 0:P.bank_info)==null?void 0:Z.account_number:""),1)])]),o("td",Ft,_(F(e.amount)),1),o("td",At,_(F(e.unused_amount)),1)])}),128))])])):f("",!0)]),o("div",Ct,[o("div",St,[l(ce,{href:a.route("receipt.index")},{svg:D(()=>[Nt]),_:1},8,["href"]),l(me,{disabled:n(t).processing},{default:D(()=>[le("Save")]),_:1},8,["disabled"]),l(ie,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:D(()=>[n(t).recentlySuccessful?(d(),u("p",$t,"Saved.")):f("",!0)]),_:1})])])],40,Ve)])])]),_:1})],64))}},Yt=fe(Ut,[["__scopeId","data-v-6c750d2f"]]);export{Yt as default};
