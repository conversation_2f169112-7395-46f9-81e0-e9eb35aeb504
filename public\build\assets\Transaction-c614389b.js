import{h as Y,r as y,j as L,m as P,o as l,c as i,a as d,u as w,w as I,F as N,Z as j,b as t,t as c,g as O,f as D,k as A,v as F,n as k,i as q}from"./app-97275a91.js";import{_ as G}from"./AdminLayout-595ad5a7.js";import{_ as X}from"./CreateButton-91ea7c7b.js";import{_ as Z}from"./SimpleDropdown-f072c5ba.js";/* empty css                                                              */import{_ as v}from"./InputLabel-eb73087c.js";const H={class:"animate-top"},J={class:"sm:flex sm:items-center"},K=t("div",{class:"sm:flex-auto"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Company Transaction")],-1),Q={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},W={class:"flex items-center space-x-4"},ee={class:"text-lg font-semibold leading-7 text-gray-900"},te={class:"flex justify-end w-20"},ae={class:"mt-8 p-5 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},oe={class:"flex justify-between mb-2"},se={class:"flex"},ne=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1),re={class:"inline-flex items-center space-x-4 justify-end w-full"},le={key:0,class:"p-2 flex justify-end text-base font-semibold leading-6 text-gray-900"},ie=["src"],de={class:"grid grid-cols-1 sm:grid-cols-12 gap-x-6 gap-y-5 items-center"},ce={class:"sm:col-span-4"},me={class:"relative mt-2"},ue={class:"sm:col-span-4"},pe={class:"sm:col-span-4"},ge={class:"mt-8 overflow-x-auto sm:rounded-lg"},ye={key:0,class:"shadow sm:rounded-lg"},_e={class:"w-full text-sm text-left rtl:text-right text-gray-500"},he=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," DATE "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," NARRATION "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," DEBIT (₹) "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," CREDIT (₹) "),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900"}," BALANCE (₹) ")])],-1),fe={key:0},xe={class:"px-4 py-2.5 min-w-24"},ve={class:"px-4 py-2.5 flex items-center space-x-2 max-w-[32rem] font-medium text-gray-900"},be={class:""},we={class:"overflow-x-auto overflow-y-hidden whitespace-nowrap"},De={class:"px-4 py-2.5 min-w-28"},ke={class:"px-4 py-2.5 min-w-28"},Ce={class:"px-4 py-2.5 min-w-28"},Se={key:1},Te=t("tr",{class:"bg-white"},[t("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1),Ee=[Te],ze={__name:"Transaction",props:["data","organization","organizationId","organizationId","creditdata","companyId","company"],setup(f){const p=f,b=Y({}),B=y("companies tranasction"),$=()=>{const a=new Date,o=a.getMonth(),e=a.getFullYear();return`${o<3?e-1:e}-04-01`},z=()=>{const a=new Date,o=a.getFullYear(),e=String(a.getMonth()+1).padStart(2,"0"),n=String(a.getDate()).padStart(2,"0");return`${o}-${e}-${n}`},m=y($()),g=y(z());y("");const _=y(p.organizationId),M=a=>{b.get(route("companies.transaction",{id:p.companyId,organization_id:a}),{preserveState:!0})},h=L(()=>{let a=0;p.data.forEach(e=>{const n=new Date(e.date),s=new Date(m.value);n<s&&(e.payment_type==="cr"?a+=parseFloat(e.amount):e.payment_type==="dr"&&(a-=parseFloat(e.amount)))});let o=p.data.filter(e=>{const n=new Date(e.date),s=new Date(m.value),u=new Date(g.value);if(m.value&&g.value)return n>=s&&n<=u;const r=!m.value||n>=s,E=!g.value||n<=u;return r&&E});return o=o.map(e=>{e.payment_type==="cr"?a+=parseFloat(e.amount):e.payment_type==="dr"&&(a-=parseFloat(e.amount));let n=a>=0?"cr":"dr",s=x(Math.abs(a))+" "+n;return{...e,balance:s}}),o}),C=()=>{};P(()=>{});const U=(a,o)=>{_.value=a,M(_.value)},R=()=>{const a=B.value.replace(/\s+/g,"_"),o={company_id:p.companyId||"",organization_id:_.value||"",from_date:m.value||"",to_date:g.value||""},n=`/company/{id}/transactions/export?${new URLSearchParams(o).toString()}`;fetch(n,{method:"GET"}).then(s=>{if(!s.ok)throw new Error("Network response was not ok");return s.blob()}).then(s=>{const u=window.URL.createObjectURL(new Blob([s])),r=document.createElement("a");r.href=u,r.setAttribute("download",`${a}.xlsx`),document.body.appendChild(r),r.click(),document.body.removeChild(r)}).catch(s=>{console.error("Error exporting data:",s)})},x=a=>{let o=a.toFixed(2).toString(),[e,n]=o.split("."),s=e.substring(e.length-3),u=e.substring(0,e.length-3);return u!==""&&(s=","+s),`${u.replace(/\B(?=(\d{2})+(?!\d))/g,",")+s}.${n}`},V=a=>{const o=new Date(a),e={year:"numeric",month:"short",day:"numeric"};return o.toLocaleDateString("en-US",e)},S=y("");S.value=p.creditdata.reduce((a,o)=>a+o.unused_amount,0);const T=a=>{switch(a){case"invoice":return{label:"SALES",style:"bg-green-100 text-green-800"};case"purchase_invoice":return{label:"PURC.",style:"bg-blue-100 text-blue-800"};case"payment_receive":return{label:"RECEIPT",style:"bg-green-100 text-green-800"};case"payment_paid":return{label:"PAYMENT",style:"bg-blue-100 text-blue-800"};case"credit_note":return{label:"CREDIT",style:"bg-yellow-100 text-yellow-800"};case"debit_note":return{label:"DEBIT",style:"bg-purple-100 text-purple-800"};default:return{label:a.toUpperCase(),style:"bg-gray-100 text-gray-800"}}};return(a,o)=>(l(),i(N,null,[d(w(j),{title:"Company Transaction"}),d(G,null,{default:I(()=>[t("div",H,[t("div",J,[K,t("div",Q,[t("div",W,[t("div",null,[t("h1",ee,c(f.company.name),1)]),t("div",te,[d(X,{href:a.route("companies.index")},{default:I(()=>[O(" Back ")]),_:1},8,["href"])])])])]),t("div",ae,[t("div",oe,[t("div",se,[ne,d(v,{for:"company_id",value:"Filters"})]),t("div",re,[f.creditdata.length>0?(l(),i("div",le," Credits Available: ₹"+c(x(S.value)),1)):D("",!0),t("button",{onClick:R},[t("img",{class:"w-8 h-8",src:"/uploads/img/export-excel.png",alt:"LOGO"},null,8,ie)])])]),t("div",de,[t("div",ce,[d(v,{for:"company_id",value:"Organization Name"}),t("div",me,[d(Z,{options:f.organization,modelValue:_.value,"onUpdate:modelValue":o[0]||(o[0]=e=>_.value=e),onOnchange:U},null,8,["options","modelValue"])])]),t("div",ue,[d(v,{for:"date",value:"From Date"}),A(t("input",{"onUpdate:modelValue":o[1]||(o[1]=e=>m.value=e),class:k(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":w(b).errors.from_date}]),type:"date",onChange:C},null,34),[[F,m.value]])]),t("div",pe,[d(v,{for:"date",value:"To Date"}),A(t("input",{"onUpdate:modelValue":o[2]||(o[2]=e=>g.value=e),class:k(["mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",{"error rounded-md":w(b).errors.to_date}]),type:"date",onChange:C},null,34),[[F,g.value]])])])]),t("div",ge,[h.value&&h.value.length>0?(l(),i("div",ye,[t("table",_e,[he,h.value&&h.value.length>0?(l(),i("tbody",fe,[(l(!0),i(N,null,q(h.value,(e,n)=>(l(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:e.id},[t("td",xe,c(V(e.date)??"-"),1),t("td",ve,[t("div",be,[e.entity_type?(l(),i("span",{key:0,class:k(`${T(e.entity_type).style} inline-flex items-center rounded-md px-2 py-1 text-xs font-medium`)},c(T(e.entity_type).label),3)):D("",!0)]),t("div",we,c(e.note??"-"),1)]),t("td",De,c(e.payment_type=="dr"?x(e.amount):"-"),1),t("td",ke,c(e.payment_type=="cr"?x(e.amount):"-"),1),t("td",Ce,c(e.balance),1)]))),128))])):(l(),i("tbody",Se,Ee))])])):D("",!0)])])]),_:1})],64))}};export{ze as default};
